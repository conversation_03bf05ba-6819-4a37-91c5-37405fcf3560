# How to Run the Application

## Installation

```bash
$ npm install

Running the App

# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod

Running the Sentinel App & Debugging Within a Docker Container
1. Install Docker on your Local Machine.
2. Configure the AWS Access Credentials in the run.sh file.

Example:
docker run -d -p 3000:3000 -p 9229:9229 --name $containerName -v /var/www/html/apachedev/git/sentinel:/usr/app/sentinel \
  -e AWS_ACCESS_KEY_ID='YOUR_ACCESS_KEY' \
  -e AWS_SECRET_ACCESS_KEY='YOUR_SECRET_ACCESS_KEY' \
  -e AWS_SESSION_TOKEN='YOUR_SESSION_TOKEN' \
  $imageName

3. Configure VS Code for Debugging
In VS Code, set up this configuration for debugging to create the .vscode/launch.json:
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Docker: Attach to Node",
      "type": "node",
      "request": "attach",
      "port": 9229,
      "address": "localhost",
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "/usr/app/sentinel",
      "protocol": "inspector",
      "skipFiles": [
        "${workspaceFolder}/node_modules/**/*.js",
        "<node_internals>/**/*.js"
      ]
    }
  ]
}
4. Modify Docker File
Change the Dockerfile CMD:

CMD ["npm", "run", "start"] ->CMD ["npm", "run", "start:debug"]

5. Run the Command
In the command prompt, run:
./run.sh
This will delete the older image and container, recreate them, and run the container.

6. Check Application Logs
To check application logs, use:
docker logs -f sentinel_accounts
7. Attach the Debugger
Attach the debugger in VS Code and run by clicking on the run icon.

Stay in Touch
Author: [Uday Singh]





