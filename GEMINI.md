# Gemini Project Configuration

This file helps <PERSON> understand the project's conventions and commands.

## Overview

This is a NestJS application written in TypeScript. It serves as a backend for user authentication and management, supporting various database backends (MongoDB, MySQL, Firebase) and authentication strategies (JWT, Social Login, SAML).

## Key Technologies

- **Framework:** NestJS
- **Language:** TypeScript
- **Databases:** MongoDB, MySQL, Firebase
- **Authentication:** JWT, OAuth 2.0 (Social), SAML, API Keys
- **Templating:** Handlebars
- **Testing:** Jest
- **Linting/Formatting:** ESLint, Prettier

## Important Commands

- **Install dependencies:** `npm install`
- **Run the app (development):** `npm run start:dev`
- **Run tests (unit & e2e):** `npm test`
- **Lint files:** `npm run lint`
