@use "variables" as vars;

.forgot-password-container {

    /* For Microsoft Edge */
    input[type="password"]::-ms-reveal {
        display: none;
    }

    /* For Firefox */
    input[type="password"]::-moz-password-reveal {
        display: none;
    }

    .logo-image {
        padding-bottom: 32px;
        text-align: center;


        a {
            color: #777;
            font-size: 12px;
            cursor: pointer;
        }

        img {
            max-height: 36px;
        }

        .logo-text {
            display: inline-block;
            width: 70px;
            line-height: 24.5px;
            font-size: 17px;
            color: #757575;
            font-weight: 400;
            padding-left: 10px;
            margin-left: 8px;
            vertical-align: middle;
            position: relative;
            font-family: "Roboto", sans-serif;
        }

        .logo-text::before {
            width: 1.2px;
            height: 34px;
            content: "";
            display: inline-block;
            background: #E0E0E0;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            margin: auto;
        }

    }

    /* ✅ Styles for content headings */
    .content-heading {
        color: vars.$heading-color;
        font-size: 20px !important;
        font-weight: 500;
        text-align: center;
        padding-bottom: 8px;
        margin: 0;
    }

    .content-sub-heading {
        padding-bottom: 0px;
        font-size: 16px;
        line-height: 20px;
        color: #646464;
        text-align: center;
        display: flex;
        font-weight: 400;
        justify-content: center;
        margin: 0 auto;
    }

    /* ✅ Link styles */
    a.link1 {
        text-decoration: none;
        color: #1179EF;
        font-family: Roboto, sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
    }

    /* ✅ Error message styling */
    .error_msg {
        text-align: center;
        padding-bottom: 20px;
        font-size: 16px;
        font-weight: 400;
        line-height: 20px;
        color: #DD3636;
    }

    .form-container {
        form {
            position: relative;
            input[type="text"]:focus,
            input[type="password"]:focus,
            input[type="email"]:focus{
              border-radius: 3px;
              outline-offset: 0px;
              outline: 1px solid #118aef;
              border: 1px solid #007bff !important;
              background: rgb(232, 240, 254);
            }
            .form-input {
                margin-bottom: 20px;

                input:required:invalid {
                    background-position: right top;
                    background-repeat: no-repeat;
                    box-shadow: none;
                }

                input[type="text"],
                input[type="password"],
                input[type="email"],
                textarea {
                    height: 48px;
                    padding-left: 24px;
                    font-size: 16px;
                    font-weight: 400;
                    border-radius: 3px;
                    background: #fff;
                    border: 1px solid #B8D6FF;
                    color: #4F4F4F;
                    margin-bottom: 0px;
                    width: 100%;
                    font-family: inherit;
                    line-height: inherit;
                    margin: 0;
                }
                input[type="text"]::placeholder,
                input[type="password"]::placeholder,
                input[type="email"]::placeholder,
                textarea::placeholder {
                  color: #999;
                }
            }

            .submit-container {

                input[type=submit][disabled],
                input[type=submit][disabled]:hover {
                    background: #88BCF7;
                    border-color: #88BCF7;
                    opacity: 1;
                    color: #fff;
                    cursor: not-allowed !important;
                    box-shadow: none;
                }

                .btn-signup {
                    line-height: 46px;
                    background: #1179EF;
                    font-size: 16px;
                    font-weight: 500;
                    letter-spacing: normal;
                    box-shadow: none;
                    border-radius: 6px;
                    color: #fff;
                }
            }
        }
    }

    .create_account {
        text-align: center;
        /* border-top: 1px solid #ccc; */
        display: block;
        width: 100%;
        margin-top: 0;

        .back-register {
            margin-top: 20px;
            margin-bottom: 0;

            span {
                padding-top: 0;
                font-size: 14px;
                font-weight: 400;
                color: #4F4F4F;
                padding-right: 5px;
                display: inline-block;

                a.link1 {
                    color: #1179EF;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    text-decoration: none;
                    background-color: transparent;
                }
                a.link1:hover{
                    text-decoration: none;
                    color: #025CC3 !important;
                }

            }
        }
    }

    .limit-exceeded-container {
        width: 100%;

        .limit-exceeded-heading {
            font-size: 15px;
        }
    }

    #redirect_sub_heading.content-sub-heading {
        padding-bottom: 20px;
        font-weight: 400 !important;
    }

    .mail-sent-line,
    .limit-sent-line {
        width: 120px;
        height: 1px;
        border: 0;
        background: #E0E0E0;
        margin: 0px auto 20px;
        opacity: 1;
    }

    .mail-sent {
        font-size: 14px;
        font-weight: 400;
        color: #646464;
        line-height: 18px;
        text-align: center;
        white-space: nowrap;
        padding: 0;
        margin: 0;
    }

    .no-active-account {
        color: red !important;
    }

    .mail-border {
        border-color: red;
    }

    .no-active-account-msg {
        font-size: 14px;
        color: #2a2f32;
        text-align: center;
        padding-bottom: 30px;
    }

    // Reset Password Styling
    .confirm-password-eye-icon {
        content: '';
        background: vars.$on-boarding-icons;
        right: 25px;
        display: inline-block;
        position: absolute;
        top: 12px;
    }

    .confirm-password-eye-slash {
        background-position: -4px -4px;
        width: 16px;
        height: 16px;
    }

    .confirm-password-eye-unslash {
        background-position: -40px -4px;
        width: 16px;
        height: 16px;
        top: 13px;
        position: absolute;
    }

    .success-icon {
        display: inline-block;
        width: 41px;
        height: 41px;
        position: relative;
        content: "";
        background: url('https://cfaccounts.simplicdn.net/frontend/images/on-boarding-icons.svg') no-repeat;
        background-position: -72px -36px;
        margin-bottom: 20px;
    }

    .reset-success {
        color: #5fcf80;
    }

    #btn_reset {
        color: white;
        background: #1179EF;
        font-size: 14px;
        font-weight: 600;
        padding: 7px 15px;
        border: none;
        box-shadow: none;
        cursor: pointer;
        width: 100%;
        text-align: center;
        transition: background-color 0.3s ease;

        &:disabled {
            background: #88BCF7;
            border-color: #88BCF7;
            opacity: 1;
            cursor: not-allowed !important;
        }

        &:disabled:hover {
            background-color: #63acff;
        }
    }

    .reset-btn-div {
        display: flex;
        justify-content: center;
        align-items: center;

        .btn-reset-success {
            color: white;
            background: #118aef;
            width: 80%;
            margin-top: 20px;
            text-transform: none;
            font-size: 14px;
            line-height: 40px;
        }
    }

    #btn_forgotPassword {
        width: 100%;
        border-radius: 6px;
        text-transform: none;
        line-height: 46px;
        font-size: 16px;
        font-weight: 500;
        letter-spacing: normal;
        margin-bottom: 20px;


        &:disabled {
            background: #88BCF7;
            border-color: #88BCF7;
            opacity: 1;
            color: #fff;
            cursor: not-allowed !important;
            box-shadow: none;
            margin-bottom: 20px;
        }

        &:not(:disabled) {
            background: #1179EF;
            box-shadow: none;
            cursor: pointer;
            color: #fff;
            margin-bottom: 20px;
        }
    }

    .text-center {
        color: vars.$text-color;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }

    .sent_reset_link {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: 10px;

        &::before {
            content: "";
            display: block;
            width: 47px;
            height: 47px;
            background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
            background-position: -248px 2px;
            margin: 0 auto 20px auto;
        }
    }

    .hide {
        display: none !important;
    }

    @media (min-width: 768px) {
        #forgotPassword {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }

        .col-md-12 {
            padding-right: 0px !important;
            padding-left: 0px !important;
        }

        .container {
            width: 400px;
            padding-right: 0 !important;
            padding-left: 0 !important;
        }
    }

    @media (min-width: 1024px) {
        .footer {
            padding-bottom: 0;
            margin-bottom: 0;
            margin-top: 32px;

            .copy-rights {
                position: relative;
                left: -20.5px;
                font-size: 14px;
                line-height: 16px;
                font-weight: 300;
                color: vars.$text-color;
            }
        }
    }
}