@use "variables" as vars;

$border-color: #EDEDED;
$text-color: #4F4F4F;
$background-color: #fff;
$shadow: 0px 2px 10px rgba(0, 0, 0, 0.20);
$icon-url: "https://www.simplilearn.com/ice9/assets/lms_user_profile_v_1.svgz";
$refer-link-icon: "https://cfaccounts.simplicdn.net/frontend/images/refer-link.svg";
$refer-and-earn-icon: "/../frontend/images/refer-and-earn.gif";

/* ==============================
   Profile Button & Dropdown Icon
   ============================== */

  .active .profile-name::after {
    background: url($icon-url) no-repeat;
    background-position: -37px -427px;
    width: 14px;
    height: 14px;
    top: 0px;
    vertical-align: middle;
    border: 0;
    position: relative;
    right: -3px;
    display: inline-block;
    content: "";
  }

  .profile-name::after {
    background: url($icon-url) no-repeat;
    background-position: -9px -427px;
    width: 14px;
    height: 14px;
    top: 0px;
    vertical-align: middle;
    border: 0;
    position: relative;
    right: -3px;
    display: inline-block;
    content: "";
  }


/* ==============================
   Help & Support Button
   ============================== */
.need-help {
  margin-right: 10px;

  .help-btn {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    color: $text-color;
    padding: 5px 10px;

    .help-icon {
      width: 18px;
      height: 18px;
      margin-right: 5px;
      background: url("https://cfs22.simplicdn.net/paperclip/sprite-img-help-supports.svg") no-repeat -2px -2px;
      display: inline-block;
    }
  }
}

/* ==============================
   Top Navigation Styling
   ============================== */
.top-nav {
  float: right;
  display: block;

  .nav:before{
    content: " ";
    display: table;
    box-sizing: border-box;
  }

  ul.nav {
    float: left;
    margin: 0;
    padding-left: 0;
    list-style: none;
    display: block;

    li {
      margin-top: 8px;
      float: left;
      display: block;
      position: relative;
      list-style: none;

      .help_and_support {
        position: relative;
        display: flex;
        border: 0;
        align-items: center;
        cursor: pointer;
        background-color: transparent;
        padding: 1px 10px 4px 4px;
        border-radius: 40px;
        box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.08);
        }

        .help_and_support>.icon {
          width: 33px;
          height: 33px;
          background: url(https://www.simplilearn.com/ice9/assets/lms_help_icon_v_2.svgz) no-repeat 7px 7px;
          margin-right: 4px;
        }
        .help_and_support>.help {
          font-size: 14px;
          margin-top: 7px;
          margin-bottom: 0;
          color: #424242;
          font-weight: 400;
        }

      a {
        color: #333;
        font-size: 14px;
        text-decoration: none;
        padding-top: 15px;

        &.dropdown-toggle {
          cursor: pointer;
        }
      }
      a.hover{
        background: #F8FBFF;
      }


      &.profile {
        margin-top: 8px;


        a.icon-set-after {
          border-radius: 40px;
          box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.08);
          padding: 4px 8px 4px 4px;
          font-weight: 500;
          line-height: 20px;
          display: block;
          position: relative;

          .profile-img {
            position: relative;
            display: inline-block;
            overflow: hidden;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            margin-right: 8px;
            border: 1px solid #fff;
            vertical-align: middle;
          
            img {
              
              width: 30px;
              height: 30px;
              
            }
          }
          .profile-img:after {
              content: '';
              width: 30px;
              height: 30px;
              border-radius: 50%;
              background: rgba(170, 170, 170, 0.18);
              display: inline-block;
              position: absolute;
              top: 0;
              left: 0;
          
          
          }

          .profile-name {
            display: inline-block;
            vertical-align: middle;
            margin-right: 8px;
            font-size: 14px;
            font-weight: 400;
            color: #424242;
            max-width: 119px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }


        }

        ul.dropdown-menu {
          margin-top: 0;
          top: 50px;
          left: inherit;
          right: 0;
          border: 1px solid #B8D6FF;
          box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.20);
          position: absolute;
          z-index: 1000;
          min-width: 160px;
          list-style: none;
          text-align: left;
          background-color: $background-color;
          background-clip: padding-box;
          border-radius: 3px !important;
          width: 270px;
          padding: 0;
          color: #b9b9ba;
          font-size: 12px;
          float: left;

          li {
            border-bottom: 1px solid $border-color;
            margin-top: 0;
            overflow: hidden;
            width: 100%;

            a {
              padding: 0;
              line-height: 54px;
              padding-left: 50px;
              font-size: 16px;
              font-weight: 400;
              color: #4F4F4F;
              text-decoration: none;
              outline: 0;
              position: relative;
              clear: both;
              display: block;
              white-space: nowrap;
            }

            &.edit-prof{
              background-color: #F8FBFF !important;
            }

            &.edit-prof > a::before {
              content: "";
              width: 24px;
              height: 24px;
              display: inline-block !important;
              background: url($icon-url) no-repeat;
              background-position: -63px -487px;
              position: absolute;
              left: 16px;
              top: 0;
              bottom: 0;
              margin: auto;
            }

            &.change-pass > a::before {
              content: "";
              width: 24px;
              height: 24px;
              display: inline-block !important;
              background: url($icon-url) no-repeat;
              background-position: -5px -487px;
              position: absolute;
              left: 16px;
              top: 0;
              bottom: 0;
              margin: auto;
            }

            &.invoices > a::before {
              content: "";
              width: 24px;
              height: 24px;
              display: inline-block !important;
              background: url($icon-url) no-repeat;
              background-position: -5px -516px;
              position: absolute;
              left: 16px;
              top: 0;
              bottom: 0;
              margin: auto;
            }

            &.digit-key > a::before {
              content: "";
              width: 24px;
              height: 24px;
              display: inline-block !important;
              background: url($icon-url) no-repeat;
              background-position: -121px -487px;
              position: absolute;
              left: 16px;
              top: 0;
              bottom: 0;
              margin: auto;
            }
            
            &.exam-voucher > a::before {
              content: "";
              width: 24px;
              height: 24px;
              display: inline-block !important;
              background: url($icon-url) no-repeat;
              background-position: -34px -516px;
              position: absolute;
              left: 16px;
              top: 0;
              bottom: 0;
              margin: auto;
            }

            &.manage-tickets > a::before {
              content: "";
              width: 24px;
              height: 24px;
              display: inline-block !important;
              background: url($icon-url) no-repeat;
              background-position: -150px -487px;
              position: absolute;
              left: 16px;
              top: 0;
              bottom: 0;
              margin: auto;
            }

            &.logout > a::before  {
              content: "";
              width: 24px;
              height: 24px;
              display: inline-block !important;
              background: url($icon-url) no-repeat;
              background-position: -92px -487px;
              position: absolute;
              left: 16px;
              top: 0;
              bottom: 0;
              margin: auto;
            }
            
              &:hover {
                color: #007bff;
              }
            
          }
          li:hover{
            background-color: #F8FBFF !important;
          }

          .new-refer-li {
            margin-top: 0;
            overflow: hidden;
            
            .new-ref-earn {
                display: flex;
                flex-direction: column;
                background: linear-gradient(180deg, rgba(244, 244, 255, 0.2) -31.87%, rgba(138, 140, 205, 0.39) 107.5%);
                margin: 10px;
                border-radius: 6px;
                
                .content-box-new {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    padding: 10.5px 8.5px 10px 8.5px;
                    gap: 7px;

                .gif-refer-and-earn {
                    background: url($refer-and-earn-icon) no-repeat;
                    background-color: #d3d3d3;
                    background-position: -0.082px -1.234px;
                    background-size: 189.723% 181.353%;
                    width: 22.436px;
                    height: 24px;
                    background-size: contain;
                    transform: rotate(-0.074deg);
                    flex-shrink: 0;
                    background-color: transparent;

                }

                p:first-child {
                    font-size: 12px;
                    font-weight: 400;
                    color: #242A99;
                    margin: 0;
                    margin-bottom: initial !important;
                    overflow: hidden;
                    margin: 0 0 10px;
                }

                p:last-child {
                  color:#333;
                  font-size: 11px;
                  font-weight: 325;
                  margin-bottom: initial !important;
                  margin: 0 0 10px;
                  overflow: hidden;
                } 
                    
              }
            }
        }


          }
        &ul>.edit-prof:hover{
          background-color: #F8FBFF !important;
        }
      }
    }
  }

  .logo-container {
    display: flex;
    align-items: center;
  }
}

.pr-refer-button {
  border-radius: 6px;
  border: 1px solid #6369D4;
  background: #FFF;
  padding: 4px;
  margin: 0 7px 8px;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &:hover {
    background: #e8f0ff;
  }}

.referral-icon{
    background: url($refer-link-icon) no-repeat;
    background-position: -0.082px -1.234px;
    background-size: 189.723% 181.353%; 
    background-size: contain;
    width: 14px;
    height: 14px;
    display: inline-block;
    vertical-align: middle;
    margin-right: 4px;
  }

.referral-icon-last{
    color: #242A99;
    font-size: 10px;
    font-weight: 350;
  }

/* ==============================
   Header Styling
   ============================== */
.header {
  background-color: $background-color;
  z-index: 11;
  top: 0;
  position: sticky;
  box-shadow: 0px 0px 8px 0px #C4DDFF;

  .container{
    max-width: 1200px;
  }

  // .container {
  //   max-width: 1200px;
  //   margin: 0 auto;
  //   padding: 0 15px;
  //   background-color: $background-color;
  // }

  .container:before{
    content: " ";
    display: table;
  }

  .header_section {
    display: block;
    padding: 0;
    box-sizing: border-box;

    img {
      border: 0;
      vertical-align: middle;
    }
  }
}


/* Show dropdown when 'show' class is added */
.profile-drop.show {
  display: block !important;
}

/* Banner styling for referral copy success message */
.banner-result {
  position: fixed;
  z-index: 3000;
  top: 0px;
  padding: 32px;
  width: 100%;
  height: 80px;
  left: 0;
  text-align: center;
  color: #fff;
  font-size: 16px;
  transition: all 0.2s ease-in-out;
  
  &.hide {
    opacity: 0;
    pointer-events: none;
  }
  
  &.success {
    opacity: 1;
    background: #28a745;
  }
  
  &.error {
    background: #dc3545;
  }
}
