@use "variables" as vars;

.logo-image {
    padding-bottom: 32px;
    text-align: center;


    a {
        color: #777;
        font-size: 12px;
        cursor: pointer;
    }

    img {
        max-height: 36px;
    }

    .logo-text {
        display: inline-block;
        width: 70px;
        line-height: 24.5px;
        font-size: 17px;
        color: #757575;
        font-weight: 400;
        padding-left: 10px;
        margin-left: 8px;
        vertical-align: middle;
        position: relative;
        font-family: "Roboto", sans-serif;
    }

    .logo-text::before {
        width: 1.2px;
        height: 34px;
        content: "";
        display: inline-block;
        background: #E0E0E0;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        margin: auto;
    }

}

.register-heading {
    font-size: 18px !important;
    margin-bottom: 12px;
    line-height: 20px;
    color: #424242;
    font-weight: 500;
    text-align: center;
    padding-bottom: 8px;

    // Account setup specific styles
    &.account-setup-heading {
        font-size: 20px !important;
        font-weight: 500;
        text-align: center;
        padding-bottom: 8px;
        line-height: 20px;
        color: #424242;
        margin-bottom: 0px !important;
    }
}

.content-sub-heading {
    font-size: 16px;
    line-height: 20px;
    color: #646464;
    padding-bottom: 20px;
    text-align: center;

    // Account setup specific styles
    &.account-setup-subheading {
        font-size: 16px;
        line-height: 20px;
        color: #646464;
        padding-bottom: 20px;
        text-align: center;
    }
}

.register-form{
    margin-top: 12px;
}
form {
    position: relative;
    input[type="text"]:focus,
    input[type="password"]:focus,
    input[type="email"]:focus{
      border-radius: 3px;
      outline-offset: 0px;
      outline: 1px solid #118aef;
      border: 1px solid #007bff !important;
      background: rgb(232, 240, 254);
    }
    input[type="number"]:focus{
        outline: none;
    }
}

button,
input,
select,
textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

#error_box {
    text-align: center;
    padding-bottom: 20px;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    color: #DD3636;
}

#error_msg {
    text-align: center;
    padding-bottom: 20px;
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    color: #DD3636;
}

.form-input {
    margin-bottom: 20px !important;

    #termCondition {
        width: 18px;
        height: 18px;
        border: 1px solid #BFCACE;
        margin-right: 6px;
        vertical-align: middle;
        margin-top: 0;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        border-radius: 3px;
        display: inline-block;

    }

    .terms-content {
        display: inline-block;
        width: auto;
        text-transform: none;
        font-size: 16px;
        color: #808890;
        font-weight: 400;
        line-height: 20px;
        padding-bottom: 8px;
        margin: 0;

        span {
            font-size: 14px;
            font-weight: 400;
            color: #828282;
            line-height: normal;
            position: relative;
            top: 2px;
            text-transform: none;
            .forgot_password{
                text-decoration: underline;
                font-size: 14px;
                font-weight: 400;
                color: #1179ef;
                font-family: Roboto;
                cursor: pointer;
            }
        }
    }
    input[type=checkbox]:checked {
        background: url(https://www.simplilearn.com/ice9/assets/lms-checkbox.svgz) no-repeat;
        background-size: contain;
        border: 0 !important;
        cursor: pointer;
    }
    input[type=checkbox] {
        border: 1px solid #E5E6E7;
        cursor: pointer;
    }
    input[type="checkbox"].invalid {
        outline: 2px solid #ff0000;
    }
}
.form-input:hover{
    input[type=checkbox]:hover, input[type=checkbox]:focus {
        border-color: #1179EF !important;
    }
}
.signup-wrap {
    label {
        font-size: 16px;
        color: #999;
        font-weight: 400;
        line-height: 20px;
        display: inline-block;
        width: 100%;
        padding-bottom: 8px;
        margin: 0;
    }

    .form-input {
        .pr-20 {
            padding-right: 20px !important;
            padding-left: 15px;
        }
        .pl-0{
            padding-left: 0 !important;
            padding-right: 15px;
        }

        .col-xs-6 {
            width: 50%;
            position: relative;
            min-height: 1px;
            padding-left: 15px;
            padding-right: 15px;
        }

        input[type="text"],
        input[type="password"],
        input[type="email"],
        textarea {
            height: 48px;
            border: 1px solid #b8d6ff;
            font-size: 16px;
            padding-left: 24px;
            font-weight: 400;
            color: #4f4f4f;
            margin-bottom: 0px;
            border-radius: 3px;
            background: #fff;
            width: 100%;
        }
        input[type="text"]::placeholder,
        input[type="password"]::placeholder,
        input[type="email"]::placeholder,
        textarea::placeholder {
          color: #999;
        }
    }

    .support_coutry_code {
        display: block;
        border: none;
        position: initial;
        height: 35px;
        border-radius: 4px;
        background: #fff;

        .support_inner {
            display: flex;

            .country {
                label {
                    font-size: 16px;
                    color: #808890;
                    font-weight: 400;
                    line-height: 20px;
                    display: inline-block;
                    width: 100%;
                    padding-bottom: 8px;
                    margin: 0;
                }

                .for_cun {
                    width: 80px;
                    margin-right: 0;
                    border: 0;
                    height: auto;
                    margin-bottom: 0;
                    float: left;
                    display: inline-block;
                    position: relative;
                    border-radius: 3px;
                    
                    select:focus {
                        outline-offset: 0px !important;
                        outline: 1px solid #118aef !important;
                    }
                    .sel_cr {
                        border: 1px solid #B8D6FF;
                        width: 80px;
                        padding-left: 24px;
                        padding-right: 33px;
                        background: #fff;
                        color: #2a2f32;
                        margin: 0;
                        font-size: 14px;
                        height: 48px;
                        font-weight: 400;
                        -webkit-appearance: none;
                        line-height: 33px;
                        border-radius: 3px;
                        overflow: hidden;
                        vertical-align: middle;
                        position: relative;
                        font-family: inherit;
                    }

                    .caret {
                        right: 18px;
                        top: 22px;
                        display: inline-block;
                        width: 0;
                        height: 0;
                        margin-left: 2px;
                        vertical-align: middle;
                        border-top: 4px solid;
                        border-right: 4px solid transparent;
                        border-left: 4px solid transparent;
                        position: absolute;
                        pointer-events: none;
                        color: #777;
                    }

                    .caret::before {
                        content: "";
                        width: 7px;
                        height: 5px;
                        display: inline-block;
                        position: absolute;
                        left: -11px;
                        top: -5px;
                        background: #fff;
                    }
                }
            }

            .contact {
                label {
                    font-size: 16px;
                    color: #808890;
                    font-weight: 400;
                    line-height: 20px;
                    display: inline-block;
                    width: 100%;
                    padding-bottom: 8px;
                    margin: 0;
                }

                .code_wrap {
                    width: 308px;
                    height: 48px;
                    border: 1px solid #B8D6FF;
                    border-radius: 3px;
                    padding-left: 24px;

                    input {
                        padding-left: 0;
                        padding-bottom: 4px;
                        width: auto !important;
                        min-width: 238px;
                    }

                    .phone-no {
                        padding-left: 0;
                        padding-bottom: 4px;
                        width: auto !important;
                        min-width: 238px;
                        border-radius: 3px;
                        height: 48px;
                        font-weight: 400;
                        color: #4F4F4F;
                        margin-bottom: 0px;
                        border: none !important;
                        position: initial;
                        padding-top: 3px;
                        display: inline-block;
                        float: left;
                        margin-left: 12px;
                        background: transparent;
                        font-family: inherit;
                        line-height: inherit;
                        font-size: 16px; 
                    }
                    .phone-no::placeholder{
                        color: #999;
                    }
                    .phone-no:focus-visible{
                        outline: none;
                        border: none;
                    }
                    .country_code_span {
                        display: inline-block;
                        position: relative;
                        vertical-align: middle;
                        left: 0;
                        float: left;
                        z-index: 1;
                        font-size: 14px;
                        font-weight: 300;
                        color: #2a2f32;
                        line-height: 48px;
                        padding-top: 0;
                    }

                }
                .code_wrap:focus-within {
                    border-radius: 3px;
                    outline-offset: 0px;
                    outline: 1px solid #118aef;
                    border: 1px solid #007bff !important;
                }
                #tmp-phone-error {
                    color: #ff0000;
                    font-size: 12px;
                    padding-top: 6px;
                }
            }
        }
    }

    .password-field {
        display: inline-block;
        width: 100%;
        margin-top: 40px;
        position: relative;

        .passowrd-wrap {
            position: relative;

            .password-icon {
                background: url("https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz") no-repeat;
                position: absolute;
                right: 54px;
                top: 25px;
                width: 22px;
                height: 22px;
                transform: translateY(-50%);
                cursor: pointer;
              }
        
              .password-icon.password-eye-slash {
                background-position: -20px -138px;
              }
        
              .password-icon.password-eye-unslash {
                background-position: -60px -138px;
              }
            .info-invalid{
                background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
                content: '';
                position: absolute;
                display: inline-block;
                background-position: -101px -81px;
                width: 22px;
                height: 22px;
                right: 22px;
                top: 25px;
              }
            .hide {
                display: none !important;
            }

            .password-info {
                background: url("https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz") no-repeat;
                position: absolute;
                transform: translateY(-50%);
                cursor: pointer;
                background-position: -21px -82px;
                width: 22px;
                height: 22px;
                right: 22px;
                top: 25px;
                
                .password-tooltip {
                    top: 0;

                    i {
                        top: 55%;
                    }

                    i::after {
                        background: #313131;
                    }
                }
            }
            .password-info.invalid {
                background-position: -101px -82px;
            }
        }

        .password-tooltip {
            min-width: 417px;
            left: 8px;
            top: 6px;
            transform: translate(0, -50%);
            padding: 8px;
            color: #EEE;
            background-color: #313131;
            font-weight: normal;
            font-size: 13px;
            border-radius: 6px;
            z-index: 9999;
            box-sizing: border-box;
            border: 1px solid #313131;
            box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.8s;
            position: absolute;
            .triangle::after {
                background: #313131;
            }
        }



        .password-info:hover .password-tooltip,
        .info-icon .password-tooltip.visible {
            visibility: visible;
            opacity: 1;
        }

        .password-heading {
            font-size: 16px;
            font-weight: 700;
            padding-bottom: 8px;
            line-height: normal;
        }

        .password-list {

            ul,
            li {
                list-style: disc;
                font-size: 14px;
                text-align: left;
            }

            ul {
                padding-left: 0;
            }

            li {
                padding-left: 10px;
                margin-bottom: 8px;
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                padding-left: 20px;
                position: relative;
                padding-bottom: 0;
            }

        }
    }

    .submit-container {
        .disabled
        .disabled:hover {
          background: #88BCF7;
          border-color: #88BCF7;
          opacity: 1;
          color: #fff;
          cursor: not-allowed !important;
          box-shadow: none;
        }
        .btn-signup {
            line-height: 46px;
            background: #1179EF;
            font-size: 16px;
            font-weight: 500;
            letter-spacing: normal;
            box-shadow: none;
            border-radius: 6px;
            color: #fff;
            position: inherit
        }
    }
}


.signup-wrap label span {
    color: #DD3636;
}

.form_container input:required:invalid {
    background-position: right top;
    background-repeat: no-repeat;
    box-shadow: none;

    .support_coutry_code {
        display: block;
        border: none;
        position: initial;
    }
}

.create_account {
    text-align: center;
    display: block;
    width: 100%;
    margin-top: 0;

    .back-register {
        margin-top: 20px;
        margin-bottom: 0;

        span {
            padding-top: 0;
            font-size: 14px;
            font-weight: 400;
            color: #4F4F4F;
            padding-right: 5px;
            display: inline-block;

            a.link1 {
                color: #1179EF;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                text-decoration: none;
                background-color: transparent;
            }
            a.link1:hover{
                text-decoration: none;
                color: #025CC3 !important;
            }

        }
    }
}

#phoneError {
    color: #ff0000; 
    display: none;
    font-size: 12px;              
    padding-top: 6px;
}

@media only screen and (min-width: 950px) and (max-width: 1024px) and (orientation: landscape) {
    .right-bar-layout > .col-md-7 {
        width: 55%;
    }
    .right-bar-layout > .col-md-5 {
        width: 45%;
        margin-left: 15px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
}