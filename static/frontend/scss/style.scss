// todo: optimize resolution to support all mobile
@use "variables" as vars;
html{
  height: 100%;
}


body {
  overflow-x: hidden;
  font-size: 13px;
  line-height: 1.42857143;
  color: #333;
  overflow-y: scroll;
  scrollbar-width: auto;
}


.body_container {
  max-height: 100vh;
  overflow-y: auto;
  // scrollbar-width: none;
  overflow-x: hidden;
  box-sizing: border-box;
  scroll-behavior: smooth;
}


body::-webkit-scrollbar {
  display: none; /* For Chrome, Safari, and Edge */
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.form-control {
  font-family: "Roboto";
  height:48px;
  font-size:16px;
  padding-left:24px;
  border:1px solid #B8D6FF;
  outline: none;
  color:#4F4F4F;
  margin-bottom: 20px;
}

.form-control::placeholder {
  color: #9E9E9E;
  font-weight: 535;
}

.form-control:focus {
  outline: 1px solid #118aef; /* Blue border on focus */
  box-shadow: none;
}

.form-control[readonly] {
  background-color: #fefeff;
  opacity: 1;
  pointer-events: none;
  cursor: not-allowed;
}

.background-image {
  background-size: cover;
  height: 100%;
  width: 100%;
  background-color: black;
  min-height: 100vh;

  .container-fluid {
    bottom: -2px;
    font-family: "Roboto", sans-serif;
    position: absolute;
    padding: 100px 40px 20px;
    width: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0)) !important;
    // background: vars.$right-background-webkit;
    // background: vars.$right-background-moz;
    // background: vars.$right-background-o;
    // background: vars.$right-background;
    // background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%) !important;

    .right-heading {
      color: #F8FBFF;
      width: 100%;
      font-weight: 700;
      font-size: 36px;
      line-height: normal;
      font-family: "Roboto", sans-serif;
      font-style: normal;
    }

    .content {
      padding-bottom: 32px;
      font-size: 24px;
      line-height: normal;
      font-weight: 400;
      margin-top: 6px;
      color: #F8FBFF;
    }

    .additional-content {
      padding-bottom: 10px;
        ul {
            list-style-type: disc; /* Adds bullet points */
            margin: 0;
            color: vars.$background-color; /* White text color for dark background */
            font-size: 20px; /* Match the font size */
            line-height: 1.5; /* Improve spacing between lines */
            padding: 0;
        }
    
        li {
          font-size: 24px;
          line-height: normal;
          list-style: none;
          font-weight: 300;
          margin-bottom: 12px;
          color: #F8FBFF;
        }
    
        li::before {
          content: "";
          width: 5px;
          height: 5px;
          display: inline-block;
          vertical-align: middle;
          background: #fff;
          border-radius: 50%;
          margin-right: 10px;
          position: relative;
          top: -2px;
        }
        .number-bold {
          font-weight: 500;
          color: vars.$background-color;
        }
    }
  }
}


.caret {
  width: 0;
  height: 0;
  vertical-align: middle;
  border-top: 4px solid;
  border-right: 4px solid rgba(0,0,0,0);
  border-left: 4px solid rgba(0,0,0,0);
  position: relative;
  display: block;
  left: 70%;
  top: -18px;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: Arial, sans-serif;
  // padding: 5.5vh 0vh 5vh 0vh;
  padding-bottom: 32px;
  
}


.logo-container.reset{
  padding-top: 40px;
}
@media (min-width: 1024px) {
  .logo-container.reset {
    padding-top: 120px;
  }
}
@media (max-width: 767px) {
  .logo-container.reset {
    padding-top: 20px; /* Reduce padding further for mobile screens */
  }
}

.logo-image img {
  height: 36px; /* Adjust size as needed */
  vertical-align:middle;
}

.divider {
  height: 34px; /* Match the height of the logo image */
  width: 1.2px;
  background-color: #E0E0E0; /* Adjust color as needed */
  margin: 0 10px; /* Space between the logo and text */
}

.logo-text {
  font-size: 17px;
  color: #757575;
  line-height: 0.7; 
  text-align: left;
  font-weight: 549;
  vertical-align:middle;
}

.logo-text span {
  display: block;
}

.form-input {
  margin-bottom: 20px;
}

.btn-default:hover,
.btn-primary:focus,
.btn-default:focus {
  background-color: #1b81bf;
  border-color: #1b81bf;
  color: #fff;
}

button,
html input[type=button],
input[type=reset],
input[type=submit] {
  -webkit-appearance: button;
  cursor: pointer;
}

.btn-social-link,
.btn-signup {
  background: #118aef;
  width: 100%;
  text-transform: none;
  font-size: 14px;
  line-height: 40px;
}

.btn-default,
.btn-info {
  position: relative;
  top: 0;
  line-height: 34px;
  padding: 0 18px;
  letter-spacing: 1px;
  margin-right: 12px;
  box-shadow: 0 1px #2971A5;
  -webkit-transition: none;
  transition: none;
}

.terms-content {
  font-size: 14px !important;
  text-transform: none;
  color: #484f57;
}

a.link1 {
  text-decoration: none;
  color: #1179EF;
  font-family: Roboto;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.error_msg {
  padding-bottom: 15px;
  color: #DD3636;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  text-align: center;
  padding-left: 30px;
  padding-right: 30px;
}

.error {
  border: 1px solid red;
}

.textError {
  // color: red;
  background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
  background-position: -71px -277px;
}
.valid{
  background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
  background-position: -45px -277px;

}

.btn-primary,
.btn-default,
.btn-info,
.btn-download,
.btn-skip {
  background-color: #1f85cd;
  color: #fff;
}

.left-container {
  display: flex;
  align-items: center;
  z-index: 0;
  padding: 0vh 2.5vw 0vh 4vw;
  padding-top: 32px;
}

/* Add a media query for mobile devices */
@media only screen and (max-width: 767px) and (orientation: portrait),
only screen and (max-width: 1023px) and (orientation: landscape) {
  .left-container {
    height: 100%;
    overflow-x: hidden;
  }
}

.right-container {
  overflow: hidden;
  padding: 0;
}

.right-container {
  overflow: -moz-hidden-unscrollable;
  /* overflow: hidden; for other browsers */
}

/* For WebKit browsers (Chrome, Safari) */
.right-container::-webkit-scrollbar {
  display: none;
}



.registration-form input {
  // margin-bottom: 20px;
  border-radius: 3px;
}

.mandatory{
    color: vars.$mandatory;
}
.create_account {
  span {
    padding-right: 10px;
    display: inline-block;
    padding-top: 10px;
    font-size: 14px
  }

  a {
    font-size: 14px
  }
}

@media only screen and (max-width: 400px) and (orientation: portrait),
(min-width: 401px) and (max-width: 540px) and (orientation: portrait),
(min-width: 541px) and (max-width: 768px) and (orientation: portrait),
(min-width: 769px) and (max-width: 912px) and (orientation: portrait),
(min-width: 913px) and (max-width: 1024px) and (orientation: portrait) {

  /* Common styles for all screen widths within the specified range */
  .hidden-sm,
  .hidden-xs,
  .hidden-lg,
  .hidden-xl {
    display: none;
  }

  .submit-div {
    width: 90%;
    margin: 0 auto;
    text-align: center;
  }

  .tablet-view {
    display: flex;
    overflow: hidden;
    align-items: center;
    height: 98vh;
    width: 98vw;
    justify-content: center;
    margin: 0 auto;

    .password-field {
      .info-icon {
        .password-tooltip i::after {
          transform: translate(0%, 0%) rotate(-45deg);
        }
      }
      .password-tooltip {
        min-width: 345px;
        top: 28px;
        left: -9px;
        transform: translate(-90%, 0);
        margin-left: 0px;
      }

      .info-icon .password-tooltip i {
        top: -13px;
        right: 8px;
        margin-top: 0px;
        bottom: 100%;
        margin-left: -12px;
        height: 15px;
      }
    }
  }
}



.footer{
  color: #6c757d;
  font-size: 13px;
  text-align: center;
  // margin: 10px 0px 10px 0px;
}



.modal {
  display: none;
  position: fixed;
  z-index: 1;
  -webkit-overflow-scrolling: touch;
  outline: 0;

  .cookie-dialog {
    top: 30%;
  }

  .modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: .3rem;
    outline: 0;

    .close-btn:before {
      width: 18px;
      height: 15px;
      background-position: -140px -22px !important;
    } 

    .close-btn {
      top: -30px;
      color: #fff;
      font-size: 18px;
      height: 24px;
      position: absolute;
      right: 0px;
    }

    .icon-set-before:before {
      background: url(https://cfs22.simplicdn.net/paperclip/dashboard_v1.svgz) no-repeat 0 0;
      display: inline-block;
      border: 0;
      content: "";
    }
  }
}

.voucher-popup {
  background: rgba(42, 47, 50, 0.59);
}

.modal-body{
  h4 {
    text-align: center;
    padding-top: 15px;
    padding-bottom: 30px;
    font-weight: 300px;
    border-bottom: 1px solid #e4e7ec;
  }
}

.tp-cookies-disabled-message {
  font-size: 18px !important;
  color: #000 !important;
  line-height: 25.31px !important;
  padding: 10px 30px !important;
}

.okay-btn {
  border-top: 0px solid #e4e7ec !important;

  button {
    background: #118AEF !important;
    color: white;
    border: 1px solid #118AEF !important;
    
    &:hover,
    &:active {
      /* Styles for the button on hover or click (same as default) */
      background: #118AEF !important;
      color: white;
      border: 1px solid #118AEF !important;
    }
  }
}