@use "variables" as vars;

/* Hide Free Trial Modal */
.freetrialModal {
    display: none !important;
}

.right-bar-layout {
    height: 100vh;
    width: 100%;
    margin: 0px;
    float: none;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .left-panel {
        position: relative;
        z-index: 1;
        .left-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            width: 100%;

            #form-wrapper.bounceInRight {
                animation: bounceInRight 0.5s forwards;
            }

            #form-wrapper {
                padding-bottom: 20px;
                margin-left: unset;
                margin-right: unset;
                padding-left: 0px;
                padding-right: 0px;
                height: auto;
                min-height: calc(100vh - 58px);

                .container-fluid {
                    height: auto;
                    min-height: calc(100vh - 58px);
                    padding-bottom: 20px;
                    margin-left: unset;
                    margin-right: unset;
                    padding-left: 0px;
                    padding-right: 0px;

                    .row {
                        margin-left: -15px;
                        margin-right: -15px;

                        .col-centered {
                            display: inline-block;
                            float: none;
                            text-align: left;
                            margin-right: -4px;

                            .footer {
                                margin-top: 32px;
                                color: #6c757d;
                                font-size: 13px;
                                text-align: center;
                                white-space: nowrap;
                                font-size: 10px;
                                text-align: center;

                                .copy-rights {
                                    position: relative;
                                    left: 1px;
                                    font-size: 14px;
                                    line-height: 16px;
                                    font-weight: 300;
                                    color: #4F4F4F;
                                    left: -20.5px;

                                    a {
                                        font-size: 14px;
                                        line-height: 16px;
                                        font-weight: 300;
                                        color: #3B83F0;
                                    }
                                }
                            }
                        }

                        .content-wrapper {
                            padding-top: 32px !important;
                            width: 400px;
                            margin-right: 0px;
                            padding: 0;
                        }
                    }


                }

                .container-fluid>.row {
                    height: auto;
                    min-height: calc(100vh - 58px);
                    display: flex;
                    vertical-align: middle;
                    align-items: center;
                    justify-content: center;
                }

            }

        }
    }

    .right-panel {
        background: #000;
        bottom: 0px;
        position: fixed;
        overflow: hidden;
        top: 0px;
        right: 0px;
        padding-left: 0;
        padding-right: 0;
        // z-index: -1;
        .background-wrapper {
            background: url(https://cfaccounts.simplilearn.com/frontend/images/right-panel-image_v1.jpg) no-repeat;
            background-size: cover;
            height: 100%;
            min-height: 100vh;
        }
    }
}


@media only screen and (min-width: 1025px) and (max-height: 1366px) and (orientation: landscape) {
    .right-bar-layout>.col-md-7 {
        width: 58.********% !important;
    }

    .right-bar-layout>.col-md-5 {
        width: 41.********% !important;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
    }
}
@media(min-width: 768px) and (max-width: 991px) {
    .right-bar-layout {
        height: 100vh;
        width: 100%;
        margin: 0px;
        float: none;
        display: flex;
        align-items: center;
        justify-content: center;
    }
  }
@media(min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
    .right-bar-layout {
        height: 100vh;
        width: 100%;
        margin: 0px;
        float: none;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
@media (max-width: 767px){
    .right-bar-layout .left-panel .left-content #form-wrapper .container-fluid .row .content-wrapper{
        padding-left: 15px;
    padding-right: 15px;
    }
}
@keyframes bounceInRight {
    0% {
        opacity: 0;
        transform: translateX(50px);
    }
}


@media only screen and (min-width: 500px) and (max-width: 1186px) {
    .left-panel .container-fluid>.row {
        margin: 0px;
        justify-content: center;
        float: none !important;
    }
}
@media (min-width: 1000px) and (max-width: 1140px) {
    .copy-rights {
        white-space: normal;
    }
}