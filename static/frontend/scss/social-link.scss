@use "variables" as vars;

.social-link-container {
    .content-heading {
        color: vars.$heading-color;
        font-size: 20px !important;
        font-weight: 500;
        text-align: center;
        padding-bottom: 32px;
    }

    .content-sub-heading {
        padding-bottom: 10px;
        font-size: 16px;
        font-weight: 400;
        color: #646464;
        text-align: center;
        display: flex;
        justify-content: center;
        .social-account-exists{
            width:400px;
            padding-bottom: 20px;
        }
    }

    .error_msg {
        text-align: center;
        padding-bottom: 20px;
        font-size: 16px;
        font-weight: 400;
        line-height: 20px;
        color: #DD3636;
    }

    .signup_link {
        text-align: center;

        a {
            font-size: 14px;
            font-weight: 500;
            color: #1179EF !important;
            text-decoration: none;
            display: inline-block;
            margin-top: 24px;
        }
    }

    .logo-container.social-link {
        padding-top: 158px;
        padding-bottom: 32px;
    }

    [type=submit]:not(:disabled).btn-signup {
        background: #118aef !important;
        padding: 0 18px !important;
        letter-spacing: 1px !important;
        margin-right: 0 !important;
        box-shadow: 0 1px #118aef !important;
        line-height: 40px !important;
        font-weight: 500 !important;
        border-radius: 3px !important;
        border-color: #118aef !important;
        transition: none !important;
    }
}

.social-link-container a {
    background-color: transparent;
}
