@use "variables" as vars;

.multi-account-header {
    text-align: center;
    padding: 20px;

    .logo-image {
        padding-bottom: 32px;
        text-align: center;
      
      
        a {
          color: #777;
          font-size: 12px;
          cursor: pointer;
        }
      
        img {
          max-height: 36px;
        }
      
        .logo-text {
          display: inline-block;
          width: 70px;
          line-height: 24.5px;
          font-size: 17px;
          color: #757575;
          font-weight: 400;
          padding-left: 10px;
          margin-left: 8px;
          vertical-align: middle;
          position: relative;
          font-family: "Roboto", sans-serif;
        }
      
        .logo-text::before {
          width: 1.2px;
          height: 34px;
          content: "";
          display: inline-block;
          background: #E0E0E0;
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          margin: auto;
        }
      
      }
    .content-heading {
        font-size: 20px;
        font-weight: 500;
        color: vars.$heading-color;
        margin-bottom: 8px;
    }

    .content-sub-heading {
        font-size: 16px;
        font-weight: 400;
        color: vars.$text-color;
        margin-bottom: 20px;
        padding: 0;
    }
}

.multi-account{
    width: 400px;
    padding: 0;
    .multi-account-container {
        padding: 20px 0 0 0;
    
        .multi-radio-box {
            border-radius: 2px;
            box-shadow: 0 0 6px 0 rgba(125, 125, 125, 0.2);
            border: solid 1px #dedede;
            height: 64px;
            cursor: pointer;
            width: 100%;
    
            & > .row,
            & > .row > div {
                height: 100%;
            }
        }
    
        .multi-logo {
            width: 100%;
            max-width: 60px;
            justify-content: center;
            position: relative;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-height: 20px;
            display: flex;
        }
    
        .multi-logo.default-logo {
            max-width: 99px;
        }
    
        .multi-logo-box {
            height: 100%;
        }
    
        .multi-group-name {
            font-size: 16px;
            font-weight: 500;
            line-height: 1.14;
            color: #1179EF;
            padding-bottom: 8px;
            text-align: left;
        }
    
        .multi-group-url {
            font-size: 14px;
            font-weight: 400;
            line-height: normal;
            color: vars.$text-color;
            overflow: hidden;
            max-width: 260px;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    
        .multi-radio-box > .multi-group-details {
            padding: 12px 0px 12px 0px !important;
        }
    
        input:checked + .multi-radio-box {
            box-shadow: 0 0 6px 0 rgba(125, 125, 125, 0.2);
            border: solid 2px #118aef;
        }
    
        .multi-radio-input {
            display: block !important;
            opacity: 0;
            margin-left: -10px !important;
        }
    
        .btn-signup {
            background: #118aef;
            width: 100%;
            text-transform: none;
            font-size: 14px;
            line-height: 40px;
        }
    
        #btn-multi-account {
            width: 100%;
            border-radius: 6px;
            text-transform: none;
            line-height: 46px;
            font-size: 16px;
            font-weight: 500;
            letter-spacing: normal;
            margin-bottom: 20px;
    
    
            &:disabled {
                background: #88BCF7;
                border-color: #88BCF7;
                opacity: 1;
                color: #fff;
                cursor: not-allowed !important;
                box-shadow: none;
                margin-bottom: 20px;
            }
    
            &:not(:disabled) {
                background: #1179EF;
                box-shadow: none;
                cursor: pointer;
                color: #fff;
                margin-bottom: 20px;
            }
        }
    
        .redirecting-text {
            font-size: 24px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    
        // Scoped Logo Container Styles
        .logo-container.multi-account {
            padding-top: 32px;
            padding-bottom: 32px;
        }
    
        .content-sub-heading {
            font-weight: 400;
        }
    
        @media (min-width: 768px) {
            .registration-form {
                margin: 0 !important;
            }
        }
    
        .text-center {
            padding: 0 !important;
            margin: 0 !important;
        }
    
        .row > * {
            padding-left: 0;
            padding-right: 0;
        }
    
        #btn-multi-account {
            left: 0;
            margin: 0;
        }
    
        .input-radio {
            display: inline-flex;
            margin-bottom: 0;
            text-transform: none;
            line-height: 14px;
            font-weight: 400;
            font-size: 11px;
            max-width: 100%;
        }
    
        input:checked + .multi-radio-box {
            box-shadow: none;
            border: 1px solid #1179EF;
        }
    
        .multi-radio-box {
            border: 1px solid #E0E0E0;
            box-shadow: none;
            margin-bottom: 20px;
            border-radius: 2px;
            cursor: pointer;
        }
    
        .multi-radio-box > .row > div {
            padding: 12px 12px 12px 0;
        }
    }
}
