.image-section {
    padding-bottom: 28px !important;
    background: linear-gradient(to top, rgba(0, 0, 0, 1), rgba(0, 0, 0, 0));
    bottom: 0px;
    position: absolute;
    padding: 70px 40px 20px;
    width: 100%;
    padding-right: 0px;
    padding-top: 100px;
    .right-heading {
        font-size: 32px;
        line-height: normal;
        font-weight: 700;
        color: #F8FBFF;
        width: 100%;
        font-family: "Roboto", sans-serif;
    }

    .content {
        padding-bottom: 30px;
        font-size: 22px;
        line-height: normal;
        margin-top: 6px;
        color: #F8FBFF;
        font-weight: 400;
        width: 100%;
    }

    .additional-content {
        padding-top: 0;
        color: #fff;
        // padding-top: 18px;
        font-size: 16px;
        line-height: 2.5;
        width: 100%;
        font-weight: 300;

        ul {
            margin: 0;
            padding: 0;
            list-style: none;

            li {
                font-size: 22px;
                line-height: normal;
                font-weight: 300;
                margin-bottom: 12px !important;
                color: #F8FBFF;
                margin: 0;
                padding: 0;
                list-style: none;

                span {
                    font-weight: 500;
                    color: #fff;
                }
            }

            li:before {
                content: "";
                width: 5px;
                height: 5px;
                display: inline-block;
                vertical-align: middle;
                background: #fff;
                border-radius: 50%;
                margin-right: 10px;
                position: relative;
                top: -2px;
            }
        }
    }
}