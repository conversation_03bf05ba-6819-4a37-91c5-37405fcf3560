@use "variables" as vars;

.left_nav_menu_wrap {
    z-index: 1;
    background: #fff;
    position: sticky;
    top: 76px;
    display: inline-block;
    width: 290px;
    margin-right: 32px;
    vertical-align: top;

    .left_nav_main {
        padding: 12px;
        border: 1px solid #EEE;
        box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.08);
        border-radius: 6px;

        .profile_info {
            padding: 6px 0px 16px 6px;
            display: flex;
            border-radius: 6px;
            border-bottom: 1px solid #EEE;
            margin-bottom: 20px;

            .profile_pic {
                width: 65px;
                height: 65px;
                display: inline-block;
                margin-right: 16px;

                .profile_picture {
                    border-radius: 50%;
                }
            }

            .profile_detail {
                width: calc(100% - 81px);
                display: inline-block;

                .profile_user_name {
                    margin: 0;
                    padding-bottom: 6px;
                    font-size: 24px;
                    color: #313131;
                    font-weight: 600;
                    letter-spacing: 0.28px;
                    line-height: normal;
                    display: -webkit-box;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    text-transform: capitalize;
                }

                .profile_user_text {
                    font-size: 14px;
                    font-weight: 400;
                    color: #646464;
                    letter-spacing: 0.14px;
                    margin-bottom: 8px;
                    line-height: normal;
                    margin: 0 0 8px;
                }

                .progress {
                    margin: 0;
                    background: #D9D9D9;
                    height: 6px;
                    position: relative;
                    display: block;
                    box-shadow: none;
                    border-radius: 4px;

                    .bar {
                        background: linear-gradient(90deg, #1179EF 0%, #6FCF97 101.65%);
                        height: 6px;
                        border-radius: 4px;
                        position: absolute;
                        left: 0;
                        display: inline-block;
                    }
                }
            }
        }

        .left_nav_list {
            margin: 0;
            padding: 0;
            list-style: none;
            li{
                margin-bottom: 8px;
                position: relative;
            }
            li:last-child  {
                margin-bottom: 0;
            }
            li a{
                padding: 16px;
                padding-left: 52px;
                color: #313131;
                font-size: 16px;
                font-weight: 400;
                line-height: 41px;
                display: block;
                border-radius: 6px;
                border: 1px solid #EEE;
                text-decoration: none; 
                outline: none; 

                &:hover {
                    outline: none;
                    border: 1px solid #1179EF;
                    border-radius: 6px;
                }
                &:focus{
                    outline: none;
                }
                &:focus-visible {
                    // Styles for keyboard focus only
                    outline: none;
                    background: #F5F9FF;
                    border-color: #B8D6FF;}
            }

            .left_nav_basic {
                margin: 0;
                padding: 0;
                list-style: none;
                display: block;
                position: relative;
                margin-bottom: 8px;
                cursor: pointer;
                overflow: hidden;
                i{
                    display: inline-block;
                    width: 24px;
                    height: 24px;
                    position: absolute;
                    left: 16px;
                    top: 0;
                    bottom: 0;
                    margin: auto;
                    background: vars.$profile-icon no-repeat;
                }
            }
            // basic icon
            .left_nav_basic.basic i {
                background-position: 0 0;
            }
            .left_nav_basic.basic.active i {
                background-position: 0 -41px;
            }

            li.complete::after {
                content:"";
                display:inline-block;
                width:18px;
                height:18px;
                background: vars.$profile-icon no-repeat -4px -94px;
                background-position: -4px -94px;
                position: absolute;
                right: 16px;
                top: 0;
                bottom: 0;
                margin: auto;
            }

            .active {
                background: #F5F9FF;
                border-color: #B8D6FF;
                outline: none;
                // border-left: 1px solid transparent;
                pointer-events: none;
            }
            li.active a {
                font-weight: 500;
            }
             li.active::before {
                content: "";
                width: 4px;
                background: #1179EF;
                display: inline-block;
                height: 100%;
                position: absolute;
                left: 0;
                bottom: 0;
                top: 0;
            }
        }

        .left_nav_list{
            li{
               i{
                display: inline-block;
                width: 24px;
                height: 24px;
                position: absolute;
                left: 16px;
                top: 0;
                bottom: 0;
                margin: auto;
                background: vars.$profile-icon no-repeat;
               }
            }
        }
           // contact icon
           .left_nav_list .contact i {
            background-position: -48px 0;
        }
        .left_nav_list .contact.active i {
            background-position: 0 -41px;
        }
         // professional icon
         .left_nav_list .professional i {
            background-position: -96px 0;
        }
        .left_nav_list .professional.active i {
            background-position: -96px -41px;
        }
           //academics  icon
           .left_nav_list .academics i {
            background-position: -145px 0;
        }
        .left_nav_list .academics.active i {
            background-position: -145px -41px;
        }
        
          //outcome  icon
          .left_nav_list .outcome i {
            background-position: -192px 0;
        }
        .left_nav_list .outcome.active i {
            background-position: -192px 0;
        }

    }
   
}