@use "./style.scss";
@use "variables" as vars;

.profile-sec {
    position: relative;
    display: block;

    select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background-image: none;
      }

    .profile-updated {
        height: 50px;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        background-color: green;
        color: #fff;
        text-align: center;
        padding: 15px;
        font-size: 18px;
        font-weight: bold;
        z-index: 9999;
        pointer-events: none;
        animation: fadeOut 5s ease-in-out;
    }    
        
    .header-wrap {
        padding: 0;
        margin: 36px 0;
        font-size: 20px;
        font-weight: 500;
        line-height: normal;
        color: vars.$heading-color;

        .drop_list {
            display: inline-block;
            margin-left: 12px;
            vertical-align: middle;
            position: relative;
            top: 2px;

            .dropdown-icon {
                display: inline-block;
                width: 28px;
                height: 28px;
                background: url(https://www.simplilearn.com/ice9/assets/lms_accounts_dots.svgz) no-repeat;
                background-position: 0 -1px;
                cursor: pointer;
            }
            .dropdown-icon:hover {
                background: url(https://www.simplilearn.com/ice9/assets/lms_accounts_dots_active.svgz) no-repeat;
                background-position: -3px -1px;
                display: inline-block;
                width: 28px;
                height: 28px;
                cursor: pointer;
            }

            .dropdown-menu {
                width: 205px;
                border: 1px solid #E0E0E0;
                border-radius: 3px;
                box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.12);
                padding: 22px 0;
                position: absolute;
                z-index: 11111;
                background: #fff;
                top: 32px;
                display: none;
                left: 0;

                .download-data {
                    display: block;
                    padding: 0;
                    padding-left: 45px;
                    position: relative;
                    color: #646464;
                    font-size: 14px;
                    line-height: normal;
                    letter-spacing: 0.14px;
                    font-weight: 400;
                    text-decoration: none;
                }
                .download-data::before {
                    background: url(https://www.simplilearn.com/ice9/assets/lms_prof_delete_ac.svgz) no-repeat;
                    width: 16px;
                    height: 16px;
                    display: inline-block;
                    content: "";
                    position: absolute;
                    left: 14px;
                    top: 0px;
                }

                li {
                    display: block;
                    width: 100%;
                    margin-bottom: 20px;
                    border: 0;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    a {
                        display: block;
                        padding: 0;
                        padding-left: 45px;
                        position: relative;
                        color: #646464;
                        font-size: 14px;
                        line-height: normal;
                        letter-spacing: 0.14px;
                        font-weight: 400;
                        text-decoration: none;
                        cursor: pointer;
                        white-space: nowrap;

                        &::before {
                            width: 16px;
                            height: 16px;
                            display: inline-block;
                            content: "";
                            position: absolute;
                            left: 14px;
                            top: 0px;
                        }
                    }

                    &.download a::before {
                        background: url(https://www.simplilearn.com/ice9/assets/lms_prof_delete_ac.svgz) no-repeat;
                    }

                    &.deleted a::before {
                        background: url(https://www.simplilearn.com/ice9/assets/lms_prof_delete_ac_v_2.svgz) no-repeat;
                    }
                }
            }
            .active-ul {
                display: inline-block;	}
        }
    }

    .p_error{
        border: none !important;
    }

    .hide {
        display: none !important;
    }

    .pr-tab-content {
        float: none !important;
        display: inline-block !important;
        width: calc(100% - 326px) !important;
        padding: 0 !important;

        .profile-wraper {
            .show-profile {
                .form_main_wrapper {
                    position: relative;
                    // opacity: 0;
                    transition: opacity 0.5s;
                    padding: 32px;
                    border-radius: 6px;
                    border: 1px solid #EEE;
                    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.08);
                    padding-top: 6px;
                    padding-bottom: 8px;

                    .panel-body {
                        padding: 0;
                        margin: 0;

                        .tab-content {
                            padding: 0;
                            margin: 0;

                            .tab_inner {
                                .edit_form {
                                    position: relative;
                                    width: 778px;}}}}}}}}}

                                

                                    .error-border {
                                        border: 1px solid #DD3636 !important;
                                    }

                                    /* Apply error border to choices inner container */
                                    .choices__inner.error-border {
                                        border: 1px solid #DD3636 !important;
                
                                    }
                            
                                    /* Special styling for job_function, industry, country and state fields */
                                    .job_function + .choices .choices__inner.error-border,
                                    .industry + .choices .choices__inner.error-border,
                                    .select_industry + .choices .choices__inner.error-border,
                                    #country_of_residence + .choices .choices__inner.error-border,
                                    #country_of_residence_choices .choices__inner.error-border,
                                    #state + .choices .choices__inner.error-border,
                                    #state_choices .choices__inner.error-border {
                                        border: 1px solid #DD3636 !important;
                                        
                                    }
                                    
                                    /* Target specific container IDs generated by Choices.js */
                                    #country_of_residence_choices.error-border .choices__inner,
                                    #state_choices.error-border .choices__inner,
                                    #timezone-dropdown_choices.error-border .choices__inner {
                                        border: 1px solid #DD3636 !important;
                                        
                                    }
                                    
                                    /* Date fields error styling */
                                    .exp_from_month + .choices .choices__inner.error-border,
                                    .exp_from_year + .choices .choices__inner.error-border,
                                    .exp_to_month + .choices .choices__inner.error-border,
                                    .exp_to_year + .choices .choices__inner.error-border,
                                    .course_from_month + .choices .choices__inner.error-border,
                                    .course_from_year + .choices .choices__inner.error-border,
                                    .course_to_month + .choices .choices__inner.error-border,
                                    .course_to_year + .choices .choices__inner.error-border,
                                    /* Added more specific selectors for date fields */
                                    .select_dates .flex-date .choices .choices__inner.error-border,
                                    .select_dates .choices .choices__inner.error-border,
                                    .select_wraper.month .choices .choices__inner.error-border,
                                    .select_wraper.year .choices .choices__inner.error-border {
                                        border: 1px solid #DD3636 !important;
                                        
                                    }
                                    
                                    /* Ensure error styles persist when dropdown is focused */
                                    .choices.is-focused .choices__inner.error-border {
                                        border-color: #DD3636 !important;
                                    }

                                    /* Add error border styling for regular text input fields */
                                    input[type="text"].error-border {
                                        border: 1px solid #DD3636 !important;
                                    }

                                    /* Add specific styling for institute_name and field_specialization */
                                    .institute_name.error-border,
                                    .field_specialization.error-border {
                                        border: 1px solid #DD3636 !important;
                                        
                                    }

                                    .p_error.error {
                                        font-size: 14px;
                                        font-weight: 400;
                                        color: #DD3636;
                                        line-height: normal;
                                        margin: 0;
                                        padding: 0;
                                        margin-top: 8px;
                                        margin-bottom: 0;
                                        display: block;
                                        

                
                                    }                          

                                                                       
                                   
/* ==================Start of Basic Details ================== */

.basic_tab {
    .form_info {
        margin: 0 -32px;
        padding: 26px 0;
        position: relative;
        padding-left: 72px;
        border-bottom: 1px solid #EEE;
    }

    .form_info::before {
        content: "";
        width: 28px;
        height: 26px;
        display: inline-block;
        background: vars.$profile-icon no-repeat;
        background-position: 0px -1px;
        position: absolute;
        left: 32px;
        top: 0;
        bottom: 0;
        margin: auto;
        background-size: 252px;
    }

    .form_info h3 {
        font-size: 20px;
        font-weight: 500;
        color: #313131;
        line-height: 20px;
        padding-bottom: 8px;
        margin: 0;
    }
    .form_info span{
        display: block;
    color: #424242;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
    }
    /* Form Wrap */
    .form-wrap {
        padding: 24px 0;
        border-bottom: 1px solid #EEE;
        display: flex;

        &:last-child {
            border-bottom: 0;
        }
        .select2-container--default{
            content: '';
            display: contents;
            height: 26px;
            position: absolute;
            right: -15px;
            top: 11px;
            width: 20px;
            cursor: pointer;
            -webkit-user-select: none;
    
            b {
                border-color: #888 rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0);
                border-style: solid;
                border-width: 5px 4px 0 4px;
                height: 0;
                right: 2%;
                margin-left: -4px;
                margin-top: -2px;
                position: absolute;
                top: 46%;
                width: 0;
            }
        }
        .select2-container--default-title {
            content: '';
            display: contents;
            height: 26px;
            position: absolute;
            right: -15px;
            top: 11px;
            width: 20px;
            cursor: pointer;
            -webkit-user-select: none;
    
            b {
                border-color: #888 rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) rgba(0, 0, 0, 0);
                border-style: solid;
                border-width: 5px 4px 0 4px;
                height: 0;
                right: 2%;
                margin-left: -4px;
                margin-top: -2px;
                position: absolute;
                top: 146px;
                width: 0;
            }
        }
                .select2-selection__arrow_title {
                    height: 26px;
                    width: 20px;
                    position: absolute;
                    cursor: pointer;
                    top: 11px;
                    right: 8px;
        
                    b {
                        border-color: transparent transparent #888 transparent;
                        border-width: 0 4px 5px 4px;
                        border-style: solid;
                        height: 0;
                        left: 50%;
                        margin-left: -4px;
                        margin-top: -2px;
                        position: absolute;
                        top: 50%;
                        width: 0;
                    }
                }
                .select2-selection__arrow {
                    height: 26px;
                    width: 20px;
                    position: absolute;
                    cursor: pointer;
                    top: 44%;
                    right: 8px;
        
                    b {
                        border-color: transparent transparent #888 transparent;
                        border-width: 0 4px 5px 4px;
                        border-style: solid;
                        height: 0;
                        left: 50%;
                        margin-left: -4px;
                        margin-top: -2px;
                        position: absolute;
                        top: 50%;
                        width: 0;
                    }
                }
    }

    .frm_abel {
        font-size: 16px;
        font-weight: 500;
        line-height: 20px;
        color: vars.$heading-color;
        width: calc(50% - 52px);
        padding-right: 24px;

        span {
            display: block;
            margin-top: 4px;
            font-size: 14px;
            color: #757575;
            font-weight: 400;
            line-height: 18px;
        }

        i {
            color: #EB5757;
            font-style: normal;
        }
    }

    .right-col {
        padding: 0;
        width: calc(50% + 48px);
    }

    /* Input Fields */
    input[type="text"], 
    select {
        background-color: transparent;
        width: 100%;
        font-size: 16px;
        font-weight: 400;
        color: #202020;
        border-radius: 3px;
        border: 1px solid #E0E0E0;
        height: 48px;
        padding-left: 8px;
        appearance: none;
    }

    input:focus, 
    select:focus {
        border-color: vars.$primary-color;
        outline: none;
    }
    input:focus, 
    select:hover {
        border-color: vars.$primary-color;
        outline: none;
    }

    /* Disabled Input */
    input:disabled, 
    select:disabled {
        background: #E0E0E0 !important;
        border-color: #E0E0E0 !important;
        pointer-events: none;
    }

    /* Date Picker */
    .date-picker {
        position: relative;

        #datepicker {
            position: relative;
        }

        .dob_icon {
            width: 24px;
            height: 24px;
            content: "";
            display: inline-block;
            background: vars.$profile-icon no-repeat;
            position: absolute;
            right: 12px;
            top: 12px;
            background-position: -5px -386px;
        }
    }

    #datepicker:hover + .dob_icon,
    #datepicker:focus + .dob_icon,
    .dob_icon:hover {
        background-position: -35px -386px;
    }

    /* Profile Picture */
    .profile-view {
        display: inline-block;
        align-items: center;
        margin-bottom: 18px;

        .profile_picture {
            width: 98px;
            height: auto;
            max-height: 108px;
            border-radius: 3px;
            margin-right: 16px;
            float: left;
        }

        .prof-name {
            width: calc(100% - 114px);
            font-size: 16px;
            color: #202020;
            font-weight: 400;
            line-height: 20px;
            display: inline-block;
            word-wrap: break-word;

            #change_picture {
                margin-top: 3px;
                display: inline;
                
                span {
                    display: block;
                    color: #202020;
                    font-size: 16px;
                    font-weight: 400;
                    line-height: 20px;
                    margin-bottom: 4px;
                    word-break: break-word;
                }
            }

            a {
                display: inline-block;
                margin-top: 3px;
                text-decoration: underline;
                font-size: 14px;
                color: #757575;
                margin-right: 8px;
                line-height: 20px;
                font-weight: 400;
            }
        }
    }

    /* Radio Buttons */
    .radio_wrap {
        display: inline-block;
        margin-right: 32px;
        display: inline-block;

        input[type="radio"] {
            display: none;
        }

        label {
            font-size: 16px;
            color: #202020;
            font-weight: 400;
            cursor: pointer;
            display: inline-block;

            &::before {
                content: '';
                width: 24px;
                height: 24px;
                margin-right: 8px;
                background: vars.$profile-icon no-repeat;
                background-position: -5px -195px;
                border: 0;
                position: relative;
                top: 6px;
                display: inline-block;
                
            }
        }

        input[type="radio"]:checked + label::before {
            background-position: -95px -195px;
            
        }

        label:hover::before {
            background-position: -50px -195px;
        }
    }

    /* Buttons */
    .btn-group {
        text-align: right;
        display: block;
        height: 50px ;

        .btn {
            padding: 13px 26px;
            font-size: 16px;
            font-weight: 500;
            border-radius: 6px !important;
            align-items: center;
            justify-content: center;
            height: 100%;
            transition: all 0.2s ease-in-out; // Adds smooth hover effect
        }

        .discard {
            background-color: transparent;
            border: 1px solid vars.$primary-color;
            color: vars.$primary-color;
            width: 107px;
            margin-right: 16px;
            
            &:hover, &:focus {
                border-color: vars.$secondary-color;
                color: vars.$secondary-color;
            }
        }

        .save {
            background: vars.$primary-color;
            color: white;
            width: 168px;

            &:hover, &:focus {
                background-color: vars.$secondary-color;
            }
        }
    }
}


/* Select2 Styling */
.select2-container {
    width: 100% !important;

    .select2-selection {
        height: 48px;
        border-radius: 3px;

        .select2-selection__rendered {
            line-height: 48px;
            font-size: 16px;
            font-weight: 400;
            color: #202020;
        }

        .select2-selection__arrow {
            height: 26px;
            position: absolute;
            right: 12px;
            top: 12px;
        }
    }
}


/* ==================End of Basic Details ================== */

/* ========== Animations ========== */
@keyframes bounceInLeft {
    0% {
        transform: translateX(-1000px);  // Move from left
        opacity: 0;
    }
    60% {
        transform: translateX(30px);
        opacity: 1;
    }
    80% {
        transform: translateX(-10px);
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.bounceInLeft {
    animation: bounceInLeft 0.5s ease-in-out forwards;
}


/* ==================Start of Contact Details ================== */

.p_error {
    color: red;
    font-size: 14px;
    margin-top: 5px;
    font-weight: 500;
    display: block;
}
.hide {
    display: none;
}
.mail-border {
    border-color: red !important;
}


.contact_tab {
    .form_info {
        margin: 0 -32px;
        padding: 26px 0;
        position: relative;
        padding-left: 72px;
        border-bottom: 1px solid #EEE;
    }

    .form_info::before {
        content: "";
        width: 28px;
        height: 26px;
        display: inline-block;
        background: vars.$profile-icon no-repeat;
        background-position: -56px 0;
        position: absolute;
        left: 32px;
        top: 0;
        bottom: 0;
        margin: auto;
        background-size: 252px;
    }

    .form_info h3 {
        font-size: 20px;
        font-weight: 500;
        color: #313131;
        line-height: 20px;
        padding: 0;
        margin: 0;
    }

    /* Form Wrap */
    .form-wrap {
        padding: 24px 0;
        border-bottom: 1px solid #EEE;
        display: flex;

        &:last-child {
            border-bottom: 0;
        }
    }

    .frm_abel {
        font-size: 16px;
        font-weight: 500;
        line-height: 20px;
        color: vars.$heading-color;
        width: calc(50% - 52px);
        padding-right: 24px;
        
        span {
            display: block;
            margin-top: 4px;
            font-size: 14px;
            color: #757575;
            font-weight: 400;
            line-height: 18px;
        }

        i {
            color: #EB5757;
            font-style: normal;
        }
    }

    .right-col {
        padding: 0;
        width: calc(50% + 48px);
    }

    /* Input Fields */
    input[type="text"], 
    input[type="number"], 
    select, 
    textarea {
        width: 100%;
        font-size: 16px;
        font-weight: 400;
        color: #202020;
        border-radius: 3px;
        border: 1px solid #E0E0E0;
        height: 48px;
        padding-left: 12px;
        background-color: transparent;
    }

    input:focus, 
    select:focus, 
    textarea:focus {
        border-color: vars.$primary-color;
        outline: none;
    }

    /* Disabled Input */
    input:disabled, 
    select:disabled, 
    textarea:disabled {
        background: #E0E0E0 !important;
        border-color: #E0E0E0 !important;
        pointer-events: none;
    }

    /* Country Code & Phone Number */
    .support_coutry_code {
        position: relative;
        display: flex;

        & + .p_error {  // Target p_error that comes directly after support_coutry_code
            margin-left: 82px;  // Align with the phone input
        }

        .for_cun {
            width: 70px;
            margin-right: 12px;
            height: 48px;
            border-right: 0;
            position: relative;

            .input_field_country_code {
                border: 1px solid #b8d6ff;
                width: 100%;
                padding-left: 14px;
                padding-right: 27px;
                background: #fff;
                color: #2a2f32;
                margin: 0;
                font-size: 14px;
                height: 48px;
                font-weight: 400;
                -webkit-appearance: none;
                line-height: 33px;
                border-radius: 3px;
                overflow: hidden;
                vertical-align: middle;
                position: relative;
                font-family: inherit;
            }
            

            .contact_country_caret {
                    position: absolute;
                    right: 8px;
                    top: 24px;
                    transform: translateY(-50%);
                    pointer-events: none;
                    border-style: solid;
                    border-width: 6px 5px 0 5px;
                    border-color: #666 transparent transparent transparent;
                    display: inline-block !important; // Force override
                    background: none !important; // Remove any background
                    
                    &::before {
                        display: none;
                    }
            }
        }

        .code_wrap {
            width: calc(100% - 82px) !important;;
            position: relative;

            .country_code_span {
                position: absolute;
                left: 10px;
                line-height: 48px;
                font-size: 14px;
                color: #2a2f32;
            }

            input[type="number"] {
                padding-left: 50px;
                margin-bottom: 0;
            }
        }
    }

    
    /* Timezone */
    .timezone-dropdown {
        width: 100%;
    }

    /* Correspondence Address */
    textarea {
        height: 98px;
        padding: 12px;
    }

    /* Buttons */
    .btn-group {
        text-align: right;
        display: block;
        height: 50px ;

        .btn {
            padding: 13px 26px;
            font-size: 16px;
            font-weight: 500;
            border-radius: 6px !important;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 16px;
            transition: all 0.2s ease-in-out; // Adds smooth hover effect
        }

        .discard {
            background-color: transparent;
            border: 1px solid vars.$primary-color;
            color: vars.$primary-color;
            width: 107px;
            margin-right: 16px;
            

            &:hover, &:focus {
                border-color: vars.$secondary-color;
                color: vars.$secondary-color;
            }
        }

        .save {
            background: vars.$primary-color;
            color: white;
            width: 168px;

            &:hover, &:focus {
                background-color: vars.$secondary-color;
            }
        }
    }
}
/* ================== End of Contact Details ================== */

/* ==================Start of Professional Details ================== */
.professional_tab {
    display: block;

    form {
        position: relative;
    }

    .form_details {
        background: #fff;
    }

    .form_info {
        margin: 0 -32px;
        padding: 26px 0;
        position: relative;
        padding-left: 72px;
        border-bottom: 1px solid #EEE;

        h3 {
            font-size: 20px;
            font-weight: 500;
            color: #313131;
            margin: 0;
        }

        &::before {
            content: "";
            width: 28px;
            height: 26px;
            display: inline-block;
            background: vars.$profile-icon no-repeat;
            background-position: -112px 0;
            position: absolute;
            left: 32px;
            top: 0;
            bottom: 0;
            margin: auto;
            background-size: 252px;
        }
    }

    .form-wrap {
        padding: 24px 0;
        border-bottom: 1px solid #EEE;
        display: flex;
        

        &:last-child {
            border-bottom: 0;
        }
    }

    .frm_abel {
        font-size: 16px;
        font-weight: 500;
        line-height: 20px;
        color: vars.$heading-color;
        width: calc(50% - 52px);
        padding-right: 24px;

        i {
            color: #EB5757;
            font-style: normal;
        }

        span {
            display: block;
            margin-top: 4px;
            font-size: 14px;
            color: #757575;
            font-weight: 400;
            line-height: 18px;
        }
    }

    .right-col {
        padding: 0;
        width: calc(50% + 48px);

        input[type="text"], select {
            width: 100%;
            font-size: 16px;
            font-weight: 400;
            color: #202020;
            border-radius: 3px;
            border: 1px solid #E0E0E0;
            height: 48px;
            padding-left: 12px;
        }
    }

    /* Work Experience Section */
    .add_plus {
        color: vars.$primary-color;
        font-size: 14px;
        font-weight: 400;
        text-decoration: underline;
        float: right;
        line-height: normal;

        i {
            font-size: 20px;
            text-decoration: none;
            display: inline-block;
            margin-right: 4px;
            line-height: 16px;
            font-style: normal;
            position: relative;
            top: 1px;
        }
    }

    .work-info {
        .exp_main_wrap {
            display: block;
        }

        .exp-inner-wrap {
            display: flex;
            padding: 8px 0;

            &:first-child {
                padding-top: 0;
            }
        }

        .frm_abel.exp_label {
            padding-bottom: 8px;
        }

        .delete {
            color: vars.$primary-color;
            font-size: 14px;
            text-decoration: underline;
            float: right;
            cursor: pointer;

            &::before {
                content: "";
                background: vars.$profile-icon no-repeat;
                background-position: -9px -347px;
                width: 16px;
                height: 16px;
                display: inline-block;
                margin-right: 4px;
                text-decoration: none;
                position: relative;
                top: 2px;
                box-sizing: border-box;
            }
        }
    }

    /* Start Date & End Date Fields */
    .select_dates {
        display: flex;
        align-items: center;
    }

    .flex-date {
        display: flex;
        gap: 20px;

        .select_wraper {
            width: calc(50% - 10px);
            height: 48px;

            &.month {
                .p_error {
                    margin-bottom: 8px;
                }
            }
        }

        select {
            background-color: transparent;
            width: 100%;
            font-size: 16px;
            font-weight: 400;
            color: #202020;
            border-radius: 3px;
            border: 1px solid #E0E0E0;
            height: 48px;
            padding-left: 12px;

            &:disabled,
            &[disabled] {
                pointer-events: none;
                border-color: #E0E0E0 !important;
                border-radius: 3px;
                background-color: #eee !important;
                cursor: default;
                color: #777;

            }
        }
    }

    /* Checkbox Styling for Current Role */
    input[type="checkbox"] {
        content: "";
        background: vars.$profile-icon no-repeat;
        background-position: -10px -286px;
        border: 1px solid #E0E0E0;
        width: 14px;
        height: 14px;
        margin-right: 4px;
        top: 2px;
        display: inline-block;
        border-radius: 3px;
        position: relative;
        background-color: vars.$primary-color;
    }

    .frm_abel input[type="checkbox"] + label {
        font-size: 14px;
        line-height: 14px;
        color: #646464;
        font-weight: 400;
        text-transform: inherit;
        cursor: pointer;
        margin-bottom: 0;
        display: inline-flex;
        align-items: center;
    }

    // input[type="checkbox"]:checked + label:before {
        
    // }

    /* Buttons */
    .btn-group {
        text-align: right;
        display: block;
        height: 50px;

        .btn {
            padding: 13px 26px;
            font-size: 16px;
            font-weight: 500;
            border-radius: 6px !important;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 16px;
            transition: all 0.2s ease-in-out;
        }

        .discard {
            background-color: transparent;
            border: 1px solid vars.$primary-color;
            color: vars.$primary-color;
            width: 107px;
            margin-right: 16px;

            &:hover, &:focus {
                border-color: vars.$secondary-color;
                color: vars.$secondary-color;
            }
        }

        .save {
            background: vars.$primary-color;
            color: white;
            width: 168px;

            &:hover, &:focus {
                background-color: vars.$secondary-color;
            }
        }
    }

    /* Select2 Styling */
    .select2-container {
        width: 100% !important;
    }

    .select2-selection {
        height: 48px;
        border-radius: 3px;
    }
}

/* ================== End of Professional Details ================== */

/* ==================Start of Academic Details ================== */

.academics_tab {
    display: block;

    form {
        position: relative;
    }

    .form_details {
        background: #fff;
    }

    .form_info {
        margin: 0 -32px;
        padding: 26px 0;
        position: relative;
        padding-left: 72px;
        border-bottom: 1px solid #EEE;

        h3 {
            font-size: 20px;
            font-weight: 500;
            color: #313131;
            margin: 0;
        }

        &::before {
            content: "";
            width: 28px;
            height: 26px;
            display: inline-block;
            background: vars.$profile-icon no-repeat;
            background-position: -168px 0;
            position: absolute;
            left: 32px;
            top: 0;
            bottom: 0;
            margin: auto;
            background-size: 252px;
        }
    }

    .form-wrap {
        padding: 24px 0;
        border-bottom: 1px solid #EEE;
        display: flex;
        justify-content: space-between;

        &:last-child {
            border-bottom: 0;
        }
    }

    .frm_abel {
        font-size: 16px;
        font-weight: 500;
        line-height: 20px;
        color: vars.$heading-color;
        width: calc(50% - 52px);
        padding-right: 24px;

        i {
            color: #EB5757;
            font-style: normal;
        }

        span {
            display: block;
            margin-top: 4px;
            font-size: 14px;
            color: #757575;
            font-weight: 400;
            line-height: 18px;
        }
    }

    .right-col {
        padding: 0;
        width: calc(50% + 48px);

        input[type="text"],
        select {
            width: 100%;
            font-size: 16px;
            font-weight: 400;
            color: #202020;
            border-radius: 3px;
            border: 1px solid #E0E0E0;
            height: 48px;
            padding-left: 12px;
        }
    }

    /* Academic Experience Section */
    .add_plus {
        color: vars.$primary-color;
        font-size: 14px;
        font-weight: 400;
        text-decoration: underline;
        float: right;
        line-height: normal;

        i {
            font-size: 20px;
            text-decoration: none;
            display: inline-block;
            margin-right: 4px;
            line-height: 16px;
            font-style: normal;
            position: relative;
            top: 1px;
        }
    }

    .work-info {
        .exp_main_wrap {
            display: block;
        }

        .exp-inner-wrap {
            display: flex;
            padding: 8px 0;

            &:first-child {
                padding-top: 0;
            }
        }

        .frm_abel.exp_label {
            padding-bottom: 8px;
        }

        .delete {
            color: vars.$primary-color;
            font-size: 14px;
            text-decoration: underline;
            float: right;
            cursor: pointer;

            &::before {
                content: "";
                background: vars.$profile-icon no-repeat;
                background-position: -9px -347px;
                width: 16px;
                height: 16px;
                display: inline-block;
                margin-right: 4px;
                text-decoration: none;
                position: relative;
                top: 2px;
                box-sizing: border-box;
            }
        }
    }

    /* Start Date & End Date Fields */
    .select_dates {
        display: flex;
        align-items: center;
    }

    .flex-date {
        display: flex;
        gap: 20px;

        .select_wraper {
            width: calc(50% - 10px);
            height: 48px;
        }

        select {
            background-color: transparent;
            width: 100%;
            font-size: 16px;
            font-weight: 400;
            color: #202020;
            border-radius: 3px;
            border: 1px solid #E0E0E0;
            height: 48px;
            padding-left: 12px;
        }
    }

    /* Buttons */
    .btn-group {
        text-align: right;
        display: block;
        height: 50px;

        .btn {
            padding: 13px 26px;
            font-size: 16px;
            font-weight: 500;
            border-radius: 6px !important;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 16px;
            transition: all 0.2s ease-in-out;
        }

        .discard {
            background-color: transparent;
            border: 1px solid vars.$primary-color;
            color: vars.$primary-color;
            width: 107px;
            margin-right: 16px;

            &:hover, &:focus {
                border-color: vars.$secondary-color;
                color: vars.$secondary-color;
            }
        }

        .save {
            background: vars.$primary-color;
            color: white;
            width: 168px;

            &:hover, &:focus {
                background-color: vars.$secondary-color;
            }
        }
    }

    /* Select2 Styling */
    .select2-container {
        width: 100% !important;
    }

    .select2-selection {
        height: 48px;
        border-radius: 3px;
    }
}








/* ================== End of Academic Details================== */

/* ================== Start of Outcome Details ================== */

.outcome_tab {
    display: block;

    form {
        position: relative;
    }

    .form_details {
        background: #fff;
    }

    .form_info {
        margin: 0 -32px;
        padding: 26px 0;
        position: relative;
        padding-left: 72px;
        border-bottom: 1px solid #EEE;

        h3 {
            font-size: 20px;
            font-weight: 500;
            color: #313131;
            margin: 0;
        }

        &::before {
            content: "";
            width: 28px;
            height: 26px;
            display: inline-block;
            background: vars.$profile-icon no-repeat;
            background-position: -224px 0; // Icon for Learning Outcome
            position: absolute;
            left: 32px;
            top: 0;
            bottom: 0;
            margin: auto;
            background-size: 252px;
        }
    }

    .form-wrap {
        padding: 24px 0;
        border-bottom: 1px solid #EEE;
        display: flex;
        justify-content: space-between;

        &:last-child {
            border-bottom: 0;
        }
    }

    .frm_abel {
        font-size: 16px;
        font-weight: 500;
        line-height: 20px;
        color: vars.$heading-color;
        width: calc(50% - 52px);
        padding-right: 24px;

        i {
            color: #EB5757;
            font-style: normal;
        }
    }

    .right-col {
        padding: 0;
        width: calc(50% + 48px);

        select {
            width: 100%;
            font-size: 16px;
            font-weight: 400;
            color: #202020;
            border-radius: 3px;
            border: 1px solid #E0E0E0;
            height: 48px;
            padding-left: 12px;
            background-color: #fff;
            cursor: pointer;
        }
    }

    /* Select Dropdown Styling */
    .select2-container {
        width: 100% !important;
    }

    .select2-selection {
        height: 48px;
        border-radius: 3px;
    }

    /* Buttons */
    .btn-group {
        text-align: right;
        display: block;
        height: 50px;

        .btn {
            padding: 13px 26px;
            font-size: 16px;
            font-weight: 500;
            border-radius: 6px !important;
            align-items: center;
            justify-content: center;
            height: 100%;
            transition: all 0.2s ease-in-out;
        }

        .discard {
            background-color: transparent;
            border: 1px solid vars.$primary-color;
            color: vars.$primary-color;
            width: 107px;
            margin-right: 16px;

            &:hover, &:focus {
                border-color: vars.$secondary-color;
                color: vars.$secondary-color;
            }
        }

        .save {
            background: vars.$primary-color;
            color: white;
            width: 168px;

            &:hover, &:focus {
                background-color: vars.$secondary-color;
            }
        }
    }

    /* Hiding Areas of Interest */
    .form-wrap.hide {
        display: none !important;
    }
}

/* ================== End of Outcome Details================== */



#exportModal {
    display: none; /* Initially hidden */
    position: fixed;
    overflow-x: hidden;
    overflow-y: auto;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5); /* Dark overlay */
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    z-index: 99999 !important; /* Higher z-index to ensure it's on top */

    &.show {
        display: block !important;
        opacity: 1 !important;
    }

    .modal-dialog {
        width: 540px;
        height: auto;
        max-width: 540px;
        margin: 30px auto;
        position: relative;
        background: transparent;
        transition: transform .3s ease-out;
        transform: translate(0, 0)!important;
    }

    .modal-content {
        
        box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
        border-radius: 10px !important;
        border: 1px solid #999 !important;
        position: relative;
        padding: 32px 47px;
        outline: 0;
        background-clip: padding-box;
        outline: 0;
        overflow: hidden;
    }

    .modal-header {
        display: block;
        min-height: 16.43px;
        padding: 0 !important;
        border-bottom: none !important;
        text-align: center;

        h4 {
            color: #424242;
            text-align: center;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            letter-spacing: 0.16px;
            padding-bottom: 0;
            margin: 0;
        }

        .close {
            color: #9E9E9E;
            right: 31px;
            top: 28px;
            font-weight: 400;
            opacity: 1;
            font-size: 28px;
            position: absolute;
            z-index: 8;
            padding: 0;
            cursor: pointer;
            background: transparent;
            border: 0;
            line-height: 1;
            text-shadow: 0 1px 0 #fff;
        }
    }

    .modal-body {
        position: relative;
        padding: 31px 0 0 0;

        &.scrolly {
            max-height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 31px 0 0 0;            
        }

        .upload-txt {

            text-align: center;

            p:first-child {
                margin-bottom: 30px;
            }
            p {
                text-align: left;
                font-size: 14px;
                font-weight: 400;
                color: #646464;
                margin-bottom: 16px;
                line-height: normal;
                letter-spacing: 0.14px;
            }
            
            .confirm {
                text-align: left;
            }

            #profile-download {
                margin-top: 15px;
                letter-spacing: normal;
                text-transform: inherit;
                font-size: 14px;
                line-height: 30px;
                background: vars.$primary-color;
                width: 110px;
                height: 32px;
                border: none;
                cursor: pointer;
                font-weight: 500;
                color: #fff;
                border-radius: 3px;
                
                &:hover {
                    background: #025CC3;
                }
            }
        }
    }
}

#deleteUserProfileModal {
    display: none; /* Initially hidden */
    overflow-x: hidden;
    overflow-y: auto;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5); /* Dark overlay */
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    z-index: 99999 !important; /* Higher z-index to ensure it's on top */

    &.show {
        display: flex !important;
        opacity: 1 !important;
    }

    .modal-dialog {
        width: 540px;
        height: auto;
        max-width: 540px;
        margin: 30px auto;
        position: relative;
        background: transparent;
        transition: transform .3s ease-out;
        transform: translate(0, 0)!important;
    }

    .modal-content {
        box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
        border-radius: 6px;
        border: 1px solid #999 !important;
        position: relative;
        padding: 32px 47px;
        outline: 0;
        overflow: hidden
    }

    .modal-header {
        min-height: 16.43px;
        padding: 0 !important;
        border-bottom: none !important;
        text-align: center;
        display: block;

        h4 {
            color: #424242;
            text-align: center;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            letter-spacing: 0.16px;
            padding-bottom: 0;
            margin: 0;
        }

        .close {
            color: #9E9E9E;
            right: 30px;
            top: 26px;
            font-weight: 400;
            opacity: 1;
            font-size: 28px;
            position: absolute;
            z-index: 8;
            padding: 0;
            cursor: pointer;
            background: transparent;
            border: 0;
            line-height: 1;
            text-shadow: 0 1px 0 #fff;
        }
    }

    .modal-body {
        position: relative;
        

        &.scrolly {
            max-height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 31px 0 0 0;
            
        }

        p {
            color: #646464;
            font-size: 14px;
            font-style: normal;
            line-height: normal;
            letter-spacing: 0.14px;
            margin-bottom: 12px;
            text-align: left;
            font-weight: 700;
            font-family: Roboto;


            &.delete_note_1 {
                margin-top: 24px;
                margin-bottom: 0 !important;
            }

            &.delete_note_2 {
                font-weight: 400 !important;
                margin-top: 24px;
                margin-bottom: 0 !important;
            }

            &.test-center {
                margin: 0 !important;
                color: #f00;
            }
        }

        .list_view {
            padding: 0;
            margin: 0;

            li {
                text-align: left;
                font-size: 14px;
                font-weight: 400;
                color: #646464;
                position: relative;
                padding-left: 16px;
                margin-bottom: 0;
                line-height: 1.2;
                letter-spacing: 0.14px;

                &::before {
                    content: "•";
                    color: #646464;
                    position: absolute;
                    left: 0;
                    font-size: 20px;
                    line-height: normal;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }
        }

        .delete_profile_modal_btn_grp {
            display: flex;
            align-items: center;
            width: 100%;
            justify-content: center;
            margin: 31px 0;
            margin-bottom: 0;

            button {
                padding: 0;
                margin: 0;
                position: relative;
                top: 0;
                transition: none;
                height: 32px !important;
                width: 160px !important;
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 7.908px;
                font-size: 14px;
                font-style: normal;
                font-weight: 500;
                line-height: normal;
                text-transform: none;
                letter-spacing: 0;
                border-radius: 4px;

                &.white-bg-btn {
                    background: #FFF;
                    color: #1179EF;
                    border: 1px solid #1179EF;
                    margin-right: 31px !important;
                    box-shadow: none;
                    
                    &:hover {
                        stroke-width: 1px;
                        stroke: #025CC3;
                        box-shadow: 0 1px #2971A5;
                        background-color: #fff;
                        color: #008CBA;
                        border-color: #118aef;
                    }
                }

                &.blue-bg-btn {
                    background: #1179EF;
                    color: #FFF;
                    border-color: #118aef;
                    box-shadow: 0 1px #118aef;
                    display: flex;
                        width: 160px !important;
                        height: 32px;
                        padding: 13.557px 15.817px;
                        justify-content: center;
                        align-items: center;
                        gap: 7.908px;
                        border-radius: 4px;
                        background: #1179EF;
                        color: #FFF;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: normal;
                        letter-spacing: 0;
                        text-transform: none;
                    
                    &:hover {
                        border-radius: 6px;
                        background: #025CC3;
                        box-shadow: 0 1px #2971A5;
                        border-color: #118aef;
                        color: #fff;
                    }
                }
            }
        }
    }
}

.choices__list--dropdown .choices__item--selectable.is-highlighted {
    background-color: #007bff !important; /* Highlight color */
    color: white !important;
    font-weight: bold;
}

/* Dropdown styling for Choices.js */
.choices__list--dropdown {
  background: #fff !important; /* White background */
  border: 1px solid #ddd !important;
  color: #333 !important;
  z-index: 99999 !important; /* Ensure dropdowns appear above everything */
  max-height: 200px !important; /* Set a fixed height */
  overflow-y: auto !important; /* Enable vertical scrolling */
}

/* Hide browser scrollbar for Choices dropdown */
.choices__list--dropdown::-webkit-scrollbar {
  width: 4px; /* Width of the scrollbar */
}

.choices__list--dropdown::-webkit-scrollbar-track {
  background: #f1f1f1; /* Track color */
}

.choices__list--dropdown::-webkit-scrollbar-thumb {
  background: #888; /* Handle color */
  border-radius: 3px;
}

.choices__list--dropdown::-webkit-scrollbar-thumb:hover {
  background: #555; /* Handle color on hover */
}

/* For Firefox */
.choices__list--dropdown {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

/* Prevent inner scrolling in dropdown items container */
.choices__list--dropdown .choices__list {
  max-height: none !important;
  overflow: visible !important;
}

/* Set a fixed height for each dropdown item */
.choices__list--dropdown .choices__item {
  padding: 8px 10px !important;
  line-height: 1.5 !important;
}

.work_exp_err.error{
    border: none !important;
    font-size: 14px;
    font-weight: 400;
    color: #DD3636;
    line-height: normal;
    margin: 0;
    padding: 0;
    margin-top: 8px;
    margin-bottom: 0;
}

.academics_exp_err.error{
    border: none !important;
    font-size: 14px;
    font-weight: 400;
    color: #DD3636;
    line-height: normal;
    margin: 0;
    padding: 0;
    margin-top: 8px;
    margin-bottom: 0;
}

/* Main dropdown container */
.choices {
    background: vars.$white !important;
    border-color: #E0E0E0;
    border-radius: 3px;
    margin-bottom: 0;

    &:hover {
        border-color: vars.$primary-color;
    }

    &.error-border {
        .choices__inner {
            border: 1px solid #DD3636 !important;
        }
    }

    &.is-disabled {
        pointer-events: none;
        cursor: default;
        
        .choices__inner {
            background-color: #eee !important;
            border-color: #E0E0E0 !important;
            cursor: default;
            color: #777;
        }
        
        .choices__item {
            color: #777 !important;
        }
        
        .choices__button {
            display: none;
        }
    }
    /* Ensure enabled state returns to normal styling */
    &:not(.is-disabled) {
        pointer-events: auto;
        cursor: pointer;
        
        .choices__inner {
            background-color: vars.$white !important;
            border-color: #E0E0E0;
            cursor: pointer;
            color: #202020;
            
            &:hover {
                border-color: vars.$primary-color;
            }
        }
        
        .choices__item {
            color: #202020 !important;
        }
    }
}

/* Selected items (chips) */
.choices__inner {
    background: vars.$white !important;
    color: #333 !important;

    &:hover {
        border-color: vars.$primary-color;
    }
}


.profile-sec {

    input[type="checkbox"],
    input[type="radio"] {
        &:focus+label::before {
            border: 1px solid #1179EF !important;
        }
    }

    .btn {
        &:focus {
            border-color: #1179EF;
            box-shadow: none;
        }

        &.save {
            &:focus {
                background-color: #025CC3;
            }
        }

        &.discard {
            &:focus {
                border-color: #025CC3;
                color: #025CC3;
            }
        }
    }


    .left_nav_main ul li a {
        &:focus {
            outline: 1px solid #1179EF;
            border-radius: 6px;
            text-decoration: none;
        }
    }

    // Add this section
    .form-wrap {

        input[type="text"],
        input[type="number"],
        input[type="password"],
        input[type="email"],
        select,
        textarea {
            &:focus {
                border-color: #1179EF;
            }

            &.error:focus {
                border-color: #DD3636;
            }
        }
    }
}
/* Fade Out Animation */
@keyframes fadeOut {
    0% { opacity: 1; }
    80% { opacity: 1; }
    100% { opacity: 0; visibility: hidden; }
}

/* Bounce In Left Animation */
@keyframes bounceInLeft {
    0% {
      transform: translateX(-1000px);
      opacity: 0;
    }
    60% {
      transform: translateX(30px);
      opacity: 1;
    }
    80% {
      transform: translateX(-10px);
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  .bounceInLeft {
    animation: bounceInLeft 0.5s ease-in-out forwards;
  }
.fade-out-animation {
    animation: fadeOut 0.2s forwards;
}

/* Profile Picture Delete Success Message */
.profile-picture-success {
    color: #28a745;
    font-size: 14px;
    margin-top: 10px;
    font-weight: 500;
    display: block;
    animation: fadeInOut 5s ease-in-out forwards;
}

/* Fade In and Out Animation for Success Message */
@keyframes fadeInOut {
    0% { 
        opacity: 0; 
        transform: translateY(-10px); 
    }
    10% { 
        opacity: 1; 
        transform: translateY(0); 
    }
    90% { 
        opacity: 1; 
        transform: translateY(0); 
    }
    100% { 
        opacity: 0; 
        transform: translateY(-10px); 
        visibility: hidden; 
    }
}

    
