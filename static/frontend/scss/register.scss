@use "variables" as vars;

.logo-image {
  padding-bottom: 32px;
  text-align: center;


  a {
      color: #777;
      font-size: 12px;
      cursor: pointer;
  }

  img {
      max-height: 36px;
  }

  .logo-text {
      display: inline-block;
      width: 70px;
      line-height: 24.5px;
      font-size: 17px;
      color: #757575;
      font-weight: 400;
      padding-left: 10px;
      margin-left: 8px;
      vertical-align: middle;
      position: relative;
      font-family: "Roboto", sans-serif;
  }

  .logo-text::before {
      width: 1.2px;
      height: 34px;
      content: "";
      display: inline-block;
      background: #E0E0E0;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      margin: auto;
  }

}
.heading {
  color: vars.$heading-color;
  font-size: 20px !important;
  font-weight: 500;
  text-align: center;
  padding-bottom: 8px;
  margin: 0;
  line-height: 20px;
}

.content-sub-heading {
  padding-bottom: 0px;
  font-size: 16px;
  line-height: 20px;
  color: #646464;
  text-align: center;
  display: flex;
  font-weight: 400;
  justify-content: center;
  margin: 0 auto;
  padding-bottom: 20px;
}
.social-login {
  text-align: center;
  max-width: 400px;
  margin: 0 auto;
  font-family: "Roboto";

  .login-button {
    font-size: 14px;
    line-height: 38px;
    height: 40px;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-bottom: 12px;
    border: 1px solid #EEE;;
    border-radius: 25px;
    color: var(--color-text-primary);
    background-color: var(--color-background);
    text-decoration: none;
    cursor: pointer;
    transition: border-color 0.2s ease, background-color 0.2s ease;
    padding: 0;
    div {
      margin-right: 15px;
    }

    &:hover {
      outline: 1px solid #1179EF;
      outline-offset: 0px;
      border-color: transparent;
      background: rgb(232, 240, 254);
    }

        
    span{
      position: relative;
      top: 0px;
      bottom: 0px;
      right: -2px;
      font-size: 14px;
      font-weight: 400;
      line-height: 38px;
      color: #4F4F4F;
    }
  }

  .more-options {
    display: inline-block;
    width: 100%;
    text-align: center;
    cursor: pointer;
    font-size: 14px;
    color: #1179EF;
    font-weight: 500;
    line-height: normal;
    margin-top: 4px;
    margin-bottom: 12px;

    &:hover {
      text-decoration: none;
      color: #025CC3;
    }
  }

  .signup_link_account{
    font-size: 14px;
    margin-top: 8px !important;
    font-weight: 520;
    color: var(--color-text-primary);
    margin: 0;
    span{
      font-size: 14px;
      font-weight: 400;
      color: #828282;
      padding-right: 2px;
      display: inline-block;
    }
  }
  .signup-text{
    display: inline-block;
    padding-right: 5px;
    padding-top: 0;
    font-size: 14px;
    font-weight: 400;
    color: #4f4f4f !important;
  }

  .hidden {
    display: none;
  }

  .social-icon {
    background: vars.$social-icon no-repeat;
    width: 20px;
    height: 20px;
    top: 6px;
    display: inline-block;
    margin-left: -7px;
    width: 20px;
    position: relative;
    height: 20px;
    top: 6px;
    right: -2px;

    &.google-icon {
      background-position: -20px 0px !important;
    }

    &.linkedin-icon {
      background-position: -152px 0px !important;
    }

    &.apple-icon {
      background-position: -64px 0px !important;
    }

    &.facebook-icon {
      background-position: -108px 0px !important;
    }
    &.email-icon {
      background-position: -197px 0px !important;
    }

  }
  .social-icon:before {
    background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
    width: 20px;
    height: 20px;
    top: 6px;
}
}