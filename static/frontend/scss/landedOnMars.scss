@use "variables" as vars;

.error_page {
  .banner_section {
    background: #212830 url(https://cfs22.simplilearn.com/ice9/404-page/1600x800/404_bg.jpg) no-repeat scroll center center / cover;
    height: 100vh;
    text-align: center;

    .container::before {
      content: " ";
      display: table;
    }

    .container::after {
      clear: both;
    }

    .banner-content {
      position: static;

      h1 {
        color: vars.$background-color;
        font-size: 55px;
        font-weight: 500;
        margin-top: 20px;
        margin-bottom: 20px;
        padding-top: 50px;
        padding-bottom: 0;
        font-family: vars.$default-font-family;
        line-height: 1.1;
      }

      h2 {
        color: vars.$background-color;
        font-size: 30px;
        font-weight: 400;
        line-height: 38px;
        margin-top: 20px;
        margin-bottom: 10px;
        font-family: vars.$default-font-family;
        padding-bottom: 60px;
      }

      .btn-warning {
        color: vars.$background-color;
        background-color: #f0ad4e;
        border-color: #eea236 !important;
        font-family: vars.$default-font-family;
      }

      .btn {
        display: inline-block;
        margin-bottom: 0;
        font-weight: 400;
        text-align: center;
        vertical-align: middle;
        -ms-touch-action: manipulation;
        touch-action: manipulation;
        cursor: pointer;
        background-image: none;
        border: 1px solid transparent;
        white-space: nowrap;
        padding: 6px 12px;
        font-size: 14px;
        line-height: 1.42857143;
        border-radius: 4px;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }
    }
  }

}

