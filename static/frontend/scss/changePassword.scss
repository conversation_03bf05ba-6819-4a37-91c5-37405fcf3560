.header {
  background-color: #f8f8f8;
  // border-bottom: 1px solid #e5e5e5;
}
.main_content_wrap {
  display: block;

  .other_detail_wrap {
    width: 100% !important;
  }

  .details-tab {
    float: none;
    display: inline-block;
    width: calc(100% - 326px);
    padding-top: 0;
    padding-left: 0;
  }

  .tab-content {
    .change-password-container {
      margin: 0 auto;
      width: 400px;
      min-height: 500px;
      
      .submit-container {
        .disabled
        .disabled:hover {
          background: #88BCF7;
          border-color: #88BCF7;
          opacity: 1;
          color: #fff;
          cursor: not-allowed !important;
          box-shadow: none;
        }
        .btn-signup {
            line-height: 46px;
            font-size: 16px;
            font-weight: 500;
            letter-spacing: normal;
            box-shadow: none;
            border-radius: 6px;
            color: #fff;
            &:enabled{
              background: #1179EF;
            }
            &:disabled{
              border-radius: 3px;
              background: #88BCF7 !important;
              opacity: 1;
            }
        }
    }
      .change-password-input {
        height: 48px;
        border: 1px solid #b8d6ff;
        font-size: 16px;
        padding-left: 24px;
        font-weight: 400;
        color: #4f4f4f;
        margin-bottom: 0px;
        border-radius: 3px;
        background: #fff;
        width: 100%;
      }
      .change-password-input::placeholder{
        font-weight: normal;
      }
      .oldpassword-icon {
        background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
        content: '';
        position: absolute;
        display: inline-block;
        cursor: pointer;
        background-position: -60px -138px;
        top: 11px !important;
        right: 24px;
        width: 20px;
        height: 20px;
      }
      .newpassword-icon{
        background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
        content: "";
        position: absolute;
        display: inline-block;
        cursor: pointer;
        background-position: -60px -138px;
        top: 11px !important;
        right: 48px;
        width: 20px;
        height: 20px;
      }

      .oldpassword-see {
        background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
        content: "";
        position: absolute;
        display: inline-block;
        cursor: pointer;
        background-position: -19px -138px;
        top: 11px !important;
        right: 24px;
        width: 20px;
        height: 20px;
      }

      .newpassword-see {
        background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
        content: "";
        position: absolute;
        display: inline-block;
        cursor: pointer;
        background-position: -19px -138px;
        top: 11px !important;
        right: 48px;
        width: 20px;
        height: 20px;
      }

      .newpassword-info {
        background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
        content: "";
        position: absolute;
        display: inline-block;
        cursor: pointer;
        background-position: -20px -81px;
        top: 11px !important;
        right: 24px;
        width: 20px;
        height: 20px;
      }

      .password-tooltip {
        min-width: 417px;
        left: 8px;
        top: 6px;
        transform: translate(0, -50%);
        padding: 8px;
        color: #EEE;
        background-color: #313131;
        font-weight: normal;
        font-size: 13px;
        border-radius: 6px;
        z-index: 9999;
        box-sizing: border-box;
        border: 1px solid #313131;
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
        visibility: hidden;
        opacity: 0;
        transition: opacity 0.8s;
        position: absolute;
        .triangle::after {
          background: #313131;
      }
      }

      .newpassword-info:hover .password-tooltip,
      .info-icon .password-tooltip.visible {
        visibility: visible;
        opacity: 1;
      }

      .password-heading {
        font-size: 16px;
        font-weight: 700;
        padding-bottom: 8px;
        line-height: normal;
      }

      .password-list {

        ul,
        li {
          list-style: disc;
          font-size: 14px;
          text-align: left;
        }

        ul {
          padding-left: 0;
        }

        li {
          padding-left: 10px;
          margin-bottom: 8px;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          padding-left: 20px;
          position: relative;
          padding-bottom: 0;
        }

      }

      .info-icon .password-tooltip i {
        position: absolute;
        top: 50%;
        right: 100%;
        margin-top: -15px;
        width: 15px;
        height: 30px;
        overflow: hidden;
      }

      .info-icon .password-tooltip i::after {
        content: '';
        position: absolute;
        width: 15px;
        height: 15px;
        left: 0;
        top: 50%;
        transform: translate(50%, -50%) rotate(-45deg);
        background-color: #FFFFFF;
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
      }

      .confirmpassword-icon {
        background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
        background-position: -60px -138px;
        width: 20px;
        height: 20px;
        top: 14px;
        right: 24px;
        position: absolute;
        content: '';
      }

      .change-pwd-form {
        .form-input {
          margin-bottom: 20px;

        }

        .password-field {
          display: inline-block;
          width: 100%;
          position: relative;


          .passowrd-wrap {
            position: relative;

            .password-eye-slash {
              background: url("https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz") no-repeat;
              position: absolute;
              right: 56px;
              top: 25px;
              width: 22px;
              height: 22px;
              transform: translateY(-50%);
              cursor: pointer;
              background-position: -21px -140px;
            }
            .info-invalid{
              background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
              content: '';
              position: absolute;
              display: inline-block;
              background-position: -101px -81px;
              width: 22px;
              height: 22px;
              right: 22px;
              top: 25px;
            }
            .hide {
              display: none !important;
            }
            .visible-hidden{
              visibility: hidden;
            }

            .password-info {
              background: url("https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz") no-repeat;
              position: absolute;
              transform: translateY(-50%);
              cursor: pointer;
              background-position: -21px -82px;
              width: 22px;
              height: 22px;
              right: 24px;
              top: 24px;

            }
          }
        }
      }
    }
  }
}



.password-container {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}

#msg_box{
  font-size: 14px;
  font-weight: 500;
}
#error_msg {
    color: #DD3636;
    font-family: Roboto;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    padding-left: 0;
    padding-right: 0;
}

#msg_box{
  margin-bottom: 20px;
}

#pass-changed {
  height: 50px;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: green;
  color: #fff;
  text-align: center;
  padding: 15px;
  font-size: 18px;
  font-weight: bold;
  z-index: 9999;
  pointer-events: none;
  animation: fadeOut 5s ease-in-out;
}

/* Keyframe animation to fade out */
@keyframes fadeOut {
  0% {
    opacity: 1;
  }

  80% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    visibility: hidden;
  }
}


.heading-wrap {
  padding: 0;
  margin: 36px 0;
  font-size: 20px;
  font-weight: 500;
  line-height: normal;
  color: #424242;
}

.info_msg {
  color: #646464 !important;
  background: none;
  display: block !important;
  padding-bottom: 0px !important;
}

.password-container {
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-field {
  position: relative;
  display: flex;
  // margin-top: 20px !important;
  margin-bottom: 0 !important;
}

.password-form {
  margin: 0 auto;
  background-color: #fff;
  border-radius: 3px;
  display: block;
  min-height: 600px;
}

.error_msg.hide {
  display: none;
}

.hidden {
  display: none;
}

.password-input-container {
  position: relative;
  width: 100%;
  margin-bottom: 20px;
}

.input-field {
  display: block;
  align-self: center;
  margin: 0 auto;
  width: 100%;
  font-size: 16px;
  font-weight: 400;
  color: #202020;
  border-radius: 3px;
  border: 1px solid #b8d6ff;
  height: 48px;
  padding-left: 12px;

  :focus {
    outline: 1px solid #118aef;
    outline-offset: 0px;
  }
}

.border-red {
  border: 1px solid #DD3636 !important;
  transition: border 0.3s ease-in-out;
}

.submit-div {
  width: 100%;
  margin-top: 20px !important;
  text-align: center;
}

.btn-signup {
  background: #118aef;
  width: 100%;
  text-transform: none;
  font-size: 14px;
  line-height: 40px;
}

@media (min-width: 1200px) {
  .password-container {
    width: 1200px;
  }
}

.password-tooltip {
  min-width: 417px;
  top: 50%;
  left: 100%;
  margin-left: 20px;
  transform: translate(0, -50%);
  padding: 30px 0px 30px 20px;
  color: #484f57;
  background-color: #FFFFFF;
  font-weight: normal;
  font-size: 13px;
  border-radius: 2px;
  position: absolute;
  z-index: 99999999;
  box-sizing: border-box;
  border: 1px solid #bfcace;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.8s;
}

.info-icon .password-tooltip i {
  position: absolute;
  top: 50%;
  right: 100%;
  margin-top: -15px;
  width: 15px;
  height: 30px;
  overflow: hidden;
}

.info-icon .password-tooltip i::after {
  content: '';
  position: absolute;
  width: 15px;
  height: 15px;
  left: 0;
  top: 50%;
  transform: translate(50%, -50%) rotate(-45deg);
  background-color: #FFFFFF;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
}

.password-heading {
  font-size: 18px;
  font-weight: 500;
  padding-bottom: 24px;
}

.password-field .password-list li {
  padding-left: 10px;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  padding-left: 20px;
  position: relative;
  padding-bottom: 0;
}

.password-field .password-list ul,
.password-field .password-list li {
  list-style: none !important;
  font-size: 14px;
  text-align: left;
}

.password-list li {
  padding-bottom: 16px;
  padding-left: 10px;
}

.password-list li:before {
  width: 5px;
  height: 5px;
  content: "";
  display: inline-block;
  background: #fff;
  border-radius: 50%;
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  left: 4px;
}

.password-list .red::before,
.password-list .correct::before {
  width: 20px;
  height: 14px;
  left: 0;
  background: url("https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz") no-repeat;
}

.password-list ul {
  padding-left: 0;
}

.password-list .red::before {
  background-position: -71px -277px;
}

.password-list .correct::before {
  background-position: -45px -277px;
}

.change-password-container .form-control::placeholder {
  color: #9e9e9e;
  font-size: 16px;
  font-weight: 400 !important;
  line-height: 20px
}