@use "variables" as vars;

.mobile-layout {
    display: none;
}

@media only screen and (max-width: 667px) and (min-width: 240px) {
    .mobile-layout {
        display: block !important;
        position: fixed;
        z-index: 999;
        width: 100%;
        height: 100%;
        overflow: scroll;
        background: vars.$background-color;

        .new-design-changes {
            padding: 40px 20px 0;
            font-family: vars.$roboto-sans-font-family;

            .enrolled {
                font-size: 20px;
                line-height: 27px;
                color: #2a2f32;
                text-align: center;
                font-weight: 700;
                margin-bottom: 0;
            }

            .mobile-app-text {
                font-size: 13px;
                line-height: 27px;
                text-align: center;
                color: #000;
                font-weight: 400;
                margin-bottom: 0;
            }

            .img-container {
                margin-bottom: 20px;
                text-align: center;
                display: -ms-flexbox;
                display: flex;
                -ms-flex-pack: center;
                justify-content: center;
                margin-top: 9px;

                .background-img {
                    width: 254px;
                    height: 220px;
                    background: #fff url(https://www.simplilearn.com/ice9/assets/mobile-bg.svgz) no-repeat;
                    background-position: -4px 2px;
                }
            }

            ul {
                margin: 0;
                padding: 0;
                list-style: none;

                li {
                    padding-left: 18px !important;
                    position: relative;
                    text-align: left;
                    font-size: 14px;
                    line-height: 26px;
                    font-weight: 400;
                    color: #2a2f32;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                }

                li::before {
                    content: "";
                    width: 6px;
                    height: 6px;
                    background: #E1A406;
                    border-radius: 50%;
                    position: absolute;
                    left: 0;
                    top: 10px;
                }

            }

            a {
                color: #0a98d7;
                text-decoration: none;

                .learning-btn {
                    width: 100%;
                    height: 48px;
                    background: #1179EF;
                    border-radius: 4px;
                    font-weight: 500;
                    font-size: 18px;
                    text-transform: inherit;
                    padding: 0;
                    color: #fff;
                    border: none;
                    margin-top: 56px;
                    text-align: center;
                    max-width: 333px;
                    line-height: 48px;
                    display: inline-block;
                    position: relative;

                    .arrow {
                        width: 18px;
                        height: 16px;
                        background: url(https://www.simplilearn.com/ice9/assets/arrow-right-wh.svgz) no-repeat;
                        background-position: 0 0;
                        display: inline-block;
                        margin-left: 14px;
                        position: relative;
                        top: 3px;
                    }
                }
            }
        }
    }
}