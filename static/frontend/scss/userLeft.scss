.nav_outeDiv {
    background: #f3f6f8;
    height: 100vh;
    padding: 0;
    margin-top: 20px;
    position: relative;
    z-index: 11;

    .nav-tabs { 
        padding: 0;
        min-height: 180px;
        padding-top: 10px;
        border-bottom: 0;
        position: relative;

        li {
            float: none;
            position: relative;
            margin-bottom: 15px;
            display: inline-block;
            width: 100%;
            height: 10px;

            a {
                display: block;
                line-height: 50px;
                padding: 0;
                padding-left: 35px;
                border-left: 0;
                border-right: 0;
                background: transparent;
                border-bottom: 1px solid #e1e6ea;
                text-align: left;
                font-size: 16px;
                color: #484f57;
                font-weight: 300;
                text-transform: capitalize;
                margin: 0;
                border-radius: 0;

                &:hover {
                    color: #00a7dd;
                    border-left: 0;
                    border-color: #ddd;
                    background: none;
                    border-right: 0;
                }

                &.active {
                    background: #fff;

                    &:hover {
                        color: #484f57;
                    }

                    &:before {
                        content: "";
                        display: inline-block;
                        width: 4px;
                        height: 50px;
                        background: #00a7dd;
                        position: absolute;
                        left: 0;
                        top: 0;
                    }
                }
            }
        }
    }
}