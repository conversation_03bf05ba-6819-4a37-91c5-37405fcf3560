footer {
  margin-top: 20px;
  background: #1A1A1A;
  width: 100%;
  list-style-type: none;

  a {
    font-weight: 400;
    cursor: pointer;
    background-color: transparent;
    color: #b3b3b3;
  }

  // FOOTER CONTAINER
  .footer-container {
    padding: 60px 0 14px;

    .footer-row{
      width: 1200px;
    }

    ul {
      list-style-type: none;
      padding: 0;
    }

    li {
      color: #969696;
      font-size: 12px;
      line-height: 15.6px;
      letter-spacing: 0;
    }
    
    li a {
      color: #ccc !important;
      padding: 0;
      font-size: 12px;
      line-height: 20px;

      &:hover {
        background: #000;
        text-decoration: none !important; 
        color: #f3f3f3 !important;
      }

      &.active {
        color: #fff !important;
      }
    }
  }

  // FOOTER TOP SECTION
  .foot-top {
    background: #333;
    min-height: 50px;
    color: #fff;
    padding: 20px 0;
    position: relative;
    z-index: 1;

    p {
      padding-top: 5px;
      letter-spacing: 0.25px;
      display: inline-block;
    }
  }

  // SUCCESS SUBSCRIBE SECTION
  .success_subscribe {
    text-align: center;
    display: none;
  }

  // FOOTER HEADINGS
  .company, .follow {
    color: gray;
    font-weight: 700;
    text-transform: capitalize;
    margin-top: 0;
    margin-bottom: 11px;
    padding-bottom: 0;
  }

  .company {
    font-size: 13px;
    line-height: 14px;
  }

  .follow {
    font-size: 18px;
    line-height: 14px;
  }

  // FOOTER SOCIAL MEDIA
  .foot-socials {
    .rae-link a {
      color: #ccc;
      font-size: 14px;
      font-weight: 500;
    }
    
    a.refer-earn {
      width: auto;
      text-indent: 0;
      display: inline-block;
      vertical-align: middle;
      padding: 9px 10px 8px 15px;
      margin: 0 0 20px;
      height: auto;
      border: 1px solid #777;
      border-radius: 5px;
      text-decoration: none;

      &:hover {
          background: #000;
          text-decoration: none !important; 
          color: #f3f3f3 !important;
        }
    }
    
    ul {
      margin: 0;
      list-style: none;
      overflow: hidden;
    }

    li {
      display: inline-block;
      background: #353535;
      vertical-align: middle;
      margin: 0 6px 4px 0;
      border-radius: 100%;
      float: left;
    }

    a, .review_sidebar .popular_tags .foot-socials ul li a {
      display: inline-block;
      vertical-align: middle;
      text-indent: -9999px;
      width: 33px;
      height: 33px;
      border-radius: 100%;
      padding: 0;
    }

    a span {
      background: url(https://cfs22.simplilearn.com/paperclip/dashboard.svgz) no-repeat;
      display: block;
      margin: 7px auto;
    }

    .facebook span {
      background-position: -10px -613px;
      width: 9px;
      height: 21px;
    }

    a.facebook:hover {
      background-color: #3b5998;
      text-decoration: none;
    }
    
    a.facebook:hover span {
        color: #ffffff;
    }

    .twitter span {
      background-position: -67px -615px;
      width: 18px;
      height: 18px;
      margin-top: 9px;
    }
    
    a.twitter:hover {
      background: #55ACEE;
    }

    .youtube span {
      background-position: -100px -614px;
      width: 20px;
      height: 16px;
    }
    
    a.youtube:hover {
      background: #DE2925;
    }

    .linkedin span {
      background-position: -35px -615px;
      width: 15px;
      height: 16px;
    }
    
    a.linkedin:hover {
      background: #1B86BD;
    }

    .google span {
      background-position: -134px -614px;
      width: 17px;
      height: 19px;
    }
    
    a.google:hover {
      background: #DD4B39;
    }
  }

  // FOOTER DISCOVER SECTION
  .footer-discover {
    li {
      a {
        color: #ccc;
        font-size: 12px;
        line-height: 20px;
      }
    }
  }

  // FOOTER BUSINESS SECTION
  .footer-business {
    ul {
      list-style-type: none;

      h5 {
        font-size: 18px;
      }

      li {
        a {
          color: #ccc !important;
        }
      }
    }
  }
  
  // Company section
  .foot_company {
    li a {
      color: #ccc !important;
      
      &:hover {
        color: #f3f3f3 !important;
      }
    }
  }

  // FOOTER REFER & EARN
  .footer-disc {
    text-align: center;

    a.refer-earn {
      width: 185px;
      display: block;
      color: #ccc !important;
      margin: 0 auto;
      font-size: 12px;
      font-weight: 300;
      text-indent: 0;
      padding: 9px 10px 8px 15px;
      height: auto;
      border: 1px solid #777;
      border-radius: 5px;
      position: relative;
      text-decoration: none;

      &::before {
        content: "";
        width: 24px;
        height: 24px;
        display: inline-block;
        background: url(https://cfs22.simplilearn.com/paperclip/dashboard.svgz);
        background-position: -5px -488px;
        float: left;
        position: relative;
        left: -5px;

      }

        &:hover {
          background: #000;
          text-decoration: none;
        }
      
    }

    a.ios-app {
      position: relative;
      top: 10px;
      text-decoration: none;

      &::before {
        content: "";
        width: 24px;
        height: 24px;
        display: inline-block;
        background: url(https://cfs22.simplilearn.com/paperclip/dashboard.svgz);
        background-position: -26px -488px;
        float: left;
        position: relative;
        left: -6px;

      }

        &:hover {
          background: #000;
          text-decoration: none !important; 
          color: #f3f3f3 !important;
        }
      
    }
  }

  // FOOTER END SECTION
  .footer-end {
    .simpli_certification {
      text-align: center;
      padding: 20px 0;
      font-size: 11px;
      color: #969696;

      ul {
        margin: 0;
        padding: 0;
        list-style: none;

        li {
          display: inline-block;
          line-height: 24px;
          font-size: 12px !important;
          text-align: center;
          position: relative;

          ul {
            width: 100%;
            text-align: center;

            li {
              width: auto;
              padding: 0 5px;
              font-size: 11px !important;
              line-height: 7px;

              a {
                margin: 0 10px;
                color: #b3b3b3;
              }
            }
          }
        }

        a:hover {
          text-decoration: none !important;
          color: #f9f0f1 !important;
        }

        li:last-child:after {
          content: none;
        }
      }
    }
  }


}

footer {
  .footer-end {
    .simpli_certification {
      .simpli_terms {
        li:after {
          content: "";
          width: 5px;
          height: 5px;
          background: #b3b3b3;
          border-radius: 100%;
          display: inline-block;
          margin-right: 10px;
          margin-left: 15px;
        }

        li:last-child:after {
          content: none;
        }
      }
    }
  }
}
.footer-profile{
  .sticky {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1;
    cursor: pointer;
    border-color: transparent;
    background-color: transparent;
    box-shadow: none;
    color: #337ab7;
    font-weight: 400;
    border-radius: 0;

    img {
      border: 0;
      vertical-align: middle;
    }
}
}