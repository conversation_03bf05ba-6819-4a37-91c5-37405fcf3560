$baseUrl: 'https://cfaccounts.simplicdn.net/frontend/images/';

// URLS ...
$right-panel-image: url("#{$baseUrl}right-panel-image_v1.jpg") no-repeat;
$on-boarding-icons: url("#{$baseUrl}on-boarding-icons.svg") no-repeat;
$sprite-community-image: url("https://cfls5.simplicdn.net/frontend/js/learning2/assets/images/community-sprite-image.png") no-repeat;
$sprite-image-v1: url("https://cfs22.simplicdn.net/paperclip/dashboard_v1.svgz");
$social-login-icons: url("../images/social-login-icons.svg") no-repeat center;
$social-icon: url("https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz");
$profile-icon: url("https://www.simplilearn.com/ice9/assets/lms_user_profile_v_1.svgz");
$social-icon-v2: url("https://cfaccounts.simplilearn.com/frontend/images/icons-for-social-login.svg");

// COLORS ...
$background-color: #FFFFFF;
$right-background-webkit: -webkit-linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
$right-background-moz: -moz-linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
$right-background-o: -o-linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
$right-background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);

// Primary Colors
$primary-color: #1179EF;
$secondary-color: #025CC3;
$accent-color: #FFC107;

// Neutral Colors
$white: #ffffff;
$black: #000000;
$gray-dark: #333333;
$gray-medium: #757575;
$gray-light: #E0E0E0;
$gray-extra-light: #F5F5F5;

// Alert Colors
$success-color: #28a745;
$warning-color: #ff9800;
$error-color: #dc3545;
$info-color: #17a2b8;

// Text Colors
$text-primary: $gray-dark;
$text-secondary: $gray-medium;
$text-light: $gray-light;

// Background Colors
$bg-primary: $white;
$bg-secondary: $gray-extra-light;
$bg-dark: $gray-dark;

// Border Colors
$border-light: $gray-light;
$border-dark: $gray-medium;

// Social Media Button Colors
$google-btn: #DD4B39;
$facebook-btn: #3B5999;
$linkedin-btn: #0077B5;
$apple-btn: #000000;

$text-color: #4F4F4F;
$mandatory: #DD3636;
$heading-color: #424242;

// FONTS ...
$default-font-family: 'Roboto', 'Gotham Rounded SSm A', 'Gotham Rounded SSm B', Helvetica, Arial, sans-serif;
$roboto-sans-font-family: "Roboto", sans-serif;

// SIZES ...
$social-login-btn-height: 40px;
