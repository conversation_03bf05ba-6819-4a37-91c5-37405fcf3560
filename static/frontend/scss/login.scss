@use "variables" as vars;

.logo-image {
  padding-bottom: 32px;
  text-align: center;


  a {
    color: #777;
    font-size: 12px;
    cursor: pointer;
  }

  img {
    max-height: 36px;
  }

  .logo-text {
    display: inline-block;
    width: 70px;
    line-height: 24.5px;
    font-size: 17px;
    color: #757575;
    font-weight: 400;
    padding-left: 10px;
    margin-left: 8px;
    vertical-align: middle;
    position: relative;
    font-family: "Roboto", sans-serif;
  }

  .logo-text::before {
    width: 1.2px;
    height: 34px;
    content: "";
    display: inline-block;
    background: #E0E0E0;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    margin: auto;
  }

}

.content-heading {
  line-height: 20px;
  color: #424242;
  font-size: 20px !important;
  font-weight: 500;
  text-align: center;
  padding-bottom: 8px;
}

.content-sub-heading {
  font-size: 16px;
  line-height: 20px;
  color: #646464;
  padding-bottom: 20px;
  text-align: center;
}

.form-container {
  .login-form {
    position: relative;

    // .error_box{
    //   display: block;
    //   width: 100%;
    //   padding-top: 15px;
    //   color: #DD3636;
    //   font-size: 16px;
    //   font-weight: 400;
    //   line-height: 20px;
    //   text-align: center;
    //   padding-left: 30px;
    //   padding-right: 30px;
    //   padding-bottom: 16px;
    //   width: 100%;
    // }

    input[type="text"]:focus,
    input[type="password"]:focus,
    input[type="email"]:focus{
      border-radius: 3px;
      outline-offset: 0px;
      outline: 1px solid #118aef;
      border: 1px solid #007bff !important;
      background: rgb(232, 240, 254);
    }
    .form-input {
      margin-bottom: 20px !important;

      input:required:invalid {
        background-position: right top;
        background-repeat: no-repeat;
        box-shadow: none;
      }

      input[type="text"],
      input[type="password"],
      input[type="email"],
      textarea {
        height: 48px;
        border: 1px solid #B8D6FF;
        font-size: 16px;
        padding-left: 24px;
        font-weight: 400;
        color: #4F4F4F;
        width: 100%;
        border-radius: 3px;
      }
      input[type="text"]::placeholder,
      input[type="password"]::placeholder,
      input[type="email"]::placeholder,
      textarea::placeholder {
        color: #999;
      }
      input[type="checkbox"].invalid {
        outline: 2px solid #ff0000;
    }
    }
    .form-input:hover{
      input[type=checkbox]:hover, input[type=checkbox]:focus {
          border-color: #1179EF !important;
      }
  }
    .password-field {
      margin-top: 0;
      display: inline-block;
      width: 100%;
      position: relative;

      input:focus:not(.password-mismatch):not(.password-same):not(.phone-no) {
        border: 1px solid #007bff !important;
      }

      #password-icon {
        top: 12px;
        right: 24px;
      }

      .password-icon {
        background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
        display: inline-block;
        position: absolute;
        content: '';
      }

      .password-icon.password-eye-slash {
        background-position: -20px -138px;
        width: 20px;
        height: 20px;
      }
      .info-invalid{
        background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
        content: '';
        position: absolute;
        display: inline-block;
        background-position: -101px -81px;
        width: 22px;
        height: 22px;
        right: 22px;
        top: 25px;
      }

      .password-icon.password-eye-unslash {
        background-position: -60px -138px;
        width: 20px;
        height: 20px;
      }
    }

    .remember {
      margin-bottom: 20px;

      input[type=checkbox]:checked {
        background: url(https://www.simplilearn.com/ice9/assets/lms-checkbox.svgz) no-repeat;
        background-size: contain;
        border: 0 !important;
        cursor: pointer;
      }

      input[type=checkbox] {
        border: 1px solid #E5E6E7;
        cursor: pointer;
      }
      input[type="checkbox"].invalid {
        outline: 2px solid #ff0000;
    }
      #remember-me {
        display: inline-block;
        width: 18px;
        height: 18px;
        margin-right: 6px;
        vertical-align: middle;
        margin-top: 0;
        appearance: none;
        border-radius: 3px;
      }

      .remember input,
      #agree_email_comm {
        width: 18px;
        height: 18px;
        border: 1px solid #BFCACE;
        margin-right: 6px;
        vertical-align: middle;
        margin-top: 0;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        border-radius: 3px;
        display: inline-block;
      }

      .rememberMe {
        font-size: 14px;
        font-weight: 400;
        line-height: 17px;
        color: #828282;
        display: inline-block;
        vertical-align: bottom;
        margin-bottom: 0;
        cursor: pointer;
      }

      .forgot-link {
        text-transform: none;
        line-height: 14px;
        font-weight: 400;
        font-size: 11px;
        margin-bottom: 5px;

        a {
          font-weight: 500;
          font-size: 14px;
          color: #3B83F0;
          line-height: 16px;
          display: inline-block;
          position: relative;
          top: 2px;
        }
      }
    }

    .submit-container {

      input[type=submit][disabled],
      input[type=submit][disabled]:hover {
        background: #88BCF7;
        border-color: #88BCF7;
        opacity: 1;
        color: #fff;
        cursor: not-allowed !important;
        box-shadow: none;
      }

      .btn-signup {
        line-height: 46px;
        background: #1179EF;
        font-size: 16px;
        font-weight: 500;
        letter-spacing: normal;
        box-shadow: none;
        border-radius: 6px;
        color: #fff;
      }
    }
  }
}

.or-div {
  padding: 0;
  margin: 26px 0;
  height: 1px;
  width: 100%;
  position: relative;
  background: #DCDCDC;
  text-align: center;
  font-size: 14px;
  color: #2a2f32;
  line-height: 14px;
}

.or-div::before {
  content: "OR";
  display: inline-block;
  width: 44px;
  height: 14px;
  line-height: 20px;
  color: #808890;
  font-size: 12px;
  font-weight: 400;
  background: #fff;
  position: absolute;
  top: -9px;
  left: 0;
  right: 0;
  margin: auto;
}

.social-login {
  text-align: center;
  max-width: 400px;
  margin: 0 auto;
  font-family: "Roboto";

  .login-button {
    font-size: 14px;
    line-height: 38px;
    height: 40px;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-bottom: 12px;
    border: 1px solid var(--color-border);
    border-radius: 25px;
    color: var(--color-text-primary);
    background-color: var(--color-background);
    text-decoration: none;
    cursor: pointer;
    transition: border-color 0.2s ease, background-color 0.2s ease;
    padding: 0;

    div {
      margin-right: 15px;
    }

    &:hover {
      outline: 1px solid #1179EF;
      outline-offset: 0px;
      border-color: transparent;
    }


    span {
      position: relative;
      top: 0px;
      bottom: 0px;
      right: -2px;
    }
  }
  .more-options {
    display: inline-block;
    width: 100%;
    text-align: center;
    cursor: pointer;
    font-size: 14px;
    color: #1179EF;
    font-weight: 500;
    line-height: normal;
    margin-top: 4px;
    margin-bottom: 12px;

    &:hover {
      text-decoration: none;
      color: #025CC3;
    }
  }

  .signup_link {
    font-size: 14px;
    margin-top: 12px !important;
    font-weight: 520;
    color: var(--color-text-primary);
    margin: 0;

    span {
      font-size: 14px;
      font-weight: 400;
      color: #828282;
    }
  }

  .signup-text {
    display: inline-block;
    padding-right: 5px;
    padding-top: 0;
    font-size: 14px;
    font-weight: 400;
    color: #4f4f4f !important;
  }

  .hidden {
    display: none;
  }

  .social-icon {
    background: vars.$social-icon no-repeat;
    width: 20px;
    height: 20px;
    top: 6px;
    display: inline-block;
    margin-left: -7px !important;
    width: 20px;
    position: relative;
    height: 20px;
    top: 6px;
    right: -1px !important;

    &.google-icon {
      background-position: -20px 0px !important;
    }

    &.linkedin-icon {
      background-position: -152px 0px !important;
    }

    &.apple-icon {
      background-position: -64px 0px !important;
    }

    &.facebook-icon {
      background-position: -108px 0px !important;
    }

    &.email-icon {
      background-position: -197px 0px !important;
    }

  }

  .social-icon:before {
    background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
    width: 20px;
    height: 20px;
    top: 6px;
  }
}

@media (min-width: 992px) {
  .col-md-6 {
    width: 50%;
  }
  .col-md-6:hover{
    input[type=checkbox]:hover, input[type=checkbox]:focus {
      border-color: #1179EF !important;
  }
  }
}
/*Mobile S size min-width: 320px*/
@media(min-width: 320px) and (max-width: 991px) {
  .hidden-sm {
    display: none !important;
  }

  .col-md-5 {
    max-width: 100% !important;
  }
}
/*Mobile M size min-width: 375px*/
@media(min-width: 375px) and (max-width: 991px) {
  .hidden-sm {
    display: none !important;
  }

  .col-md-5 {
    max-width: 100% !important;
  }
} 
/*Mobile L size min-width: 425px*/
@media(min-width: 425px) and (max-width: 991px) {
  .hidden-sm {
    display: none !important;
  }

  .col-md-5 {
    max-width: 100% !important;
  }
}

@media(min-width: 768px) and (max-width: 991px) {
  .hidden-sm {
    display: none !important;
  }

  .col-md-5 {
    max-width: 100% !important;
  }
}
@media screen and (min-width: 768px) and (max-width:1024px) and (orientation: landscape) {
  .right-panel {
    .background-wrapper {
      .image-section {
        background: linear-gradient(to top, black 70%, transparent 100%) !important;

        .right-heading {
          font-size: 32px;
          line-height: normal;
          font-weight: 700;
          color: #F8FBFF;
          width: 100%;
          font-family: "Roboto", sans-serif;
        }

        .content {
          line-height: normal;
          margin-top: 6px;
          color: #F8FBFF;
          padding-bottom: 30px;
          font-weight: 400;
          font-size: 22px;
        }

        .additional-content {
          font-size: 16px;

          li {
            line-height: normal;
            font-size: 22px;
            font-weight: 300;
            margin-bottom: 12px;
            color: #F8FBFF;
          }
        }
      }
    }
  }
}
@media screen and (min-width: 820px) and (max-width:1180px) and (orientation: landscape) {
  .right-panel {
    .background-wrapper {
      .image-section {
        background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 1) 50%, rgba(0, 0, 0, 0) 100%) !important;
        padding-top: 100px;

        .right-heading {
          font-size: 32px;
          line-height: normal;
          font-weight: 700;
          color: #F8FBFF;
          width: 100%;
          font-family: "Roboto", sans-serif;
        }

        .content {
          line-height: normal;
          margin-top: 6px;
          color: #F8FBFF;
          padding-bottom: 30px;
          font-weight: 400;
          font-size: 22px;
        }

        .additional-content {
          font-size: 16px;

          li {
            line-height: normal;
            font-size: 22px;
            font-weight: 300;
            margin-bottom: 12px;
            color: #F8FBFF;
          }
        }
      }
    }
  }
}
@media screen and (min-width: 1024px) and (max-width:1366px) and (orientation: landscape) {
  .right-panel {
    .background-wrapper {
      .image-section {
        background: linear-gradient(to top, black 70%, transparent 100%) !important;
        padding-right: 0 !important;
      }
    }
  }
}
@media screen and (min-width: 600px) and (max-width:2560px) and (orientation: landscape) {
  .right-panel {
    .background-wrapper {
      .image-section {
        background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%);
        bottom: 0px;
        position: absolute;
        padding: 70px 40px 20px;
        width: 100%;
      }
    }
  }
}
@media only screen and (max-height: 770px) and (orientation: landscape) {
  .right-panel {
    .background-wrapper {
      .image-section {
        background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%) !important;
        bottom: 0px;
        position: absolute;
        padding: 70px 40px 20px;
        width: 100%;
      }
    }
  }
}
@media (max-width: 1280px) {
  .right-panel {
    .background-wrapper {
      .image-section {
        .right-heading {
          font-size: 32px;
          line-height: normal;
          font-weight: 700;
          color: #F8FBFF;
          width: 100%;
          font-family: "Roboto", sans-serif;
        }

        .content {
          line-height: normal;
          margin-top: 6px;
          color: #F8FBFF;
          padding-bottom: 30px;
          font-weight: 400;
          font-size: 22px;
        }

        .additional-content {
          font-size: 16px;

          li {
            line-height: normal;
            font-size: 22px;
            font-weight: 300;
            margin-bottom: 12px;
            color: #F8FBFF;
          }
        }
      }
    }
  }
}