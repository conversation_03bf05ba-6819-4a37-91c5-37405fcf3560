#termsModal {
  
  font-family: Roboto, sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  z-index: 1050;
  overflow-y: hidden;
  text-align: start;
  box-sizing: border-box;
  padding: 0 !important;
  bottom: 0;
  right: 0;
  outline: 0;

  a:focus, button:focus {
    outline: 2px solid #1179EF;
    outline-offset: 2px;
    border-radius: 4px;
  }

  button.tac-btn-close:focus {
    border-radius: 50%;
  }

  .modal-dialog {
    position: relative;
    margin: 10px;

    &.custom-dialog-box {
      display: flex;
      align-items: center;
      min-height: 93vh;
      justify-content: center;
    }
  }

  .modal-content {
    position: relative;
    background-color: #fff;
    background-clip: padding-box;
    outline: 0;
    border-radius: 10px;
    border: 1px solid #999 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
    width: 100%;
  }

  .modal-body {
    position: relative;
    padding: unset;
    width: 100%;
  }

  .heading {
    padding: 30px 30px 20px 30px;
    box-shadow: 0px 1px 0px 0px rgba(0, 0, 0, 0.10);
  }

  .top-heading {
    text-align: left;
    padding: unset;
    margin: unset;
  }

  .info {
    position: relative;
    padding: 15px 20px 18px 30px;
    height: 400px;

    .content {
      height: 100%;
      overflow-y: auto;
      padding: 0 25px 4px 4px;

      &::-webkit-scrollbar {
        width: 10px;
        height: 90%;
      }

      &::-webkit-scrollbar-track {
        background: #F0F0F0;
        border-radius: 8px;
      }

      &::-webkit-scrollbar-thumb {
        background: #757575;
        border-radius: 8px;
      }

      &:focus {
        outline: none;
        outline-offset: none;
        border-radius: unset;
      }

      h4 {
        padding-bottom: unset;
        margin: 20px 0 20px 0;
        text-align: left;
        border-bottom: none;
        padding-top: 0px;
        &:first-child {
          margin-top: unset;
        }
      }

      p {
        color: #757575;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        margin: 0px 0 10px;
      }

      ul {
        padding-left: 25px;

        &:last-of-type {
          margin-top: 20px;
          margin-bottom: unset;

          li {
            margin-bottom: 20px;

            &:last-of-type {
              margin-bottom: unset;
            }
          }
        }
      }

      a {
        font-size: 16px;
        text-decoration: underline;
        color: #1179EF;
      }
    }
  }

  #bodyContentTC .info .content:focus {
    outline: none;
    outline-offset: none;
    border-radius: unset;
  }

  .scroll-info-new {
    color: #313131;
    text-align: left;
    font-family: Roboto;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
    margin: 2px 0 0 0;
  }

  .bottom-action {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 0 0;
    margin-top: 16px;
    border-top: 1px solid #ccc;

    .bottom-text {
      color: #000 !important;
      font-family: Roboto;
      font-size: 14px !important;
      font-style: normal;
      font-weight: 400;
      line-height: 24px !important;
      margin-bottom: unset !important;
    }

    .accept-btn {
      display: flex;
      width: 240px;
      padding: 12px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      background: #1179EF;
      color: white;
      border: none;
      cursor: pointer;
      
      &:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
    }
  }

  .tac-btn-close {
    position: absolute;
    right: 20px;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    padding: unset;
    cursor: pointer;
    z-index: 20;
    top: 7%;
    appearance: none;
    background: none;

    svg {
      border-radius: 50%;
    }
  }


  /* Add these rules at the bottom of the file to handle the nested flexbox structure */

  /* Handle nested flex containers */
  .modal-dialog.custom-dialog-box.modal-dialog-centered {
    display: flex !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  .modal-content.custom-modal-content {
    width: 100% !important;
  }

  .modal-dialog.custom-dialog-box .modal-content {
    width: 100% !important;
  }

  /* Class shown in Chrome dev tools */
  .modal-dialog.custom-dialog-box.modal-dialog-centered[role="document"] {
    width: 800px !important;
    max-width: 800px !important;
  }
}

/* Cloud6 sets body overflow hidden when modal is open */
#termsModal:not([style*="display: none"]) ~ body,
#termsModal[style*="display: block"] ~ body {
  overflow: hidden;
}

@media (max-width: 430px) {
  #termsModal .tac-btn-close {
    top: 6% !important;
  }
}

@media (max-width: 768px) {
  #termsModal .bottom-action {
    flex-wrap: wrap;
  }

  #termsModal .bottom-action .accept-btn {
    width: 100%;
  }
}

@media (min-width: 768px) {
  #termsModal .modal-dialog {
    margin: 30px auto !important;
  }
  
  #termsModal .modal-content {
    box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  #termsModal .modal-dialog.custom-dialog-box.modal-dialog-centered {
    width: 640px !important;
    min-height: 95vh !important;
  }
  
  #termsModal .info {
    height: calc(80vh - 100px) !important;
    overflow-y: auto;
  }

  #termsModal .bottom-action {
    flex-wrap: wrap;
  }

  #termsModal .bottom-action .accept-btn {
    width: 100%;
  }
}

@media (min-width: 1280px) {
  #termsModal .modal-dialog.custom-dialog-box.modal-dialog-centered {
    width: 800px !important;
    height: 90vh !important;

  }
  
  #termsModal .modal-content.custom-modal-content {
    width: 100% !important;
  }
  
  #termsModal .info {
    height: calc(80vh - 100px) !important;
  }
  
  /* Override bootstrap defaults if present */
  #termsModal .modal-lg,
  #termsModal .modal-xl {
    max-width: 800px !important;
  }
}

@media (min-width: 1366px) {
  #termsModal .modal-dialog.custom-dialog-box.modal-dialog-centered {
    width: 800px !important;
    height: 610px !important;
    min-width: 800px !important;
    max-width: 800px !important;
  }
  
  #termsModal .info {
    height: 510px !important;
  }
}

@media (min-width: 1530px) {
  #termsModal .modal-dialog.custom-dialog-box.modal-dialog-centered {
    width: 960px !important;
    height: 692px !important;
    min-width: 960px !important;
    max-width: 960px !important;
  }
  
  #termsModal .info {
    height: 592px !important;
  }
}

@media (min-width: 1920px) {
  #termsModal .modal-dialog.custom-dialog-box.modal-dialog-centered {
    width: 1200px !important;
    height: 692px !important;
    min-width: 1200px !important;
    max-width: 1200px !important;
  }
  
  #termsModal .info {
    height: 592px !important;
  }
}

/* Cloud6 iOS Safari specific enhancements */
@supports (-webkit-touch-callout: none) {
  @media screen and (max-device-width: 1024px) {
    #termsModal .info {
      position: relative;
      padding: 15px 20px 18px 30px;
      height: 400px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    #termsModal .info .content {
      flex: 1;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      padding: 0 25px 4px 4px;
      overflow-y: auto !important;
      -webkit-overflow-scrolling: touch !important;
    }
  }
}

