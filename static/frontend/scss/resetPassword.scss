.reset-password-container {

  input[type="password"]::-ms-reveal {
    display: none; /* For the Eye icon in edge browser */
  }


  .logo-image {
    padding-bottom: 32px;
    text-align: center;


    a {
      color: #777;
      font-size: 12px;
      cursor: pointer;
    }

    img {
      max-height: 36px;
    }

    .logo-text {
      display: inline-block;
      width: 70px;
      line-height: 24.5px;
      font-size: 17px;
      color: #757575;
      font-weight: 400;
      padding-left: 10px;
      margin-left: 8px;
      vertical-align: middle;
      position: relative;
      font-family: "Roboto", sans-serif;
    }

    .logo-text::before {
      width: 1.2px;
      height: 34px;
      content: "";
      display: inline-block;
      background: #E0E0E0;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      margin: auto;
    }

  }

  .content-heading {
    line-height: 20px;
    color: #424242;
    font-size: 20px !important;
    font-weight: 500;
    text-align: center;
    padding-bottom: 8px;
  }

  .additional-wrapper {
    text-align: center;
    padding: 20px 0;
  }

  .success-icon {
    display: inline-block;
    width: 41px;
    height: 41px;
    position: relative;
    content: "";
    background: url('https://cfaccounts.simplicdn.net/frontend/images/on-boarding-icons.svg') no-repeat;
    background-position: -72px -36px;
    margin-bottom: 20px;
  }

  .content-heading.green {
    color: #5fcf80;
    font-size: 20px !important;
    font-weight: 500;
    margin-bottom: 8px;
    padding-bottom: 0;
  }

  .btn-signup {
    line-height: 46px;
    background: #1179EF;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: normal;
    box-shadow: none;
    border-radius: 6px;
    color: #fff;
    width: 100%;
    text-transform: none;
    position: relative;
    top: 0;
    padding: 0 18px;
    margin-right: 12px;
    transition: none;
    border-color: #118aef;
    color: #fff;
  }

  .content-sub-heading {
    font-size: 16px;
    line-height: 20px;
    color: #646464;
    padding-bottom: 20px;
    text-align: center;
  }

  .reset-form {

    .form-input {
      margin-bottom: 20px;

      input[type="text"],
      input[type="password"],
      input[type="email"],
      textarea {
        height: 48px;
        border: 1px solid #b8d6ff;
        font-size: 16px;
        padding-left: 24px;
        font-weight: 400;
        color: #4f4f4f;
        margin-bottom: 0px;
        border-radius: 3px;
        background: #fff;
        width: 100%;
      }
      input[type="text"]::placeholder,
      input[type="password"]::placeholder,
      input[type="email"]::placeholder,
      textarea::placeholder {
        color: #999;
      }
      input[type="checkbox"].invalid {
        outline: 2px solid #ff0000;
    }
    }

    .password-field {
      display: inline-block;
      width: 100%;
      position: relative;
      overflow: visible;

      .confirmpassword {
        background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
        background-position: -60px -138px;
        width: 20px;
        height: 20px;
        top: 14px;
        right: 24px;
        position: absolute;
        content: '';
      }

      .passowrd-wrap {
        position: relative;
        overflow: visible;

        .password-eye-slash {
          background: url("https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz") no-repeat;
          position: absolute;
          right: 56px;
          top: 25px;
          width: 22px;
          height: 22px;
          transform: translateY(-50%);
          cursor: pointer;
          background-position: -21px -140px;
        }
        .info-invalid{
          background: url(https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz) no-repeat;
          content: '';
          position: absolute;
          display: inline-block;
          background-position: -101px -81px;
          width: 22px;
          height: 22px;
          right: 22px;
          top: 25px;
        }
        .hide {
          display: none !important;
        }

        .password-info {
          background: url("https://www.simplilearn.com/ice9/assets/lms_sprite_v_1.svgz") no-repeat;
          position: absolute;
          transform: translateY(-50%);
          cursor: pointer;
          background-position: -21px -82px;
          width: 22px;
          height: 22px;
          right: 24px;
          top: 24px;
          z-index: 100;
          
          &:hover + .password-tooltip,
          &:hover ~ .password-tooltip {
            visibility: visible;
            opacity: 1;
          }
        }
      }

      .password-tooltip {
        min-width: 417px;
        position: absolute;
        margin-left: 228px;
        padding: 8px;
        color: #EEE;
        background-color: #313131;
        font-weight: normal;
        font-size: 13px;
        border-radius: 6px;
        z-index: 99999999;
        box-sizing: border-box;
        border: 1px solid #313131;
        box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
        visibility: hidden;
        opacity: 0;
        transition: opacity 0.8s;
        
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        
        &.visible {
          visibility: visible;
          opacity: 1;
          top: 45%;
        }
        
        .triangle::after {
          background: #313131;
        }
      }

      .password-info:hover + .password-tooltip,
      .password-info:hover ~ .password-tooltip,
      .info-icon:hover .password-tooltip,
      .info-icon .password-tooltip.visible {
        visibility: visible;
        opacity: 1;
      }

      .password-heading {
        font-size: 16px;
        font-weight: 700;
        padding-bottom: 8px;
        line-height: normal;
      }

      .password-list {

        ul,
        li {
          list-style: disc;
          font-size: 14px;
          text-align: left;
        }

        li {
          padding-left: 10px;
          margin-bottom: 8px;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          padding-left: 20px;
          position: relative;
          padding-bottom: 0;
        }

      }
    }

    .submit-container {
      .disabled .disabled:hover {
        background: #88BCF7;
        border-color: #88BCF7;
        opacity: 1;
        color: #fff;
        cursor: not-allowed !important;
        box-shadow: none;
      }

      .btn-signup {
        line-height: 46px;
        background: #1179EF;
        font-size: 16px;
        font-weight: 500;
        letter-spacing: normal;
        box-shadow: none;
        border-radius: 6px;
        color: #fff;
        width: 100%;
        text-transform: none;
        position: relative;
        top: 0;
        padding: 0 18px;
        margin-right: 12px;
        transition: none;
        border-color: #118aef;
        color: #fff;
      }
    }
  }
}