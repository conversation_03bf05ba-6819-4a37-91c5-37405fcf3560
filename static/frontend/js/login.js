document.addEventListener('DOMContentLoaded', () => {
    const directEvents = ["View Login Page", "Start Login", "View Signup Page", "Start Signup", "Link Social Account", "Complete Signup", "Complete Login", "Learner multiaccount continue clicked", "Account Setup"];
    let obj = {};
    const selectedGroupId = 2;
    
    const emailField = document.getElementById("mail");
    const passwordField = document.getElementById("password");
    const loginButton = document.getElementById("btn_login");

    
    function toggleButtonState() {
        if (emailField.value.trim() !== "" && passwordField.value.trim() !== "") {
            loginButton.disabled = false;
        } else {
            loginButton.disabled = true;
        }
    }

    // Add event listeners to both fields
    emailField.addEventListener("input", toggleButtonState);
    passwordField.addEventListener("input", toggleButtonState);


    if (typeof directEvents !== 'undefined' && directEvents.includes("View Login Page")) {
        amplitudeWebengageLoad(selectedGroupId);
        const ga4EventObject = new window.GA4Events();
        ga4EventObject.sendEvent('start_login_click', obj);
        WebengageTracking.eventReq.triggerWebengageEvent('View Login Page', obj);
    } 

    const loginUserForm = document.getElementById('loginUser');
    loginUserForm.addEventListener('submit', function(e) {

        var obj = {
            type: loginButton.getAttribute('data-clickedType')
        };
        //webengage event
        if (typeof directEvents !== 'undefined' && directEvents.includes("Start Login")) {
            amplitudeWebengageLoad(selectedGroupId);
            WebengageTracking.eventReq.triggerWebengageEvent('Start Login', obj); 
        } else {
            amplitudeWebengageLoad(selectedGroupId);
            WebengageTracking.eventReq.triggerWebengageEvent('Start Login', obj);   
        }

        e.preventDefault();
        const errorMessages = document.getElementsByClassName('error-msg');
        while (errorMessages.length > 0) {
            errorMessages[0].parentNode.removeChild(errorMessages[0]);
        }
        const inputs = this.querySelectorAll('input');
        const email = document.getElementById('mail').value;
        const password = document.getElementById('password').value;
        let isValid = true;

        inputs.forEach((input) => {
            input.classList.remove('error');
        });

        inputs.forEach(function(input) {
            console.log(input, input.id); 
            if(!(input.id == 'redirect_url' || input.id == 'calendar_url' || input.id == 'domainGid' || input.id == 'isB2BAndB2C' || input.id == 'domainUrl') ){
            if (input.value.trim() === '') {
                isValid = false;
                input.classList.add('error');
            }
            }
        });

        if (!isValid) {
            const errorMessage = document.createElement('span');
            errorMessage.classList.add('error-msg');
            errorMessage.textContent = 'Please fill in all required fields';
            document.getElementById('error_msg').appendChild(errorMessage);
        }

        if (!validateEmail(email)){
            isValid = false;
            const errorMessage = document.getElementById('error_msg');
            errorMessage.textContent = 'Please enter a valid email address';
        }

        if (isValid) {
            this.submit();
        }
    });

    const socialLoginButtons = document.querySelectorAll('#google-login, #facebook-login');
    socialLoginButtons.forEach((button) => {
        button.addEventListener('click', (e)=>{
            newTabSocialLoginClicked(e, 'login')
        });
    });

    const otherSocialLoginButtons = document.querySelectorAll('#linkedin-login, #SignInWithApple-login');
    otherSocialLoginButtons.forEach((button) => {
        // button.addEventListener('click', sameTabSocialLoginClicked);
        button.addEventListener('click', (e)=>{
            sameTabSocialLoginClicked(e, 'login')
        });
    });
});

document.getElementById("view-more").addEventListener("click", function(event) {
    event.preventDefault();  // Prevent the link from navigating

    // Select the hidden buttons
    var hiddenButtons = document.querySelectorAll(".hidden");
    var viewMoreButton = document.getElementById("view-more")

    // Toggle the visibility of hidden buttons
    hiddenButtons.forEach(function(button) {
        button.classList.toggle("hidden");
    });

    // Hide the view more button
    viewMoreButton.classList.toggle("hidden")
});
