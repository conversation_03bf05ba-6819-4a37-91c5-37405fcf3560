document.addEventListener('DOMContentLoaded', function() {
  // To set the country code value
  const select = document.getElementById('country_code');
  const optionsLength = select.options.length;
  let countryData = '';
  let serverValidationPassed = false;
  
  // Function to reset validation state
  window.resetServerValidation = function() {
    serverValidationPassed = false;
  };

  if (optionsLength === 10) {
    const separatorOption = document.createElement('option');
    separatorOption.disabled = true;
    select.add(separatorOption);
  }
  const emailField = document.getElementById("mail");
  const passwordField = document.getElementById("password");
  const register = document.getElementById('btn_register');
  const firstNameField = document.getElementById("first_name");
  const lastNameField = document.getElementById("last_name")
  const phoneInput = document.getElementById("phone_no");

  function toggleButtonState() {
    let isDisabled;
    if (emailField.value.trim() !== "" && passwordField.value.trim() !== "" && firstNameField.value.trim() !== "" && lastNameField.value.trim() !== "" && phoneInput.value.replace(/\D/g, "").trim() !== "") {
      isDisabled = false;
      register.toggleAttribute("disabled", isDisabled);
      register.classList.toggle("disabled", isDisabled);
    } else {
      isDisabled = true;
      register.toggleAttribute("disabled", isDisabled);
      register.classList.toggle("disabled", isDisabled);
    }
  }

    // Add event listeners to both fields
    emailField.addEventListener("input", toggleButtonState);
    passwordField.addEventListener("input", toggleButtonState);
    firstNameField.addEventListener("input", toggleButtonState);
    lastNameField.addEventListener("input", toggleButtonState);
    phoneInput.addEventListener("input", toggleButtonState);

  for (let index in countryDataIe) {
    const optionText = countryDataIe[index]['code'] + '\u00A0\u00A0 - \u00A0\u00A0' + countryDataIe[index]['name'];
    const optionValue = countryDataIe[index]['phnCode'];
    if (index === '10') {  // Checking if it's the 11th country (index 10)
      const separator = document.createElement('option');
      separator.text = '---------------------------------------------------------';  // Create a separator option
      separator.value = '';  // No value for separator
      separator.disabled = true;  // Disable the separator
      select.add(separator)
    }
    const newOption = document.createElement('option');
    newOption.text = optionText;
    newOption.value = optionValue;
    select.add(newOption);
  }

  countryData = JSON.stringify(countryDataIe[0]);
  const selectedCountryCodeSpan = document.getElementById('country_code_span');
  select.addEventListener('change', function(event) {
    const selectedCountry = event.target.value;
    countryData = countryDataIe.find(function(o) {
      return o.phnCode === selectedCountry;
    });
    countryData = JSON.stringify(countryData);
    selectedCountryCodeSpan.textContent = '+' + selectedCountry;
  });

  select.dispatchEvent(new Event('change'));

  // Register form validation
  document.getElementById('registerUser').addEventListener('submit', function(event) {
    event.preventDefault(); // Prevent the form from submitting

    // Clear previous error messages
    const errorMessages = document.querySelectorAll('.error-msg');
    errorMessages.forEach(function(errorMessage) {
      errorMessage.remove();
    });

    // Get the form and input fields
    const form = document.getElementById('registerUser');
    const inputs = form.querySelectorAll('input');
    const phoneInput = document.getElementById("phone_no");
    const phoneNumber = phoneInput.value.replace(/\D/g, ""); // Remove non-numeric characters
    const phoneError = document.getElementById("phoneError");

    document.querySelectorAll('input').forEach(input => input.classList.remove('error'));

    // Perform validation
    let isValid = true;

    // Remove the "error" class from all input fields
    inputs.forEach(function(input) {
      input.classList.remove('error');
    });
    
    // Check for empty fields and mark them with the "error" class
    inputs.forEach(function(input) {
      if (input.value.trim() === '' && input.type !== 'hidden') {
        isValid = false;
        input.classList.add('error');
      }
    });

    // Validate form using the common validation function
    const termConditionElement = document.getElementById('termCondition');
    isValid = validateSignUpForm({
      password: passwordField.value,
      email: emailField.value,
      phoneNumber: phoneNumber,
      phoneError,
      termCondition: termConditionElement ? termConditionElement.value : undefined
    }) && isValid;

    // Display a generic error message if no specific error is set
    if (!isValid && !document.getElementById('error_msg').textContent) {
      document.getElementById('error_msg').textContent = 'Please fill in all required fields.';
      return; // Stop form submission if not valid
    }

    // Set the country data before form submission
      document.getElementById('country_data').value = countryData;
    
    // If form is valid, validate via AJAX before showing terms modal (only if not already validated)
    if (isValid && !TermsConsent.accepted && !serverValidationPassed) {
      event.preventDefault();

      const mode = window.pageType === 'account-setup' ? 'account-setup' : 'register';
      
      // Prepare validation data - only essential fields needed
      const validationData = {
        client_id: 'sl_looper',
        user_email: emailField.value.trim(),
        user_name: firstNameField.value.trim() + ' ' + lastNameField.value.trim(),
        user_pwd: passwordField.value,
        time_zone: 'UTC',
        redirect_url: '',
        from_mobile: 'N',
        device_type: 'web',
        register_mode: 'email',
        country_id: 1,
        user_roles: [],
        user_type: 'learner',
        mode: mode
      };
      
      // Make AJAX validation request
      fetch('/user-api/v1/check-signup-validation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validationData)
      })
      .then(response => {
        return response.json().then(data => {
          if (!response.ok) {
            // Handle HTTP error responses (400, 500, etc.)
            const errorMsg = data.msg || data.message || 'Error occurred while validating. Please try again.';
            document.getElementById('error_msg').textContent = errorMsg;
            document.getElementById('error_msg').classList.remove('hide');
            return;
          }
          
          if (data.type === 'error' || data.statusCode >= 400) {
            // Display error message
            const errorMsg = data.msg || data.message || 'Error occurred while validating. Please try again.';
            document.getElementById('error_msg').textContent = errorMsg;
            document.getElementById('error_msg').classList.remove('hide');
            return;
          }
          
          // If validation passes, mark as validated and show terms modal
          serverValidationPassed = true;
          TermsConsent.showModalAndWait(form);
        });
      })
      .catch(error => {
        console.error('Validation error:', error);
        document.getElementById('error_msg').textContent = 'Error occurred while validating. Please try again.';
        document.getElementById('error_msg').classList.remove('hide');
      });
      
      return;
    }
    
    // If terms are accepted, allow form submission
    if (isValid && TermsConsent.accepted) {
      return true;
    }
  });
});


