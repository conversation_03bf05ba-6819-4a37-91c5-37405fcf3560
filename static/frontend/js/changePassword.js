document.addEventListener('DOMContentLoaded', function () {
  const currPassword = document.getElementById('cur_passwd');
  const newPassword = document.getElementById('new_passwd');
  const confirmPassword = document.getElementById('confirm_passwd');
  const passwordFields = [currPassword, newPassword, confirmPassword];
  const icons = document.querySelectorAll('.eye-password-icon');
  const submitBtn = document.getElementById('btn_login');
  const infoIconTooltip = document.querySelector('#new_passwd ~ .newpassword-info .password-tooltip');
  const errorBox = document.getElementById('error_msg');
  const changePasswdForm = document.getElementById('fmr_change_passwd');

  // Initially hide the eye icons
  icons.forEach((icon) => (icon.style.display = 'none'));

  // Show the eye icon when the user starts typing
  passwordFields.forEach((field) => {
    field.addEventListener('input', () => {
      const icon = document.querySelector(`.eye-password-icon[data-target="${field.id}"]`);
      if (icon) icon.style.display = field.value.trim() !== '' ? 'inline-block' : 'none';

      if (submitBtn) {
        submitBtn.disabled = !passwordFields.every((input) => input.value.trim() !== '');
      }

      validateField(field);
    });
  });

  icons.forEach((icon) => {
    icon.onclick = function () {
      const inputId = this.getAttribute('data-target');
      const passwordInput = document.getElementById(inputId);

      if (passwordInput) {
        passwordInput.type = passwordInput.type === 'password' ? 'text' : 'password';
        this.classList.toggle('oldpassword-icon', passwordInput.type === 'text');
        this.classList.toggle('oldpassword-see', passwordInput.type === 'password');
      }
    };
  });

  const criteria = {
    lower: '.crit-lower',
    upper: '.crit-upper',
    min: '.crit-min',
    max: '.crit-max',
    num: '.crit-num',
  };

  const ActUtil = {
    validateMinimumChar: (text, min) => text.length >= min,
    validateMaximumChar: (text, max) => text.length <= max,
    validateOneNumber: (text) => /[0-9]/.test(text),
    validateLowerCase: (text) => /[a-z]/.test(text),
    validateUpperCase: (text) => /[A-Z]/.test(text),
  };

  const validatePassword = () => {
    if (!newPassword) return;

    const passwordValue = newPassword.value;
    const validations = {
      lower: ActUtil.validateLowerCase(passwordValue),
      upper: ActUtil.validateUpperCase(passwordValue),
      min: ActUtil.validateMinimumChar(passwordValue, 8),
      max: ActUtil.validateMaximumChar(passwordValue, 128),
      num: ActUtil.validateOneNumber(passwordValue),
    };

    let invalid = false;

    Object.entries(validations).forEach(([key, isValid]) => {
      document.querySelectorAll(criteria[key]).forEach((element) => {
        element.classList.toggle('red', !isValid);
        element.classList.toggle('correct', isValid);
      });

      if (!isValid) invalid = true;
    });

    newPassword.classList.toggle('invalid', invalid);
      infoIconTooltip.style.opacity = invalid ? '1' : '0.5';
    if (infoIconTooltip) {
      infoIconTooltip.style.opacity = invalid ? '1' : '0.5'; // Opacity increased when invalid
      infoIconTooltip.classList.toggle('visible', invalid);
    }
  };

  const validateField = (field) => {
    const value = field.value.trim();
    let errorMsg = '';
    
    if (value === '') {
      field.classList.remove('border-red');
      displayError('');
      return;
    }

    if (field === currPassword && value === '') {
      errorMsg = 'Please enter your old password.';
      currPassword.classList.add('border-red');
    }

    if (field === newPassword) {
      if (value === '') {
        errorMsg = 'Please enter your new password.';
      } else if (value === currPassword.value.trim()) {
          errorMsg = 'New password cannot be the same as the old password';
          newPassword.classList.add('border-red');
      } else if (value.length > 128) {
        errorMsg = 'Password cannot be longer than 128 characters.';
      }
    }

    if (field === confirmPassword) {
      if (value === '') {
        errorMsg = 'Please confirm your password.';
      } else if (value === currPassword.value.trim()) {
          errorMsg = 'New password cannot be the same as the old password';
          confirmPassword.classList.add('border-red');
      } else if (value !== newPassword.value) {
        errorMsg = 'The passwords you entered do not match';
        newPassword.classList.add('border-red');
        confirmPassword.classList.add('border-red');
      } else {
        newPassword.classList.remove('border-red');
        confirmPassword.classList.remove('border-red');
      }
    }

    displayError(errorMsg);

    if (field === newPassword) {
      validatePassword();
    }
  };

  // Display error message
  function displayError(msg) {
    if (errorBox) {
      errorBox.textContent = msg;
      errorBox.classList.toggle('hide', msg === '');
    }
  }

  // Form validation before submission
  function validateForm() {
    let valid = true;

    passwordFields.forEach((field) => {
      validateField(field);
      if (!field.value.trim()) {
          field.classList.add('border-red');  // Add red border to empty fields
          valid = false;
        } else {
          field.classList.remove('border-red');
        }
    });

    return valid;
  }
  
  if (changePasswdForm) {
    changePasswdForm.addEventListener('submit', (event) => {
      if (!validateForm()) {
        event.preventDefault();
        passwordFields.forEach((field) => {
          if (!field.value.trim()) {
            field.classList.add('border-red');
          }
        });
      }
    });
  }
});