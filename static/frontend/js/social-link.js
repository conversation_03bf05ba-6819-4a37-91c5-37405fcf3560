document.addEventListener("DOMContentLoaded", function () {
    const directEvents = ["View Login Page","Start Login","View Signup Page","Start Signup","Link Social Account","Complete Signup","Complete Login","Learner multiaccount continue clicked","Account Setup"];
    const linkAccountButton = document.getElementById("btn_login");
    const redirectAnchor = document.querySelector(".redirect-anchor");
    const emailAddress = document.getElementById("emailAddress");
    if (linkAccountButton) {
        linkAccountButton.addEventListener("click", function () {
            const obj = {
                type: emailAddress.getAttribute("value") !== undefined ? emailAddress.getAttribute("value") : "unknown",
            }
            const userObj = {
                email: emailAddress.value,
            }
            if (typeof directEvent == 'undefined' && directEvent.includes("Link Social Account")) {
                WebengageTracking.eventReq.triggerWebengageEvent('Link Social Account',obj, true, userObj);
            } else {
                WebengageTracking.eventReq.triggerWebengageEvent('Link Social Account',obj);
            }
            const userAuthState = document.getElementById("userAuthState").value;
            const email = document.getElementById("emailAddress").value;
            const type = document.getElementById("type").value;

            if (userAuthState === "Link Account") {
                console.log("Anonymous learner clicked Link Account for type:", type, "Email:", email);
            }
        });
    }

    if (redirectAnchor) {
        redirectAnchor.addEventListener("click", function () {
            console.log("User clicked on Go Back to Login.");
        });
    }
});
