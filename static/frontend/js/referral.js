// Referral functionality 
// <PERSON>les fetching referral URLs and copying them to clipboard

let referralLink = null;
let isReferralEligible = false;

/**
 * Check if user is eligible for referral program
 * Based on the same logic as in the header template
 */
function checkReferralEligibility() {
  // Use the same flag as Cloud6
  return typeof loadrefEarn !== 'undefined' && loadrefEarn;
}

/**
 * Fetch referral URL from backend (Cloud6's prepareRefLink equivalent)
 */
async function prepareRefLink() {
  try {
    const response = await fetch('/user/profile/get-refer-earn-url', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.status === true && data.url) {
      referralLink = data.url;
      // Show the referral section (like Cloud6)
      const referSection = document.getElementById('refer_earn_section');
      if (referSection) {
        referSection.style.display = 'block';
      }
      return data.url;
    } else {
      console.log('Referral URL not available yet, will retry later');
      return null;
    }
  } catch (error) {
    console.error('Error fetching referral URL:', error);
    return null;
  }
}

/**
 * Copy text to clipboard
 */
function copyToClipboard(text) {
  return new Promise((resolve, reject) => {
    if (navigator.clipboard && window.isSecureContext) {
      // Use modern clipboard API
      navigator.clipboard.writeText(text)
        .then(() => resolve(true))
        .catch(err => {
          console.error('Clipboard API failed:', err);
          // Fallback to old method
          fallbackCopyToClipboard(text, resolve, reject);
        });
    } else {
      // Fallback for older browsers
      fallbackCopyToClipboard(text, resolve, reject);
    }
  });
}

/**
 * Fallback method for copying to clipboard
 */
function fallbackCopyToClipboard(text, resolve, reject) {
  const textArea = document.createElement('textarea');
  textArea.value = text;
  textArea.style.position = 'fixed';
  textArea.style.left = '-999999px';
  textArea.style.top = '-999999px';
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  try {
    const successful = document.execCommand('copy');
    document.body.removeChild(textArea);
    if (successful) {
      resolve(true);
    } else {
      reject(new Error('Failed to copy'));
    }
  } catch (err) {
    document.body.removeChild(textArea);
    reject(err);
  }
}

/**
 * Show success message after copying
 */
function showCopySuccessMessage() {
  // Create or update banner message
  let banner = document.querySelector('.banner-result');
  if (!banner) {
    banner = document.createElement('div');
    banner.className = 'banner-result hide';
    document.body.appendChild(banner);
  }

  banner.textContent = 'Your unique referral link has been copied to your clipboard. Share it with your friends now!';
  banner.classList.remove('hide');
  banner.classList.add('success');

  // Auto-hide after 5 seconds
  setTimeout(() => {
    banner.classList.add('hide');
    banner.classList.remove('success');
  }, 5000);
}

/**
 * Copy referral link and show success message (Cloud6's copyTextAndLink equivalent)
 */
async function copyTextAndLink() {
  try {
    // If we don't have the referral link yet, try to fetch it
    if (!referralLink) {
      referralLink = await prepareRefLink();
      if (!referralLink) {
        console.log('Referral URL not available');
        return;
      }
    }

    // Build share message like Cloud6
    const shareMessage = `Check out Simplilearn! I'm learning here and you can too. Use my referral link: ${referralLink}`;

    // Copy to clipboard
    await copyToClipboard(shareMessage);
    
    // Show success message
    showCopySuccessMessage();
    
    // Track the event
    if (window.trackWEevent) {
      window.trackWEevent('sl_lms_referral_link_copy', '');
    }
    
    console.log('Referral link copied successfully');
  } catch (error) {
    console.error('Error copying referral link:', error);
  }
}

/**
 * Initialize referral functionality (Cloud6's document ready equivalent)
 */
function initializeReferral() {
  // Check eligibility using Cloud6's pattern
  if (typeof loadrefEarn !== 'undefined' && loadrefEarn) {
    prepareRefLink();
  }

  // Add click handler to copy button
  const copyButton = document.querySelector('.copy-referral-link');
  if (copyButton) {
    copyButton.addEventListener('click', copyTextAndLink);
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', initializeReferral);

// Export functions for potential use in other scripts
window.referralUtils = {
  copyTextAndLink,
  prepareRefLink,
  checkReferralEligibility,
  initializeReferral
}; 