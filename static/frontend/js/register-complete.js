document.addEventListener('DOMContentLoaded', function() {
    // To set the country code value
    const select = document.getElementById('country_code');
    const optionsLength = select.options.length;
    let countryData = '';
  
    if (optionsLength === 10) {
      const separatorOption = document.createElement('option');
      separatorOption.disabled = true;
      select.add(separatorOption);
    }

    const emailField = document.getElementById("mail");
    const register = document.getElementById('btn_register');
    const firstNameField = document.getElementById("first_name");
    const lastNameField = document.getElementById("last_name")
    const phoneInput = document.getElementById("phone_no");
    function toggleButtonState() {
      let isDisabled;
      if (emailField.value.trim() !== "" && firstNameField.value.trim() !== "" && lastNameField.value.trim() !== "" && phoneInput.value.replace(/\D/g, "").trim() !== "") {
        isDisabled = false;
        register.toggleAttribute("disabled", isDisabled);
        register.classList.toggle("disabled", isDisabled);
      } else {
        isDisabled = true;
        register.toggleAttribute("disabled", isDisabled);
        register.classList.toggle("disabled", isDisabled);
      }
    }
  
      // Add event listeners to both fields
      emailField.addEventListener("input", toggleButtonState);
      firstNameField.addEventListener("input", toggleButtonState);
      lastNameField.addEventListener("input", toggleButtonState);
      phoneInput.addEventListener("input", toggleButtonState);
  
      for (let index in countryDataIe) {
        const optionText = countryDataIe[index]['code'] + '\u00A0\u00A0 - \u00A0\u00A0' + countryDataIe[index]['name'];
        const optionValue = countryDataIe[index]['phnCode'];
        if (index === '10') {  // Checking if it's the 11th country (index 10)
          const separator = document.createElement('option');
          separator.text = '---------------------------------------------------------';  // Create a separator option
          separator.value = '';  // No value for separator
          separator.disabled = true;  // Disable the separator
          select.add(separator)
        }
        const newOption = document.createElement('option');
        newOption.text = optionText;
        newOption.value = optionValue;
        select.add(newOption);
      }

  countryData = JSON.stringify(countryDataIe[0]);
  const selectedCountryCodeSpan = document.getElementById('country_code_span');
  select.addEventListener('change', function(event) {
    const selectedCountry = event.target.value;
    countryData = countryDataIe.find(function(o) {
      return o.phnCode === selectedCountry;
    });
    countryData = JSON.stringify(countryData);
    selectedCountryCodeSpan.textContent = '+' + selectedCountry;
  });

  
    select.dispatchEvent(new Event('change'));
  
    // Register form validation
    document.getElementById('registerComplete').addEventListener('submit', function(event) {
      event.preventDefault(); // Prevent the form from submitting
  
      // Clear previous error messages
      const errorMessages = document.querySelectorAll('.error-msg');
      errorMessages.forEach(function(errorMessage) {
        errorMessage.remove();
      });
  
      // Get form input values
      const email = emailField.value;
      const termConditionCheckbox = document.getElementById('termCondition');
      const termCondition = termConditionCheckbox && termConditionCheckbox.type === 'checkbox' 
        ? termConditionCheckbox.checked 
        : (document.getElementById('termCondition').value === 'Y');
      // Get the form and input fields
      const form = document.getElementById('registerComplete');
      const inputs = form.querySelectorAll('input');
      const phoneNumber = phoneInput.value.replace(/\D/g, ""); // Remove non-numeric characters
  
      // Perform validation
      let isValid = true;
  
      // Remove the "error" class from all input fields
      inputs.forEach(function(input) {
        input.classList.remove('error');
      });

  
      // Check for empty fields and mark them with the "error" class
      inputs.forEach(function(input) {
        if (input.value.trim() === '' && input.type !== 'hidden' && input.type !== 'checkbox') {
          isValid = false;
          input.classList.add('error');
        }
      });
  
      const phoneError = document.getElementById("phoneError");
      isValid = validateSignUpForm({
        termCondition,
        email: emailField.value,
        phoneNumber: phoneNumber,
        phoneError
      });

      // If form is valid, check if we need to show terms and conditions modal
      if (isValid) {
        // Set email and country data for form submission
        document.getElementById('mail').value = email;
        document.getElementById('country_data').value = countryData;

        // If form is valid, show the terms and conditions modal if not already accepted
        if (isValid && !TermsConsent.accepted) {
          event.preventDefault();
          TermsConsent.showModalAndWait(form);
          return;
        }

        // Otherwise just submit the form
        document.getElementById('registerComplete').submit();
      }
    });
  });
  