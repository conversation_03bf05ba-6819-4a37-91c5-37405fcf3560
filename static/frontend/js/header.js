// Global variables initialization
const {
  accountsUrl,
  enableCertificateTab,
  ice9Url,
  showHelpSupport,
  helpSupportUrl,
  imageLogoUrl,
  showMyResources,
  isWhiteLabelingEnabled,
  showCommunity,
  communityUrl,
  is_enterprise_learner,
  siteLogoUrl,
  baseUrl
} = window;

let showLeaderBoard = '';
let showWelcomelinkInMenu = false;
let isShowJobsTab = false;
let helpAndSupportClicked = false;
let isB2bB2cGroups = false;
let userType;
const onFeedbackRoute = window.location.href.includes('nps');

function logout() {
  const logoutLink = document.getElementById('logout');
  
  if (!logoutLink) {
    console.error("Element with ID 'logout' not found.");
    return;
  }

  logoutLink.addEventListener('click', async (event) => {
    event.preventDefault();
    try {
      const response = await fetch('../../auth/logout', { method: 'GET' });
      
      if (response.redirected) {
        window.location.href = response.url;
      } else {
        console.log('Logout successful, but no redirect received.');
      }
    } catch (error) {
      console.error('An error occurred:', error);
    }
  });
}

function setupProfileDropdown() {
  const profileBtn = document.getElementById('profileToggle');
  const dropdownMenu = document.getElementById('profileDropdown');
  if (!profileBtn || !dropdownMenu) return;

  profileBtn.addEventListener('click', (event) => {
    event.preventDefault();
    event.stopPropagation();
    
    const isExpanded = profileBtn.getAttribute('aria-expanded') === 'true';
    profileBtn.setAttribute('aria-expanded', !isExpanded);
    dropdownMenu.classList.toggle('show');
    profileBtn.classList.toggle('active');

    console.log('Dropdown Clicked:', dropdownMenu.classList.contains('show'));
  });

  // Prevent hover behavior
  profileBtn.addEventListener('mouseenter', (event) => {
    event.preventDefault();
    console.log('❌ Preventing Hover from Opening Dropdown');
  });

  dropdownMenu.addEventListener('mouseenter', (event) => {
    event.preventDefault();
    console.log('❌ Preventing Hover from Triggering Menu');
  });

  // Close dropdown when clicking outside
  document.addEventListener('click', (event) => {
    if (!profileBtn.contains(event.target) && !dropdownMenu.contains(event.target)) {
      dropdownMenu.classList.remove('show');
      profileBtn.setAttribute('aria-expanded', 'false');
      profileBtn.classList.remove('active');
      console.log('Dropdown Closed on Outside Click');
    }
  });

  // Keyboard accessibility
  profileBtn.addEventListener('keydown', (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      const isExpanded = profileBtn.getAttribute('aria-expanded') === 'true';
      profileBtn.setAttribute('aria-expanded', !isExpanded);
      dropdownMenu.classList.toggle('show');
      profileBtn.classList.toggle('active');
    }
  });
}

// With defer attribute, DOM is always ready when script executes
setupProfileDropdown();
logout();
