document.addEventListener('DOMContentLoaded', function() {
    var confirmPasswordInput = document.getElementById('confirm-password');
    var confirmPasswordIcon = document.getElementById('reset-password-icon');
    const passwordField = document.getElementById("password");
    const resetButton = document.getElementById('btn_reset');
    const confirmPasswordField = document.getElementById("confirm-password");
    const passwordIcon = document.getElementById("eye-icon");
    const passwordInfoIcon = document.getElementById("password-icon");
    const infoIconTooltip = document.querySelector('.password-tooltip');
    const errorBox = document.getElementById('error_msg');
   
    // Initially hide the eye icons
    if (confirmPasswordIcon) confirmPasswordIcon.style.display = 'none';
    if (passwordIcon) passwordIcon.style.display = 'none';
  
    // Add event listeners to both fields
    function toggleButtonState() {
        const passwordValue = passwordField.value.trim();
        const confirmPasswordValue = confirmPasswordField.value.trim();

        if (passwordValue !== "" && confirmPasswordValue !== "") {
            resetButton.removeAttribute("disabled");
            resetButton.classList.remove("disabled"); // Ensure UI updates visually
        } else {
            resetButton.setAttribute("disabled", "true");
            resetButton.classList.add("disabled"); // Ensure UI updates visually
        }
    }

    // Show eye icon when typing begins with correct background position
    if (passwordField) {
        passwordField.addEventListener('input', function() {
            if (passwordIcon) {
                passwordIcon.style.display = this.value.trim() !== '' ? 'inline-block' : 'none';
                // Set correct initial eye state
                passwordIcon.style.backgroundPosition = '-19px -138px';
            }
            toggleButtonState();
        });
    }

    if (confirmPasswordField) {
        confirmPasswordField.addEventListener('input', function() {
            if (confirmPasswordIcon) {
                confirmPasswordIcon.style.display = this.value.trim() !== '' ? 'inline-block' : 'none';
                // Set correct initial eye state to open eye when typing (-19px -138px)
                confirmPasswordIcon.style.backgroundPosition = '-19px -138px';
            }
            toggleButtonState();
        });
    }

    // Add hover and click handlers for password info icon to show tooltip
    if (passwordInfoIcon && infoIconTooltip) {
        // Show tooltip on hover
        passwordInfoIcon.addEventListener('mouseenter', function() {
            infoIconTooltip.classList.add('visible');
        });
        
        // Hide tooltip when mouse leaves
        passwordInfoIcon.addEventListener('mouseleave', function() {
            // Only hide if we're not in validation mode
            if (!passwordField.classList.contains('invalid')) {
                infoIconTooltip.classList.remove('visible');
            }
        });
        
        // Toggle tooltip on click (for mobile devices)
        passwordInfoIcon.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            infoIconTooltip.classList.toggle('visible');
        });
        
        // Make tooltip accessible via keyboard
        passwordInfoIcon.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                infoIconTooltip.classList.toggle('visible');
            }
        });
    }

    // Password validation criteria
    const criteria = {
        lower: '.crit-lower',
        upper: '.crit-upper',
        min: '.crit-min',
        max: '.crit-max',
        num: '.crit-num',
    };

    const ActUtil = {
        validateMinimumChar: (text, min) => text.length >= min,
        validateMaximumChar: (text, max) => text.length <= max,
        validateOneNumber: (text) => /[0-9]/.test(text),
        validateLowerCase: (text) => /[a-z]/.test(text),
        validateUpperCase: (text) => /[A-Z]/.test(text),
    };

    // Position the tooltip in center of screen for better visibility
    const positionTooltip = () => {
        if (infoIconTooltip) {
            // We're using fixed positioning in CSS now
            // No need for additional positioning here
        }
    };

    // Only validate password on form submission, not in real-time
    const validatePassword = (password) => {
        if (!password) return false;

        const validations = {
            lower: ActUtil.validateLowerCase(password),
            upper: ActUtil.validateUpperCase(password),
            min: ActUtil.validateMinimumChar(password, 8),
            max: ActUtil.validateMaximumChar(password, 128),
            num: ActUtil.validateOneNumber(password),
        };

        let invalid = false;

        Object.entries(validations).forEach(([key, isValid]) => {
            document.querySelectorAll(criteria[key]).forEach((element) => {
                element.classList.toggle('red', !isValid);
                element.classList.toggle('correct', isValid);
            });

            if (!isValid) invalid = true;
        });

        // Use class toggling for tooltip
        if (infoIconTooltip) {
            infoIconTooltip.classList.toggle('visible', invalid);
            positionTooltip();
        }

        return !invalid;
    };

    // Toggle password visibility
    if (passwordIcon) {
        passwordIcon.addEventListener('click', function() {
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                this.style.backgroundPosition = '-60px -138px'; // Eye closed position
            } else {
                passwordField.type = 'password';
                this.style.backgroundPosition = '-19px -138px'; // Eye open position
            }
        });
    }

    if (confirmPasswordIcon) {
        confirmPasswordIcon.addEventListener('click', function() {
            if (confirmPasswordField.type === 'password') {
                confirmPasswordField.type = 'text';
                this.style.backgroundPosition = '-60px -138px'; // Eye closed position
            } else {
                confirmPasswordField.type = 'password';
                this.style.backgroundPosition = '-19px -138px'; // Eye open position
            }
        });
    }

    // Close tooltip when clicking anywhere else on the page
    document.addEventListener('click', function(event) {
        if (infoIconTooltip && infoIconTooltip.classList.contains('visible')) {
            if (!infoIconTooltip.contains(event.target) && event.target !== passwordInfoIcon) {
                infoIconTooltip.classList.remove('visible');
            }
        }
    });

    var resetPasswordForm = document.getElementById('resetPassword');
    if (resetPasswordForm) {
        resetPasswordForm.addEventListener('submit', function(e) {
            e.preventDefault();
            var password = document.getElementById('password').value;
            var confirmedPassword = document.getElementById('confirm-password').value;
            var errorMessages = document.querySelectorAll('.error-msg');
            errorMessages.forEach(function(errorMessage) {
                errorMessage.remove();
            });
            var isValid = true;

            // Validate password criteria only on form submission
            if (!validatePassword(password)) {
                isValid = false;
                displayError('Password must meet all criteria.');
            }

            if (password.trim() === '' || confirmedPassword.trim() === '') {
                isValid = false;
                displayError('Please fill in all required fields');
            } else if (password !== confirmedPassword) {
                isValid = false;
                displayError('Passwords you have entered do not match.');
            }
            
            if (isValid) {
                this.submit();
            }
        });
    }

    function displayError(msg) {
        if (errorBox) {
            errorBox.textContent = msg;
            errorBox.style.display = msg === '' ? 'none' : 'block';
        }
        return false;
    }
});


document.addEventListener('DOMContentLoaded', function() {
    var button = document.getElementById('btn-reset-success');
    if (button) {
      // Attach the click event listener to the button
      button.addEventListener('click', function() {
        // Redirect to the login page
        window.location.href = `${baseUrl}/auth/login`;
      });
    } else {
      console.error("Element with ID 'btn-reset-success' not found.");
    }
  });