// Variables
let closeActualChatBot = false;
let hideFeedbackPopup = true;
let isTimeOutError = false;
let timeOutErrorOccured = false;
let isLiveChatTriggered = false;
let iscallChatBotInitated = false;
let hnsOpenStatus = '';
let step_question = null;
let unique_key = `hns${new Date().getTime()}`;
let selectedOption = [];
let ispageloadingcomplete = 0;

// Get user data from window object with fallbacks to dummy data

const firstName = userData?.first_name || '';
const lastName = userData?.last_name || '';
const email = userData?.email || '';
const platformType = 'LMS';
const userGid = affiliateId || '';
const userEmail = userData?.email || '';
const baseUrlHelp = window.baseUrl;
const displayName = userData?.display_name || '';

// const gId = affiliateId || "2";
const gId = "2";
const userId = userData?.uid || '';
const lmsUrl = window.location.hostname || '';

// Add dummy tracking function if not available
if (typeof window.trackWEevent !== 'function') {
    window.trackWEevent = function(eventName, properties) {
    console.log(`[TRACKING] Event: ${eventName}`, properties);
    };
}

// Initialize payload for feedback
let hnsPayload = {
    rating: null,
    payload: [{
        "form_id": '',
        "user_id": userId,
        "entity_type": "help-and-support",
        "entity_value": unique_key,
        "entity_sub_type": "",
        "formModule": "help-and-support",
        "entity_sub_type_value": "",
        "question_id": '',
        "response": [],
        "gid": userGid,
        "user_agent": window.navigator.userAgent,
        "user_ip": userIp,
        "parent": true
    }]
};

let checkboxPayload = JSON.parse(JSON.stringify(hnsPayload.payload[0]));

// Initialize ESW (Embedded Service for Web)
const initESW = function(gslbBaseURL) {
    embedded_svc.addEventHandler("afterMaximize", function(data) {
        console.log("[HNS] ESW Event: afterMaximize", data);
        isTimeOutError = false;
        if (closeActualChatBot == true || isTimeOutError == true) {
            embedded_svc.liveAgentAPI.endChat();
            embedded_svc.liveAgentAPI.clearSession();

            // Hide feedback modal
            const feedbackModal = document.getElementById('feedbackModal');
            if (feedbackModal && typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                const modalInstance = bootstrap.Modal.getInstance(feedbackModal);
                if (modalInstance) modalInstance.hide();
            }

            hideComponentlikeDislikeModal();
        }

        if (timeOutErrorOccured == false) {
            console.log("[HNS] ESW Event: afterMaximize - Hiding dummy chat bot");
            setTimeout(function() {
                document.getElementById("default-chat-bot").classList.add('hidden');
                document.querySelector(".embeddedServiceSidebar-dummy").classList.remove('help-bg-color');

                const helpSupportElements = document.querySelectorAll('.help_and_support');
                helpSupportElements.forEach(el => el.removeAttribute('disabled'));

                const btnCursorElements = document.querySelectorAll('.btnCursor');
                btnCursorElements.forEach(el => el.removeAttribute('disabled'));

                document.getElementById('dummy-text').classList.add('hidden');
            }, 1000);
        }
    });

    embedded_svc.addEventHandler("afterDestroy", function(data) {
        console.log("[HNS] ESW Event: afterDestroy", data);
        checkFeedbackState();
        isTimeOutError = false;
        console.log("[HNS] ESW Event: afterDestroy - Checking feedback state Done");
        if (closeActualChatBot == false) {
            let analyticAttributes = {
                "Position": window.chatPosition || "Profile",
                "LMS Instance": gId,
                "User type": isPglearner ? "Pg" : "Non pg",
                "Platform type": window.platformType
            };
            window.trackWEevent('slLmsCloseHelpSupport', analyticAttributes);
            document.getElementById('selectedOption').value = '';
            document.getElementById('liked').value = '';

            console.log("[HNS] ESW Event: afterDestroy - Showing feedback modal",hideFeedbackPopup);
            // [Disable] Feedback popup after live agent transfer
            if (hideFeedbackPopup == false) {
                if (!isPglearner || userGid != 2) {
                    if (!isLiveChatTriggered) {
                        // $('#feedbackModal').modal('show');
                        // $('#feedbackModal .modal-body').empty().append($('#did-you-find-template').html());
                        const modalElement = document.getElementById('feedbackModal');
                        modalElement.show();

                        const modalBody = modalElement.querySelector('.modal-body');
                        const template = document.getElementById('did-you-find-template');

                        if (modalBody && template) {
                            modalBody.innerHTML = template.innerHTML;
                        }

                    }
                }
            }

            console.log("[HNS] ESW Event: afterDestroy - Resetting live chat triggered flag");
            isLiveChatTriggered = false;
            hideComponentlikeDislikeModal();
        }

        if (timeOutErrorOccured == false) {
            document.getElementById("default-chat-bot")?.classList.add("hidden");

            document.querySelectorAll(".embeddedServiceSidebar-dummy").forEach(el => {
                el.classList.remove("help-bg-color");
            });

            document.querySelectorAll(".help_and_support").forEach(el => {
                el.removeAttribute("disabled");
            });

            document.querySelectorAll(".btnCursor").forEach(el => {
                el.removeAttribute("disabled");
            });

            document.getElementById("dummy-text")?.classList.add("hidden");

        }
        helpIconFocusWhenClose();
    });

    embedded_svc.addEventHandler("onSettingsCallCompleted", function(data) {
        console.log("[HNS] ESW Event: onSettingsCallCompleted triggered", data);
        if (window.embedded_svc.availableFeatures.includes('invite')) {
            setTimeout(() => {
                embedded_svc.bootstrapEmbeddedService().then(res => {
                    console.log("[HNS] ESW Bootstrap completed after settings call", res);
                    if (!iscallChatBotInitated)
                        document.querySelectorAll('div[data-aura-class="embeddedServiceSidebar"]').forEach(el => el.remove());
                });
            }, 100);
        }
    });

    embedded_svc.addEventHandler("onChatTransferSuccessful", function(data) {
        console.log("[HNS] ESW Event: onChatTransferSuccessful", data);
        isLiveChatTriggered = true;
        document.body.setAttribute("showchasitorinputwrapper", "show");
        hnsOpenStatus = 'agentconnected';
    });

    embedded_svc.addEventHandler("onChatEstablished", function(data) {
        console.log("[HNS] ESW Event: onChatEstablished", data);
        hnsOpenStatus = 'aryaconnected';
    });

    window.addEventListener("message", function(event) {
        console.log("[HNS] Window message event received", event.data);
        if (event.data == 'Ticket Created') {
            hnsOpenStatus = 'ticketCreated';
        }
    }, false);

    embedded_svc.settings.displayHelpButton = false;
    embedded_svc.settings.language = '';
    embedded_svc.settings.autoOpenPostChat = true;
    embedded_svc.settings.defaultMinimizedText = 'Simplilearn Chat';
    embedded_svc.settings.disabledMinimizedText = 'Wait for Simplilearn Agent';
    embedded_svc.settings.enabledFeatures = ['LiveAgent'];
    embedded_svc.settings.chatbotAvatarImgURL = "https://cfs22.simplilearn.com/paperclip/simpli-bot.svg";
    embedded_svc.settings.entryFeature = 'LiveAgent';
    embedded_svc.settings.loadingText = 'Simplilearn Help';

    // Prepopulated fields for prechat
    embedded_svc.settings.prepopulatedPrechatFields = {
        FirstName: firstName || displayName,
        LastName: lastName || '',
        Email: userEmail || email,
        Group_Id__c: gId,
        Is_PG_Learner__c: isPglearner,
        Origin_Type__c: platformType,
        URL__c: lmsUrl,
        Exam_Id__c: "",
        Cost_Id__c: "",
        Course_Id__c: "",
        Elearning_Id__c: ""
    };

    // Extra prechat form details
    embedded_svc.settings.extraPrechatFormDetails = [{
            "label": "First Name",
            "transcriptFields": ["First_Name__c"],
            "value": firstName || displayName
        },
        {
            "label": "Last Name",
            "transcriptFields": ["Last_Name__c"],
            "value": lastName || ''
        },
        {
            "label": "Email",
            "transcriptFields": ["Email_Id__c"],
            "value": userEmail || email
        },
        {
            "label": "URL",
            "transcriptFields": ["URL__c"],
            "value": lmsUrl
        },
        {
            "label": "Company Name",
            "transcriptFields": ["Company_Name__c"],
            "value": groupName
        },
        {
            "label": "Group Id",
            "transcriptFields": ["group_Id_of_LMS__c"],
            "value": gId
        },
        {
            "label": "Is PG Learner",
            "transcriptFields": ["Is_PG_Learner__c"],
            "value": isPglearner
        },
        {
            "label": "Origin Type",
            "transcriptFields": ["Origin_Type__c"],
            "value": platformType
        },
        {
            "label": "Selected Main Menu",
            "transcriptFields": ["Selected_Main_Menu__c"]
        },
        {
            "label": "File Id",
            "transcriptFields": ["File_Id__c"]
        },
        {
            "label": "Selected Sub Menu",
            "transcriptFields": ["Selected_Sub_Menu__c"]
        },
        {
            "label": "Problem Description",
            "transcriptFields": ["Problem_Description__c"]
        },
        {
            "label": "Selected Course Name",
            "transcriptFields": ["Selected_Course_Name__c"]
        },
        {
            "label": "Selected Child Menu",
            "transcriptFields": ["Selected_Child_Menu__c"]
        },
        {
            "label": "Master Bundle JSON",
            "transcriptFields": ["Master_Bundle_JSON__c"]
        },
        {
            "label": "CLP Bundle JSON",
            "transcriptFields": ["CLP_Bundle_JSON__c"]
        },
        {
            "label": "Individual Course JSON",
            "transcriptFields": ["Individual_Course_JSON__c"]
        },
        {
            "label": "Exam Id",
            "transcriptFields": ["Exam_Id__c"],
            "value": ""
        },
        {
            "label": "Cost Id",
            "transcriptFields": ["Cost_Id__c"],
            "value": ""
        },
        {
            "label": "Course Id",
            "transcriptFields": ["Course_Id__c"],
            "value": ""
        },
        {
            "label": "Elearning Id",
            "transcriptFields": ["Elearning_Id__c"],
            "value": ""
        }
    ];

    // Event listener for custom field setting
    document.addEventListener(
        "setCustomField",
        function(event) {
            console.log("[HNS] ESW Event: setCustomField event received", event.detail);
            if (!isPglearner) {
                return;
            }
            embedded_svc.settings.extraPrechatFormDetails[8].value = event.detail.Selected_Main_Menu__c;
            embedded_svc.settings.extraPrechatFormDetails[9].value = event.detail.File_Id__c;
            embedded_svc.settings.extraPrechatFormDetails[10].value = event.detail.Selected_Sub_Menu__c;
            embedded_svc.settings.extraPrechatFormDetails[11].value = event.detail.Problem_Description__c;
            embedded_svc.settings.extraPrechatFormDetails[12].value = event.detail.Selected_Course_Name__c;
            embedded_svc.settings.extraPrechatFormDetails[13].value = event.detail.Selected_Child_Menu__c;
            embedded_svc.settings.extraPrechatFormDetails[14].value = event.detail.Master_Bundle_JSON__c;
            embedded_svc.settings.extraPrechatFormDetails[15].value = event.detail.CLP_Bundle_JSON__c;
            embedded_svc.settings.extraPrechatFormDetails[16].value = event.detail.Individual_Course_JSON__c;

            // Fire startChat callback
            if (event && event.detail && typeof event.detail.callback === 'function') {
                event.detail.callback();
            } else {
                console.error('Invalid event structure or callback function');
            }
        },
        false
    );

    document.addEventListener("Open Article", function(event) {
        console.log("[HNS] ESW Event: Open Article event received", event);
        hnsOpenStatus = 'openarticle';
    }, false);

    document.addEventListener("Exploration", function(event) {
        console.log("[HNS] ESW Event: Exploration event received", event);
        hnsOpenStatus = 'Exploration';
    }, false);

    // Initialize embedded service based on environment
    const isProduction = window.ENVIRONMENT === 'production';
    console.log("[HNS] ESW Initialization: Environment is", isProduction ? "production" : "development");

    if (isProduction) {
        console.log("[HNS] ESW Initialization: Initializing production environment");
        embedded_svc.init(
            'https://simplilearn.my.salesforce.com',
            'https://simplilearn.my.salesforce-sites.com/support',
            gslbBaseURL,
            '00D28000000sMrr',
            'Market_Motive_Chat', {
                baseLiveAgentContentURL: 'https://c.la2-c2-ukb.salesforceliveagent.com/content',
                deploymentId: '5722x000000L1bw',
                buttonId: '5732x000000L1cD',
                baseLiveAgentURL: 'https://d.la2-c2-ukb.salesforceliveagent.com/chat',
                eswLiveAgentDevName: 'EmbeddedServiceLiveAgent_Parent04I2x000000GnLFEA0_1716d59057f',
                isOfflineSupportEnabled: false
            }
        );
    } else {
        console.log("[HNS] ESW Initialization: Initializing development/QA environment");
        embedded_svc.init(
            'https://simplilearn--partialqa.sandbox.my.salesforce.com',
            'https://simplilearn--partialqa.sandbox.my.salesforce-sites.com/support',
            gslbBaseURL,
            '00D1s0000000ZDA',
            'Market_Motive_Chat', {
                baseLiveAgentContentURL: 'https://c.la2-c1cs-ukb.salesforceliveagent.com/content',
                deploymentId: '5722x000000L1bw',
                buttonId: '5732x000000L1cD',
                baseLiveAgentURL: 'https://d.la2-c1cs-ukb.salesforceliveagent.com/chat',
                eswLiveAgentDevName: 'EmbeddedServiceLiveAgent_Parent04I2x000000GnLFEA0_1716d59057f',
                isOfflineSupportEnabled: false
            }
        );
    }
};

// Load ESW script based on environment
// Load ESW script based on environment
if (!window.embedded_svc) {

    const s = document.createElement('script');

    if (window.ENVIRONMENT === 'production') {
        s.setAttribute('src', 'https://simplilearn.my.salesforce.com/embeddedservice/5.0/esw.min.js');
    } else {
        s.setAttribute('src', 'https://simplilearn--PartialQA.cs75.my.salesforce.com/embeddedservice/5.0/esw.min.js');
    }

    s.onload = function() {
        initESW(null);
    };

    s.onerror = function(error) {
        console.error('[HNS] Failed to load ESW script:', error);
    };

    document.body.appendChild(s);
} else {
    initESW('https://service.force.com');
}


// Document ready functionslLmsOpenHelpSupport
document.addEventListener('DOMContentLoaded', function() {
    ispageloadingcomplete = 1;
});

// Function to call chatbot
function callChatbot(position = "", _examId = "", _costId = "", _courseId = "", _elearningId = "", ticketNo = "", ticketStatus = "") {

    if (!ispageloadingcomplete) {
        return;
    }


    disableClickOnExamVoucher();

    const closeDummyChatBotElement = document.getElementById("close-dummy-chat-bot");
    closeDummyChatBotElement.className = position;

    let examId = "",
        costId = "",
        courseId = "",
        elearningId = "";
    if (position === 'exam') {
        examId = _examId;
        costId = _costId;
        courseId = _courseId;
        elearningId = _elearningId;
    }

    // Prepopulate fields
    embedded_svc.settings.prepopulatedPrechatFields.Exam_Id__c = examId;
    embedded_svc.settings.prepopulatedPrechatFields.Cost_Id__c = costId;
    embedded_svc.settings.prepopulatedPrechatFields.Course_Id__c = courseId;
    embedded_svc.settings.prepopulatedPrechatFields.Elearning_Id__c = elearningId;

    embedded_svc.settings.extraPrechatFormDetails[17] = {
        "label": "Exam Id",
        "transcriptFields": ["Exam_Id__c"],
        "value": examId
    };
    embedded_svc.settings.extraPrechatFormDetails[18] = {
        "label": "Cost Id",
        "transcriptFields": ["Cost_Id__c"],
        "value": costId
    };
    embedded_svc.settings.extraPrechatFormDetails[19] = {
        "label": "Course Id",
        "transcriptFields": ["Course_Id__c"],
        "value": courseId
    };
    embedded_svc.settings.extraPrechatFormDetails[20] = {
        "label": "Elearning Id",
        "transcriptFields": ["Elearning_Id__c"],
        "value": elearningId
    };

    iscallChatBotInitated = true;
    hnsOpenStatus = '';
    unique_key = `hns${new Date().getTime()}`;
    document.querySelector(".embeddedServiceSidebar-dummy").classList.remove('help-bg-color');
    closeActualChatBot = false;
    isTimeOutError = false;
    window.chatPosition = position;

    const properties = {
        "Position": position,
        "LMS Instance": gId,
        "product id": 0,
        "product type": "",
        "category Name": "",
        "training type": "",
        "category Id": "",
        "User type": isPglearner ? "Pg" : "Non pg",
        "Platform type": platformType,
        "User email": email,
        "SL product name": "",
        "First name": firstName,
        "Last name": lastName,
        "exam_id": (_examId === '') ? 0 : parseInt(_examId, 10),
        "cost_id": (_costId === '') ? 0 : parseInt(_costId, 10),
        "course_id": (_courseId === '') ? 0 : parseInt(_courseId, 10),
        "e_learning_id": (_elearningId === '') ? 0 : parseInt(_elearningId, 10),
        "ticket_no": ticketNo.toString(),
        "ticket_status": ticketStatus.toString()
    };
    window.trackWEevent('slLmsOpenHelpSupport', properties);

    // Show loading UI
    document.getElementById("default-chat-bot").classList.remove('hidden'); // making the chat box visible

    document.getElementById("img-loader").classList.remove('hidden');
    document.getElementById("dummy-text").classList.remove('hidden');

    document.querySelectorAll('.help_and_support').forEach(el => el.setAttribute("disabled", "disabled"));
    document.querySelectorAll('.btnCursor').forEach(el => el.setAttribute("disabled", "disabled"));

    document.getElementById('cookie-error-block').classList.add('hidden');
    document.getElementById('generic-error-block').classList.add('hidden');

    // Timeout check
    if (closeActualChatBot === false) {
        isTimeOutError = true;
        if (!window.embedded_svc.availableFeatures.includes('invite')) {
            console.warn("Invite feature not available. Triggering cookie-block error UI.");
            setTimeout(function() {
                logClientError('cookies-blocked');
                document.getElementById('cookie-error-block').classList.remove('hidden');
                document.getElementById("img-loader").classList.add('hidden');
                document.getElementById("dummy-text").classList.add('hidden');
                document.querySelector(".embeddedServiceSidebar-dummy").classList.add('help-bg-color');
                closeActualChatBot = true;
                timeOutErrorOccured = true;
            }, 1000);
        }
    }

    if (isPglearner && userGid === 2) {
        document.body.setAttribute('showchasitorinputwrapper', 'hide');
    }

    embedded_svc.bootstrapEmbeddedService().then(() => {}).catch((err) => {
        console.error("embedded_svc.bootstrapEmbeddedService failed:", err);
        logClientError('Timeout/CORS');
        document.getElementById('generic-error-block').classList.remove('hidden');
        document.getElementById("img-loader").classList.add('hidden');
        document.getElementById("dummy-text").classList.add('hidden');
        document.querySelector(".embeddedServiceSidebar-dummy").classList.add('help-bg-color');
        embedded_svc.inviteAPI.inviteButton.rejectInvite();
        closeActualChatBot = true;
        timeOutErrorOccured = true;
    });
}


// Function to log client errors
function logClientError(errorType = 'unknown') {
    var data = {};
    // Prepare data to be inserted
    data.user_id = userId;
    data.user_email = userEmail;
    data.group_id = gId;
    data.error_type = errorType;

    const xhr = new XMLHttpRequest();
    xhr.open('POST', baseUrlHelp + '/internal/user-api-v1/help-and-support/log-client-error', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.onload = function() {
        if (xhr.status === 200) {}
    };
    xhr.send(new URLSearchParams(data).toString());
}

// Function to close dummy chat bot
function closeDummyChatBot() {
    closeActualChatBot = true;
    timeOutErrorOccured = false;
    if (embedded_svc.inviteAPI && embedded_svc.inviteAPI.inviteButton) {
        embedded_svc.inviteAPI.inviteButton.rejectInvite();
    }
    document.getElementById("default-chat-bot").classList.add('hidden');
    document.querySelector(".embeddedServiceSidebar-dummy").classList.remove('help-bg-color');

    const helpSupportElements = document.querySelectorAll('.help_and_support');
    helpSupportElements.forEach(el => el.removeAttribute('disabled'));

    const btnCursorElements = document.querySelectorAll('.btnCursor');
    btnCursorElements.forEach(el => el.removeAttribute('disabled'));

    document.getElementById('dummy-text').classList.add('hidden');
    window.trackWEevent('slLmsCloseDummyWindow', '');
}

// Event listeners for feedback support modal
document.addEventListener('click', function(event) {
    console.log("[HNS] Event target CLICKED:", event.target);
    // Like button click
    if (event.target.parentElement.classList.contains('like-btn')) {
        document.getElementById('liked').setAttribute('value', 1);
        hnsPayload['payload'][0]['response'] = [true];
        saveFeedback();
        openthankYouModal();
    }

    // Dislike button click
    if (event.target.parentElement.classList.contains('dislike-btn')) {
        document.getElementById('liked').setAttribute('value', 0);
        hnsPayload['payload'][0]['response'] = [false];
        saveFeedback();
        openFeedbackOption();
    }

    // Feedback option click
    if (event.target.closest('#thank-you-for-feedback-id span')) {
        const span = event.target.closest('#thank-you-for-feedback-id span');

        if (span.hasAttribute('checked')) {
            span.removeAttribute('checked');
            span.classList.remove('active');
        } else {
            span.classList.add('active');
            span.setAttribute('checked', true);
        }

        selectedOption = [];
        document.querySelectorAll('#thank-you-for-feedback-id span.active').forEach(function(el) {
            selectedOption.push(el.getAttribute('name'));
        });

        checkboxPayload['response'] = selectedOption;

        document.getElementById('selectedOption').value = selectedOption.toString();
        showSubmitButton(selectedOption.length > 0 ? true : false);
    }
});

document.addEventListener('click', function(event) {
    if (event.target.closest('#feedbackModal .hns-modal-header .close')) {
        const id = document.querySelector('#feedbackModal .modal-body > div').id;
        const action = 'close';
        if (id === 'thank-you-very-much-template') {
            document.getElementById('feedbackModal').classList.add('hidden');
        } else {
            submitSupportFeedback(action);
            openthankYouModal();
        }
    }
});

document.addEventListener('click', function(event) {
    if (event.target.closest('#feedbackModal .modal-footer #thank-you-for-feedback-footer-template button')) {
        const action = 'submit';
        submitSupportFeedback(action);
        // openthankYouModal();
    }
});

// Function to show/hide submit button
function showSubmitButton(flag) {
    const button = document.querySelector('#feedbackModal .modal-footer #thank-you-for-feedback-footer-template button');
    if (flag) {
        button.classList.add('submit');
        button.textContent = 'Submit';
        button.setAttribute('title', 'Submit');
        button.setAttribute('aria-label', 'Submit');
    } else {
        button.classList.remove('submit');
        button.textContent = 'Skip & Close';
        button.setAttribute('title', 'Skip & Close');
        button.setAttribute('aria-label', 'Skip & Close');
    }
}

// TODO validate the function
// Function to submit support feedback
function submitSupportFeedback() {
    console.log('[HNS] submitSupportFeedback triggered')
    preparePayloadForSubmit();
    return;
}

// Function to open thank you modal
function openthankYouModal() {
    const feedbackModal = document.getElementById('feedbackModal');
    const modalBody = feedbackModal.querySelector('.modal-body');
    const modalFooter = feedbackModal.querySelector('.modal-footer');

    // Clone templates
    const thankYouTemplate = document.getElementById('thank-you-very-much-template').cloneNode(true);
    const thankYouFooterTemplate = document.getElementById('thank-you-very-much-footer-template').cloneNode(true);

    // Clear and append new content
    modalBody.innerHTML = '';
    modalBody.appendChild(thankYouTemplate);
    modalFooter.innerHTML = '';
    modalFooter.appendChild(thankYouFooterTemplate);

    // Show templates
    thankYouTemplate.classList.remove('hidden');
    thankYouFooterTemplate.classList.remove('hidden');

    // Show modal
    if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
        const modal = new bootstrap.Modal(feedbackModal);
        modal.show();
    }

    // Replace PHP variable with JavaScript variable
    const thankYouHeading = modalBody.querySelector('.thank-you-container h3');
    if (thankYouHeading) {
        thankYouHeading.textContent = `Thank you for your feedback, ${firstName}!`;
    }
}

function closeThankyouModal() {
    const feedbackModal = document.getElementById('feedbackModal');
    const backdropElement = document.getElementById('feedbackModalBackdrop');
    const modalBody = feedbackModal.querySelector('.modal-body');
    const modalFooter = feedbackModal.querySelector('.modal-footer');

    // Clear modal content
    modalBody.innerHTML = '';
    modalFooter.innerHTML = '';
    
    // Hide modal
    feedbackModal.classList.remove('show');
    feedbackModal.style.display = 'none';
    feedbackModal.setAttribute('aria-hidden', 'true');
    feedbackModal.removeAttribute('aria-modal');
    document.body.classList.remove('modal-open');
    
    // Hide backdrop
    if (backdropElement) {
        backdropElement.classList.add('hidden');
    }
    
    // Reset state
    helpIconFocusWhenClose();
}



// Function to handle focus when help icon is closed
function helpIconFocusWhenClose() {
    window.addEventListener('keypress', function(event) {
        if (event.target.classList.contains('chatHeaderBranding') || event.target.classList.contains('close-btn')) {
            document.querySelector('.help_and_support').focus();
        }
    });

    document.addEventListener("keyup", function(event) {
        if ((event.key === "Escape" || event.keyCode === 27)) {
            if (event.target.classList.contains('slds-scope') || event.target.classList.contains('close-btn')) {
                document.querySelector('.help_and_support').focus();
            }
        } else if (event.key === "Enter" || event.keyCode === 13) {
            if (event.target.classList.contains('close-btn')) {
                document.querySelector('.help_and_support').focus();
            }
        }
    });
}

// Function to hide component like/dislike modal
function hideComponentlikeDislikeModal() {
    console.log("[HNS] Starting hideComponentlikeDislikeModal function");
    
    // Get modal elements
    const feedbackModal = document.getElementById('feedbackModal');
    if (!feedbackModal) {
        console.error("[HNS] Error: feedbackModal element not found");
        return;
    }
    
    const modalBody = feedbackModal.querySelector('.modal-body');
    if (!modalBody) {
        console.error("[HNS] Error: .modal-body element not found");
        return;
    }
    
    const modalFooter = feedbackModal.querySelector('.modal-footer');
    if (!modalFooter) {
        console.error("[HNS] Error: .modal-footer element not found");
        return;
    }

    // Check if modal body has content
    if (modalBody.children.length === 0) {
        console.log("[HNS] Modal body has no children");
        return;
    }

    // Check modal body content
    const firstChild = modalBody.children[0];
    const id = firstChild ? firstChild.id : undefined;
    console.log(`[HNS] Modal body first child ID: ${id || 'undefined'}`);
    
    // Set footer display based on content
    if (id === 'did-you-find-content-id') {
        console.log("[HNS] Found like/dislike content, hiding footer");
        modalFooter.style.display = 'none';
    } else {
        console.log("[HNS] Found other content, showing footer");
        modalFooter.style.display = 'block';
    }
    
    console.log("[HNS] Completed hideComponentlikeDislikeModal function");
}

// Function to open feedback options
function openFeedbackOption() {
    const feedbackModal = document.getElementById('feedbackModal');
    const modalBody = feedbackModal.querySelector('.modal-body');
    const modalFooter = feedbackModal.querySelector('.modal-footer');

    // Clone templates
    const feedbackTemplate = document.getElementById('thank-you-for-feedback-template').cloneNode(true);
    const feedbackFooterTemplate = document.getElementById('thank-you-for-feedback-footer-template').cloneNode(true);

    // Clear and append new content
    modalBody.innerHTML = '';
    modalBody.appendChild(feedbackTemplate);
    modalFooter.innerHTML = '';
    modalFooter.appendChild(feedbackFooterTemplate);

    hideComponentlikeDislikeModal();

    // Set question text
    const questionHeading = feedbackTemplate.querySelector('h3');
    if (questionHeading && step_question) {
        questionHeading.innerHTML = step_question.question;
    }

    // Generate questions
    if (step_question && step_question.options) {
        const subQuesCheckbox = feedbackTemplate.querySelector("#sub_ques_checkbox");
        if (subQuesCheckbox) {
            subQuesCheckbox.innerHTML = '';

            for (const [key, value] of Object.entries(step_question.options)) {
                const span = document.createElement('span');
                span.setAttribute('name', key);
                span.className = 'feeback-question';
                span.innerHTML = `<span class="icon"></span>${value}`;
                subQuesCheckbox.appendChild(span);
            }
        }
    }

    // Show templates
    feedbackTemplate.classList.remove('hidden');
    feedbackFooterTemplate.classList.remove('hidden');

    // Show button
    const button = feedbackFooterTemplate.querySelector('button');
    if (button) {
        button.style.display = 'block';
    }
}



// Function to check feedback state
function checkFeedbackState() {
    // RE-ENABLE Click activity
    const examIdElement = document.getElementById("examId");
    if (examIdElement && examIdElement.value !== "") {
        examIdElement.value = "";

        if (typeof ActUtil !== 'undefined' && ActUtil.showLoader) {
            ActUtil.showLoader();
        }

        setTimeout(function() {
            location.reload();
        }, 3000);

        return false;
    }

    enableClickOnExamVoucher();
    resetData();
    unique_key = `hns${new Date().getTime()}`;
    hnsPayload.payload[0].entity_value = unique_key;

    console.log("[HNS] Checking feedback state [hnsOpenStatus]", hnsOpenStatus , isPglearner, userGid);
    if (isPglearner == true && userGid == 2) {
        switch (hnsOpenStatus) {
            case 'Exploration':
                openLikeDislikeFeedback(7);
                break;
            case 'openarticle':
                openLikeDislikeFeedback(6);
                break;
            case 'aryaconnected':
                openLikeDislikeFeedback(8);
                break;
            default:
                break;
        }
    }
}

// Function to save feedback
function saveFeedback() {
    let payload = { 'nps-save': JSON.stringify(hnsPayload.payload) };
    console.log("[HNS] saveFeedback triggered with payload", payload)
    fetch(`${baseUrlHelp}/internal/user-api-v1/Nps/save-user-feedback`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json', // ✅ Critical
            },
            body: JSON.stringify(payload)
        })
        .then(response => response.json())
        .then(res => {
            console.log('[HNS] Sucess:  save nps feedback response : ',JSON.stringify(res))
            document.getElementById('hns_loader').classList.add('hidden');
        })
        .catch(error => {
            console.error('Error saving feedback:', error);
            document.getElementById('hns_loader').classList.add('hidden');
        });
}

// Function to prepare payload for submit
function preparePayloadForSubmit() {
    console.log("[HNS] preparePayloadForSubmit triggered")
    openthankYouModal();
    if (selectedOption && selectedOption.length) {
        hnsPayload.payload[0].form_id = step_question.form_id;
        hnsPayload.payload[0].question_id = step_question.id;
        hnsPayload.payload[0].response = selectedOption;
        saveFeedback();
    }
}

// Function to close modal
function closeModal() {
    document.getElementById('feedbackModal').classList.add('hidden');
    helpIconFocusWhenClose();
}

// Function to reset data
function resetData() {
    document.querySelector('#feedbackModal .modal-body').innerHTML = '';
    step_question = null;
    unique_key = `hns${new Date().getTime()}`;
    selectedOption = [];

    hnsPayload = {
        rating: null,
        payload: [{
            "form_id": '',
            "user_id": userId,
            "entity_type": "help-and-support",
            "entity_value": unique_key,
            "entity_sub_type": "",
            "formModule": "help-and-support",
            "entity_sub_type_value": "",
            "question_id": '',
            "response": [],
            "gid": userGid,
            "user_agent": window.navigator.userAgent,
            "user_ip": userIp,
            "parent": true
        }]
    };

    checkboxPayload = JSON.parse(JSON.stringify(hnsPayload['payload'][0]));
}

// Function to disable click on exam voucher
function disableClickOnExamVoucher() {
    let classes = ["info-help", "goto-practice-assesments", "revisit-prerequisites", "complete_exam_preparedness",
        "revisit-prerequisites-exam", "visit-guide-application", "revisit-prerequisites-application",
        "process_application", "do-later", "start-chat", "requestVoucher", "proceed", "cancel",
        "reschedule", "resendVoucher"
    ];

    classes.forEach(function(value) {
        const elements = document.querySelectorAll(`.${value}`);
        elements.forEach(function(element) {
            element.classList.add("unclickable");
        });
    });

    let checkBoxButton = ["submit", "exam-voucher-checkbox2", "application-check", "practice-assessment-checkbox"];

    checkBoxButton.forEach(function(value) {
        const elements = document.querySelectorAll(`.${value}`);
        elements.forEach(function(element) {
            element.classList.add("unclickable");
            element.disabled = true;
        });
    });
}

// Function to enable click on exam voucher
function enableClickOnExamVoucher() {
    let classes = ["info-help", "goto-practice-assesments", "revisit-prerequisites", "complete_exam_preparedness",
        "revisit-prerequisites-exam", "visit-guide-application", "revisit-prerequisites-application",
        "process_application", "do-later", "start-chat", "requestVoucher", "proceed", "cancel",
        "reschedule", "resendVoucher"
    ];

    classes.forEach(function(value) {
        const elements = document.querySelectorAll(`.${value}`);
        elements.forEach(function(element) {
            element.classList.remove("unclickable");
        });
    });

    let checkBoxButton = ["submit", "exam-voucher-checkbox2", "application-check", "practice-assessment-checkbox"];

    checkBoxButton.forEach(function(value) {
        const elements = document.querySelectorAll(`.${value}`);
        elements.forEach(function(element) {
            element.classList.remove("unclickable");
            element.disabled = false;
        });
    });
}

function openLikeDislikeFeedback(form_id) {
    // Show modal (vanilla version)
    console.log("[HNS] Starting openLikeDislikeFeedback function with form_id:", form_id);
    const modalElement = document.getElementById('feedbackModal');
    const backdropElement = document.getElementById('feedbackModalBackdrop');
    
    if (!modalElement) {
        console.error("[HNS] ERROR: feedbackModal element not found in DOM");
        return;
    }
    
    // Show backdrop
    if (backdropElement) {
        backdropElement.classList.remove('hidden');
    }
    
    // Show modal
    modalElement.classList.add('show');
    modalElement.style.display = 'block';
    modalElement.removeAttribute('aria-hidden');
    modalElement.setAttribute('aria-modal', 'true');
    document.body.classList.add('modal-open');
    console.log("[HNS] Modal display properties set");
  
    // Clear modal header and show loader
    const headerElement = document.getElementById('modal-header-h1-id');
    if (headerElement) {
        headerElement.innerHTML = ' ';
        console.log("[HNS] Cleared modal header content");
    } else {
        console.warn("[HNS] WARNING: modal-header-h1-id element not found");
    }
    
    const loaderElement = document.getElementById('hns_loader');
    if (loaderElement) {
        loaderElement.classList.remove('hidden');
        console.log("[HNS] Showing loader");
    } else {
        console.warn("[HNS] WARNING: hns_loader element not found");
    }
  
    // Create and send XHR request
    console.log("[HNS] Creating XHR request");
    const xhr = new XMLHttpRequest();
    const requestUrl = `${baseUrlHelp}/internal/user-api-v1/Nps/get-feedback-form?formId=${form_id}&formModule=help-and-support`;
    console.log("[HNS] Request URL:", requestUrl);
    
    xhr.open('GET', requestUrl, true);
    xhr.setRequestHeader('Accept', 'application/json');
    console.log("[HNS] XHR request configured");
    
    xhr.onload = function() {
        console.log("[HNS] XHR onload triggered with status:", xhr.status);
        
        if (loaderElement) {
            loaderElement.classList.add('hidden');
            console.log("[HNS] Hiding loader");
        }
        
        if (xhr.status === 200) {
            console.log("[HNS] Received 200 response, parsing JSON");
            try {
                const res = JSON.parse(xhr.responseText);
                console.log("[HNS] Response parsed successfully:", res);
                
                if (res && 
                    res.status === 200 && 
                    res.data && 
                    res.data.length && 
                    res.data[0].form_questions && 
                    res.data[0].form_questions.length && 
                    res.data[0].form_questions[0].range_type === 'like') {
                    
                    console.log("[HNS] Valid response structure confirmed");
                    const modalBody = document.querySelector('#feedbackModal .modal-body');
                    if (modalBody) {
                        console.log("[HNS] Found modal body element");
                        const template = document.getElementById('did-you-find-template');
                        if (template) {
                            console.log("[HNS] Found template element, applying to modal body");
                            modalBody.innerHTML = template.innerHTML;
                            
                            console.log("[HNS] Looking for rule with index 0");
                            const ruleIndex = res.data[0].form_questions[0].rule.findIndex(item => {
                                return item.rule === 0;
                            });
                            console.log("[HNS] Rule index found:", ruleIndex);
                            
                            if (ruleIndex > -1) {
                                const subQid = res.data[0].form_questions[0].rule[ruleIndex].subQid;
                                console.log("[HNS] Found subQid:", subQid);
                                step_question = res.data[0].form_questions[0].sub_questions[subQid];
                                console.log("[HNS] Set step_question:", step_question);
                            } else {
                                console.log("[HNS] No matching rule found, setting empty step_question");
                                step_question = [];
                            }
                            
                            hnsPayload.payload[0].form_id = form_id;
                            hnsPayload.payload[0].question_id = res.data[0].form_questions[0].id;
                            console.log("[HNS] Updated hnsPayload:", hnsPayload);
                        } else {
                            console.error("[HNS] ERROR: did-you-find-template element not found");
                        }
                    } else {
                        console.error("[HNS] ERROR: Modal body element not found");
                    }
                } else {
                    console.error("[HNS] ERROR: Response structure invalid", {
                        hasRes: !!res,
                        status: res?.status,
                        hasData: !!res?.data,
                        dataLength: res?.data?.length,
                        hasFormQuestions: !!res?.data?.[0]?.form_questions,
                        formQuestionsLength: res?.data?.[0]?.form_questions?.length,
                        rangeType: res?.data?.[0]?.form_questions?.[0]?.range_type
                    });
                }
            } catch (error) {
                console.error('[HNS] ERROR: Failed to parse JSON response:', error);
                console.error('[HNS] Raw response:', xhr.responseText);
            }
        } else {
            console.error('[HNS] ERROR: Request failed with status:', xhr.status);
            console.error('[HNS] Response text:', xhr.responseText);
        }
    };
    
    xhr.onerror = function(error) {
        console.error('[HNS] NETWORK ERROR:', error);
        
        if (loaderElement) {
            loaderElement.classList.add('hidden');
            console.log("[HNS] Hiding loader after error");
        }
    };
    
    console.log("[HNS] Sending XHR request");
    xhr.send();
    console.log("[HNS] XHR request sent");
}
