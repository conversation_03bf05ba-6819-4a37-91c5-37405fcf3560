document.addEventListener("DOMContentLoaded", function() {
    // Select all navigation items
    let navItems = document.querySelectorAll(".left_nav_list li");
    let lastActiveTab = document.querySelector(".left_nav_list li.active");
    
    // Track first load for each tab section
    const firstLoadStates = {
        'basic_tab': true,
        'contact_tab': true,
        'professional_tab': true,
        'academics_tab': true,
        'outcome_tab': true
    };
    
    // Function to update aria-selected attributes
    function updateAriaSelected(activeTabId) {
        navItems.forEach(item => {
            const tabId = item.getAttribute("data-navmenu");
            const isActive = tabId === activeTabId;
            item.setAttribute("aria-selected", isActive ? "true" : "false");
        });
    }
    
    // Function to activate tab and sync with profiles.js
    function activateTab(targetId) {
        // Remove active class from all tabs
        navItems.forEach(nav => nav.classList.remove("active"));
        
        // Find and activate the target tab
        const targetTab = document.querySelector(`[data-navmenu="${targetId}"]`);
        if (targetTab) {
            targetTab.classList.add("active");
            lastActiveTab = targetTab;
            
            // Update aria-selected attributes
            updateAriaSelected(targetId);
        }

        // Hide all sections
        document.querySelectorAll(".tab-section").forEach(section => {
            section.style.display = "none";
            section.classList.remove("active");
        });

        // Show the target section
        const targetSection = document.getElementById(targetId);
        if (targetSection) {
            targetSection.style.display = "block";
            targetSection.classList.add("active");
        }
    }
    
    // Initialize on page load - check URL hash or default to basic_tab
    const hash = window.location.hash.slice(1);
    const initialTab = hash || 'basic_tab';
    activateTab(initialTab);
    
    // Loop through each item
    navItems.forEach(item => {
        item.addEventListener("click", function(event) {
            event.preventDefault(); // Prevent default anchor behavior
            
            // Don't do anything if clicking the already active tab
            if (this === lastActiveTab) {
                return;
            }

            const targetId = this.getAttribute("data-navmenu");
            const currentTabId = lastActiveTab ? lastActiveTab.getAttribute("data-navmenu") : "";
            const currentSection = currentTabId.replace("_tab", "");
            
            // Check for unsaved changes in current tab FIRST (regardless of target tab state)
            if (!window.isLeaving && 
                window.tabFormState && 
                window.tabFormState[currentSection] === true) {
                var confirmation = confirm("You have unsaved changes. Are you sure you want to leave?");
                if (!confirmation) {
                    return;
                }
                // Reset form state for the current tab since the user confirmed
                if (window.tabFormState) {
                    window.tabFormState[currentSection] = false;
                    
                    // Check if all tabs are now false, if so reset global flag
                    const anyTabModified = Object.values(window.tabFormState).some(value => value === true);
                    if (!anyTabModified) {
                        window.isFormModified = false;
                    }
                }
            }
            
            // Handle first load state of target tab (separate from unsaved changes check)
            if (firstLoadStates[targetId]) {
                firstLoadStates[targetId] = false;
            }
            
            // Activate the new tab
            activateTab(targetId);
            
            // Update URL hash
            if (location.hash !== `#${targetId}`) {
                history.replaceState(null, '', `#${targetId}`);
            }
        });
    });

    // Also handle anchor link clicks within the tab content
    document.addEventListener('click', function(event) {
        const link = event.target.closest('a[href^="#"]');
        if (link && link.closest('.left_nav_list')) {
            const targetId = link.getAttribute('href').slice(1);
            if (targetId && document.getElementById(targetId)) {
                event.preventDefault();
                
                // Find the corresponding nav item
                const navItem = document.querySelector(`[data-navmenu="${targetId}"]`);
                if (navItem) {
                    navItem.click(); // Trigger the nav item click to handle unsaved changes logic
                }
            }
        }
    });
});
