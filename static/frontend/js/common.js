/**
 * Function to validate email format
 * @param {*} email
 * @returns
 */

//isThirdPartyCookieEnabled()

(function() {
  // Set this variable based on your environment
  // e.g., 'production', 'development', etc.
  const ENVIRONMENT = window.ENVIRONMENT || 'development';

  if (ENVIRONMENT === 'production') {
    const noop = function() {};
    console.log = noop;
    console.info = noop;
    console.warn = noop;
    console.error = noop;
    console.debug = noop;
  }
})();

function validateEmail(email) {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  let isEmailValid = emailRegex.test(email);
  const errorMsgElement = document.getElementById('error_msg'); // Get reference here
  if (!isEmailValid) {
    errorMsgElement.textContent = 'Please enter a valid email address';
    errorMsgElement.classList.remove('hide'); // <--- ADD THIS LINE
  } else {
    // If the email is valid, ensure the error message is hidden
    if (errorMsgElement.textContent === 'Please enter a valid email address') {
      errorMsgElement.textContent = ''; // Clear only if it's this specific error
      errorMsgElement.classList.add('hide'); // <--- ADD THIS LINE
    }
  }
  return isEmailValid;
}

// signup form validation
function validateSignUpForm({ password, email, phoneNumber, phoneError, termCondition }) {
  let isValid = true;
  const errorMsgElement = document.getElementById('error_msg');

  // Clear previous error
  errorMsgElement.textContent = '';
  errorMsgElement.classList.add('hide');

  // Email validation
  if (!validateEmail(email)) {
      errorMsgElement.textContent = 'Please enter a valid email address';
      isValid = false;
  } else if (password && !checkPassword(password)) {
    errorMsgElement.textContent = 'Password does not meet the required criteria.';
    isValid = false;
  } else if (termCondition !== undefined && (termCondition !== true && termCondition !== 'Y')) {
    errorMsgElement.textContent = 'Please accept the Terms and Conditions to continue.';
    // Add a visual indicator if we have a checkbox
    const termConditionElement = document.getElementById('termCondition');
    if (termConditionElement && termConditionElement.type === 'checkbox') {
      termConditionElement.classList.add('invalid');
    }
    isValid = false;
  }

  const isNumeric = /^[0-9]+$/.test(phoneNumber);

  if (phoneNumber.length > 0) {
    if (!isNumeric || phoneNumber.length < 7 || phoneNumber.length > 15) {
      isValid = false;
      phoneError.style.display = "inline";
      phoneError.textContent = phoneNumber.length < 7 
        ? 'Your contact number should be at least 7 characters long' 
        : 'Please enter a valid contact number';
    } else {
      phoneError.style.display = "none";
    }
  }

  return isValid;
}

/**
 * Function to check all the conditions of password
 * @param password: string
 * @returns boolean
 */
function checkPassword(password) {
  const validations = [
    {
      regex: /^.{10,}$/,
      errorClass: '.crit-min',
    },
    {
      regex: /^.{0,128}$/,
      errorClass: '.crit-max',
    },
    {
      regex: /[a-z]/,
      errorClass: '.crit-lower',
    },
    {
      regex: /[A-Z]/,
      errorClass: '.crit-upper',
    },
    {
      regex: /\d/,
      errorClass: '.crit-num',
    },
  ];

  let isValid = true;

  document.querySelectorAll('.crit-min, .crit-max, .crit-lower, .crit-upper, .crit-num').forEach(function (element) {
    element.classList.remove('textError', 'valid');
  });

  for (const validation of validations) {
    document.querySelector(validation.errorClass).classList.remove('correct');
    document.querySelector(validation.errorClass).classList.remove('red');
    if (!validation.regex.test(password)) {
      isValid = false;
      document.querySelector(validation.errorClass).classList.add('red');
    } else {
      document.querySelector(validation.errorClass).classList.add('correct');
    }
  }

  if (!isValid) {
    document.getElementById('password').classList.add('error');
    document.getElementById('password-icon').classList.add('invalid');
    document.querySelector('.password-tooltip').classList.add('visible');
  }else {
    document.getElementById('password').classList.remove('error');
    document.getElementById('password-icon').classList.remove('invalid');
    document.querySelector('.password-tooltip').classList.remove('visible'); // Hide tooltip when valid
  }
 
  return isValid;
}

//learner outcome
// document.getElementById('select_interests').addEventListener('change',function(){
//   const saveBtn = document.getElementById('save-btn');
//   const discardBtn = document.getElementById('discard-btn');

//   saveBtn.disabled = false;
//   discardBtn.disabled = false;
// });
/**
 * Condition to hide password tooltip on click outside
 */
document.addEventListener('click', function (e) {
  const container = document.querySelector('.password-tooltip');

  // if the target of the click isn't the container nor a descendant of the container
  if (container && !container.contains(e.target)) {
    container.classList.remove('visible');
  }
});

let passwordField = document.getElementById('password');
if (passwordField) {
  passwordField.addEventListener('input', function () {
    if(passwordField.value.length > 0) {
      const passwordInfoIcon = document.getElementById('password-icon');
      if (passwordInfoIcon) {
        passwordInfoIcon.classList.remove('invalid');
      }
    }
  });
}

const phoneInput = document.getElementById('phone_no');
if(phoneInput) {
  phoneInput.addEventListener('input', function () {
    if (this.value.length > 15) {
      this.value = this.value.slice(0, 15);
    }
  });
}

/**
 * Condition for info icon and make password visible.
 */
document.addEventListener('DOMContentLoaded', function () {
  const passwordInput = document.getElementById('password');
  const passwordIcon = document.getElementById('password-icon');
  const passwordEyeSlashText = 'password-eye-slash';
  const passwordEyeUnslashText = 'password-eye-unslash';
  if (passwordInput && passwordIcon && passwordEyeSlashText && passwordEyeUnslashText) {
    passwordInput.addEventListener('input', function () {
      let passwordValue = this.value;
      if (passwordValue.length > 0) {
        passwordInput.setAttribute('type', 'password');
        // passwordIcon.classList.add('password-info', 'password-eye-slash');
        passwordIcon.classList.add('password-eye-slash');
      } else {
        passwordInput.setAttribute('type', 'password');
        passwordIcon.classList.remove('password-eye-slash', 'password-eye-unslash');
        // passwordIcon.classList.add('password-info');
      }
    });
    togglePasswordEyeIcon(passwordInput, passwordIcon, passwordEyeSlashText, passwordEyeUnslashText);
  }
});

/**
 *
 * @param {*} passwordIcon
 * @param {*} passwordEyeSlash
 * @param {*} passwordEyeUnslash
 */
function togglePasswordEyeIcon(passwordInput, passwordIcon, passwordEyeSlash, passwordEyeUnslash) {
  passwordIcon.addEventListener('click', function () {
    if (passwordIcon.classList.contains(passwordEyeSlash)) {
      passwordInput.setAttribute('type', 'text');
      passwordIcon.classList.remove(passwordEyeSlash);
      passwordIcon.classList.add(passwordEyeUnslash);
    } else if (passwordIcon.classList.contains(passwordEyeUnslash)) {
      passwordInput.setAttribute('type', 'password');
      passwordIcon.classList.remove(passwordEyeUnslash);
      passwordIcon.classList.add(passwordEyeSlash);
    }
  });
}

function newTabSocialLoginClicked(e) {
  var isCookieEnabled = isThirdPartyCookieEnabled()
  if(isCookieEnabled){
  const type = e.currentTarget.id.split('-')[0];
  const width = 40; // Width of the window in viewport units (vw)
  const height = 80; // Height of the window in viewport units (vh)
  const url = `${baseUrl}/auth/${type}`;
  var modal = document.getElementById('showEnableCookies');
  modal.style.display = 'none';
  const newWindow = window.open(
    url,
    '_blank',
    `width=${(window.innerWidth * width) / 100}, height=${(window.innerHeight * height) / 100}`,
  );
  newWindow.focus();
  }else {
    openCookieModel()
  }
}

function sameTabSocialLoginClicked(e) {
  let idp = e.currentTarget.id.split('-')[0];
  idp = 'SignInWithApple' === idp ? 'apple' : idp
  const url = `${baseUrl}/auth/${idp}`;
  window.location.replace(url);
}

function allowTextOnly(e) {
  // Get the input element
  var targetDoc = document.getElementById(`${e.target.id}`);

  // Get the current value of the input
  var targetVal = targetDoc.value;

  // Remove any non-letter and non-space characters
  var sanitizedValue = targetVal.replace(/[^a-zA-Z\s]/g, '');

  // Update the input value
  targetDoc.value = sanitizedValue;
}

function allowNumbersOnly(e) {
  var targetDoc = document.getElementById(`${e.target.id}`);
  targetDoc.value = targetDoc.value.replace(/\D/g, ''); // Remove non-numeric characters
}


function isThirdPartyCookieEnabled() {
  // Generate a random key for the third-party cookie
  const cookieKey = 'testThirdPartyCookie_' + Math.random().toString(36).substring(2);

  // Attempt to set the third-party cookie
  document.cookie = cookieKey + 'domain=.simplilearn.com; path=/';
  // Check if the cookie was successfully set
  const cookieIsSet = document.cookie.indexOf(cookieKey) !== -1;

  // Delete the cookie
  document.cookie = cookieKey + 'domain=.simplilearn.com; path=/';
  return cookieIsSet
};


function openCookieModel() {
  var modal = document.getElementById('showEnableCookies');
  modal.style.opacity = 1;
  modal.style.zIndex = 11;
  modal.style.display = 'block';
  var leftContainer = document.getElementsByClassName('left-container')[0];
  leftContainer.style.zIndex = 0;
}

function closeCookieModel() {
  var modal = document.getElementById('showEnableCookies');
  modal.style.opacity = 0;
  modal.style.zIndex = 0;
  modal.style.display = 'none';
  var leftContainer = document.getElementsByClassName('left-container')[0];
  leftContainer.style.zIndex = 10;
}


function showToast(message, {
  type = "success", 
  duration = 5000, 
  position = "top-right", 
}) {
  let toast = document.createElement("div");
  toast.className = `toast ${type} ${position}`;
  toast.textContent = message;

  document.body.appendChild(toast);

  setTimeout(() => {
    toast.classList.add("show");
  }, 100); 

  setTimeout(() => {
    toast.classList.remove("show");
    setTimeout(() => {
      toast.remove();
    }, 500);
  }, duration);
}

// Terms and Conditions Modal Functionality
const TermsConsent = {
  accepted: false,
  content: '',
  styles: '',
  
  showModalAndWait: function(form) {
    try {
      const modal = document.getElementById('termsModal');
      const bodyContent = document.getElementById('bodyContentTC');
      
      // Get T&C content and styles from the hidden fields
      if (!this.content) {
        const tcElement = document.getElementById('termsAndConditionsContent');
        this.content = tcElement && tcElement.value ? tcElement.value : '';
        
      }
      
      if (!this.styles) {
        const stylesElement = document.getElementById('termsAndConditionsStyles');
        this.styles = stylesElement && stylesElement.value ? stylesElement.value : '';
        
      }
      
      // Check if content is available
      if (!this.content || this.content.trim() === '') {
        console.warn('T&C content is empty, cannot display modal');
        return;
      }
      
      // Clear any existing content
      bodyContent.innerHTML = '';
      
      // Add Cloud6 styles if they exist (as enhancements on top of base styles)
      if (this.styles && this.styles.trim() !== '') {
        // Remove any existing terms styles
        const existingStyles = document.getElementById('termsModalStyles');
        if (existingStyles) {
          existingStyles.remove();
        }
        
        // Create and add Cloud6 style element (as enhancements)
        const styleElement = document.createElement('style');
        styleElement.id = 'termsModalStyles';
        styleElement.textContent = this.styles;
        
        // Insert at the end of head to allow Cloud6 styles to enhance our base styles
        document.head.appendChild(styleElement);
        
      } else {
        
      }
      
      // Create the heading
      const heading = document.createElement('div');
      heading.className = 'heading';
      heading.innerHTML = '<h1 class="top-heading" id="termsModalLabel">Terms and Conditions</h1>' +
                          '<p class="scroll-info-new">Scroll to the end to accept the Terms &amp; Conditions</p>';
      
      // Create the info section - single scrollable container
      const infoSection = document.createElement('section');
      infoSection.className = 'info';
      
      const contentDiv = document.createElement('div');
      contentDiv.className = 'content';
      contentDiv.tabIndex = 0;
      
      // Use the clean content (styles already extracted)
      let cleanContent = this.content;
      
      // Remove any heading divs that might already be in the content
      if (cleanContent.includes('<div class="heading">')) {
        // Find and remove the heading section to avoid duplication
        cleanContent = cleanContent.replace(/<div class="heading">[\s\S]*?<\/div>/, '');
      }
      
      // Remove nested section.info and div.content to avoid double scrolling
      if (cleanContent.includes('<section class="info">')) {
        cleanContent = cleanContent.replace(/<section class="info">[\s\S]*?<div class="content"[^>]*>/, '');
        cleanContent = cleanContent.replace(/<\/div><\/section>/, '');
      }
      
      // Set the content
      contentDiv.innerHTML = cleanContent;
      
      // Create the bottom action section
      const bottomAction = document.createElement('div');
      bottomAction.className = 'bottom-action';
      bottomAction.innerHTML = '<p class="bottom-text">By accepting, you agree to the Terms and Conditions.</p>' + 
                              '<button id="acceptTermsBtn" class="accept-btn" disabled>Accept</button>';
      
      // Add bottom action to the content div (not to body content)
      contentDiv.appendChild(bottomAction);
      
      // Append elements in the right hierarchy (matching Cloud6 structure)
      infoSection.appendChild(contentDiv);
      bodyContent.appendChild(heading);
      bodyContent.appendChild(infoSection);
      
      // Show the modal
      modal.style.display = 'block';
      modal.setAttribute('aria-hidden', 'false');
      document.body.style.overflow = 'hidden';
      bodyContent.style.textAlign = 'start'; // Explicitly set text-align to start
      TermsConsent.accepted = false;
      
      // Handle scrolling and button events
      const content = contentDiv;
      const acceptBtn = document.getElementById('acceptTermsBtn');
      
      if (!content || !acceptBtn) {
        console.error('Required elements not found in modal');
        return;
      }
      
      // Disable Accept button initially
      acceptBtn.disabled = true;
      
      // Enable Accept button when user scrolls to bottom
      const onScroll = function() {
        if (content.scrollTop + content.clientHeight >= content.scrollHeight - 10) {
          acceptBtn.disabled = false;
        }
      };
      content.addEventListener('scroll', onScroll);
      
      // Handle Accept button click
      const onAccept = function() {
        TermsConsent.accepted = true;
        const termConditionInput = document.getElementById('termCondition');
        if (termConditionInput) {
          if (termConditionInput.type === 'checkbox') {
            termConditionInput.checked = true;
          } else {
            termConditionInput.value = 'Y';
          }
        }
        modal.style.display = 'none';
        modal.setAttribute('aria-hidden', 'true');
        document.body.style.overflow = '';
        acceptBtn.removeEventListener('click', onAccept);
        content.removeEventListener('scroll', onScroll);
        
        // Clean up styles when modal is closed
        const existingStyles = document.getElementById('termsModalStyles');
        if (existingStyles) {
          existingStyles.remove();
        }
        
        form.submit();
      };
      acceptBtn.addEventListener('click', onAccept);
      
      // Handle Close button click
      const closeBtn = document.getElementById('closeTermsModalBtn');
      const onClose = function() {
        modal.style.display = 'none';
        modal.setAttribute('aria-hidden', 'true');
        document.body.style.overflow = '';
        closeBtn.removeEventListener('click', onClose);
        acceptBtn.removeEventListener('click', onAccept);
        content.removeEventListener('scroll', onScroll);
        
        // Clean up styles when modal is closed
        const existingStyles = document.getElementById('termsModalStyles');
        if (existingStyles) {
          existingStyles.remove();
        }
        
        // Reset server validation state so the modal can be shown again
        if (typeof window.resetServerValidation === 'function') {
          window.resetServerValidation();
        }
        
        // Reset terms acceptance state
        TermsConsent.accepted = false;
      };
      
      if (closeBtn) {
        closeBtn.addEventListener('click', onClose);
      }
    } catch (error) {
      console.error('Error in showModalAndWait:', error);
    }
  },

  // Cloud6 compatible close method
  closeTncModal: function() {
    const modal = document.getElementById('termsModal');
    if (modal) {
      modal.style.display = 'none';
      modal.setAttribute('aria-hidden', 'true');
      document.body.style.overflow = '';
      
      // Clean up styles when modal is closed
      const existingStyles = document.getElementById('termsModalStyles');
      if (existingStyles) {
        existingStyles.remove();
      }
      
      // Reset server validation state so the modal can be shown again
      if (typeof window.resetServerValidation === 'function') {
        window.resetServerValidation();
      }
      
      // Reset terms acceptance state
      TermsConsent.accepted = false;
      
      // Remove any event listeners if needed
      const acceptBtn = document.getElementById('acceptTermsBtn');
      const closeBtn = document.getElementById('closeTermsModalBtn');
      const content = document.querySelector('#termsModal .info .content');
      
      if (acceptBtn) {
        acceptBtn.replaceWith(acceptBtn.cloneNode(true));
      }
      if (closeBtn) {
        closeBtn.replaceWith(closeBtn.cloneNode(true));
      }
      if (content) {
        content.replaceWith(content.cloneNode(true));
      }
    }
  }
};



//to check the pattern of a password
function checkPasswordPattern(password){
  const hasLowerCase = /[a-z]/.test(password); // At least one lowercase letter
    const hasUpperCase = /[A-Z]/.test(password); // At least one uppercase letter
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password); // At least one special character
    const hasNumber = /[0-9]/.test(password); // At least one number
    const isValidLength = password.length >= 8 && password.length <= 128; // Length between 8 and 128 characters

    // Check if all the rules are met
    if (hasLowerCase && hasUpperCase && hasSpecialChar && hasNumber && isValidLength) {
        return true;
    } else {
        return false;
    }
}
