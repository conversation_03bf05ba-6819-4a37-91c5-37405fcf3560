(function (e, t) {
  var n = e.amplitude || { _q: [], _iq: {} };
  var r = t.createElement("script");
  r.type = "text/javascript";
  r.integrity = "sha384-girahbTbYZ9tT03PWWj0mEVgyxtZoyDF9KVZdL+R53PP5wCY0PiVUKq0jeRlMx9M";
  r.crossOrigin = "anonymous";
  r.async = true;
  r.src = "https://cdn.amplitude.com/libs/amplitude-7.2.1-min.gz.js";
  r.onload = function () {
    if (!e.amplitude.runQueuedFunctions) {
      console.log("[Amplitude] Error: could not load SDK");
    }
  };
  var i = t.getElementsByTagName("script")[0];
  i.parentNode.insertBefore(r, i);

  function s(e, t) {
    e.prototype[t] = function () {
      this._q.push([t].concat(Array.prototype.slice.call(arguments, 0)));
      return this;
    };
  }

  var o = function () {
    this._q = [];
    return this;
  };
  var a = ["add", "append", "clearAll", "prepend", "set", "setOnce", "unset"];
  for (var c = 0; c < a.length; c++) {
    s(o, a[c]);
  }
  n.Identify = o;

  var u = function () {
    this._q = [];
    return this;
  };
  var l = ["setProductId", "setQuantity", "setPrice", "setRevenueType", "setEventProperties"];
  for (var p = 0; p < l.length; p++) {
    s(u, l[p]);
  }
  n.Revenue = u;

  var d = [
    "init",
    "logEvent",
    "logRevenue",
    "setUserId",
    "setUserProperties",
    "setOptOut",
    "setVersionName",
    "setDomain",
    "setDeviceId",
    "enableTracking",
    "setGlobalUserProperties",
    "identify",
    "clearUserProperties",
    "setGroup",
    "logRevenueV2",
    "regenerateDeviceId",
    "groupIdentify",
    "onInit",
    "logEventWithTimestamp",
    "logEventWithGroups",
    "setSessionId",
    "resetSessionId"
  ];

  function v(e) {
    function t(t) {
      e[t] = function () {
        e._q.push([t].concat(Array.prototype.slice.call(arguments, 0)));
      };
    }
    for (var n = 0; n < d.length; n++) {
      t(d[n]);
    }
  }
  v(n);

  n.getInstance = function (e) {
    e = (!e || e.length === 0 ? "$default_instance" : e).toLowerCase();
    if (!n._iq.hasOwnProperty(e)) {
      n._iq[e] = { _q: [] };
      v(n._iq[e]);
    }
    return n._iq[e];
  };

  e.amplitude = n;
})(window, document);

function amplitudeWebengageLoad(groupId) {
  if (groupId == undefined || groupId == "" || groupId == 2) {
    webengage.init(b2c_webengage_key);
  } else {
    webengage.init(b2b_webengage_key);
  }
}



function initWebengageEvent() {
  var WebengageTracking = WebengageTracking || {};
  if (typeof window !== "undefined") {
    window.WebengageTracking = WebengageTracking;
  }
  WebengageTracking.eventReq = {
    user: "",
    userEmail: "",
    loginUser: function loginUser(userEmail = null) {
      this.user = userEmail;
      webengage.user.login(this.user);
    },
    setWebengageUserProperties: function setWebengageUserProperties(userProperties) {
      console.log("User Properties: ", userProperties);
      for (var key in userProperties) {
        if (userProperties.hasOwnProperty(key)) {
          if (typeof key !== "undefined" && typeof userProperties[key] !== "undefined") {
            webengage.user.setAttribute(key, userProperties[key]);
          }
        }
      }
    },
    logWebengageEvent: function logWebengageEvent(eventName, eventProperties) {
      if (webengage) {
        console.log("Webengage Event Name: ", eventName);
        console.log("Webengage Event Properties: ", eventProperties);
        webengage.track(eventName, eventProperties);
      }
    },
    triggerWebengageEvent: function triggerWebengageEvent(eventName, eventProperties, identify = false, userObj = null) {
      if (identify) {
        this.loginUser(userObj["email"]);
        webengage.user.setAttribute("we_email", userObj["email"]);
      }
      if (userObj) {
        this.setWebengageUserProperties(userObj);
      }
      eventProperties.sl_utm_src = fetchUTMSource();
      this.logWebengageEvent(eventName, eventProperties);
      return true;
    }
  };
}


function fetchUTMSource() {
  var cookieVal = "utmcsr=(direct)|utmccn=(direct)|utmcmd=(none)|create_type=manual"; // default value

  // from cookie
  var utmzCookie = getCookieByName('sl_su_utmz');

  // from url
  if (typeof utmzCookie === 'undefined' || !utmzCookie) {
    utmzCookie = fetchUtmSourceFromUrl();
  }

  // from referrer
  if ((typeof utmzCookie === 'undefined' || !utmzCookie) && document.referrer) {
    var referrer = document.referrer;
    var domainData = referrer.match(/\/\/[^/]+/);
    var domain = '';
    var medium = 'referral';
    var campaign = '(referral)';
    var selfDomains = ['simplilearn', 'marketmotive'];
    var organicDomains = ['google', 'bing', 'yahoo', 'baidu', 'ask', 'youtube', 'yandex', 'aol'];
    var selfRefer = false;

    if (Array.isArray(domainData) && domainData[0]) {
      domain = (typeof domainData[0] === 'string') ? domainData[0].replace('//', '') : '';

      for (var i in selfDomains) {
        var key = selfDomains[i];
        if (domain.indexOf(key) !== -1) {
          selfRefer = true;
        }
      }

      if (!selfRefer) {
        for (var i in organicDomains) {
          var key = organicDomains[i];
          if (domain.indexOf(key) !== -1) {
            domain = key;
            medium = 'organic';
            campaign = '(organic)';
            break;
          }
        }
      }
    }
    if (!selfRefer) {
      var utmcsr = domain;
      var utmcmd = medium;
      var utmcct = referrer;
      var utmccn = campaign;
      var utmctr = '';
      var utmgclid = '';
      utmzCookie = "utmcsr=" + utmcsr + "|utmccn=" + utmccn + "|utmcmd=" + utmcmd + "|utmctr=" + utmctr + "|utmcct=" + utmcct + "|utmgclid=" + utmgclid + "|create_type=manual_ref";
    }
  }

  if (typeof utmzCookie === 'undefined' || !utmzCookie) {
    if (getCookieByName('sl_utmz')) {
      utmzCookie = getCookieByName('sl_utmz');
    } else {
      utmzCookie = cookieVal; // default value
    }
  }

  if (!getCookieByName('sl_su_utmz')) {
    var date = new Date();
    date.setTime(date.getTime() + (90 * 24 * 60 * 60 * 1000));
    document.cookie = 'sl_utmz=' + utmzCookie + '; expires=' + date.toGMTString() + '; path=/;domain=.simplilearn.com;SameSite=Lax';
  }
  if (window && window.user_params) {
    window.user_params.utm_params = utmzCookie;
  }
  return utmzCookie;
}

function getCookieByName(c_name, cookies) {
  var i, x, y;
  var ARRcookies = '';
  if (isClient()) {
    ARRcookies = document.cookie.split(";");
  } else {
    ARRcookies = (cookies || '').split(";");
  }
  for (i = 0; i < ARRcookies.length; i++) {
    x = ARRcookies[i].substr(0, ARRcookies[i].indexOf("="));
    y = ARRcookies[i].substr(ARRcookies[i].indexOf("=") + 1);
    x = x.replace(/^\s+|\s+$/g, "");
    if (x == c_name) {
      return unescape(y);
    }
  }
}

function isClient() {
  return (typeof window !== 'undefined');
}

function fetchUtmSourceFromUrl() {
  var utmcsr = getParameterByName('utm_source');
  var utmccn = getParameterByName('utm_campaign');
  var utmcmd = getParameterByName('utm_medium');
  var utmctr = getParameterByName('utm_term');
  var utmcct = getParameterByName('utm_content');
  var utmgclid = getParameterByName('gclid');
  var utmzCookie = null;
  if (utmcsr || utmccn || utmcmd || utmctr || utmcct || utmgclid) {
    utmzCookie = "utmcsr=" + utmcsr + "|utmgclid=" + utmgclid + "|utmccn=" + utmccn + "|utmcmd=" + utmcmd + "|utmctr=" + utmctr + "|utmcct=" + utmcct + "|create_type=manual_url";
  }
  return utmzCookie;
}

function getParameterByName(name) {
  name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
  var regex = new RegExp("[\\?&]" + name + "=([^&#]*)");
  var results = regex.exec(decodeURIComponent(location.search));
  return typeof results === "undefined" || results === null ? "" : decodeURIComponent(results[1].replace(/\+/g, " "));
}


initWebengageEvent();