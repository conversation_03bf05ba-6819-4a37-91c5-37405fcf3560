// Variables
let is_freemium_user = false;
let currentYear = null;
const ice9Url = window['ice9Url'];
const communityUrl = window['communityUrl'];
const helpSupportUrl = window['helpSupport'];
let is_enterprise_learner = false;
let isB2bB2cGroups = LE_CONSTANT.FALSE;
let showFooter = true;
let subscription = null;
let restrictFooterData = null;
let checkForRestriction = false;

// Function to initialize component
function initializeComponent() {
    showFooter = false;
    isB2bB2cGroups = window['isB2bB2cGroups'];
    is_enterprise_learner = window['is_enterprise_learner'];
    currentYear = new Date().getFullYear();
}

// Function to check if the user is a referral post user
function isReffaralPostUser() {
    return !(is_freemium_user || is_enterprise_learner);
}

// Function to open terms and conditions
function openTermsAndCond() {
    window.open(ice9Url + '/terms-and-conditions#/terms-of-use', '_blank');
}

// Function to open privacy policy
function openPrivacyPolicy() {
    window.open(ice9Url + '/terms-and-conditions#/privacy-policy', '_blank');
}

// Function to open refund policy
function openRefundPolicy() {
    window.open(ice9Url + '/terms-and-conditions#/refund-policy', '_blank');
}

// Function to open reschedule policy
function openReschedule() {
    window.open(ice9Url + '/terms-and-conditions#/rescheduling-policy', '_blank');
}

// Function to open help and support
function helpAndSupport() {
    window.open(helpSupportUrl, '_blank');
}

// Function to open free trial
function openFreeTrial() {
    window.open(ice9Url + '/terms-and-conditions#/free-trial', '_blank');
}

// Function to open refer and earn
function openReferAndEarn() {
    window.open(ice9Url + '/refer-and-earn', '_blank');
}

// Function to open about us
function openAboutUs() {
    window.open(ice9Url + '/about-us', '_blank');
}

// Function to open in the media
function openInTheMedia() {
    window.open(ice9Url + '/media', '_blank');
}

// Function to open contact us
function openContactUs() {
    if (window['IS_HELP_AND_SUPPORT_ENABLED']) {
        if (window['IS_HELP_AND_SUPPORT_ENABLED'] === 'Yes') {
            window['pcdata']['Position'] = "ContactUs_footer";
            window['pcdata']['product id'] = 0;
            window['pcdata']['product type'] = "";
            window['pcdata']['category Name'] = "";
            window['pcdata']['training type'] = "";
            window['pcdata']['category Id'] = "";
            window['pcdata']['User type'] = window['ispglearner'] ? "Pg" : "Non pg";
            window['pcdata']['User_email'] = window['useremail'];
            window['pcdata']['First name'] = window['pcuser']['first_name'];
            window['pcdata']['Last name'] = window['pcuser']['last_name'];
            window['pcdata']['Platform type'] = 'LMS';
            window['trackWEevent']("slLmsOpenHelpSupport", "");
            window['callChatbot']();
        } else if (window['IS_HELP_AND_SUPPORT_ENABLED'] === 'Custom') {
            window.open(window['EXCULSIVE_HELP_AND_SUPPORT_URL'], '_blank');
        }
    } else {
        window.open(ice9Url + '/contact-us', '_blank');
    }
}

// Function to open customer speak
function openCustomerSpeak() {
    window.open(ice9Url + '/reviews', '_blank');
}

// Function to open grievance redressal
function openGrievanceRedressal() {
    window.open(ice9Url + '/help-and-support#/grievance-redressal', '_blank');
}

// Attach event listeners to elements after DOM is ready
document.addEventListener("DOMContentLoaded", function () {
    // Initialize component
    initializeComponent();

    // Attach event listeners to elements
    const referEarnLink = document.querySelector(".refer-earn");
    referEarnLink.addEventListener("click", openReferAndEarn);

    // You can similarly attach event listeners to other elements
    // For example:
    // const termsAndCondLink = document.querySelector(".terms-and-cond");
    // termsAndCondLink.addEventListener("click", openTermsAndCond);
});
