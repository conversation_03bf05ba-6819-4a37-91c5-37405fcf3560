document.addEventListener('DOMContentLoaded', function () {
    const directEvents = ["View Login Page","Start Login","View Signup Page","Start Signup","Link Social Account","Complete Signup","Complete Login","Learner multiaccount continue clicked","Account Setup"];
    var segmentObj = {
        "Group ids": [],
        "Enterprise names": [],
        "Enterprise selected": '',
        "Group Ids selected": '',
    };
    const continueButton = document.getElementById('btn-multi-account');
    const radioButtons = document.querySelectorAll('.multi-radio-input');

    var groupIds = [], EnterpriseName = [];
    radioButtons.forEach(radioButton => {
        groupIds.push(radioButton.getAttribute('groupId'));
        EnterpriseName.push(radioButton.getAttribute('groupName'));
        radioButton.addEventListener('change', function () {
            segmentObj["Group Ids selected"] = radioButton.getAttribute('groupId');
            segmentObj["Enterprise selected"] = radioButton.getAttribute('groupName');
            const anyRadioSelected = Array.from(radioButtons).some(radio => radio.checked);
            continueButton.disabled = !anyRadioSelected;
        });
    });
    segmentObj["Group ids"] = groupIds;
    segmentObj["Enterprise names"] = EnterpriseName;
    // Get the "Continue" button element
    const multiAccountContainer = document.querySelector('.multi-account-container');
    const contentHeading = document.querySelector('.content-heading');
    const subHeading = document.querySelector('.content-sub-heading');
    const redirectingText = document.createElement('div');
    redirectingText.textContent = 'Redirecting ...';
    redirectingText.classList.add('redirecting-text');
    // Add a click event listener to the "Continue" button
    continueButton.addEventListener("click", function () {
        //webengage event
        if (typeof directEvents !== 'undefined' && directEvents.includes("Learner multiaccount continue clicked")) {
            amplitudeWebengageLoad(segmentObj["Group Ids selected"]);
            WebengageTracking.eventReq.triggerWebengageEvent('Learner multiaccount continue clicked', segmentObj); 
        } else {
            amplitudeWebengageLoad(segmentObj["Group Ids selected"]);
            WebengageTracking.eventReq.triggerWebengageEvent('Learner multiaccount continue clicked', segmentObj);  
        }
        // Find the selected radio button
        let selectedRadioValue = null;
        radioButtons.forEach((radioButton) => {
            if (radioButton.checked) {
                selectedRadioValue = radioButton.value;
            }
        });
        multiAccountContainer.style.display = 'none';
        contentHeading.style.display = 'none';
        subHeading.style.display = 'none';
        multiAccountContainer.parentNode.appendChild(redirectingText);
    });
});