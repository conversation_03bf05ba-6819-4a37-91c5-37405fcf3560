document.addEventListener('DOMContentLoaded', function() {
    var forgotPasswordForm = document.getElementById('forgotPassword');
    var emailInput = document.getElementById('mail');
    var sendButton = document.getElementById('btn_forgot');
    const errorBox = document.getElementById('error_msg');
    // Disable the send button initially
    sendButton.disabled = true;

    // Listen for input in email field
    emailInput.addEventListener('input', function () {
        var email = emailInput.value.trim();

        // Validate email using common.js function
        if (email != '') {
            sendButton.disabled = false; // Enable button if valid email
        } else {
            sendButton.disabled = true; // Keep it disabled if invalid
        }
    });

    forgotPasswordForm.addEventListener('submit', function(e) {
        e.preventDefault();
        var errorMessages = document.querySelectorAll('.error-msg');
        errorMessages.forEach(function(errorMessage) {
            errorMessage.remove();
        });
        var isValid = true;
        var email = document.getElementById('mail').value;

        if (!validateEmail(email)) {
            isValid = false;
            return displayError('Please enter a valid email address');
        }

        if (isValid) {
            this.submit();
        }
        function displayError(msg) {
            errorBox.textContent = msg;
            errorBox.classList.toggle('hide', msg === '');
            return false;
          }
    });
});

