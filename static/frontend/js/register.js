document.addEventListener('DOMContentLoaded', function() {
    var signupBtn = document.getElementById('signupBtn');
    const directEvents = ["View Login Page","Start Login","View Signup Page","Start Signup","Link Social Account","Complete Signup","Complete Login","Learner multiaccount continue clicked","Account Setup"];
    // webengage event
    var obj = {};
    var selectedGroupId = 2;
    if (typeof directEvents !== 'undefined' && directEvents.includes("View Signup Page")) {
        amplitudeWebengageLoad(selectedGroupId);
        WebengageTracking.eventReq.triggerWebengageEvent('View Signup Page', obj);
    }
    signupBtn.addEventListener('click', function() {
        var obj = {
            type: signupBtn.getAttribute('data-clickedType')
        }
        if (typeof directEvents !== 'undefined' && directEvents.includes("Start Signup")) {
            amplitudeWebengageLoad(selectedGroupId);
            WebengageTracking.eventReq.triggerWebengageEvent('Start Signup', obj);
        }
        window.location.href = 'register-email';
    });
 
    const googleLoginButtons = document.querySelectorAll('#google-login, #facebook-login');
    googleLoginButtons.forEach(button => {
        button.addEventListener('click', e => {
            var obj = {
                type: button.getAttribute('data-clickedType')
            }
            if (typeof directEvents !== 'undefined' && directEvents.includes("Start Signup")) {
                amplitudeWebengageLoad(selectedGroupId);
                WebengageTracking.eventReq.triggerWebengageEvent('Start Signup', obj);
            }
            newTabSocialLoginClicked(e);
        });
    });
    
    const otherLoginButtons = document.querySelectorAll('#linkedin-login, #SignInWithApple-login');
    otherLoginButtons.forEach(button => {
        button.addEventListener('click', e => {
            var obj = {
                type: button.getAttribute('data-clickedType')
            }
            if (typeof directEvents !== 'undefined' && directEvents.includes("Start Signup")) {
                amplitudeWebengageLoad(selectedGroupId);
                WebengageTracking.eventReq.triggerWebengageEvent('Start Signup', obj);
            }
            sameTabSocialLoginClicked(e);
        });
    });

    
  });
