const timezoneData = [
    {
        "text": "Niue:",
        "children": [{
            "id": "Pacific/Niue",
            "name": "Pacific/Niue (UTC-11:00)",
            "offset": "UTC-11:00",
            "nOffset": -660
        }],
        "firstNOffset": -660
    }, {
        "text": "American Samoa:",
        "children": [{
            "id": "Pacific/Pago_Pago",
            "name": "Pacific/Pago Pago (UTC-11:00)",
            "offset": "UTC-11:00",
            "nOffset": -660
        }],
        "firstNOffset": -660
    }, {
        "text": "United States Outlying Islands:",
        "children": [{
            "id": "Pacific/Midway",
            "name": "Pacific/Midway (UTC-11:00)",
            "offset": "UTC-11:00",
            "nOffset": -660
        }, {
            "id": "Pacific/Johnston",
            "name": "Pacific/Johnston (UTC-10:00)",
            "offset": "UTC-10:00",
            "nOffset": -600
        }, {
            "id": "Pacific/Wake",
            "name": "Pacific/Wake (UTC+12:00)",
            "offset": "UTC+12:00",
            "nOffset": 720
        }],
        "firstNOffset": -600
    }, {
        "text": "Cook Islands:",
        "children": [{
            "id": "Pacific/Rarotonga",
            "name": "Pacific/Rarotonga (UTC-10:00)",
            "offset": "UTC-10:00",
            "nOffset": -600
        }],
        "firstNOffset": -600
    }, {
        "text": "United States:",
        "children": [{
            "id": "Pacific/Honolulu",
            "name": "Pacific/Honolulu (UTC-10:00)",
            "offset": "UTC-10:00",
            "nOffset": -600
        }, {
            "id": "America/Adak",
            "name": "America/Adak (UTC-09:00)",
            "offset": "UTC-09:00",
            "nOffset": -540
        }, {
            "id": "America/Anchorage",
            "name": "America/Anchorage (UTC-08:00)",
            "offset": "UTC-08:00",
            "nOffset": -480
        }, {
            "id": "America/Juneau",
            "name": "America/Juneau (UTC-08:00)",
            "offset": "UTC-08:00",
            "nOffset": -480
        }, {
            "id": "America/Metlakatla",
            "name": "America/Metlakatla (UTC-08:00)",
            "offset": "UTC-08:00",
            "nOffset": -480
        }, {
            "id": "America/Nome",
            "name": "America/Nome (UTC-08:00)",
            "offset": "UTC-08:00",
            "nOffset": -480
        }, {
            "id": "America/Sitka",
            "name": "America/Sitka (UTC-08:00)",
            "offset": "UTC-08:00",
            "nOffset": -480
        }, {
            "id": "America/Yakutat",
            "name": "America/Yakutat (UTC-08:00)",
            "offset": "UTC-08:00",
            "nOffset": -480
        }, {
            "id": "America/Los_Angeles",
            "name": "America/Los Angeles (UTC-07:00)",
            "offset": "UTC-07:00",
            "nOffset": -420
        }, {
            "id": "America/Phoenix",
            "name": "America/Phoenix (UTC-07:00)",
            "offset": "UTC-07:00",
            "nOffset": -420
        }, {
            "id": "America/Boise",
            "name": "America/Boise (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }, {
            "id": "America/Denver",
            "name": "America/Denver (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }, {
            "id": "America/Chicago",
            "name": "America/Chicago (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Indiana/Knox",
            "name": "America/Indiana/Knox (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Indiana/Tell_City",
            "name": "America/Indiana/Tell City (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Menominee",
            "name": "America/Menominee (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/North_Dakota/Beulah",
            "name": "America/North Dakota/Beulah (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/North_Dakota/Center",
            "name": "America/North Dakota/Center (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/North_Dakota/New_Salem",
            "name": "America/North Dakota/New Salem (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Detroit",
            "name": "America/Detroit (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Indiana/Indianapolis",
            "name": "America/Indiana/Indianapolis (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Indiana/Marengo",
            "name": "America/Indiana/Marengo (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Indiana/Petersburg",
            "name": "America/Indiana/Petersburg (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Indiana/Vevay",
            "name": "America/Indiana/Vevay (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Indiana/Vincennes",
            "name": "America/Indiana/Vincennes (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Indiana/Winamac",
            "name": "America/Indiana/Winamac (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Kentucky/Louisville",
            "name": "America/Kentucky/Louisville (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Kentucky/Monticello",
            "name": "America/Kentucky/Monticello (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/New_York",
            "name": "America/New York (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -540
    }, {
        "text": "French Polynesia:",
        "children": [{
            "id": "Pacific/Tahiti",
            "name": "Pacific/Tahiti (UTC-10:00)",
            "offset": "UTC-10:00",
            "nOffset": -600
        }, {
            "id": "Pacific/Marquesas",
            "name": "Pacific/Marquesas (UTC-09:30)",
            "offset": "UTC-09:30",
            "nOffset": -570
        }, {
            "id": "Pacific/Gambier",
            "name": "Pacific/Gambier (UTC-09:00)",
            "offset": "UTC-09:00",
            "nOffset": -540
        }],
        "firstNOffset": -540
    }, {
        "text": "Pitcairn:",
        "children": [{
            "id": "Pacific/Pitcairn",
            "name": "Pacific/Pitcairn (UTC-08:00)",
            "offset": "UTC-08:00",
            "nOffset": -480
        }],
        "firstNOffset": -480
    }, {
        "text": "Belize:",
        "children": [{
            "id": "America/Belize",
            "name": "America/Belize (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }],
        "firstNOffset": -360
    }, {
        "text": "Costa Rica:",
        "children": [{
            "id": "America/Costa_Rica",
            "name": "America/Costa Rica (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }],
        "firstNOffset": -360
    }, {
        "text": "El Salvador:",
        "children": [{
            "id": "America/El_Salvador",
            "name": "America/El Salvador (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }],
        "firstNOffset": -360
    }, {
        "text": "Guatemala:",
        "children": [{
            "id": "America/Guatemala",
            "name": "America/Guatemala (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }],
        "firstNOffset": -360
    }, {
        "text": "Nicaragua:",
        "children": [{
            "id": "America/Managua",
            "name": "America/Managua (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }],
        "firstNOffset": -360
    }, {
        "text": "Honduras:",
        "children": [{
            "id": "America/Tegucigalpa",
            "name": "America/Tegucigalpa (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }],
        "firstNOffset": -360
    }, {
        "text": "Canada:",
        "children": [{
            "id": "America/Creston",
            "name": "America/Creston (UTC-07:00)",
            "offset": "UTC-07:00",
            "nOffset": -420
        }, {
            "id": "America/Dawson",
            "name": "America/Dawson (UTC-07:00)",
            "offset": "UTC-07:00",
            "nOffset": -420
        }, {
            "id": "America/Dawson_Creek",
            "name": "America/Dawson Creek (UTC-07:00)",
            "offset": "UTC-07:00",
            "nOffset": -420
        }, {
            "id": "America/Vancouver",
            "name": "America/Vancouver (UTC-07:00)",
            "offset": "UTC-07:00",
            "nOffset": -420
        }, {
            "id": "America/Whitehorse",
            "name": "America/Whitehorse (UTC-07:00)",
            "offset": "UTC-07:00",
            "nOffset": -420
        }, {
            "id": "America/Cambridge_Bay",
            "name": "America/Cambridge Bay (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }, {
            "id": "America/Edmonton",
            "name": "America/Edmonton (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }, {
            "id": "America/Inuvik",
            "name": "America/Inuvik (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }, {
            "id": "America/Regina",
            "name": "America/Regina (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }, {
            "id": "America/Swift_Current",
            "name": "America/Swift Current (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }, {
            "id": "America/Yellowknife",
            "name": "America/Yellowknife (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }, {
            "id": "America/Atikokan",
            "name": "America/Atikokan (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Rainy_River",
            "name": "America/Rainy River (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Rankin_Inlet",
            "name": "America/Rankin Inlet (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Resolute",
            "name": "America/Resolute (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Winnipeg",
            "name": "America/Winnipeg (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Blanc-Sablon",
            "name": "America/Blanc-Sablon (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Iqaluit",
            "name": "America/Iqaluit (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Montreal",
            "name": "America/Montreal (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Nipigon",
            "name": "America/Nipigon (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Pangnirtung",
            "name": "America/Pangnirtung (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Thunder_Bay",
            "name": "America/Thunder Bay (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Toronto",
            "name": "America/Toronto (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Glace_Bay",
            "name": "America/Glace Bay (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Goose_Bay",
            "name": "America/Goose Bay (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Halifax",
            "name": "America/Halifax (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Moncton",
            "name": "America/Moncton (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/St_Johns",
            "name": "America/St Johns (UTC-02:30)",
            "offset": "UTC-02:30",
            "nOffset": -150
        }],
        "firstNOffset": -300
    }, {
        "text": "Mexico:",
        "children": [{
            "id": "America/Hermosillo",
            "name": "America/Hermosillo (UTC-07:00)",
            "offset": "UTC-07:00",
            "nOffset": -420
        }, {
            "id": "America/Santa_Isabel",
            "name": "America/Santa Isabel (UTC-07:00)",
            "offset": "UTC-07:00",
            "nOffset": -420
        }, {
            "id": "America/Tijuana",
            "name": "America/Tijuana (UTC-07:00)",
            "offset": "UTC-07:00",
            "nOffset": -420
        }, {
            "id": "America/Chihuahua",
            "name": "America/Chihuahua (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }, {
            "id": "America/Mazatlan",
            "name": "America/Mazatlan (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }, {
            "id": "America/Ojinaga",
            "name": "America/Ojinaga (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }, {
            "id": "America/Bahia_Banderas",
            "name": "America/Bahia Banderas (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Cancun",
            "name": "America/Cancun (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Matamoros",
            "name": "America/Matamoros (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Merida",
            "name": "America/Merida (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Mexico_City",
            "name": "America/Mexico City (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Monterrey",
            "name": "America/Monterrey (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }],
        "firstNOffset": -300
    }, {
        "text": "Colombia:",
        "children": [{
            "id": "America/Bogota",
            "name": "America/Bogota (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }],
        "firstNOffset": -300
    }, {
        "text": "Ecuador:",
        "children": [{
            "id": "Pacific/Galapagos",
            "name": "Pacific/Galapagos (UTC-06:00)",
            "offset": "UTC-06:00",
            "nOffset": -360
        }, {
            "id": "America/Guayaquil",
            "name": "America/Guayaquil (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }],
        "firstNOffset": -300
    }, {
        "text": "Jamaica:",
        "children": [{
            "id": "America/Jamaica",
            "name": "America/Jamaica (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }],
        "firstNOffset": -300
    }, {
        "text": "Peru:",
        "children": [{
            "id": "America/Lima",
            "name": "America/Lima (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }],
        "firstNOffset": -300
    }, {
        "text": "Panama:",
        "children": [{
            "id": "America/Panama",
            "name": "America/Panama (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }],
        "firstNOffset": -300
    }, {
        "text": "Venezuela:",
        "children": [{
            "id": "America/Caracas",
            "name": "America/Caracas (UTC-04:30)",
            "offset": "UTC-04:30",
            "nOffset": -270
        }],
        "firstNOffset": -270
    }, {
        "text": "Anguilla:",
        "children": [{
            "id": "America/Anguilla",
            "name": "America/Anguilla (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Antigua And Barbuda:",
        "children": [{
            "id": "America/Antigua",
            "name": "America/Antigua (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Aruba:",
        "children": [{
            "id": "America/Aruba",
            "name": "America/Aruba (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Paraguay:",
        "children": [{
            "id": "America/Asuncion",
            "name": "America/Asuncion (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Barbados:",
        "children": [{
            "id": "America/Barbados",
            "name": "America/Barbados (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Cayman Islands:",
        "children": [{
            "id": "America/Cayman",
            "name": "America/Cayman (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Curacao:",
        "children": [{
            "id": "America/Curacao",
            "name": "America/Curacao (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Dominica:",
        "children": [{
            "id": "America/Dominica",
            "name": "America/Dominica (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Turks And Caicos Islands:",
        "children": [{
            "id": "America/Grand_Turk",
            "name": "America/Grand Turk (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Grenada:",
        "children": [{
            "id": "America/Grenada",
            "name": "America/Grenada (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Guadeloupe:",
        "children": [{
            "id": "America/Guadeloupe",
            "name": "America/Guadeloupe (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Guyana:",
        "children": [{
            "id": "America/Guyana",
            "name": "America/Guyana (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Cuba:",
        "children": [{
            "id": "America/Havana",
            "name": "America/Havana (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Bonaire:",
        "children": [{
            "id": "America/Kralendijk",
            "name": "America/Kralendijk (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Bolivia:",
        "children": [{
            "id": "America/La_Paz",
            "name": "America/La Paz (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Sint Maarten:",
        "children": [{
            "id": "America/Lower_Princes",
            "name": "America/Lower Princes (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Saint Martin:",
        "children": [{
            "id": "America/Marigot",
            "name": "America/Marigot (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Martinique:",
        "children": [{
            "id": "America/Martinique",
            "name": "America/Martinique (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Montserrat:",
        "children": [{
            "id": "America/Montserrat",
            "name": "America/Montserrat (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Bahamas:",
        "children": [{
            "id": "America/Nassau",
            "name": "America/Nassau (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Haiti:",
        "children": [{
            "id": "America/Port-au-Prince",
            "name": "America/Port-au-Prince (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Trinidad And Tobago:",
        "children": [{
            "id": "America/Port_of_Spain",
            "name": "America/Port of Spain (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Puerto Rico:",
        "children": [{
            "id": "America/Puerto_Rico",
            "name": "America/Puerto Rico (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Dominican Republic:",
        "children": [{
            "id": "America/Santo_Domingo",
            "name": "America/Santo Domingo (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Saint Barthelemy:",
        "children": [{
            "id": "America/St_Barthelemy",
            "name": "America/St Barthelemy (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Saint Kitts And Nevis:",
        "children": [{
            "id": "America/St_Kitts",
            "name": "America/St Kitts (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Saint Lucia:",
        "children": [{
            "id": "America/St_Lucia",
            "name": "America/St Lucia (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Virgin Islands (U.S.):",
        "children": [{
            "id": "America/St_Thomas",
            "name": "America/St Thomas (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Saint Vincent And Grenadines:",
        "children": [{
            "id": "America/St_Vincent",
            "name": "America/St Vincent (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Virgin Islands (British):",
        "children": [{
            "id": "America/Tortola",
            "name": "America/Tortola (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }],
        "firstNOffset": -240
    }, {
        "text": "Brazil:",
        "children": [{
            "id": "America/Eirunepe",
            "name": "America/Eirunepe (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Rio_Branco",
            "name": "America/Rio Branco (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Boa_Vista",
            "name": "America/Boa Vista (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Campo_Grande",
            "name": "America/Campo Grande (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Cuiaba",
            "name": "America/Cuiaba (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Manaus",
            "name": "America/Manaus (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Porto_Velho",
            "name": "America/Porto Velho (UTC-04:00)",
            "offset": "UTC-04:00",
            "nOffset": -240
        }, {
            "id": "America/Araguaina",
            "name": "America/Araguaina (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Bahia",
            "name": "America/Bahia (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Belem",
            "name": "America/Belem (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Fortaleza",
            "name": "America/Fortaleza (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Maceio",
            "name": "America/Maceio (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Recife",
            "name": "America/Recife (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Santarem",
            "name": "America/Santarem (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Sao_Paulo",
            "name": "America/Sao Paulo (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Noronha",
            "name": "America/Noronha (UTC-02:00)",
            "offset": "UTC-02:00",
            "nOffset": -120
        }],
        "firstNOffset": -180
    }, {
        "text": "Argentina:",
        "children": [{
            "id": "America/Argentina/Buenos_Aires",
            "name": "America/Argentina/Buenos Aires (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Argentina/Catamarca",
            "name": "America/Argentina/Catamarca (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Argentina/Cordoba",
            "name": "America/Argentina/Cordoba (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Argentina/Jujuy",
            "name": "America/Argentina/Jujuy (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Argentina/La_Rioja",
            "name": "America/Argentina/La Rioja (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Argentina/Mendoza",
            "name": "America/Argentina/Mendoza (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Argentina/Rio_Gallegos",
            "name": "America/Argentina/Rio Gallegos (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Argentina/Salta",
            "name": "America/Argentina/Salta (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Argentina/San_Juan",
            "name": "America/Argentina/San Juan (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Argentina/San_Luis",
            "name": "America/Argentina/San Luis (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Argentina/Tucuman",
            "name": "America/Argentina/Tucuman (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Argentina/Ushuaia",
            "name": "America/Argentina/Ushuaia (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }],
        "firstNOffset": -180
    }, {
        "text": "French Guiana:",
        "children": [{
            "id": "America/Cayenne",
            "name": "America/Cayenne (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }],
        "firstNOffset": -180
    }, {
        "text": "Uruguay:",
        "children": [{
            "id": "America/Montevideo",
            "name": "America/Montevideo (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }],
        "firstNOffset": -180
    }, {
        "text": "Suriname:",
        "children": [{
            "id": "America/Paramaribo",
            "name": "America/Paramaribo (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }],
        "firstNOffset": -180
    }, {
        "text": "Chile:",
        "children": [{
            "id": "Pacific/Easter",
            "name": "Pacific/Easter (UTC-05:00)",
            "offset": "UTC-05:00",
            "nOffset": -300
        }, {
            "id": "America/Santiago",
            "name": "America/Santiago (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }],
        "firstNOffset": -180
    }, {
        "text": "Bermuda:",
        "children": [{
            "id": "Atlantic/Bermuda",
            "name": "Atlantic/Bermuda (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }],
        "firstNOffset": -180
    }, {
        "text": "Falkland Islands (Malvinas):",
        "children": [{
            "id": "Atlantic/Stanley",
            "name": "Atlantic/Stanley (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }],
        "firstNOffset": -180
    }, {
        "text": "Saint Pierre And Miquelon:",
        "children": [{
            "id": "America/Miquelon",
            "name": "America/Miquelon (UTC-02:00)",
            "offset": "UTC-02:00",
            "nOffset": -120
        }],
        "firstNOffset": -120
    }, {
        "text": "South Georgia And Sandwich Isl.:",
        "children": [{
            "id": "Atlantic/South_Georgia",
            "name": "Atlantic/South Georgia (UTC-02:00)",
            "offset": "UTC-02:00",
            "nOffset": -120
        }],
        "firstNOffset": -120
    }, {
        "text": "Cape Verde:",
        "children": [{
            "id": "Atlantic/Cape_Verde",
            "name": "Atlantic/Cape Verde (UTC-01:00)",
            "offset": "UTC-01:00",
            "nOffset": -60
        }],
        "firstNOffset": -60
    }, {
        "text": "Cote D'Ivoire:",
        "children": [{
            "id": "Africa/Abidjan",
            "name": "Africa/Abidjan (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Ghana:",
        "children": [{
            "id": "Africa/Accra",
            "name": "Africa/Accra (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Mali:",
        "children": [{
            "id": "Africa/Bamako",
            "name": "Africa/Bamako (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Gambia:",
        "children": [{
            "id": "Africa/Banjul",
            "name": "Africa/Banjul (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Guinea-Bissau:",
        "children": [{
            "id": "Africa/Bissau",
            "name": "Africa/Bissau (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Guinea:",
        "children": [{
            "id": "Africa/Conakry",
            "name": "Africa/Conakry (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Senegal:",
        "children": [{
            "id": "Africa/Dakar",
            "name": "Africa/Dakar (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Sierra Leone:",
        "children": [{
            "id": "Africa/Freetown",
            "name": "Africa/Freetown (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Togo:",
        "children": [{
            "id": "Africa/Lome",
            "name": "Africa/Lome (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Liberia:",
        "children": [{
            "id": "Africa/Monrovia",
            "name": "Africa/Monrovia (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Mauritania:",
        "children": [{
            "id": "Africa/Nouakchott",
            "name": "Africa/Nouakchott (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Burkina Faso:",
        "children": [{
            "id": "Africa/Ouagadougou",
            "name": "Africa/Ouagadougou (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Sao Tome And Principe:",
        "children": [{
            "id": "Africa/Sao_Tome",
            "name": "Africa/Sao Tome (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Greenland:",
        "children": [{
            "id": "America/Thule",
            "name": "America/Thule (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "America/Godthab",
            "name": "America/Godthab (UTC-02:00)",
            "offset": "UTC-02:00",
            "nOffset": -120
        }, {
            "id": "America/Danmarkshavn",
            "name": "America/Danmarkshavn (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }, {
            "id": "America/Scoresbysund",
            "name": "America/Scoresbysund (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Portugal:",
        "children": [{
            "id": "Atlantic/Azores",
            "name": "Atlantic/Azores (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }, {
            "id": "Atlantic/Madeira",
            "name": "Atlantic/Madeira (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }, {
            "id": "Europe/Lisbon",
            "name": "Europe/Lisbon (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 0
    }, {
        "text": "Iceland:",
        "children": [{
            "id": "Atlantic/Reykjavik",
            "name": "Atlantic/Reykjavik (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Saint Helena:",
        "children": [{
            "id": "Atlantic/St_Helena",
            "name": "Atlantic/St Helena (UTC+00:00)",
            "offset": "UTC+00:00",
            "nOffset": 0
        }],
        "firstNOffset": 0
    }, {
        "text": "Algeria:",
        "children": [{
            "id": "Africa/Algiers",
            "name": "Africa/Algiers (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Central African Republic:",
        "children": [{
            "id": "Africa/Bangui",
            "name": "Africa/Bangui (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Congo:",
        "children": [{
            "id": "Africa/Brazzaville",
            "name": "Africa/Brazzaville (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Morocco:",
        "children": [{
            "id": "Africa/Casablanca",
            "name": "Africa/Casablanca (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Cameroon:",
        "children": [{
            "id": "Africa/Douala",
            "name": "Africa/Douala (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Western Sahara:",
        "children": [{
            "id": "Africa/El_Aaiun",
            "name": "Africa/El Aaiun (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Congo (Democratic Republic):",
        "children": [{
            "id": "Africa/Kinshasa",
            "name": "Africa/Kinshasa (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }, {
            "id": "Africa/Lubumbashi",
            "name": "Africa/Lubumbashi (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 60
    }, {
        "text": "Nigeria:",
        "children": [{
            "id": "Africa/Lagos",
            "name": "Africa/Lagos (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Gabon:",
        "children": [{
            "id": "Africa/Libreville",
            "name": "Africa/Libreville (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Angola:",
        "children": [{
            "id": "Africa/Luanda",
            "name": "Africa/Luanda (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Equatorial Guinea:",
        "children": [{
            "id": "Africa/Malabo",
            "name": "Africa/Malabo (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Chad:",
        "children": [{
            "id": "Africa/Ndjamena",
            "name": "Africa/Ndjamena (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Niger:",
        "children": [{
            "id": "Africa/Niamey",
            "name": "Africa/Niamey (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Benin:",
        "children": [{
            "id": "Africa/Porto-Novo",
            "name": "Africa/Porto-Novo (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Tunisia:",
        "children": [{
            "id": "Africa/Tunis",
            "name": "Africa/Tunis (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Faroe Islands:",
        "children": [{
            "id": "Atlantic/Faroe",
            "name": "Atlantic/Faroe (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Ireland:",
        "children": [{
            "id": "Europe/Dublin",
            "name": "Europe/Dublin (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Guernsey:",
        "children": [{
            "id": "Europe/Guernsey",
            "name": "Europe/Guernsey (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Isle Of Man:",
        "children": [{
            "id": "Europe/Isle_of_Man",
            "name": "Europe/Isle of Man (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Jersey:",
        "children": [{
            "id": "Europe/Jersey",
            "name": "Europe/Jersey (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "United Kingdom:",
        "children": [{
            "id": "Europe/London",
            "name": "Europe/London (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }],
        "firstNOffset": 60
    }, {
        "text": "Malawi:",
        "children": [{
            "id": "Africa/Blantyre",
            "name": "Africa/Blantyre (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Burundi:",
        "children": [{
            "id": "Africa/Bujumbura",
            "name": "Africa/Bujumbura (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Egypt:",
        "children": [{
            "id": "Africa/Cairo",
            "name": "Africa/Cairo (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Spain:",
        "children": [{
            "id": "Atlantic/Canary",
            "name": "Atlantic/Canary (UTC+01:00)",
            "offset": "UTC+01:00",
            "nOffset": 60
        }, {
            "id": "Africa/Ceuta",
            "name": "Africa/Ceuta (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }, {
            "id": "Europe/Madrid",
            "name": "Europe/Madrid (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Botswana:",
        "children": [{
            "id": "Africa/Gaborone",
            "name": "Africa/Gaborone (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Zimbabwe:",
        "children": [{
            "id": "Africa/Harare",
            "name": "Africa/Harare (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "South Africa:",
        "children": [{
            "id": "Africa/Johannesburg",
            "name": "Africa/Johannesburg (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Rwanda:",
        "children": [{
            "id": "Africa/Kigali",
            "name": "Africa/Kigali (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Zambia:",
        "children": [{
            "id": "Africa/Lusaka",
            "name": "Africa/Lusaka (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Mozambique:",
        "children": [{
            "id": "Africa/Maputo",
            "name": "Africa/Maputo (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Lesotho:",
        "children": [{
            "id": "Africa/Maseru",
            "name": "Africa/Maseru (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Swaziland:",
        "children": [{
            "id": "Africa/Mbabane",
            "name": "Africa/Mbabane (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Libyan Arab Jamahiriya:",
        "children": [{
            "id": "Africa/Tripoli",
            "name": "Africa/Tripoli (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Namibia:",
        "children": [{
            "id": "Africa/Windhoek",
            "name": "Africa/Windhoek (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Svalbard And Jan Mayen:",
        "children": [{
            "id": "Arctic/Longyearbyen",
            "name": "Arctic/Longyearbyen (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Netherlands:",
        "children": [{
            "id": "Europe/Amsterdam",
            "name": "Europe/Amsterdam (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Andorra:",
        "children": [{
            "id": "Europe/Andorra",
            "name": "Europe/Andorra (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Serbia:",
        "children": [{
            "id": "Europe/Belgrade",
            "name": "Europe/Belgrade (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Germany:",
        "children": [{
            "id": "Europe/Berlin",
            "name": "Europe/Berlin (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }, {
            "id": "Europe/Busingen",
            "name": "Europe/Busingen (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Slovakia:",
        "children": [{
            "id": "Europe/Bratislava",
            "name": "Europe/Bratislava (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Belgium:",
        "children": [{
            "id": "Europe/Brussels",
            "name": "Europe/Brussels (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Hungary:",
        "children": [{
            "id": "Europe/Budapest",
            "name": "Europe/Budapest (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Denmark:",
        "children": [{
            "id": "Europe/Copenhagen",
            "name": "Europe/Copenhagen (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Gibraltar:",
        "children": [{
            "id": "Europe/Gibraltar",
            "name": "Europe/Gibraltar (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Slovenia:",
        "children": [{
            "id": "Europe/Ljubljana",
            "name": "Europe/Ljubljana (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Luxembourg:",
        "children": [{
            "id": "Europe/Luxembourg",
            "name": "Europe/Luxembourg (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Malta:",
        "children": [{
            "id": "Europe/Malta",
            "name": "Europe/Malta (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Monaco:",
        "children": [{
            "id": "Europe/Monaco",
            "name": "Europe/Monaco (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Norway:",
        "children": [{
            "id": "Europe/Oslo",
            "name": "Europe/Oslo (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "France:",
        "children": [{
            "id": "Europe/Paris",
            "name": "Europe/Paris (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Montenegro:",
        "children": [{
            "id": "Europe/Podgorica",
            "name": "Europe/Podgorica (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Czech Republic:",
        "children": [{
            "id": "Europe/Prague",
            "name": "Europe/Prague (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Italy:",
        "children": [{
            "id": "Europe/Rome",
            "name": "Europe/Rome (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "San Marino:",
        "children": [{
            "id": "Europe/San_Marino",
            "name": "Europe/San Marino (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Bosnia And Herzegovina:",
        "children": [{
            "id": "Europe/Sarajevo",
            "name": "Europe/Sarajevo (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Macedonia:",
        "children": [{
            "id": "Europe/Skopje",
            "name": "Europe/Skopje (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Sweden:",
        "children": [{
            "id": "Europe/Stockholm",
            "name": "Europe/Stockholm (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Albania:",
        "children": [{
            "id": "Europe/Tirane",
            "name": "Europe/Tirane (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Liechtenstein:",
        "children": [{
            "id": "Europe/Vaduz",
            "name": "Europe/Vaduz (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Holy See (Vatican City State):",
        "children": [{
            "id": "Europe/Vatican",
            "name": "Europe/Vatican (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Austria:",
        "children": [{
            "id": "Europe/Vienna",
            "name": "Europe/Vienna (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Poland:",
        "children": [{
            "id": "Europe/Warsaw",
            "name": "Europe/Warsaw (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Croatia:",
        "children": [{
            "id": "Europe/Zagreb",
            "name": "Europe/Zagreb (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Switzerland:",
        "children": [{
            "id": "Europe/Zurich",
            "name": "Europe/Zurich (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }],
        "firstNOffset": 120
    }, {
        "text": "Ethiopia:",
        "children": [{
            "id": "Africa/Addis_Ababa",
            "name": "Africa/Addis Ababa (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Eritrea:",
        "children": [{
            "id": "Africa/Asmara",
            "name": "Africa/Asmara (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Tanzania:",
        "children": [{
            "id": "Africa/Dar_es_Salaam",
            "name": "Africa/Dar es Salaam (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Djibouti:",
        "children": [{
            "id": "Africa/Djibouti",
            "name": "Africa/Djibouti (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "South Sudan:",
        "children": [{
            "id": "Africa/Juba",
            "name": "Africa/Juba (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Uganda:",
        "children": [{
            "id": "Africa/Kampala",
            "name": "Africa/Kampala (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Sudan:",
        "children": [{
            "id": "Africa/Khartoum",
            "name": "Africa/Khartoum (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Somalia:",
        "children": [{
            "id": "Africa/Mogadishu",
            "name": "Africa/Mogadishu (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Kenya:",
        "children": [{
            "id": "Africa/Nairobi",
            "name": "Africa/Nairobi (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Yemen:",
        "children": [{
            "id": "Asia/Aden",
            "name": "Asia/Aden (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Jordan:",
        "children": [{
            "id": "Asia/Amman",
            "name": "Asia/Amman (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Iraq:",
        "children": [{
            "id": "Asia/Baghdad",
            "name": "Asia/Baghdad (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Bahrain:",
        "children": [{
            "id": "Asia/Bahrain",
            "name": "Asia/Bahrain (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Lebanon:",
        "children": [{
            "id": "Asia/Beirut",
            "name": "Asia/Beirut (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Syrian Arab Republic:",
        "children": [{
            "id": "Asia/Damascus",
            "name": "Asia/Damascus (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Palestinian Territory (Occupied):",
        "children": [{
            "id": "Asia/Gaza",
            "name": "Asia/Gaza (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }, {
            "id": "Asia/Hebron",
            "name": "Asia/Hebron (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Israel:",
        "children": [{
            "id": "Asia/Jerusalem",
            "name": "Asia/Jerusalem (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Kuwait:",
        "children": [{
            "id": "Asia/Kuwait",
            "name": "Asia/Kuwait (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Cyprus:",
        "children": [{
            "id": "Asia/Nicosia",
            "name": "Asia/Nicosia (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Qatar:",
        "children": [{
            "id": "Asia/Qatar",
            "name": "Asia/Qatar (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Saudi Arabia:",
        "children": [{
            "id": "Asia/Riyadh",
            "name": "Asia/Riyadh (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Greece:",
        "children": [{
            "id": "Europe/Athens",
            "name": "Europe/Athens (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Romania:",
        "children": [{
            "id": "Europe/Bucharest",
            "name": "Europe/Bucharest (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Moldova:",
        "children": [{
            "id": "Europe/Chisinau",
            "name": "Europe/Chisinau (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Finland:",
        "children": [{
            "id": "Europe/Helsinki",
            "name": "Europe/Helsinki (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Turkey:",
        "children": [{
            "id": "Europe/Istanbul",
            "name": "Europe/Istanbul (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Ukraine:",
        "children": [{
            "id": "Europe/Kiev",
            "name": "Europe/Kiev (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }, {
            "id": "Europe/Uzhgorod",
            "name": "Europe/Uzhgorod (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }, {
            "id": "Europe/Zaporozhye",
            "name": "Europe/Zaporozhye (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Aland Islands:",
        "children": [{
            "id": "Europe/Mariehamn",
            "name": "Europe/Mariehamn (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Belarus:",
        "children": [{
            "id": "Europe/Minsk",
            "name": "Europe/Minsk (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Latvia:",
        "children": [{
            "id": "Europe/Riga",
            "name": "Europe/Riga (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Bulgaria:",
        "children": [{
            "id": "Europe/Sofia",
            "name": "Europe/Sofia (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Estonia:",
        "children": [{
            "id": "Europe/Tallinn",
            "name": "Europe/Tallinn (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Lithuania:",
        "children": [{
            "id": "Europe/Vilnius",
            "name": "Europe/Vilnius (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Madagascar:",
        "children": [{
            "id": "Indian/Antananarivo",
            "name": "Indian/Antananarivo (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Comoros:",
        "children": [{
            "id": "Indian/Comoro",
            "name": "Indian/Comoro (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Mayotte:",
        "children": [{
            "id": "Indian/Mayotte",
            "name": "Indian/Mayotte (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }],
        "firstNOffset": 180
    }, {
        "text": "Iran (Islamic Republic Of):",
        "children": [{
            "id": "Asia/Tehran",
            "name": "Asia/Tehran (UTC+03:30)",
            "offset": "UTC+03:30",
            "nOffset": 210
        }],
        "firstNOffset": 210
    }, {
        "text": "United Arab Emirates:",
        "children": [{
            "id": "Asia/Dubai",
            "name": "Asia/Dubai (UTC+04:00)",
            "offset": "UTC+04:00",
            "nOffset": 240
        }],
        "firstNOffset": 240
    }, {
        "text": "Oman:",
        "children": [{
            "id": "Asia/Muscat",
            "name": "Asia/Muscat (UTC+04:00)",
            "offset": "UTC+04:00",
            "nOffset": 240
        }],
        "firstNOffset": 240
    }, {
        "text": "Georgia:",
        "children": [{
            "id": "Asia/Tbilisi",
            "name": "Asia/Tbilisi (UTC+04:00)",
            "offset": "UTC+04:00",
            "nOffset": 240
        }],
        "firstNOffset": 240
    }, {
        "text": "Armenia:",
        "children": [{
            "id": "Asia/Yerevan",
            "name": "Asia/Yerevan (UTC+04:00)",
            "offset": "UTC+04:00",
            "nOffset": 240
        }],
        "firstNOffset": 240
    }, {
        "text": "Seychelles:",
        "children": [{
            "id": "Indian/Mahe",
            "name": "Indian/Mahe (UTC+04:00)",
            "offset": "UTC+04:00",
            "nOffset": 240
        }],
        "firstNOffset": 240
    }, {
        "text": "Mauritius:",
        "children": [{
            "id": "Indian/Mauritius",
            "name": "Indian/Mauritius (UTC+04:00)",
            "offset": "UTC+04:00",
            "nOffset": 240
        }],
        "firstNOffset": 240
    }, {
        "text": "Reunion:",
        "children": [{
            "id": "Indian/Reunion",
            "name": "Indian/Reunion (UTC+04:00)",
            "offset": "UTC+04:00",
            "nOffset": 240
        }],
        "firstNOffset": 240
    }, {
        "text": "Afghanistan:",
        "children": [{
            "id": "Asia/Kabul",
            "name": "Asia/Kabul (UTC+04:30)",
            "offset": "UTC+04:30",
            "nOffset": 270
        }],
        "firstNOffset": 270
    }, {
        "text": "Turkmenistan:",
        "children": [{
            "id": "Asia/Ashgabat",
            "name": "Asia/Ashgabat (UTC+05:00)",
            "offset": "UTC+05:00",
            "nOffset": 300
        }],
        "firstNOffset": 300
    }, {
        "text": "Azerbaijan:",
        "children": [{
            "id": "Asia/Baku",
            "name": "Asia/Baku (UTC+05:00)",
            "offset": "UTC+05:00",
            "nOffset": 300
        }],
        "firstNOffset": 300
    }, {
        "text": "Tajikistan:",
        "children": [{
            "id": "Asia/Dushanbe",
            "name": "Asia/Dushanbe (UTC+05:00)",
            "offset": "UTC+05:00",
            "nOffset": 300
        }],
        "firstNOffset": 300
    }, {
        "text": "Pakistan:",
        "children": [{
            "id": "Asia/Karachi",
            "name": "Asia/Karachi (UTC+05:00)",
            "offset": "UTC+05:00",
            "nOffset": 300
        }],
        "firstNOffset": 300
    }, {
        "text": "Uzbekistan:",
        "children": [{
            "id": "Asia/Samarkand",
            "name": "Asia/Samarkand (UTC+05:00)",
            "offset": "UTC+05:00",
            "nOffset": 300
        }, {
            "id": "Asia/Tashkent",
            "name": "Asia/Tashkent (UTC+05:00)",
            "offset": "UTC+05:00",
            "nOffset": 300
        }],
        "firstNOffset": 300
    }, {
        "text": "French Southern Territories:",
        "children": [{
            "id": "Indian/Kerguelen",
            "name": "Indian/Kerguelen (UTC+05:00)",
            "offset": "UTC+05:00",
            "nOffset": 300
        }],
        "firstNOffset": 300
    }, {
        "text": "Maldives:",
        "children": [{
            "id": "Indian/Maldives",
            "name": "Indian/Maldives (UTC+05:00)",
            "offset": "UTC+05:00",
            "nOffset": 300
        }],
        "firstNOffset": 300
    }, {
        "text": "Sri Lanka:",
        "children": [{
            "id": "Asia/Colombo",
            "name": "Asia/Colombo (UTC+05:30)",
            "offset": "UTC+05:30",
            "nOffset": 330
        }],
        "firstNOffset": 330
    }, {
        "text": "India:",
        "children": [{
            "id": "Asia/Kolkata",
            "name": "Asia/Kolkata (UTC+05:30)",
            "offset": "UTC+05:30",
            "nOffset": 330
        }],
        "firstNOffset": 330
    }, {
        "text": "Nepal:",
        "children": [{
            "id": "Asia/Kathmandu",
            "name": "Asia/Kathmandu (UTC+05:45)",
            "offset": "UTC+05:45",
            "nOffset": 345
        }],
        "firstNOffset": 345
    }, {
        "text": "Kazakhstan:",
        "children": [{
            "id": "Asia/Aqtau",
            "name": "Asia/Aqtau (UTC+05:00)",
            "offset": "UTC+05:00",
            "nOffset": 300
        }, {
            "id": "Asia/Aqtobe",
            "name": "Asia/Aqtobe (UTC+05:00)",
            "offset": "UTC+05:00",
            "nOffset": 300
        }, {
            "id": "Asia/Oral",
            "name": "Asia/Oral (UTC+05:00)",
            "offset": "UTC+05:00",
            "nOffset": 300
        }, {
            "id": "Asia/Almaty",
            "name": "Asia/Almaty (UTC+06:00)",
            "offset": "UTC+06:00",
            "nOffset": 360
        }, {
            "id": "Asia/Qyzylorda",
            "name": "Asia/Qyzylorda (UTC+06:00)",
            "offset": "UTC+06:00",
            "nOffset": 360
        }],
        "firstNOffset": 360
    }, {
        "text": "Kyrgyzstan:",
        "children": [{
            "id": "Asia/Bishkek",
            "name": "Asia/Bishkek (UTC+06:00)",
            "offset": "UTC+06:00",
            "nOffset": 360
        }],
        "firstNOffset": 360
    }, {
        "text": "Bangladesh:",
        "children": [{
            "id": "Asia/Dhaka",
            "name": "Asia/Dhaka (UTC+06:00)",
            "offset": "UTC+06:00",
            "nOffset": 360
        }],
        "firstNOffset": 360
    }, {
        "text": "Bhutan:",
        "children": [{
            "id": "Asia/Thimphu",
            "name": "Asia/Thimphu (UTC+06:00)",
            "offset": "UTC+06:00",
            "nOffset": 360
        }],
        "firstNOffset": 360
    }, {
        "text": "British Indian Ocean Territory:",
        "children": [{
            "id": "Indian/Chagos",
            "name": "Indian/Chagos (UTC+06:00)",
            "offset": "UTC+06:00",
            "nOffset": 360
        }],
        "firstNOffset": 360
    }, {
        "text": "Myanmar:",
        "children": [{
            "id": "Asia/Rangoon",
            "name": "Asia/Rangoon (UTC+06:30)",
            "offset": "UTC+06:30",
            "nOffset": 390
        }],
        "firstNOffset": 390
    }, {
        "text": "Cocos (Keeling) Islands:",
        "children": [{
            "id": "Indian/Cocos",
            "name": "Indian/Cocos (UTC+06:30)",
            "offset": "UTC+06:30",
            "nOffset": 390
        }],
        "firstNOffset": 390
    }, {
        "text": "Thailand:",
        "children": [{
            "id": "Asia/Bangkok",
            "name": "Asia/Bangkok (UTC+07:00)",
            "offset": "UTC+07:00",
            "nOffset": 420
        }],
        "firstNOffset": 420
    }, {
        "text": "Viet Nam:",
        "children": [{
            "id": "Asia/Ho_Chi_Minh",
            "name": "Asia/Ho Chi Minh (UTC+07:00)",
            "offset": "UTC+07:00",
            "nOffset": 420
        }],
        "firstNOffset": 420
    }, {
        "text": "Indonesia:",
        "children": [{
            "id": "Asia/Jakarta",
            "name": "Asia/Jakarta (UTC+07:00)",
            "offset": "UTC+07:00",
            "nOffset": 420
        }, {
            "id": "Asia/Pontianak",
            "name": "Asia/Pontianak (UTC+07:00)",
            "offset": "UTC+07:00",
            "nOffset": 420
        }, {
            "id": "Asia/Makassar",
            "name": "Asia/Makassar (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }, {
            "id": "Asia/Jayapura",
            "name": "Asia/Jayapura (UTC+09:00)",
            "offset": "UTC+09:00",
            "nOffset": 540
        }],
        "firstNOffset": 420
    }, {
        "text": "Cambodia:",
        "children": [{
            "id": "Asia/Phnom_Penh",
            "name": "Asia/Phnom Penh (UTC+07:00)",
            "offset": "UTC+07:00",
            "nOffset": 420
        }],
        "firstNOffset": 420
    }, {
        "text": "Lao People's Democratic Republic:",
        "children": [{
            "id": "Asia/Vientiane",
            "name": "Asia/Vientiane (UTC+07:00)",
            "offset": "UTC+07:00",
            "nOffset": 420
        }],
        "firstNOffset": 420
    }, {
        "text": "Christmas Island:",
        "children": [{
            "id": "Indian/Christmas",
            "name": "Indian/Christmas (UTC+07:00)",
            "offset": "UTC+07:00",
            "nOffset": 420
        }],
        "firstNOffset": 420
    }, {
        "text": "Antarctica:",
        "children": [{
            "id": "Antarctica/Palmer",
            "name": "Antarctica/Palmer (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "Antarctica/Rothera",
            "name": "Antarctica/Rothera (UTC-03:00)",
            "offset": "UTC-03:00",
            "nOffset": -180
        }, {
            "id": "Antarctica/Troll",
            "name": "Antarctica/Troll (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }, {
            "id": "Antarctica/Syowa",
            "name": "Antarctica/Syowa (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }, {
            "id": "Antarctica/Mawson",
            "name": "Antarctica/Mawson (UTC+05:00)",
            "offset": "UTC+05:00",
            "nOffset": 300
        }, {
            "id": "Antarctica/Vostok",
            "name": "Antarctica/Vostok (UTC+06:00)",
            "offset": "UTC+06:00",
            "nOffset": 360
        }, {
            "id": "Antarctica/Davis",
            "name": "Antarctica/Davis (UTC+07:00)",
            "offset": "UTC+07:00",
            "nOffset": 420
        }, {
            "id": "Antarctica/Casey",
            "name": "Antarctica/Casey (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }, {
            "id": "Antarctica/DumontDUrville",
            "name": "Antarctica/DumontDUrville (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }, {
            "id": "Antarctica/McMurdo",
            "name": "Antarctica/McMurdo (UTC+13:00)",
            "offset": "UTC+13:00",
            "nOffset": 780
        }],
        "firstNOffset": 480
    }, {
        "text": "Brunei Darussalam:",
        "children": [{
            "id": "Asia/Brunei",
            "name": "Asia/Brunei (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }],
        "firstNOffset": 480
    }, {
        "text": "China:",
        "children": [{
            "id": "Asia/Kashgar",
            "name": "Asia/Kashgar (UTC+06:00)",
            "offset": "UTC+06:00",
            "nOffset": 360
        }, {
            "id": "Asia/Urumqi",
            "name": "Asia/Urumqi (UTC+06:00)",
            "offset": "UTC+06:00",
            "nOffset": 360
        }, {
            "id": "Asia/Chongqing",
            "name": "Asia/Chongqing (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }, {
            "id": "Asia/Harbin",
            "name": "Asia/Harbin (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }, {
            "id": "Asia/Shanghai",
            "name": "Asia/Shanghai (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }],
        "firstNOffset": 480
    }, {
        "text": "Hong Kong:",
        "children": [{
            "id": "Asia/Hong_Kong",
            "name": "Asia/Hong Kong (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }],
        "firstNOffset": 480
    }, {
        "text": "Malaysia:",
        "children": [{
            "id": "Asia/Kuala_Lumpur",
            "name": "Asia/Kuala Lumpur (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }, {
            "id": "Asia/Kuching",
            "name": "Asia/Kuching (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }],
        "firstNOffset": 480
    }, {
        "text": "Macao:",
        "children": [{
            "id": "Asia/Macau",
            "name": "Asia/Macau (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }],
        "firstNOffset": 480
    }, {
        "text": "Philippines:",
        "children": [{
            "id": "Asia/Manila",
            "name": "Asia/Manila (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }],
        "firstNOffset": 480
    }, {
        "text": "Singapore:",
        "children": [{
            "id": "Asia/Singapore",
            "name": "Asia/Singapore (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }],
        "firstNOffset": 480
    }, {
        "text": "Taiwan:",
        "children": [{
            "id": "Asia/Taipei",
            "name": "Asia/Taipei (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }],
        "firstNOffset": 480
    }, {
        "text": "North Korea:",
        "children": [{
            "id": "Asia/Pyongyang",
            "name": "Asia/Pyongyang (UTC+08:30)",
            "offset": "UTC+08:30",
            "nOffset": 510
        }],
        "firstNOffset": 510
    }, {
        "text": "Mongolia:",
        "children": [{
            "id": "Asia/Hovd",
            "name": "Asia/Hovd (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }, {
            "id": "Asia/Choibalsan",
            "name": "Asia/Choibalsan (UTC+09:00)",
            "offset": "UTC+09:00",
            "nOffset": 540
        }, {
            "id": "Asia/Ulaanbaatar",
            "name": "Asia/Ulaanbaatar (UTC+09:00)",
            "offset": "UTC+09:00",
            "nOffset": 540
        }],
        "firstNOffset": 540
    }, {
        "text": "Timor-Leste:",
        "children": [{
            "id": "Asia/Dili",
            "name": "Asia/Dili (UTC+09:00)",
            "offset": "UTC+09:00",
            "nOffset": 540
        }],
        "firstNOffset": 540
    }, {
        "text": "Korea:",
        "children": [{
            "id": "Asia/Seoul",
            "name": "Asia/Seoul (UTC+09:00)",
            "offset": "UTC+09:00",
            "nOffset": 540
        }],
        "firstNOffset": 540
    }, {
        "text": "Japan:",
        "children": [{
            "id": "Asia/Tokyo",
            "name": "Asia/Tokyo (UTC+09:00)",
            "offset": "UTC+09:00",
            "nOffset": 540
        }],
        "firstNOffset": 540
    }, {
        "text": "Palau:",
        "children": [{
            "id": "Pacific/Palau",
            "name": "Pacific/Palau (UTC+09:00)",
            "offset": "UTC+09:00",
            "nOffset": 540
        }],
        "firstNOffset": 540
    }, {
        "text": "Micronesia (Federated States Of):",
        "children": [{
            "id": "Pacific/Chuuk",
            "name": "Pacific/Chuuk (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }, {
            "id": "Pacific/Kosrae",
            "name": "Pacific/Kosrae (UTC+11:00)",
            "offset": "UTC+11:00",
            "nOffset": 660
        }, {
            "id": "Pacific/Pohnpei",
            "name": "Pacific/Pohnpei (UTC+11:00)",
            "offset": "UTC+11:00",
            "nOffset": 660
        }],
        "firstNOffset": 600
    }, {
        "text": "Guam:",
        "children": [{
            "id": "Pacific/Guam",
            "name": "Pacific/Guam (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }],
        "firstNOffset": 600
    }, {
        "text": "Papua New Guinea:",
        "children": [{
            "id": "Pacific/Port_Moresby",
            "name": "Pacific/Port Moresby (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }],
        "firstNOffset": 600
    }, {
        "text": "Northern Mariana Islands:",
        "children": [{
            "id": "Pacific/Saipan",
            "name": "Pacific/Saipan (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }],
        "firstNOffset": 600
    }, {
        "text": "Australia:",
        "children": [{
            "id": "Australia/Perth",
            "name": "Australia/Perth (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }, {
            "id": "Australia/Eucla",
            "name": "Australia/Eucla (UTC+08:45)",
            "offset": "UTC+08:45",
            "nOffset": 525
        }, {
            "id": "Australia/Adelaide",
            "name": "Australia/Adelaide (UTC+09:30)",
            "offset": "UTC+09:30",
            "nOffset": 570
        }, {
            "id": "Australia/Broken_Hill",
            "name": "Australia/Broken Hill (UTC+09:30)",
            "offset": "UTC+09:30",
            "nOffset": 570
        }, {
            "id": "Australia/Darwin",
            "name": "Australia/Darwin (UTC+09:30)",
            "offset": "UTC+09:30",
            "nOffset": 570
        }, {
            "id": "Australia/Brisbane",
            "name": "Australia/Brisbane (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }, {
            "id": "Australia/Currie",
            "name": "Australia/Currie (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }, {
            "id": "Australia/Hobart",
            "name": "Australia/Hobart (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }, {
            "id": "Australia/Lindeman",
            "name": "Australia/Lindeman (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }, {
            "id": "Australia/Melbourne",
            "name": "Australia/Melbourne (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }, {
            "id": "Australia/Sydney",
            "name": "Australia/Sydney (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }, {
            "id": "Australia/Lord_Howe",
            "name": "Australia/Lord Howe (UTC+10:30)",
            "offset": "UTC+10:30",
            "nOffset": 630
        }, {
            "id": "Antarctica/Macquarie",
            "name": "Antarctica/Macquarie (UTC+11:00)",
            "offset": "UTC+11:00",
            "nOffset": 660
        }],
        "firstNOffset": 660
    }, {
        "text": "Vanuatu:",
        "children": [{
            "id": "Pacific/Efate",
            "name": "Pacific/Efate (UTC+11:00)",
            "offset": "UTC+11:00",
            "nOffset": 660
        }],
        "firstNOffset": 660
    }, {
        "text": "Solomon Islands:",
        "children": [{
            "id": "Pacific/Guadalcanal",
            "name": "Pacific/Guadalcanal (UTC+11:00)",
            "offset": "UTC+11:00",
            "nOffset": 660
        }],
        "firstNOffset": 660
    }, {
        "text": "Norfolk Island:",
        "children": [{
            "id": "Pacific/Norfolk",
            "name": "Pacific/Norfolk (UTC+11:00)",
            "offset": "UTC+11:00",
            "nOffset": 660
        }],
        "firstNOffset": 660
    }, {
        "text": "New Caledonia:",
        "children": [{
            "id": "Pacific/Noumea",
            "name": "Pacific/Noumea (UTC+11:00)",
            "offset": "UTC+11:00",
            "nOffset": 660
        }],
        "firstNOffset": 660
    }, {
        "text": "Russian Federation:",
        "children": [{
            "id": "Europe/Kaliningrad",
            "name": "Europe/Kaliningrad (UTC+02:00)",
            "offset": "UTC+02:00",
            "nOffset": 120
        }, {
            "id": "Europe/Moscow",
            "name": "Europe/Moscow (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }, {
            "id": "Europe/Simferopol",
            "name": "Europe/Simferopol (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }, {
            "id": "Europe/Volgograd",
            "name": "Europe/Volgograd (UTC+03:00)",
            "offset": "UTC+03:00",
            "nOffset": 180
        }, {
            "id": "Europe/Samara",
            "name": "Europe/Samara (UTC+04:00)",
            "offset": "UTC+04:00",
            "nOffset": 240
        }, {
            "id": "Asia/Yekaterinburg",
            "name": "Asia/Yekaterinburg (UTC+05:00)",
            "offset": "UTC+05:00",
            "nOffset": 300
        }, {
            "id": "Asia/Novosibirsk",
            "name": "Asia/Novosibirsk (UTC+06:00)",
            "offset": "UTC+06:00",
            "nOffset": 360
        }, {
            "id": "Asia/Omsk",
            "name": "Asia/Omsk (UTC+06:00)",
            "offset": "UTC+06:00",
            "nOffset": 360
        }, {
            "id": "Asia/Krasnoyarsk",
            "name": "Asia/Krasnoyarsk (UTC+07:00)",
            "offset": "UTC+07:00",
            "nOffset": 420
        }, {
            "id": "Asia/Novokuznetsk",
            "name": "Asia/Novokuznetsk (UTC+07:00)",
            "offset": "UTC+07:00",
            "nOffset": 420
        }, {
            "id": "Asia/Irkutsk",
            "name": "Asia/Irkutsk (UTC+08:00)",
            "offset": "UTC+08:00",
            "nOffset": 480
        }, {
            "id": "Asia/Khandyga",
            "name": "Asia/Khandyga (UTC+09:00)",
            "offset": "UTC+09:00",
            "nOffset": 540
        }, {
            "id": "Asia/Yakutsk",
            "name": "Asia/Yakutsk (UTC+09:00)",
            "offset": "UTC+09:00",
            "nOffset": 540
        }, {
            "id": "Asia/Magadan",
            "name": "Asia/Magadan (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }, {
            "id": "Asia/Sakhalin",
            "name": "Asia/Sakhalin (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }, {
            "id": "Asia/Ust-Nera",
            "name": "Asia/Ust-Nera (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }, {
            "id": "Asia/Vladivostok",
            "name": "Asia/Vladivostok (UTC+10:00)",
            "offset": "UTC+10:00",
            "nOffset": 600
        }, {
            "id": "Asia/Anadyr",
            "name": "Asia/Anadyr (UTC+12:00)",
            "offset": "UTC+12:00",
            "nOffset": 720
        }, {
            "id": "Asia/Kamchatka",
            "name": "Asia/Kamchatka (UTC+12:00)",
            "offset": "UTC+12:00",
            "nOffset": 720
        }],
        "firstNOffset": 720
    }, {
        "text": "Fiji:",
        "children": [{
            "id": "Pacific/Fiji",
            "name": "Pacific/Fiji (UTC+12:00)",
            "offset": "UTC+12:00",
            "nOffset": 720
        }],
        "firstNOffset": 720
    }, {
        "text": "Tuvalu:",
        "children": [{
            "id": "Pacific/Funafuti",
            "name": "Pacific/Funafuti (UTC+12:00)",
            "offset": "UTC+12:00",
            "nOffset": 720
        }],
        "firstNOffset": 720
    }, {
        "text": "Marshall Islands:",
        "children": [{
            "id": "Pacific/Kwajalein",
            "name": "Pacific/Kwajalein (UTC+12:00)",
            "offset": "UTC+12:00",
            "nOffset": 720
        }, {
            "id": "Pacific/Majuro",
            "name": "Pacific/Majuro (UTC+12:00)",
            "offset": "UTC+12:00",
            "nOffset": 720
        }],
        "firstNOffset": 720
    }, {
        "text": "Nauru:",
        "children": [{
            "id": "Pacific/Nauru",
            "name": "Pacific/Nauru (UTC+12:00)",
            "offset": "UTC+12:00",
            "nOffset": 720
        }],
        "firstNOffset": 720
    }, {
        "text": "Wallis And Futuna:",
        "children": [{
            "id": "Pacific/Wallis",
            "name": "Pacific/Wallis (UTC+12:00)",
            "offset": "UTC+12:00",
            "nOffset": 720
        }],
        "firstNOffset": 720
    }, {
        "text": "New Zealand:",
        "children": [{
            "id": "Pacific/Auckland",
            "name": "Pacific/Auckland (UTC+13:00)",
            "offset": "UTC+13:00",
            "nOffset": 780
        }, {
            "id": "Pacific/Chatham",
            "name": "Pacific/Chatham (UTC+13:45)",
            "offset": "UTC+13:45",
            "nOffset": 825
        }],
        "firstNOffset": 780
    }, {
        "text": "Kiribati:",
        "children": [{
            "id": "Pacific/Tarawa",
            "name": "Pacific/Tarawa (UTC+12:00)",
            "offset": "UTC+12:00",
            "nOffset": 720
        }, {
            "id": "Pacific/Enderbury",
            "name": "Pacific/Enderbury (UTC+13:00)",
            "offset": "UTC+13:00",
            "nOffset": 780
        }, {
            "id": "Pacific/Kiritimati",
            "name": "Pacific/Kiritimati (UTC+14:00)",
            "offset": "UTC+14:00",
            "nOffset": 840
        }],
        "firstNOffset": 780
    }, {
        "text": "Tokelau:",
        "children": [{
            "id": "Pacific/Fakaofo",
            "name": "Pacific/Fakaofo (UTC+13:00)",
            "offset": "UTC+13:00",
            "nOffset": 780
        }],
        "firstNOffset": 780
    }, {
        "text": "Tonga:",
        "children": [{
            "id": "Pacific/Tongatapu",
            "name": "Pacific/Tongatapu (UTC+13:00)",
            "offset": "UTC+13:00",
            "nOffset": 780
        }],
        "firstNOffset": 780
    }, {
        "text": "Samoa:",
        "children": [{
            "id": "Pacific/Apia",
            "name": "Pacific/Apia (UTC+14:00)",
            "offset": "UTC+14:00",
            "nOffset": 840
        }],
        "firstNOffset": 840
    }
]

function fetchTimezones() {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve(timezoneData);
        }, 500); 
    });
}