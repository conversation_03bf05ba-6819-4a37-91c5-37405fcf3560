var commonUserProperties = ['user_id', 'branch_campaign', 'branch_channel', 'branch_feature', 'utm_campaign', 'utm_source', 'utm_medium', 'sl_freemium_user'];

var GA4PageEvents = {
  "start_login_click": {
    event_name: "start_login",
    event_label: "When user clicks on any Login from anywhere",
    section: "",
    segment: "",
  },
  "start_signup_click": {
    event_name: "start_signup",
    event_label: "When user clicks on sign up button from anywhere",
    section: "",
    segment: ""
  },
  "Complete_Signup_Click": {
    event_name: "complete_signup",
    event_label: "When user successfully completes the sign up process",
    section: "",
    segment: ""
  },
  "Complete_Login_Click": {
    event_name: "complete_login",
    event_label: "When user successfully completes the login process",
    section: "",
    segment: ""
  },
  "Link_Social_Account_Click": {
    event_name: "link_social_account",
    event_label: "When user link social account",
    section: "",
    segment: ""
  },
  "Delink_Social_Account_Click": {
    event_name: "delink_social_account",
    event_label: "When user delink social account",
    section: "",
    segment: ""
  }
};

var GA4CommonEvents = {};

function GA4Events(printInConsole = true) {
  this.log = function () {};
  if (printInConsole) {
    this.log = function () {
      var args = Array.from(arguments);
      if (args.length > 0) {
        console.group(typeof args[0] === "string" ? args[0] : "GA4 Debug Log");
        args.map(function (arg) {
          return console.log(arg);
        });
        console.groupEnd();
      }
    };
  }

  this.setCommonParams = function (eventData) {
    if (Object.keys(eventData).length > 0 && window.gtag) {
      window.gtag("set", "user_properties", eventData);
    }
  };

  this.sendEvent = function (eventIdentifier = "", eventPayload = {}) {
    var eventName = "";
    var dataFromEventsList = {};

    if (typeof GA4CommonEvents[eventIdentifier] !== "undefined") {
      dataFromEventsList = Object.assign({}, GA4CommonEvents[eventIdentifier] || {});
    }

    if (!dataFromEventsList.event_label) {
      var pageLevelEventsList = GA4PageEvents || {};

      if (Object.keys(pageLevelEventsList).length > 0) {
        dataFromEventsList = Object.assign({}, pageLevelEventsList[eventIdentifier] || {});
      }

      if (!dataFromEventsList.event_label) {
        this.log("Event Identifier - " + eventIdentifier + " does not exist in Events List");
        return;
      }
    }

    eventName = dataFromEventsList.event_name;
    delete dataFromEventsList.event_name;

    var predefinedEventProperties = dataFromEventsList || {};

    var filteredEventData = {};
    var eventLevelUserData = {
      'branch_campaign': '',
      'branch_channel': '',
      'branch_feature': '',
      'utm_campaign': '',
      'utm_source': '',
      'utm_medium': ''
    };
    var utmZCookie = fetchUTMSource().split("|");

    /**
     * Setting User Properties
     */
    eventPayload['branch_campaign'] = eventPayload['utm_campaign'] = utmZCookie[1].split("=")[1];
    eventPayload['branch_channel'] = eventPayload['utm_source'] = utmZCookie[2].split("=")[1];
    eventPayload['branch_feature'] = eventPayload['utm_medium'] = utmZCookie[0].split("=")[1];
    console.log("Event Payload: ", eventPayload);

    Object.keys(eventPayload).length > 0 && Object.keys(eventPayload).map(function (key) {
      var eventDataValue = eventPayload[key];

      if (commonUserProperties.includes(key)) {
        eventLevelUserData[key] = eventDataValue;
      } else {
        filteredEventData[key] = eventDataValue;
      }
    });

    console.log("UserProperties: ", eventLevelUserData);
    eventLevelUserData = Object.assign({}, eventLevelUserData);
    this.setCommonParams(eventLevelUserData);

    eventPayload = Object.assign({}, predefinedEventProperties);
    eventPayload = Object.assign(eventPayload, filteredEventData);
    eventPayload = Object.assign(eventPayload, window.pageLevelParams || {});

    if (window.gtag) {
      this.log("GA4 Send Event", eventIdentifier, eventName, eventPayload);
      window.gtag("event", eventName, eventPayload);
    } else {
      this.log("GA4 Send Event", eventIdentifier, eventName, eventPayload);
    }
  };
}
