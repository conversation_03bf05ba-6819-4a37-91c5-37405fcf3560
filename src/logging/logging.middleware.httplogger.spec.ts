import { HttpLoggerMiddleware } from './logging.middleware.httplogger';
import { Request, Response, NextFunction } from 'express';
import { AccessLog } from './logger';

// Mock AccessLog to avoid actual logging
jest.mock('./logger', () => ({
  AccessLog: {
    info: jest.fn(),
  },
}));

describe('HttpLoggerMiddleware', () => {
  let middleware: HttpLoggerMiddleware;

  beforeEach(() => {
    middleware = new HttpLoggerMiddleware();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(middleware).toBeDefined();
  });

  it('should log request and response details on close', () => {
    const req = { ip: '127.0.0.1', method: 'GET', path: '/test', get: jest.fn().mockReturnValue('test-agent') } as unknown as Request;
    const res = { on: jest.fn((event, callback) => {
        if (event === 'close') {
            callback();
        }
    }), get: jest.fn().mockReturnValue('123'), statusCode: 200 } as unknown as Response;
    const next = jest.fn() as NextFunction;

    middleware.use(req, res, next);

    expect(res.on).toHaveBeenCalledWith('close', expect.any(Function));
    expect(AccessLog.info).toHaveBeenCalled();
    expect(next).toHaveBeenCalled();
  });

  it('should handle missing user-agent and content-length', () => {
    const req = { ip: '127.0.0.1', method: 'GET', path: '/test', get: jest.fn().mockReturnValue(undefined) } as unknown as Request;
    const res = { on: jest.fn((event, callback) => {
        if (event === 'close') {
            callback();
        }
    }), get: jest.fn().mockReturnValue(undefined), statusCode: 200 } as unknown as Response;
    const next = jest.fn() as NextFunction;

    middleware.use(req, res, next);

    expect(AccessLog.info).toHaveBeenCalledWith('GET /test 200 0 -  127.0.0.1', {}, req);
  });
});
