import winston, { Container, format, transports } from 'winston';
import { Request } from 'express';

const sensitiveKeys = [
  'user_login',
  'user_email',
  'userEmail',
  'login',
  'pwd',
  'user_pwd',
  'email',
  'username',
  'confirm_pwd',
  'password',
  'phone_no',
  'created_by',
  'first_name',
  'last_name',
  'name',
  'user_id',
  'gId',
  'cur_passwd',
  'new_passwd',
  'confirm_passwd',
];

function redact(data) {
  if (typeof data === 'object') {
    const redacted = JSON.stringify(data, (key, value) => (sensitiveKeys.includes(key) ? 'XXXXXX' : value), 2);

    return JSON.parse(redacted);
  } else if (typeof data === 'string') {
    return data;
  }
}

const levels = { error: 0, warn: 1, info: 2, debug: 4 };
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  return env === 'development' ? 'debug' : 'warn';
};
const colors = { error: 'red', warn: 'yellow', info: 'green', debug: 'blue' };

enum LoggerMode {
  DEFAULT = 'default',
  ACCESS_LOG = 'access',
  API_LOG = 'api',
}

const colorizeFormat = format.colorize({ all: true, colors: colors });
const container = new Container();
const consoleTransport = new transports.Console({
  format: format.combine(colorizeFormat),
});

const accessLogFileTransport = new transports.File({
  filename: 'logs/access_log.log',
});
const errorFileTransport = new transports.File({
  filename: 'logs/app.log',
});
const apiFileTransport = new transports.File({
  filename: 'logs/api.log',
});

const loggerOptions = {
  level: level(),
  levels: levels,
  json: true,
  transports: [consoleTransport],
  exitOnError: false,
};

container.add(LoggerMode.DEFAULT, {
  ...loggerOptions,
  transports: [consoleTransport, errorFileTransport],
});
container.add(LoggerMode.ACCESS_LOG, {
  ...loggerOptions,
  transports: [consoleTransport, accessLogFileTransport],
});
container.add(LoggerMode.API_LOG, {
  ...loggerOptions,
  transports: [consoleTransport, apiFileTransport],
});

const getLogger = (mode: LoggerMode): winston.Logger => container.get(mode);
const processAccessLog = (data, request: Request) => {
  const details: any = typeof data == 'string' ? JSON.parse(data) : data;
  if (request) {
    details['HTTP_USER_AGENT'] = request['headers']['user-agent'];
    details['REQUEST'] = request.originalUrl;
    details['REQUEST_BODY'] = redact(request.body);
    details['REQUEST_PARAMS'] = redact(request.params);
    details['REQUEST_QUERY_PARAMS'] = redact(request.query);
  }
  return JSON.stringify(details);
};

const Logger = {
  log: (message: string, data?: object) => getLogger(LoggerMode.DEFAULT).debug(message, redact(data)),
  error: (message, data?: object) => getLogger(LoggerMode.DEFAULT).error(message, redact(data)),
  debug: (message, data?: object) => getLogger(LoggerMode.DEFAULT).debug(message, redact(data)),
  warn: (message, data?: object) => getLogger(LoggerMode.DEFAULT).warn(message, redact(data)),
  info: (message, data?: object) => getLogger(LoggerMode.DEFAULT).info(message, redact(data)),
};

const AccessLog = {
  debug: (message: string, logData?: object, request?: Request) => {
    const logDetails = processAccessLog(logData, request);
    getLogger(LoggerMode.ACCESS_LOG).debug(message + `------->` + logDetails);
  },
  error: (message: string, logData?: object, request?: Request) => {
    const logDetails = processAccessLog(logData, request);
    getLogger(LoggerMode.ACCESS_LOG).error(message + `------->` + logDetails);
  },
  info: (message: string, logData?: object, request?: Request) => {
    const logDetails = processAccessLog(logData, request);
    getLogger(LoggerMode.ACCESS_LOG).info(message + `------->` + logDetails);
  },
};

const APILog = {
  log: (message: string, data?: object) => getLogger(LoggerMode.API_LOG).debug(message, redact(data)),
  error: (message, data?: object) => getLogger(LoggerMode.API_LOG).error(message, redact(data)),
  debug: (message, data?: object) => getLogger(LoggerMode.API_LOG).debug(message, redact(data)),
  warn: (message, data?: object) => getLogger(LoggerMode.API_LOG).warn(message, redact(data)),
  info: (message, data?: object) => getLogger(LoggerMode.API_LOG).info(message, redact(data)),
};

export { Logger, AccessLog, APILog };
