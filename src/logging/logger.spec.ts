const mockLogger = {
    debug: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
};

import { Logger, AccessLog, APILog } from './logger';

// Mock winston to avoid actual logging
jest.mock('winston', () => ({
    ...jest.requireActual('winston'),
    Container: jest.fn().mockImplementation(() => ({
        add: jest.fn(),
        get: jest.fn().mockReturnValue(mockLogger),
    })),
}));

describe('Logger', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should call debug with redacted data', () => {
    Logger.debug('test message', { password: '123' });
    expect(mockLogger.debug).toHaveBeenCalledWith('test message', { password: 'XXXXXX' });
  });

  it('should call error with redacted data', () => {
    Logger.error('test message', { email: '<EMAIL>' });
    expect(mockLogger.error).toHaveBeenCalledWith('test message', { email: 'XXXXXX' });
  });

  it('should call warn with non-object data', () => {
    Logger.warn('test message', {data: 'a string'});
    expect(mockLogger.warn).toHaveBeenCalledWith('test message', {data: 'a string'});
  });

  it('should call info', () => {
    Logger.info('test message');
    expect(mockLogger.info).toHaveBeenCalledWith('test message', undefined);
  });
});

describe('AccessLog', () => {
    it('should process and log debug message', () => {
        const req = { headers: { 'user-agent': 'test-agent' }, originalUrl: '/test', body: {pwd: '123'}, params: {}, query: {} } as any;
        AccessLog.debug('test', {}, req);
        expect(mockLogger.debug).toHaveBeenCalled();
    });

    it('should process and log error message with string data', () => {
        const req = { headers: { 'user-agent': 'test-agent' }, originalUrl: '/test', body: {}, params: {}, query: {} } as any;
        AccessLog.error('test', {}, req);
        expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should process and log info message without request', () => {
        AccessLog.info('test', {});
        expect(mockLogger.info).toHaveBeenCalled();
    });
});

describe('APILog', () => {
    it('should call log (debug)', () => {
        APILog.log('test', { name: 'test' });
        expect(mockLogger.debug).toHaveBeenCalledWith('test', { name: 'XXXXXX' });
    });

    it('should call info', () => {
        APILog.info('test');
        expect(mockLogger.info).toHaveBeenCalled();
    });

    it('should call warn', () => {
        APILog.warn('test');
        expect(mockLogger.warn).toHaveBeenCalled();
    });
});