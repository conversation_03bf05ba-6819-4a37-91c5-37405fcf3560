import {
  Injectable,
  NestMiddleware,
} from '@nestjs/common'; // Importing necessary decorators and interfaces from NestJS

import { Request, Response, NextFunction } from 'express'; // Importing types from Express
import { AccessLog } from './logger'; // Importing the custom logger

/**
 * Middleware to log HTTP requests and responses.
 * This middleware logs details such as the HTTP method, URL, status code, content length,
 * user agent, and client IP address for every request.
 */
@Injectable()
export class HttpLoggerMiddleware implements NestMiddleware {
  /**
   * The `use` method is invoked for every incoming request.
   * It logs the request details after the response is sent.
   *
   * @param request - The incoming HTTP request object
   * @param response - The outgoing HTTP response object
   * @param next - The next middleware function in the request-response cycle
   */
  use(request: Request, response: Response, next: NextFunction): void {
    // Extracting relevant details from the request
    const { ip, method, path: url } = request;
    const userAgent = request.get('user-agent') || '';

    // Listening for the 'close' event to log details after the response is sent
    response.on('close', () => {
      const { statusCode } = response; // HTTP status code of the response
      const contentLength = response.get('content-length') || 0; // Content length of the response

      // Logging the request and response details
      AccessLog.info(
        `${method} ${url} ${statusCode} ${contentLength} - ${userAgent} ${ip}`,
        {}, // Additional metadata (if any) can be passed here
        request // Passing the request object for context
      );
    });

    // Passing control to the next middleware in the chain
    next();
  }
}
