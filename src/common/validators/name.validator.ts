import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { Logger } from '../../logging/logger';
const validate = (value: any, args: ValidationArguments) => {
  Logger.log('NameValidator', args);
  const nameRegex = /^[a-zA-Z\s,'.\-]+$/; // Updated regex

  if (value && nameRegex.test(value)) {
    return true;
  }

  return false;
};
export const IsNameValid = (fieldName: string, customErrorMessage = '', validationOptions?: ValidationOptions) => {
  return (object: object, propertyName: string) => {
    registerDecorator({
      name: 'isNameValid',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate,
        defaultMessage(args: ValidationArguments) {
          Logger.log('NameValidator', args);
          const field = fieldName || propertyName;
          const errorMessage = customErrorMessage || `Please provide a valid ${field}.`;
          return errorMessage;
        },
      },
    });
  };
};
