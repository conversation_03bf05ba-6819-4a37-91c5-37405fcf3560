import { IsCtypeAlpha } from './ctype-alpha.validator';
import { validate } from 'class-validator';

describe('IsCtypeAlpha', () => {
  class TestClass {
    @IsCtypeAlpha()
    name: string;
  }

  it('should pass for a valid alphabetic string', async () => {
    const test = new TestClass();
    test.name = 'test';
    const errors = await validate(test);
    expect(errors.length).toBe(0);
  });

  it('should fail for a non-alphabetic string', async () => {
    const test = new TestClass();
    test.name = '123';
    const errors = await validate(test);
    expect(errors.length).toBe(1);
  });

  it('should fail for a mixed string', async () => {
    const test = new TestClass();
    test.name = 'test123';
    const errors = await validate(test);
    expect(errors.length).toBe(1);
  });

  it('should fail for an empty string', async () => {
    const test = new TestClass();
    test.name = '';
    const errors = await validate(test);
    expect(errors.length).toBe(1);
  });
});
