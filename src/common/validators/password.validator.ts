import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { Logger } from '../../logging/logger';
const PASSWORD_LENGTH = 8; // Define your password length constants
const MAX_PASSWORD_LENGTH = 20; // Define your maximum password length constant
const validate = (value: any, args: ValidationArguments) => {
  Logger.log('PasswordValidator', args);
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).*$/;

  if (value && value.length >= PASSWORD_LENGTH && value.length <= MAX_PASSWORD_LENGTH && passwordRegex.test(value)) {
    return true;
  }

  return false;
};
export const IsPasswordValid = (errorMessage = '', validationOptions?: ValidationOptions) => {
  return (object: object, propertyName: string) => {
    registerDecorator({
      name: 'isPasswordValid',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate,
        defaultMessage(args: ValidationArguments) {
          Logger.log('PasswordValidator', args);
          const message = `Your password should be minimum ${PASSWORD_LENGTH} characters long, with at least one uppercase, one lowercase letter, one number and length cannot be longer than ${MAX_PASSWORD_LENGTH} characters.`;
          return errorMessage || message; // Use the custom error message provided
        },
      },
    });
  };
};
