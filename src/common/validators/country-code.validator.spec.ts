import { IsCountryCodeValid } from './country-code.validator';
import { validate } from 'class-validator';

describe('IsCountryCodeValid', () => {
  class TestClass {
    @IsCountryCodeValid()
    countryCode: string;
  }

  it('should pass for a valid country code', async () => {
    const test = new TestClass();
    test.countryCode = 'US';
    const errors = await validate(test);
    expect(errors.length).toBe(0);
  });

  it('should fail for an invalid country code', async () => {
    const test = new TestClass();
    test.countryCode = 'USA1';
    const errors = await validate(test);
    expect(errors.length).toBe(1);
  });

  it('should fail for an empty country code', async () => {
    const test = new TestClass();
    test.countryCode = '';
    const errors = await validate(test);
    expect(errors.length).toBe(1);
  });
});
