import { IsPhoneNoValid } from './phone.validator';
import { validate } from 'class-validator';

describe('IsPhoneNoValid', () => {
  class TestClass {
    @IsPhoneNoValid()
    phone: string;
  }

  it('should pass for a valid phone number', async () => {
    const test = new TestClass();
    test.phone = '1234567';
    const errors = await validate(test);
    expect(errors.length).toBe(0);
  });

  it('should fail for an invalid phone number', async () => {
    const test = new TestClass();
    test.phone = '123';
    const errors = await validate(test);
    expect(errors.length).toBe(1);
  });

  it('should fail for a non-string phone number', async () => {
    const test = new TestClass();
    (test.phone as any) = 1234567;
    const errors = await validate(test);
    expect(errors.length).toBe(1);
  });

  it('should fail for an empty phone number', async () => {
    const test = new TestClass();
    test.phone = '';
    const errors = await validate(test);
    expect(errors.length).toBe(1);
  });
});
