import { IsPasswordValid } from './password.validator';
import { validate } from 'class-validator';

describe('IsPasswordValid', () => {
  class TestClass {
    @IsPasswordValid()
    password: string;
  }

  it('should pass for a valid password', async () => {
    const test = new TestClass();
    test.password = 'Password123';
    const errors = await validate(test);
    expect(errors.length).toBe(0);
  });

  it('should fail for an invalid password', async () => {
    const test = new TestClass();
    test.password = 'weak';
    const errors = await validate(test);
    expect(errors.length).toBe(1);
  });

  it('should fail for an empty password', async () => {
    const test = new TestClass();
    test.password = '';
    const errors = await validate(test);
    expect(errors.length).toBe(1);
  });
});
