import { PasswordNotSameConstraint } from './password-not-same.validator';

describe('PasswordNotSameConstraint', () => {
  const constraint = new PasswordNotSameConstraint();

  it('should return true if passwords are not the same', () => {
    const args = {
      value: 'newPassword',
      constraints: ['oldPassword'],
      object: { oldPassword: 'oldPassword' },
      property: 'password',
      targetName: 'TestClass',
    };
    expect(constraint.validate('newPassword', args as any)).toBe(true);
  });

  it('should return false if passwords are the same', () => {
    const args = {
      value: 'oldPassword',
      constraints: ['oldPassword'],
      object: { oldPassword: 'oldPassword' },
      property: 'password',
      targetName: 'TestClass',
    };
    expect(constraint.validate('oldPassword', args as any)).toBe(false);
  });

  it('should return the default message', () => {
    expect(constraint.defaultMessage({} as any)).toBe('Your new password should not be same as old password!');
  });
});
