import { IsNameValid } from './name.validator';
import { validate } from 'class-validator';

describe('IsNameValid', () => {
  class TestClass {
    @IsNameValid('name')
    name: string;
  }

  it('should pass for a valid name', async () => {
    const test = new TestClass();
    test.name = '<PERSON>';
    const errors = await validate(test);
    expect(errors.length).toBe(0);
  });

  it('should fail for an invalid name', async () => {
    const test = new TestClass();
    test.name = '123';
    const errors = await validate(test);
    expect(errors.length).toBe(1);
  });

  it('should fail for an empty name', async () => {
    const test = new TestClass();
    test.name = '';
    const errors = await validate(test);
    expect(errors.length).toBe(1);
  });
});
