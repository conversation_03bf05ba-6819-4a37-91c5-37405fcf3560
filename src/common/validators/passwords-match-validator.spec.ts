import { PasswordsMatchConstraint } from './passwords-match-validator';

describe('PasswordsMatchConstraint', () => {
  const constraint = new PasswordsMatchConstraint();

  it('should return true if passwords match', () => {
    const args = {
      value: 'password',
      constraints: ['confirmPassword'],
      object: { confirmPassword: 'password' },
      property: 'password',
      targetName: 'TestClass',
    };
    expect(constraint.validate('password', args as any)).toBe(true);
  });

  it('should return false if passwords do not match', () => {
    const args = {
      value: 'password',
      constraints: ['confirmPassword'],
      object: { confirmPassword: 'differentPassword' },
      property: 'password',
      targetName: 'TestClass',
    };
    expect(constraint.validate('password', args as any)).toBe(false);
  });

  it('should return the default message', () => {
    expect(constraint.defaultMessage({} as any)).toBe('Your new password and confirm password did not match.');
  });
});
