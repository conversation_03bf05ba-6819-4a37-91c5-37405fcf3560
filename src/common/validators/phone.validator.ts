import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { Logger } from '../../logging/logger';

const validate = (value: any, args: ValidationArguments) => {
  Logger.log('PhoneNoValidator', args);
  if (typeof value !== 'string') {
    return false;
  }

  const phoneNumberValidator = /^\d{7,}$/; // Assuming you want at least 7 digits
  return phoneNumberValidator.test(value);
};

export const IsPhoneNoValid = (errorMessage = '', validationOptions?: ValidationOptions) => {
  return (object: object, propertyName: string) => {
    registerDecorator({
      name: 'isPhoneNoValid',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate,
        defaultMessage(args: ValidationArguments) {
          Logger.error('PhoneNoValidator', args);
          const message = `Please provide valid phone no. (atleast 7 digits required)`;
          return errorMessage || message;
        },
      },
    });
  };
};
