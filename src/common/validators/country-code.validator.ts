import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { Logger } from '../../logging/logger';

const validate = (value: any, args: ValidationArguments) => {
  Logger.log('CountryCodeValidator', args);
  const countryCodeRegex = /^[A-Za-z]{2,3}$/;

  if (value && countryCodeRegex.test(value)) {
    return true;
  }

  return false;
};

export const IsCountryCodeValid = (errorMessage = '', validationOptions?: ValidationOptions) => {
  return (object: object, propertyName: string) => {
    registerDecorator({
      name: 'isCountryCodeValid',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate,
        defaultMessage(args: ValidationArguments) {
          Logger.log('CountryCodeValidator', args);
          const message = `Please select the country code for your phone number.`;
          return errorMessage || message;
        },
      },
    });
  };
};
