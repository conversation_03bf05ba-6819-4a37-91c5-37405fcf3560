import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { Logger } from '../../logging/logger';

const validate = (value: any, args: ValidationArguments) => {
  Logger.log('CtypeAlphaValidator', args);
  if (typeof value !== 'string') {
    return false;
  }

  // Your ctype_alpha logic here
  return /^[a-zA-Z]+$/.test(value);
};

export const IsCtypeAlpha = (errorMessage = '', validationOptions?: ValidationOptions) => {
  return (object: object, propertyName: string) => {
    registerDecorator({
      name: 'isCtypeAlpha',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate,
        defaultMessage(args: ValidationArguments) {
          Logger.log('CtypeAlphaValidator', args);
          const message = 'The value must contain only alphabetical characters.';
          return errorMessage || message; // Use the custom error message provided
        },
      },
    });
  };
};
