import { ValidatorConstraint, ValidatorConstraintInterface, ValidationArguments } from 'class-validator';
import { Logger } from '../../logging/logger';

@ValidatorConstraint({ name: 'passwordNotSame', async: false })
export class PasswordNotSameConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    const [relatedPropertyName] = args.constraints;
    const relatedValue = (args.object as any)[relatedPropertyName];
    return value !== relatedValue;
  }

  defaultMessage(args: ValidationArguments) {
    Logger.log('PasswordNotSameConstraint', args);
    return 'Your new password should not be same as old password!';
  }
}
