import { ConfigService } from '@nestjs/config';
import { Communication } from '../communication/communication';
import { Injectable } from '@nestjs/common';
import { Logger } from '../../../logging/logger';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class EmailService extends Communication {
  url: string;
  private authValue: string;
  constructor(
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
  ) {
    super();
    this.url = this.configService.get('emailCommunicationUrl');
  }

  resetUri() {
    this.url = this.configService.get('emailCommunicationUrl');
  }
  private generateJwtToken(): string {
    const payload = {
      userName: this.configService.get('communicationUsername'),
      password: this.configService.get('communicationPassword'),
    };
    return this.jwtService.sign(payload, {
      secret: this.configService.get('jwtSecret'),
      expiresIn: '1h',
    });
  }
  /**
   * pass this information to test the email service
   *   let mailParams = {
          mailTo :'<EMAIL>',
          mailIdentifier : "engagexPostRegistration", // pass here email template which exist in db
          mailTokens :{
              'firstName' : 'test',
              'engagexURL' :'test',
              'userEmail' :'<EMAIL>',
              'password' :"dssd",
              }
        
      };
      const response = await this.emailService.sendEmail(mailParams);
   * @param params
   * @returns 
   */
  public async sendEmail(params: any): Promise<any> {
    try {
      this.authValue = `Bearer ${this.generateJwtToken()}`;
      this.setRawBody(JSON.stringify(params));
      this.setRequestHeader('Authorization', this.authValue);
      this.setRequestHeader('Content-Type', 'application/json');
      this.resetUri();
      const response = await this.post('/email-template/send-email-api');
      return response;
    } catch (error: any) {
      Logger.error('Failed to send email', error);
      throw error;
    }
  }
}
