import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { EmailService } from './email.service';
import { Logger } from '../../../logging/logger';

// Mock the Logger to prevent actual logging during tests
jest.mock('../../../logging/logger', () => ({
  Logger: {
    error: jest.fn(),
  },
}));

describe('EmailService', () => {
  let emailService: EmailService;
  let jwtService: JwtService;

  // Mock values
  const mockEmailUrl = 'http://email.service.com';
  const mockJwtSecret = 'test-secret';
  const mockCommunicationUsername = 'testuser';
  const mockCommunicationPassword = 'testpassword';
  const mockToken = 'mock-jwt-token';

  beforeEach(() => {
    // Mock ConfigService
    const configServiceMock = {
      get: jest.fn((key: string) => {
        const configMap = {
          emailCommunicationUrl: mockEmailUrl,
          jwtSecret: mockJwtSecret,
          communicationUsername: mockCommunicationUsername,
          communicationPassword: mockCommunicationPassword,
        };
        return configMap[key];
      }),
    };

    // Mock JwtService
    jwtService = new JwtService({ secret: mockJwtSecret });
    jest.spyOn(jwtService, 'sign').mockReturnValue(mockToken);

    // Create an instance of EmailService with mocked dependencies
    emailService = new EmailService(
      configServiceMock as any,
      jwtService as any,
    );

    // Spy on methods from the base Communication class
    jest.spyOn(emailService as any, 'post').mockResolvedValue({});
    jest.spyOn(emailService as any, 'setRawBody').mockImplementation(() => {});
    jest.spyOn(emailService as any, 'setRequestHeader').mockImplementation(() => {});
    // We will call resetUri directly in its own test, but spy on it for the sendEmail test
    jest.spyOn(emailService as any, 'resetUri').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(emailService).toBeDefined();
  });

  it('constructor should set the url correctly', () => {
    // Re-create service to test constructor specifically without resetUri mock
    emailService = new EmailService(new ConfigService({ emailCommunicationUrl: mockEmailUrl }), new JwtService());
    expect(emailService.url).toBe(mockEmailUrl);
  });

  describe('resetUri', () => {
    it('should reset the URI to the value from configService', () => {
      // To test the actual implementation, we need a fresh instance without the method mocked
      const config = new ConfigService({ emailCommunicationUrl: mockEmailUrl });
      const jwt = new JwtService();
      const service = new EmailService(config, jwt);
      
      service.url = 'http://something.else'; // Change url
      service.resetUri(); // Call method
      expect(service.url).toBe(mockEmailUrl); // Assert it's reset
    });
  });

  describe('sendEmail', () => {
    const mailParams = {
      mailTo: '<EMAIL>',
      mailIdentifier: 'testTemplate',
      mailTokens: { name: 'Test User' },
    };

    it('should send an email successfully', async () => {
      const mockResponse = { success: true };
      (emailService as any).post.mockResolvedValue(mockResponse);

      const result = await emailService.sendEmail(mailParams);

      // 1. Check token generation
      expect(jwtService.sign).toHaveBeenCalledWith(
        {
          userName: mockCommunicationUsername,
          password: mockCommunicationPassword,
        },
        {
          secret: mockJwtSecret,
          expiresIn: '1h',
        },
      );

      // 2. Check header setup
      expect(emailService['setRequestHeader']).toHaveBeenCalledWith('Authorization', `Bearer ${mockToken}`);
      expect(emailService['setRequestHeader']).toHaveBeenCalledWith('Content-Type', 'application/json');

      // 3. Check body setup
      expect(emailService['setRawBody']).toHaveBeenCalledWith(JSON.stringify(mailParams));

      // 4. Check URI reset
      expect(emailService['resetUri']).toHaveBeenCalled();

      // 5. Check post call
      expect(emailService['post']).toHaveBeenCalledWith('/email-template/send-email-api');

      // 6. Check result
      expect(result).toEqual(mockResponse);
    });

    it('should handle errors when sending email fails', async () => {
      const mockError = new Error('Network Error');
      (emailService as any).post.mockRejectedValue(mockError);

      // Use rejects.toThrow to assert that the promise is rejected
      await expect(emailService.sendEmail(mailParams)).rejects.toThrow(mockError);

      // Verify that the error was logged
      expect(Logger.error).toHaveBeenCalledWith('Failed to send email', mockError);
    });
  });
});