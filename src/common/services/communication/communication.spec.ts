import axios, { AxiosRequestConfig } from 'axios';
import { Communication } from './communication';
// Create a mock class that extends Communication
class CommunicationMock extends Communication {
  // Expose a public method to call the protected method
  public publicFormat(url) {
    return this['format'](url);
  }
  public publicSetQueryParams(params: any) {
    return this['setQueryParams'](params);
  }
  public publicSetRawBody(params: any) {
    return this['setRawBody'](params);
  }
  public publicSetRequestHeader(key: string, value: string) {
    return this['setRequestHeader'](key, value);
  }

  public publicSetRequestHeaders(params: any) {
    return this['setRequestHeaders'](params);
  }
  public publicGetRequestConfig() {
    return this['getRequestConfig']();
  }
  public publicPutRequestConfig() {
    return this['getPutRequestConfig']();
  }
  public publicPostRequestConfig() {
    return this['getPostRequestConfig']();
  }

  public publicSetFormData(data) {
    return this['setFormData'](data);
  }
  public publicGet(path) {
    return this['get'](path);
  }
  public publicPut(path) {
    return this['put'](path);
  }
  public publicPost(path) {
    return this['post'](path);
  }
}
jest.mock('axios');

describe('Communication', () => {
  let communication: CommunicationMock;

  beforeEach(() => {
    communication = new CommunicationMock();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
  it('should update the URL correctly', () => {
    const initialUrl = communication['url']; // Access the protected 'url' property directly
    const path1 = '/api/some-endpoint';
    const path2 = '/api/another-endpoint';

    communication.publicFormat(path1);
    expect(communication['url']).toBe(initialUrl + path1); // Verify URL after format(path1)

    communication.publicFormat(path2);
    expect(communication['url']).toBe(initialUrl + path1 + path2); // Verify URL after format(path2)
  });

  it('should set queryParams correctly', () => {
    const params1 = { key1: 'value1' };
    const params2 = { key2: 'value2' };

    communication.publicSetQueryParams(params1);
    expect(communication['queryParams']).toEqual(params1); // Verify queryParams after setQueryParams(params1)

    communication.publicSetQueryParams(params2);
    expect(communication['queryParams']).toEqual(params2); // Verify queryParams after setQueryParams(params2)
  });
  it('should set the raw body correctly', () => {
    const params = { key: 'value' };

    // Call the protected method setRawBody
    communication['publicSetRawBody'](params);

    // Expect the rawBody property to be set correctly
    expect(communication['rawBody']).toEqual(params);
  });

  it('should set the request header correctly', () => {
    const key = 'Authorization';
    const value = 'Bearer token';

    // Call the protected method setRequestHeader
    communication['publicSetRequestHeader'](key, value);

    // Expect the requestHeader property to be set correctly
    expect(communication['requestHeader']).toEqual({ [key]: value });
  });

  it('should set the request headers correctly', () => {
    const headers = { 'Content-Type': 'application/json' };

    // Call the protected method setRequestHeaders
    communication['publicSetRequestHeaders'](headers);

    // Expect the requestHeader property to be set correctly
    expect(communication['requestHeader']).toEqual(headers);
  });
  describe('getRequestConfig', () => {
    it('should return the correct GET request config', () => {
      const expectedConfig: AxiosRequestConfig = {
        method: 'GET',
        url: '',
        headers: communication['requestHeader'],
        params: communication['queryParams'],
      };

      const config = communication.publicGetRequestConfig();
      expect(config).toEqual(expectedConfig);
    });
  });
  it('should set queryParams correctly', () => {
    const params1 = { key1: 'value1' };
    const params2 = { key2: 'value2' };

    communication.publicSetQueryParams(params1);
    expect(communication['queryParams']).toEqual(params1); // Verify queryParams after setQueryParams(params1)

    communication.publicSetQueryParams(params2);
    expect(communication['queryParams']).toEqual(params2); // Verify queryParams after setQueryParams(params2)
  });
  describe('getPostRequestConfig', () => {
    it('should return the correct POST request config', () => {
      communication.publicSetFormData({ key: 'value' });
      const expectedConfig: AxiosRequestConfig = {
        method: 'POST',
        url: '',
        headers: communication['requestHeader'],
        data: communication['formData'],
        params: communication['queryParams'],
      };

      const config = communication.publicPostRequestConfig();
      expect(config).toEqual(expectedConfig);
    });
  });

  describe('getPutRequestConfig', () => {
    it('should return the correct PUT request config', () => {
      communication.publicSetFormData({ key: 'value' });
      const expectedConfig: AxiosRequestConfig = {
        method: 'PUT',
        url: '',
        headers: communication['requestHeader'],
        data: communication['formData'],
        params: communication['queryParams'],
      };

      const config = communication.publicPutRequestConfig();
      expect(config).toEqual(expectedConfig);
    });
  });
  it('should make a GET request with the correct configuration', async () => {
    const path = '/api/some-endpoint';
    const expectedConfig: AxiosRequestConfig = {
      method: 'GET',
      url: path,
      headers: null,
      params: null,
    };

    // Mock the axios function to return a resolved promise with some data
    const responseData = { data: 'mocked response' };
    (axios as any).mockResolvedValueOnce(responseData);

    // Call the get method
    const response = await communication['publicGet'](path);

    // Expect the axios function to be called with the correct configuration
    expect(axios).toHaveBeenCalledWith(expectedConfig);

    // Expect the response to be equal to the mocked response
    expect(response).toEqual(responseData);
  });

  it('should make a PUT request with the correct configuration', async () => {
    const path = '/api/some-endpoint';
    const expectedConfig: AxiosRequestConfig = {
      method: 'PUT',
      url: path,
      headers: null,
      data: null,
      params: null,
    };

    // Mock the axios function to return a resolved promise with some data
    const responseData = { data: 'mocked response' };
    (axios as any).mockResolvedValueOnce(responseData);

    // Call the put method
    const response = await communication['publicPut'](path);

    // Expect the axios function to be called with the correct configuration
    expect(axios).toHaveBeenCalledWith(expectedConfig);

    // Expect the response to be equal to the mocked response data
    expect(response).toEqual(responseData);
  });

  it('should make a POST request with the correct configuration', async () => {
    const path = '/api/some-endpoint';
    const expectedConfig: AxiosRequestConfig = {
      method: 'POST',
      url: path,
      headers: null,
      data: null,
      params: null,
    };

    // Mock the axios function to return a resolved promise with some data
    const responseData = { data: 'mocked response' };
    (axios as any).mockResolvedValueOnce(responseData);

    // Call the get method
    const response = await communication['publicPost'](path);

    // Expect the axios function to be called with the correct configuration
    expect(axios).toHaveBeenCalledWith(expectedConfig);

    // Expect the response to be equal to the mocked response
    expect(response).toEqual(responseData.data);
  });
});
