import { ConfigService } from '@nestjs/config';
import { Communication } from '../communication';
import {  Injectable } from '@nestjs/common';
import { Logger } from '../../../../logging/logger';


@Injectable()
export class DeviantsService extends Communication {
  protected url: string;
  constructor(private readonly configService: ConfigService) {
    super();
    this.url = this.configService.get('deviantsUrl') + 'v1';
  }
  public resetUri() {
    this.url = this.configService.get('deviantsUrl') + 'v1';
  }
  public _setRemoteMethod(method: string): void {
    this.url = this.url.replace(/\/$/, '') + '/' + method.replace(/^\//, '');
}


async pushMessageToQueue(data: any): Promise<{ status: string; message: string }> {
  this.resetUri();

 
  this.setRawBody(JSON.stringify(data));
  try {
    const result = await this.post('/users')

if (result?.Status === 'success') {
  return { status: 'success', message: 'Data pushed into queue' };
}
return { status: 'failed', message:  'Something went wrong while pushing data to the queue' };
      
  } catch (error: any) {
    Logger.error('pushMessageToQueue', {
      METHOD: this.constructor.name + '@' + this.pushMessageToQueue.name,
      MESSAGE: error.message,
      REQUEST: { data },
      RESPONSE: error.stack,
      TIMESTAMP: new Date().getTime(), });

      return { status: "error", message: error.message };
  }
}

}
