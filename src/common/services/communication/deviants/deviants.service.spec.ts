import { Test, TestingModule } from '@nestjs/testing';
import { DeviantsService } from './deviants.service';
import { ConfigService } from '@nestjs/config';
//import { Communication } from '../communication';

jest.mock('../communication'); // Mock Communication base class

describe('DeviantsService', () => {
  let service: DeviantsService;
  let configService: ConfigService;

  beforeEach(async () => {
    configService = new ConfigService();
    jest.spyOn(configService, 'get').mockImplementation((key: string) => {
      if (key === 'deviantsUrl') return 'http://mock-url/';
      return null;
    });

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeviantsService,
        { provide: ConfigService, useValue: configService },
      ],
    }).compile();

    service = module.get<DeviantsService>(DeviantsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should initialize url correctly', () => {
    expect(service['url']).toBe('http://mock-url/v1');
  });

  it('should reset URI correctly', () => {
    service.resetUri();
    expect(service['url']).toBe('http://mock-url/v1');
  });

  it('should set remote method correctly', () => {
    service['_setRemoteMethod']('testMethod');
    expect(service['url']).toBe('http://mock-url/v1/testMethod');
  });

  it('should push message to queue successfully', async () => {
    const mockPost = jest.spyOn(service, 'post').mockResolvedValue({
      data: { Status: true },
    });

    const response = await service.pushMessageToQueue({ key: 'value' });
    expect(mockPost).toHaveBeenCalledWith('/users');
    expect(response).toEqual({ status: 'success', message: 'Data pushed into queue' });
  });

  it('should handle failure when pushing message to queue', async () => {
    jest.spyOn(service, 'post').mockResolvedValue({
      data: { Status: false },
    });

    const response = await service.pushMessageToQueue({ key: 'value' });
    expect(response).toEqual({
      status: 'failed',
      message: 'Something went wrong while pushing data to the queue',
    });
  });

  it('should handle error when pushing message to queue', async () => {
    jest.spyOn(service, 'post').mockRejectedValue(new Error('Network Error'));

    const response = await service.pushMessageToQueue({ key: 'value' });
    expect(response).toEqual({ status: 'error', message: 'Network Error' });
  });
});