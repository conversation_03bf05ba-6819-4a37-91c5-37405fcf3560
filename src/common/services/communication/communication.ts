import axios, { AxiosRequestConfig } from 'axios';
import { Logger } from '../../../logging/logger';

export class Communication {
  protected url = '';
  private queryParams: any = null;
  private formData: any = null;
  private rawBody: any = null;
  private requestHeader: any = null;

  protected format(path: string) {
    this.url += path;
  }

  protected setQueryParams(params: any) {
    this.queryParams = params;
  }

  public setRawBody(params: any) {
    this.rawBody = params;
  }

  protected setFormData(params: any) {
    this.formData = params;
  }

  protected setRequestHeader(key: string, value: string) {
    if (!this.requestHeader) {
      this.requestHeader = {};
    }
    this.requestHeader[key] = value;
  }

  protected setRequestHeaders(params: any) {
    this.requestHeader = params;
  }

  protected getRequestConfig(): AxiosRequestConfig {
    return {
      method: 'GET',
      url: this.url,
      headers: this.requestHeader,
      params: this.queryParams,
    };
  }

  protected getPostRequestConfig(): AxiosRequestConfig {
    return {
      method: 'POST',
      url: this.url,
      headers: this.requestHeader,
      data: this.formData || this.rawBody,
      params: this.queryParams,
    };
  }

  protected getPutRequestConfig(): AxiosRequestConfig {
    return {
      method: 'PUT',
      url: this.url,
      headers: this.requestHeader,
      data: this.formData || this.rawBody,
      params: this.queryParams,
    };
  }

  get(path: string) {
    this.format(path);
    return axios(this.getRequestConfig());
  }

  public post(path: string) {
    this.format(path);
    Logger.info('Path', this.getPostRequestConfig());
    return axios(this.getPostRequestConfig())
      .then((response) => {
        return response.data;
      })
      .catch((error) => {
        Logger.error('Communication post call', error);
        throw error;
      });
  }

  put(path: string) {
    this.format(path);
    return axios(this.getPutRequestConfig());
  }
}
