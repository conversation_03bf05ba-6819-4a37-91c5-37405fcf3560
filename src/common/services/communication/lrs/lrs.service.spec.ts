import { Test, TestingModule } from '@nestjs/testing';
import { LrsService } from './lrs.service';
import { ConfigService } from '@nestjs/config';
import { Logger } from '../../../../logging/logger';

jest.mock('../../../../logging/logger', () => ({
  Logger: {
    log: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock('../communication'); // to mock Communication base class

describe('LrsService', () => {
  let lrsService: LrsService;
  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LrsService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    lrsService = module.get<LrsService>(LrsService);

    // Default mock implementation
    mockConfigService.get.mockImplementation((key: string) => {
      if (key === 'lrsApiUrl') return 'http://lrsapi.example.com';
      return null;
    });

    jest.clearAllMocks();
  });

  const mockPayload = {
    verb: 'register',
    objectType: 'accounts',
    objectId: '1004029',
    dataVals: '{"client_id":"sl_looper"}',
    applicationId: 2,
    applicationName: 'cloud6',
    userId: 'anonymous_userid',
    userEmail: 'anonymous_email',
    userName: '',
    firstName: 'XXXXXX',
    lastName: 'XXXXXX',
    timezone: 'America/Chicago',
    userRole: '{"2":"authenticated user","59":"looper_student"}',
    signature: '74c5ffce294eda9d89d393c4058348aa',
    eventTime: *************,
    ip: '127.0.0.1',
    referalUrl: '',
    url: 'localhost-cli',
    userAgent: 'localhost-cli',
    from_server: 'true',
    ttl: *************,
  };

  it('should be defined', () => {
    expect(lrsService).toBeDefined();
  });

  it('should send LRS data successfully and log the result', async () => {
    const mockResponse = { status: 'ok' };

    const postSpy = jest
      .spyOn(lrsService as any, 'post')
      .mockResolvedValue(mockResponse);

    const response = await lrsService.sendLrsData(mockPayload);

    expect(mockConfigService.get).toHaveBeenCalledWith('lrsApiUrl');
    expect(postSpy).toHaveBeenCalledWith('/save_data');
    expect(response).toEqual(mockResponse);
    expect(Logger.log).toHaveBeenCalledWith('LRS sent successfully', { data: mockResponse });
  });

  it('should log and return error if sending LRS fails', async () => {
    const error = new Error('Failed to send data to LRS');
    const postSpy = jest
      .spyOn(lrsService as any, 'post')
      .mockRejectedValue(error);

    const response = await lrsService.sendLrsData(mockPayload);

    expect(mockConfigService.get).toHaveBeenCalledWith('lrsApiUrl');
    expect(postSpy).toHaveBeenCalledWith('/save_data');
    expect(Logger.error).toHaveBeenCalledWith('Failed to send to LRS', { error });
    expect(response).toBe(error);
  });
});
