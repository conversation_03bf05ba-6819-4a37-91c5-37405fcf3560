import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Logger } from '../../../../logging/logger';
import { Communication } from '../communication';

@Injectable()
export class LrsService extends Communication {
  protected url: string;

  constructor(private readonly configService: ConfigService) {
    super();
  }

  protected setUri() {
    this.url = this.configService.get<string>('lrsApiUrl');
  }

  async sendLrsData(payload: object) {
    this.setUri();
    //Set request headers
    this.setRequestHeaders({ 'Content-Type': 'application/x-www-form-urlencoded' });
    //Set post data
    this.setFormData(payload);
    try {
      const response = await this.post('/save_data');
      Logger.log('LRS sent successfully', { data: response });
      return response;
    } catch (error) {
      Logger.error('Failed to send to LRS', { error: error });
      return error;
    }
  }
}
