import { ChangeLogService } from './changelog.service';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { Logger } from '../../../../logging/logger';

jest.mock('axios');
jest.mock('../../../../logging/logger');

describe('ChangeLogService', () => {
  let service: ChangeLogService;
  let configService: ConfigService;

  beforeEach(() => {
    configService = {
      get: jest.fn(),
    } as any;

    service = new ChangeLogService(configService);
  });

  it('should return failure if API flag is not set', async () => {
    (configService.get as jest.Mock).mockReturnValueOnce(null); // simulate the flag not being set
    const result = await service.changeLogRequest({});
    expect(result).toEqual({ status: 'failed', message: 'API flag is not set' });
  });

  it('should throw error if data is null', async () => {
    (configService.get as jest.Mock).mockReturnValueOnce('true'); // simulate API flag being true
    await expect(service.changeLogRequest(null)).rejects.toThrow("No data received in changeLogRequest");
  });

  it('should throw error if userId is not a valid number', async () => {
    (configService.get as jest.Mock).mockReturnValueOnce('true');
    await expect(service.changeLogRequest({ userId: 'NaN' })).rejects.toThrow('Invalid actor_id: Must be a valid number');
  });

  it('should throw error if userEmail is invalid', async () => {
    (configService.get as jest.Mock).mockReturnValueOnce('true');
    await expect(service.changeLogRequest({ userId: '123', userEmail: 'invalid-email' })).rejects.toThrow("Invalid email format: invalid-email");
  });

  it('should call axios.post and return response if no errors', async () => {
    const mockData = {
      userId: '123',
      userEmail: '<EMAIL>',
      changeDate: new Date(),
      module: 'test',
      recordTable: 'users',
      type: 'update',
      oldValue: { name: 'old' },
      newValue: { name: 'new' },
    };

    (configService.get as jest.Mock)
      .mockReturnValueOnce('true') // enableDataChangeLog
      .mockReturnValueOnce('http://test-api'); // dataChangeLogApiUrl

    (axios.post as jest.Mock).mockResolvedValueOnce({ data: { success: true } });

    const result = await service.changeLogRequest(mockData);

    expect(result).toEqual({ success: true });
    expect(axios.post).toHaveBeenCalledWith(
      expect.stringContaining('http://test-api/produce-message'),
      expect.objectContaining({
        actor_id: 123,
        actor_email: '<EMAIL>',
      }),
      { headers: { 'Content-Type': 'application/json' } }
    );
  });

  it('should log error if response contains errors', async () => {
    const mockData = {
      userId: '123',
      userEmail: '<EMAIL>',
      changeDate: new Date(),
      module: 'test',
      recordTable: 'users',
      type: 'update',
      oldValue: { name: 'old' },
      newValue: { name: 'new' },
    };

    (configService.get as jest.Mock)
      .mockReturnValueOnce('true')
      .mockReturnValueOnce('http://test-api');

    const mockResponse = { data: { errors: ['Something went wrong'] } };
    (axios.post as jest.Mock).mockResolvedValueOnce(mockResponse);

    const result = await service.changeLogRequest(mockData);

    expect(Logger.error).toHaveBeenCalledWith(expect.objectContaining({
      METHOD: 'changeLogRequest',
      MESSAGE: 'Failed to send data change log',
    }));
    expect(result).toEqual(mockResponse.data);
  });

  it('should log and return error when axios throws', async () => {
    const mockData = {
      userId: '123',
      userEmail: '<EMAIL>',
      changeDate: new Date(),
      module: 'test',
      recordTable: 'users',
      type: 'update',
      oldValue: {},
      newValue: {},
    };

    const mockError = new Error('Network error');
    (mockError as any).response = { data: 'error response' };

    (configService.get as jest.Mock)
      .mockReturnValueOnce('true')
      .mockReturnValueOnce('http://test-api');

    (axios.post as jest.Mock).mockRejectedValueOnce(mockError);

    const result = await service.changeLogRequest(mockData);

    expect(Logger.error).toHaveBeenCalledWith(expect.objectContaining({
      METHOD: 'changeLogRequest',
      MESSAGE: 'Network error',
    }));

    expect(result).toBe(mockError);
  });

  it('should correctly reset URI in resetUri()', () => {
    (configService.get as jest.Mock).mockReturnValueOnce('http://test-api');
    service.resetUri();
    expect((service as any).url).toBe('http://test-api/');
  });

  it('should correctly append method in setRemoteMethod()', () => {
    (configService.get as jest.Mock).mockReturnValue('http://test-api/');
    service.resetUri();
    service.setRemoteMethod('custom-method');
    expect((service as any).url).toBe('http://test-api/custom-method');
  });

  it('should correctly format URL when base has slash and method has slash', () => {
    (configService.get as jest.Mock).mockReturnValue('http://test-api/');
    service.resetUri();
    service.setRemoteMethod('/custom-method');
    expect((service as any).url).toBe('http://test-api/custom-method');
  });

  it('should correctly format URL when base has no slash and method has no slash', () => {
    (configService.get as jest.Mock).mockReturnValue('http://test-api');
    service.resetUri();
    service.setRemoteMethod('custom-method');
    expect((service as any).url).toBe('http://test-api/custom-method');
  });

  it('should handle cases where the base URL and method both contain slashes', () => {
    (configService.get as jest.Mock).mockReturnValue('http://test-api');
    service.resetUri();
    service.setRemoteMethod('/custom-method/');
    expect((service as any).url).toBe('http://test-api/custom-method/');
  });
});