import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import axios from "axios";
import { Logger } from "../../../../logging/logger";
import { Communication } from "../communication";

@Injectable()
export class ChangeLogService extends Communication{
    protected url: string;
    private DATA_CHANGE_LOG_METHOD = 'produce-message';
    private APPLICATION_NAME = 'CLOUD6';
    
    constructor(private readonly configService: ConfigService) {
        super();
        this.resetUri(); // Initialize `url` when the class is instantiated
      }
    
      /**
       * Reset the URL to the configured data changelog endpoint.
       */
      public resetUri(): void {
        this.url = this.configService.get<string>('dataChangeLogApiUrl') + '/';
      }
    
      /**
       * Append a method name to the base URL, ensuring proper formatting.
       * @param method - The method name to append to the URL.
       */
      public setRemoteMethod(method: string): void {
        this.url = this.url.replace(/\/$/, '') + '/' + method.replace(/^\//, '');
      }

    async changeLogRequest(data:any): Promise<{status: string; message: string}>{
        const logFlag = await this.configService.get<string>('enableDataChangeLog');
        if (!logFlag){
            return {status: 'failed', message: 'API flag is not set'};
        }

        if(!data){
            throw new Error("No data received in changeLogRequest");
        }
        
        const actorId = parseInt(data.userId,10);
        if(isNaN(actorId)){
            throw new Error('Invalid actor_id: Must be a valid number')
        }

        const actorEmail = data.userEmail?.trim().toLowerCase();
        if (!actorEmail || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(actorEmail)) {
            throw new Error("Invalid email format: " + actorEmail);
        }
        
        this.resetUri();
        
        const date = new Date(data.changeDate);
        const formattedDate = `${date.getDate().toString().padStart(2, '0')}-${(date.getMonth() + 1)
            .toString().padStart(2, '0')}-${date.getFullYear()} ${date.getHours()
            .toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date
            .getSeconds().toString().padStart(2, '0')}`;

        const inputParams = {
            application_name: this.APPLICATION_NAME,
            actor_id: actorId,
            actor_email: actorEmail,
            module: data.module,
            record_table: data.recordTable,
            change_type: {
            type: data.type,
            old_value: JSON.stringify(data.oldValue).replace(/"/g, "'"),
            new_value: JSON.stringify(data.newValue).replace(/"/g, "'"),
            },
            change_date: formattedDate,
        };
        
        const headers = {
            'Content-Type': 'application/json',
        };
    
        try{
            const response = await axios.post(`${this.url}${this.DATA_CHANGE_LOG_METHOD}`,inputParams, { headers });

            if (response.data.errors) {
                Logger.error({
                    METHOD: 'changeLogRequest',
                    MESSAGE: 'Failed to send data change log',
                    RESPONSE: response.data,
                    USERID: data.userId,
                });
            }
            return response.data;
        } 
        catch(error: any) {
            Logger.error({
                METHOD: 'changeLogRequest',
                MESSAGE: error.message,
                EXCEPTION: error.stack,
                REQUEST: data,
                RESPONSE: error.response ? error.response.data : null,
                USERID: data.userId,
            });
            return error;
        }
    }

}