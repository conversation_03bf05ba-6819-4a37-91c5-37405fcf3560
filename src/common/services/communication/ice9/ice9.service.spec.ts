
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { Ic9Service } from './ice9.service';

describe('Ic9Service', () => {
  let service: Ic9Service;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        Ic9Service,
        {
          provide: ConfigService,
          useValue: mockConfigService
        }
      ],
    }).compile();

    service = module.get<Ic9Service>(Ic9Service);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getCountryJson', () => {
    it('should return country data successfully', async () => {
      const mockCountryData = { countries: [] };
      mockConfigService.get.mockReturnValue('http://mock-cdn-url');
      jest.spyOn(service as any, 'get').mockResolvedValue(mockCountryData);

      const result = await service.getCountryJson();

      expect(configService.get).toHaveBeenCalledWith('countyDataCdnLink');
      expect(result).toEqual(mockCountryData);
    });

    it('should throw error when API call fails', async () => {
      const error = new Error('API Error');
      mockConfigService.get.mockReturnValue('http://mock-cdn-url');
      jest.spyOn(service as any, 'get').mockRejectedValue(error);

      await expect(service.getCountryJson()).rejects.toThrow('API Error');
    });
  });

  describe('getCountryRegionJson', () => {
    it('should return region data successfully', async () => {
      const mockRegionData = { data: { regions: [] } };
      const countryId = 'US';
      mockConfigService.get.mockReturnValue('http://mock-api-url');
      jest.spyOn(service as any, 'get').mockResolvedValue(mockRegionData);

      const result = await service.getCountryRegionJson(countryId);

      expect(configService.get).toHaveBeenCalledWith('ice9ApiUrl');
      expect(result).toEqual(mockRegionData.data);
    });

    it('should throw error when countryId is empty', async () => {
      await expect(service.getCountryRegionJson('')).rejects.toThrow('Country ID cannot be empty.');
    });

    it('should throw error when API call fails', async () => {
      const error = new Error('API Error');
      const countryId = 'US';
      mockConfigService.get.mockReturnValue('http://mock-api-url');
      jest.spyOn(service as any, 'get').mockRejectedValue(error);

      await expect(service.getCountryRegionJson(countryId)).rejects.toThrow('API Error');
    });
  });
});