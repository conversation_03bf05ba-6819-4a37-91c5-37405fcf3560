import { ConfigService } from '@nestjs/config';
import { Communication } from '../communication';
import { Injectable } from '@nestjs/common';
import { Logger } from './../../../../logging/logger';

@Injectable()
export class Ic9Service extends Communication {
  protected url: string;
  constructor(private readonly configService: ConfigService) {
    super();
  }
  // private _resetUri() {
  //   this.url = this.configService.get<string>('IC9_URL');
  // }
  protected _resetIc9SimplexUri() {
    this.url = this.configService.get<string>('ice9ApiUrl');
  }
  protected _setRemoteMethod(method: string): void {
    this.url += method; // Appends the method name to the URL
  }

  async getCountryJson() {
    try {
      this.url = this.configService.get('countyDataCdnLink');
      return await this.get('');
    } catch (error: any) {
      Logger.error('getCountryJson', {
        METHOD: this.constructor.name + '@' + this.getCountryJson.name,
        MESSAGE: error.message,
      });
      throw error;
    }
  }

  async getCountryRegionJson(countryId: string) {
    try {
      if (!countryId) {
        Logger.warn('Country ID cannot be empty.');
        throw new Error('Country ID cannot be empty.');
      }
      const params = { countryId: countryId, method: 'getRegionCityByCountry' };
      this._resetIc9SimplexUri();
      this.setQueryParams(params);
      const response = await this.get('/api/v3/index');
      return response?.data;
    } catch (error: any) {
      Logger.error('getCountryJson', {
        METHOD: this.constructor.name + '@' + this.getCountryJson.name,
        MESSAGE: error.message,
        REQUEST: countryId,
        STACK: error.stack,
      });
      throw error;
    }
  }
}
