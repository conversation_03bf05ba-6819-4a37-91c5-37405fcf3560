import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { Cloud6Service } from './cloud6.service';
import { Logger } from '../../../../logging/logger';
import { CryptoHelper } from '../../../../helper/helper.crypto';

jest.mock('../../../../logging/logger', () => ({
  Logger: {
    error: jest.fn(),
  },
}));
jest.mock('../communication');

describe('Cloud6Service', () => {
  let service: Cloud6Service;
  let configService: ConfigService;
  let cryptoHelper: CryptoHelper;

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn(),
    };

    const mockCryptoHelper = {
      createHmac: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        Cloud6Service,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: mockCryptoHelper,
        },
      ],
    }).compile();

    service = module.get<Cloud6Service>(Cloud6Service);
    configService = module.get<ConfigService>(ConfigService);
    cryptoHelper = module.get<CryptoHelper>('CRYPTO_HELPER');
  });

  beforeEach(() => {
    (Logger.error as jest.Mock).mockClear(); // ✅ Clears logger mock calls
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getUserEnterpriseList', () => {
    it('should fetch user enterprise list successfully', async () => {
      const mockResponse = { data: 'mockData' };
      jest.spyOn(service as any, 'post').mockResolvedValue(mockResponse);

      const result = await service.getUserEnterpriseList({ key: 'value' });

      expect(service['post']).toHaveBeenCalledWith('/get-user-enterprise-list-course');
      expect(result).toEqual(mockResponse);
    });

    it('should log error and throw when fetching user enterprise list fails', async () => {
      const mockError = new Error('Network error');
      jest.spyOn(service as any, 'post').mockRejectedValue(mockError);

      await expect(service.getUserEnterpriseList({ key: 'value' })).rejects.toThrow('Network error');
      expect(Logger.error).toHaveBeenCalledWith('getUserEnterpriseList', expect.any(Object));
    });
  });

  describe('getLmsEnterpriseSettings', () => {
    it('should fetch LMS enterprise settings successfully', async () => {
      const mockResponse = { data: 'mockData' };
      jest.spyOn(service as any, 'get').mockResolvedValue(mockResponse);

      const result = await service.getLmsEnterpriseSettings({ key: 'value' });

      expect(service['get']).toHaveBeenCalledWith('get-enterprise-lms-settings-for-gid');
      expect(result).toEqual(mockResponse);
    });

    it('should log error and return error object when fetching LMS enterprise settings fails', async () => {
      const mockError = new Error('Network error');
      jest.spyOn(service as any, 'get').mockRejectedValue(mockError);

      const result = await service.getLmsEnterpriseSettings({ key: 'value' });

      expect(Logger.error).toHaveBeenCalledWith('getLmsEnterpriseSettings', {
        METHOD: expect.stringContaining('Cloud6Service@getLmsEnterpriseSettings'),
        MESSAGE: mockError.message,
        REQUEST: { reqObj: { key: 'value' } },
        RESPONSE: mockError.stack,
        TIMESTAMP: expect.any(Number),
      });
      expect(result).toEqual(mockError);
    });
  });

  describe('sendWelcomeEmailToManager', () => {
    it('should send welcome email successfully', async () => {
      const mockResponse = { data: 'mockData' };
      jest.spyOn(service as any, 'get').mockResolvedValue(mockResponse);

      const result = await service.sendWelcomeEmailToManager({ email: '<EMAIL>' });

      expect(service['get']).toHaveBeenCalledWith('send-welcome-email-to-manager');
      expect(result).toEqual(mockResponse);
    });

    it('should log error and throw when sending welcome email fails', async () => {
      const mockError = new Error('Network error');
      jest.spyOn(service as any, 'get').mockRejectedValue(mockError);

      await expect(service.sendWelcomeEmailToManager({ email: '<EMAIL>' })).rejects.toThrow('Network error');
      expect(Logger.error).toHaveBeenCalledWith('sendWelcomeEmailToManager', expect.any(Object));
    });
  });

  describe('setCloud6AuthorizationHeader', () => {
    it('should set authorization header correctly', () => {
      const mockTimestamp = Math.round(new Date().getTime() / 1000);
      const mockAuthorization = 'mockAuthorization';
      jest.spyOn(global.Date, 'now').mockReturnValue(mockTimestamp * 1000);
      (cryptoHelper.createHmac as jest.Mock).mockReturnValue(mockAuthorization);
      (configService.get as jest.Mock)
        .mockReturnValueOnce('mockSalt')
        .mockReturnValueOnce('mockAlgo')
        .mockReturnValueOnce('mockEncoding');

      service['setCloud6AuthorizationHeader']();

      expect(cryptoHelper.createHmac).toHaveBeenCalledWith(
        mockTimestamp.toString(),
        'mockSalt',
        'mockAlgo',
        'mockEncoding',
      );
      expect(service['setRequestHeaders']).toHaveBeenCalledWith({
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: mockAuthorization,
        timestamp: mockTimestamp,
      });
    });
  });

  describe('getTermsAndConditions', () => {
    it('should fetch and extract terms and styles correctly', async () => {
      const mockResponse = {
        data: {
          termsAndConditions: `
          <style>
            .example { color: red; }
          </style>
          <p>Welcome to the terms!</p>
        `,
          source: 'cms',
        },
      };

      jest.spyOn(service as any, 'get').mockResolvedValue(mockResponse);
      jest
        .spyOn(service as any, 'scopeStylesToModal')
        .mockImplementation((css) => `.modal { ${css} }`);
      service['setCloud6AuthorizationHeader'] = jest.fn();
      service['resetUri'] = jest.fn();

      const result = await service.getTermsAndConditions();

      expect(service['resetUri']).toHaveBeenCalled();
      expect(service['setCloud6AuthorizationHeader']).toHaveBeenCalled();
      expect(service['get']).toHaveBeenCalledWith('/get-terms-and-conditions');

      expect(result.termsAndConditions).toBe('<p>Welcome to the terms!</p>');
      expect(result.source).toBe('cms');
      expect(result.styles.replace(/\s+/g, ' ').trim()).toBe('.modal { .example { color: red; } }');
    });

    it('should log and throw error if request fails', async () => {
      const mockError = new Error('Something failed');
      jest.spyOn(service as any, 'get').mockRejectedValue(mockError);
      service['resetUri'] = jest.fn();
      service['setCloud6AuthorizationHeader'] = jest.fn();

      await expect(service.getTermsAndConditions()).rejects.toThrow('Something failed');

      expect(Logger.error).toHaveBeenCalledWith('getTermsAndConditions', {
        METHOD: expect.stringContaining('Cloud6Service@getTermsAndConditions'),
        MESSAGE: mockError.message,
        REQUEST: {},
        RESPONSE: mockError.stack,
        TIMESTAMP: expect.any(Number),
      });
    });

    it('should return empty terms and styles if response has no content', async () => {
      const mockResponse = {
        data: {
          termsAndConditions: '',
          source: 'cms',
        },
      };

      jest.spyOn(service as any, 'get').mockResolvedValue(mockResponse);
      service['resetUri'] = jest.fn();
      service['setCloud6AuthorizationHeader'] = jest.fn();

      const result = await service.getTermsAndConditions();

      expect(result).toEqual({
        termsAndConditions: '',
        styles: '',
        source: 'cms',
      });

      // scopeStylesToModal should not be called
      expect(service['get']).toHaveBeenCalledWith('/get-terms-and-conditions');
    });

  });

  describe('scopeStylesToModal', () => {
    it('should scope global and generic selectors correctly', () => {
      const rawStyles = `
    body {
      margin: 0;
    }
    html {
      height: 100%;
    }
    div {
      padding: 10px;
    }
  `;

      const scoped = service['scopeStylesToModal'](rawStyles);

      expect(scoped).toContain('#termsModal body {');
      expect(scoped).toContain('#termsModal html {');
      expect(scoped).toContain('#termsModal div{'); // <-- fix here
    });


    it('should skip already scoped, media, keyframes, and class selectors', () => {
      const rawStyles = `
      #termsModal div {
        padding: 5px;
      }
      @media screen and (max-width: 600px) {
        div {
          padding: 0;
        }
      }
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      .custom-class {
        color: blue;
      }
    `;

      const scoped = service['scopeStylesToModal'](rawStyles);

      expect(scoped).toContain('#termsModal div {'); // already scoped
      expect(scoped).toContain('@media screen and (max-width: 600px) {');
      expect(scoped).toContain('@keyframes fadeIn {');
      expect(scoped).toContain('.custom-class {');
    });
  });

  describe('getUserTeamByEmail', () => {
    it('should fetch team info for given email', async () => {
      const reqObj = { email: '<EMAIL>' };
      const mockResponse = { data: 'teamData' };

      jest.spyOn(configService, 'get').mockReturnValue('https://mock-cloud6');
      service['setCloud6AuthorizationHeader'] = jest.fn();
      service['setRawBody'] = jest.fn();
      jest.spyOn(service as any, 'post').mockResolvedValue(mockResponse);

      const result = await service.getUserTeamByEmail(reqObj);

      expect(service['setCloud6AuthorizationHeader']).toHaveBeenCalled();
      expect(service['setRawBody']).toHaveBeenCalledWith(JSON.stringify(reqObj));
      expect(service['post']).toHaveBeenCalledWith('get-user-team-by-email');
      expect(result).toEqual(mockResponse);
    });

    it('should log and throw error on failure', async () => {
      const reqObj = { email: '<EMAIL>' };
      const error = new Error('API failed');

      jest.spyOn(configService, 'get').mockReturnValue('https://mock-cloud6');
      service['setCloud6AuthorizationHeader'] = jest.fn();
      service['setRawBody'] = jest.fn();
      jest.spyOn(service as any, 'post').mockRejectedValue(error);

      await expect(service.getUserTeamByEmail(reqObj)).rejects.toThrow('API failed');

      expect(Logger.error).toHaveBeenCalledWith('getUserTeamByEmail', {
        METHOD: expect.stringContaining('Cloud6Service@getUserTeamByEmail'),
        MESSAGE: error.message,
        REQUEST: reqObj,
        RESPONSE: error.stack,
        TIMESTAMP: expect.any(Number),
      });
    });
  });

  describe('getUserGroupByEmail', () => {
    it('should fetch group info for given email', async () => {
      const reqObj = { email: '<EMAIL>' };
      const mockResponse = { data: 'groupData' };

      jest.spyOn(configService, 'get').mockReturnValue('https://mock-cloud6');
      service['setCloud6AuthorizationHeader'] = jest.fn();
      service['setRawBody'] = jest.fn();
      jest.spyOn(service as any, 'post').mockResolvedValue(mockResponse);

      const result = await service.getUserGroupByEmail(reqObj);

      expect(service['setCloud6AuthorizationHeader']).toHaveBeenCalled();
      expect(service['setRawBody']).toHaveBeenCalledWith(JSON.stringify(reqObj));
      expect(service['post']).toHaveBeenCalledWith('get-user-group-by-email');
      expect(result).toEqual(mockResponse);
    });

    it('should log and throw error on failure', async () => {
      const reqObj = { email: '<EMAIL>' };
      const error = new Error('API failed');

      jest.spyOn(configService, 'get').mockReturnValue('https://mock-cloud6');
      service['setCloud6AuthorizationHeader'] = jest.fn();
      service['setRawBody'] = jest.fn();
      jest.spyOn(service as any, 'post').mockRejectedValue(error);

      await expect(service.getUserGroupByEmail(reqObj)).rejects.toThrow('API failed');

      expect(Logger.error).toHaveBeenCalledWith('getUserGroupByEmail', {
        METHOD: expect.stringContaining('Cloud6Service@getUserGroupByEmail'),
        MESSAGE: error.message,
        REQUEST: reqObj,
        RESPONSE: error.stack,
        TIMESTAMP: expect.any(Number),
      });
    });
  });

  describe('syncUserDataWithMySQL', () => {
    it('should sync user data to MySQL', async () => {
      const reqObj = { uid: '123', name: 'Test User' };
      const mockResponse = { success: true };

      service['resetUri'] = jest.fn();
      service['setCloud6AuthorizationHeader'] = jest.fn();
      service['setRawBody'] = jest.fn();
      jest.spyOn(configService, 'get').mockReturnValue('https://mock-cloud6');
      jest.spyOn(service as any, 'post').mockResolvedValue(mockResponse);

      const result = await service.syncUserDataWithMySQL(reqObj);

      expect(service['resetUri']).toHaveBeenCalled();
      expect(service['setCloud6AuthorizationHeader']).toHaveBeenCalled();
      expect(service['setRawBody']).toHaveBeenCalledWith(JSON.stringify(reqObj));
      expect(service['post']).toHaveBeenCalledWith('/update-user-sync');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('createUserDataWithMySQL', () => {
    it('should create user in MySQL', async () => {
      const reqObj = { uid: '123', name: 'Test User' };
      const mockResponse = { success: true };

      service['resetUri'] = jest.fn();
      service['setCloud6AuthorizationHeader'] = jest.fn();
      service['setRawBody'] = jest.fn();
      jest.spyOn(configService, 'get').mockReturnValue('https://mock-cloud6');
      jest.spyOn(service as any, 'post').mockResolvedValue(mockResponse);

      const result = await service.createUserDataWithMySQL(reqObj);

      expect(service['resetUri']).toHaveBeenCalled();
      expect(service['setCloud6AuthorizationHeader']).toHaveBeenCalled();
      expect(service['setRawBody']).toHaveBeenCalledWith(JSON.stringify(reqObj));
      expect(service['post']).toHaveBeenCalledWith('/create-user');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('syncTaxonomyUserDataWithMySQL', () => {
    it('should sync taxonomy user data in MySQL', async () => {
      const reqObj = { uid: '789', taxonomy: 'demo' };
      const mockResponse = { success: true };

      service['resetUri'] = jest.fn();
      service['setCloud6AuthorizationHeader'] = jest.fn();
      service['setRawBody'] = jest.fn();
      jest.spyOn(configService, 'get').mockReturnValue('https://mock-cloud6');
      jest.spyOn(service as any, 'post').mockResolvedValue(mockResponse);

      const result = await service.syncTaxonomyUserDataWithMySQL(reqObj);

      expect(service['resetUri']).toHaveBeenCalled();
      expect(service['setCloud6AuthorizationHeader']).toHaveBeenCalled();
      expect(service['setRawBody']).toHaveBeenCalledWith(JSON.stringify(reqObj));
      expect(service['post']).toHaveBeenCalledWith('/update-user-taxonomy-data');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getUserNameByUids', () => {
    it('should return user names by UIDs', async () => {
      const payload = { uids: ['u1', 'u2'] };
      const mockResponse = { data: [{ uid: 'u1', name: 'User 1' }] };

      service['resetUri'] = jest.fn();
      service['setCloud6AuthorizationHeader'] = jest.fn();
      service['setRawBody'] = jest.fn();
      jest.spyOn(configService, 'get').mockReturnValue('https://mock-cloud6');
      jest.spyOn(service as any, 'post').mockResolvedValue(mockResponse);

      const result = await service.getUserNameByUids(payload);

      expect(service['resetUri']).toHaveBeenCalled();
      expect(service['setCloud6AuthorizationHeader']).toHaveBeenCalled();
      expect(service['setRawBody']).toHaveBeenCalledWith(JSON.stringify(payload));
      expect(service['post']).toHaveBeenCalledWith('get-user-names-by-uids');
      expect(result).toEqual(mockResponse);
    });

    it('should log and throw error if fetch fails', async () => {
      const payload = { uids: ['u1'] };
      const error = new Error('User fetch failed');

      service['resetUri'] = jest.fn();
      service['setCloud6AuthorizationHeader'] = jest.fn();
      service['setRawBody'] = jest.fn();
      jest.spyOn(configService, 'get').mockReturnValue('https://mock-cloud6');
      jest.spyOn(service as any, 'post').mockRejectedValue(error);

      await expect(service.getUserNameByUids(payload)).rejects.toThrow('User fetch failed');

      expect(Logger.error).toHaveBeenCalledWith(
        'getUserNameByUids',
        expect.objectContaining({
          METHOD: expect.stringContaining('Cloud6Service@getUserNameByUids'),
          MESSAGE: error.message,
          REQUEST: { payload },
          RESPONSE: expect.stringContaining('User fetch failed'),
          TIMESTAMP: expect.any(Number),
        })
      );
    });
  });

  describe('getGroupNameByIds', () => {
    it('should return group names by IDs', async () => {
      const payload = { ids: ['g1', 'g2'] };
      const mockResponse = { data: [{ id: 'g1', name: 'Group 1' }] };

      service['resetUri'] = jest.fn();
      service['setCloud6AuthorizationHeader'] = jest.fn();
      service['setRawBody'] = jest.fn();
      jest.spyOn(configService, 'get').mockReturnValue('https://mock-cloud6');
      jest.spyOn(service as any, 'post').mockResolvedValue(mockResponse);

      const result = await service.getGroupNameByIds(payload);

      expect(service['resetUri']).toHaveBeenCalled();
      expect(service['setCloud6AuthorizationHeader']).toHaveBeenCalled();
      expect(service['setRawBody']).toHaveBeenCalledWith(JSON.stringify(payload));
      expect(service['post']).toHaveBeenCalledWith('get-group-names-by-ids');
      expect(result).toEqual(mockResponse);
    });

    it('should log and throw error if group fetch fails', async () => {
      const payload = { ids: ['g1'] };
      const error = new Error('Group fetch failed');

      service['resetUri'] = jest.fn();
      service['setCloud6AuthorizationHeader'] = jest.fn();
      service['setRawBody'] = jest.fn();
      jest.spyOn(configService, 'get').mockReturnValue('https://mock-cloud6');
      jest.spyOn(service as any, 'post').mockRejectedValue(error);

      await expect(service.getGroupNameByIds(payload)).rejects.toThrow('Group fetch failed');

      expect(Logger.error).toHaveBeenCalledWith(
        'getUserNameByUids', // ❗ Typo in original method — same log key used
        expect.objectContaining({
          METHOD: expect.stringContaining('Cloud6Service@getUserNameByUids'), // ❗ Also shares same method call
          MESSAGE: error.message,
          REQUEST: { payload },
          RESPONSE: expect.stringContaining('Group fetch failed'),
          TIMESTAMP: expect.any(Number),
        })
      );
    });
  });


});