import { ConfigService } from '@nestjs/config';
import { Communication } from '../communication';
import { Inject, Injectable } from '@nestjs/common';
import { Logger } from '../../../../logging/logger';
import { CryptoHelper } from '../../../../helper/helper.crypto';

@Injectable()
export class Cloud6Service extends Communication {
  @Inject('CRYPTO_HELPER') private readonly crypto: CryptoHelper;
  protected url: string;
  constructor(private readonly configService: ConfigService) {
    super();
    this.url = this.configService.get('cloud6Url') + '/user-api/v1';
  }
  protected resetUri() {
    this.url = this.configService.get('cloud6Url') + '/user-api/v1';
  }

  public async getUserEnterpriseList(reqObj: any): Promise<any> {
    try {
      this.resetUri();
      this.setCloud6AuthorizationHeader();
      this.setQueryParams('');
      this.setFormData(reqObj);
      const response = await this.post('/get-user-enterprise-list-course');
      return response;
    } catch (error: any) {
      Logger.error('getUserEnterpriseList', {
        METHOD: this.constructor.name + '@' + this.getUserEnterpriseList.name,
        MESSAGE: error.message,
        REQUEST: { reqObj },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async getLmsEnterpriseSettings(reqObj: any): Promise<any> {
    this.resetUri();
    this.url = this.configService.get('cloud6Url') + '/internal/enterprise-lms-v1/';
    this.setCloud6AuthorizationHeader();
    this.setQueryParams(reqObj);
    return await this.get('get-enterprise-lms-settings-for-gid')
      .then((response) => {
        return response;
      })
      .catch((error) => {
        Logger.error('getLmsEnterpriseSettings', {
          METHOD: this.constructor.name + '@' + this.getLmsEnterpriseSettings.name,
          MESSAGE: error.message,
          REQUEST: { reqObj },
          RESPONSE: error.stack,
          TIMESTAMP: new Date().getTime(),
        });
        return error;
      });
  }

  private setCloud6AuthorizationHeader() {
    const reqTimestamp = Math.round(new Date().getTime() / 1000);
    const authorization = this.crypto.createHmac(
      reqTimestamp.toString(),
      this.configService.get<string>('cloud6ApiAuthSecretSalt'),
      this.configService.get('hmacAlgo'),
      this.configService.get('hmacEncoding'),
    );
    this.setRequestHeaders({
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: authorization,
      timestamp: reqTimestamp,
    });
  }

  async sendWelcomeEmailToManager(payload: any): Promise<any> {
    try {
      this.resetUri();
      this.url = this.configService.get('cloud6Url') + '/internal/enterprise-lms-v1/';
      this.setCloud6AuthorizationHeader();
      this.setRawBody(JSON.stringify(payload));
      const response = await this.get('send-welcome-email-to-manager');
      return response;
    } catch (error: any) {
      Logger.error('sendWelcomeEmailToManager', {
        METHOD: this.constructor.name + '@' + this.sendWelcomeEmailToManager.name,
        MESSAGE: error.message,
        REQUEST: { payload },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  /**
   * Fetch Terms and Conditions content from Cloud6
   * @returns Terms and Conditions HTML content with properly scoped styles
   */
  async getTermsAndConditions(): Promise<any> {
    try {
      this.resetUri();
      this.setCloud6AuthorizationHeader();
      // Use the Sentinel-compatible endpoint that returns source info
      const response = await this.get('/get-terms-and-conditions');

      const rawContent = response?.data?.termsAndConditions || '';
      const source = response?.data?.source || 'api';

      if (!rawContent) {
        return { termsAndConditions: '', styles: '', source: source };
      }

      // Extract styles and content separately (like Cloud6's extractStylesAndTerms)
      let styles = '';
      let content = rawContent;

      // Extract all <style> tags
      const styleMatches = content.match(/<style[^>]*>([\s\S]*?)<\/style>/gi);
      if (styleMatches) {
        styleMatches.forEach(styleTag => {
          // Extract just the CSS content (not the <style> tags themselves)
          const cssContent = styleTag.replace(/<\/?style[^>]*>/gi, '');
          styles += cssContent + '\n';
        });

        // Remove all <style> tags from content
        content = content.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
      }

      // Scope all Cloud6 styles to the modal container
      if (styles.trim()) {
        styles = this.scopeStylesToModal(styles);
      }
      return {
        termsAndConditions: content.trim(),
        styles: styles.trim(),
        source: source,
      };
    } catch (error: any) {
      Logger.error('getTermsAndConditions', {
        METHOD: this.constructor.name + '@' + this.getTermsAndConditions.name,
        MESSAGE: error.message,
        REQUEST: {},
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  /**
   * Scope Cloud6 styles to the terms modal to prevent conflicts with existing Sentinel styles
   * @param styles Raw CSS styles from Cloud6 API
   * @returns Scoped CSS styles that only apply within #termsModal
   */
  private scopeStylesToModal(styles: string): string {
    // Define global selectors that need to be scoped to the modal
    const globalSelectors = [
      'body',
      'html',
      '*',
      '*::before',
      '*::after',
      '*, ::after, ::before',
    ];

    let scopedStyles = styles;

    // Scope global selectors to the modal
    globalSelectors.forEach((selector) => {
      const escapedSelector = selector.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(`\\b${escapedSelector}\\s*\\{`, 'gi');
      scopedStyles = scopedStyles.replace(regex, `#termsModal ${selector} {`);
    });

    // Process line by line to scope remaining selectors
    const lines = scopedStyles.split('\n');
    const scopedLines = lines.map((line) => {
      const trimmedLine = line.trim();

      // Skip empty lines, comments, or already scoped lines
      if (
        !trimmedLine ||
        trimmedLine.startsWith('/*') ||
        trimmedLine.startsWith('//') ||
        line.includes('#termsModal') ||
        !trimmedLine.includes('{')
      ) {
        return line;
      }

      // Check if line contains CSS selector (has { and is not a property)
      const hasSelector =
        trimmedLine.includes('{') &&
        !trimmedLine.includes(':') &&
        !trimmedLine.includes(';');
      if (hasSelector) {
        // Extract selector part before {
        const selectorPart = trimmedLine.substring(0, trimmedLine.indexOf('{')).trim();

        // Don't scope if already has specific selectors, keyframes, or media queries
        if (
          selectorPart.startsWith('#') ||
          selectorPart.startsWith('.') ||
          selectorPart.includes('@') ||
          selectorPart.includes('keyframes') ||
          selectorPart.includes('media')
        ) {
          return line;
        }

        // Scope the selector by replacing the selector part
        const restOfLine = trimmedLine.substring(trimmedLine.indexOf('{'));
        const leadingWhitespace = line.substring(0, line.indexOf(trimmedLine));
        return `${leadingWhitespace}#termsModal ${selectorPart}${restOfLine}`;
      }

      return line;
    });

    return scopedLines.join('\n');
  }

  /**
  * this api is used to get the team id using email
  */
  public async getUserTeamByEmail(reqObj: { email: string }): Promise<any> {
    try {
      this.url = this.configService.get('cloud6Url') + '/internal/communication/';
      this.setCloud6AuthorizationHeader(); 
      this.setRawBody(JSON.stringify(reqObj));
      const response = await this.post('get-user-team-by-email'); 
      return response;
    } catch (error: any) {
      Logger.error('getUserTeamByEmail', {
        METHOD: this.constructor.name + '@' + this.getUserTeamByEmail.name,
        MESSAGE: error.message,
        REQUEST: reqObj,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }


   /**
  * this api is used to get the group id using email
  */
  public async getUserGroupByEmail(reqObj: { email: string }): Promise<any> {
    try {
      this.url = this.configService.get('cloud6Url') + '/internal/communication/';
      this.setCloud6AuthorizationHeader();
      this.setRawBody(JSON.stringify(reqObj));
      const response = await this.post('get-user-group-by-email'); 
      return response;
    } catch (error: any) {
      Logger.error('getUserGroupByEmail', {
        METHOD: this.constructor.name + '@' + this.getUserGroupByEmail.name,
        MESSAGE: error.message,
        REQUEST: reqObj,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

 public syncUserDataWithMySQL(reqObj: any): Promise<any> {
    try {
      this.resetUri();
      this.url = this.configService.get('cloud6Url') + '/internal/sentinel';
      this.setCloud6AuthorizationHeader();
      this.setRawBody(JSON.stringify(reqObj));
      return this.post('/update-user-sync');
    } catch (error: any) {
      Logger.error('syncUserDataWithMySQL', {
        METHOD: this.constructor.name + '@' + this.syncUserDataWithMySQL.name,
        MESSAGE: error.message,
        REQUEST: reqObj,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
 }

 public createUserDataWithMySQL(reqObj: any): Promise<any> {
    try {
      this.resetUri();
      this.url = this.configService.get('cloud6Url') + '/internal/sentinel';
      this.setCloud6AuthorizationHeader();
      this.setRawBody(JSON.stringify(reqObj));
      return this.post('/create-user');
    } catch (error: any) {
      Logger.error('createUserDataWithMySQL', {
        METHOD: this.constructor.name + '@' + this.createUserDataWithMySQL.name,
        MESSAGE: error.message,
        REQUEST: reqObj,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
 }

 public syncTaxonomyUserDataWithMySQL(reqObj: any): Promise<any> {
    try {
      this.resetUri();
      this.url = this.configService.get('cloud6Url') + '/internal/sentinel';
      this.setCloud6AuthorizationHeader();
      this.setRawBody(JSON.stringify(reqObj));
      return this.post('/update-user-taxonomy-data');
    } catch (error: any) {
      Logger.error('syncTaxonomyUserDataWithMySQL', {
        METHOD: this.constructor.name + '@' + this.syncTaxonomyUserDataWithMySQL.name,
        MESSAGE: error.message,
        REQUEST: reqObj,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
 }
  
  async getUserNameByUids(payload: any): Promise<any> {
    try {
      this.resetUri();
      this.url = this.configService.get('cloud6Url') + '/internal/communication/';
      this.setCloud6AuthorizationHeader();
      this.setRawBody(JSON.stringify(payload));
      const response = await this.post('get-user-names-by-uids');
      return response;
    } catch (error: any) {
      Logger.error('getUserNameByUids', {
        METHOD: this.constructor.name + '@' + this.getUserNameByUids.name,
        MESSAGE: error.message,
        REQUEST: { payload },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async getGroupNameByIds(payload: any): Promise<any> {
    try {
      this.resetUri();
      this.url = this.configService.get('cloud6Url') + '/internal/communication/';
      this.setCloud6AuthorizationHeader();
      this.setRawBody(JSON.stringify(payload));
      const response = await this.post('get-group-names-by-ids');
      return response;
    } catch (error: any) {
      Logger.error('getUserNameByUids', {
        METHOD: this.constructor.name + '@' + this.getUserNameByUids.name,
        MESSAGE: error.message,
        REQUEST: { payload },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
}
  
