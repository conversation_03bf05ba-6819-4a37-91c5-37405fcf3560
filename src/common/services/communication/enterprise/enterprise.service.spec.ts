import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { EnterpriseService } from './enterprise.service';

describe('EnterpriseService', () => {
    let service: EnterpriseService;
    const configServiceMock = {
        get: jest.fn((key: string) => {
            if (key === 'enterpriseUrl') return 'http://mocked-url';
            if (key === 'cloud6ApiAuthSecretSalt') return 'mocked-salt';
            if (key === 'hmacAlgo') return 'sha256';
            if (key === 'hmacEncoding') return 'hex';
            return 'mocked-value';
        }),
    };
    const cryptoHelperMock = {
        createHmac: jest.fn().mockReturnValue('mocked-hmac'),
    };

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                EnterpriseService,
                { provide: ConfigService, useValue: configServiceMock },
                { provide: 'CRYPTO_HELPER', useValue: cryptoHelperMock },
            ],
        }).compile();

        service = module.get<EnterpriseService>(EnterpriseService);
        jest.clearAllMocks();
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('resetUri', () => {
        it('should reset the url property', () => {
            service['url'] = 'something-else';
            service['resetUri']();
            expect(service['url']).toBe('http://mocked-url/enterprise/');
        });
    });

    describe('setCloud6AuthorizationHeader', () => {
        it('should set request headers with correct values', () => {
            const setRequestHeadersSpy = jest.spyOn(service as any, 'setRequestHeaders').mockImplementation();
            service['setCloud6AuthorizationHeader']();
            expect(cryptoHelperMock.createHmac).toHaveBeenCalledWith(
                expect.any(String),
                'mocked-salt',
                'sha256',
                'hex'
            );
            expect(setRequestHeadersSpy).toHaveBeenCalledWith(
                expect.objectContaining({
                    'Content-Type': 'application/x-www-form-urlencoded',
                    Authorization: 'mocked-hmac',
                    timestamp: expect.any(Number),
                })
            );
        });
    });

    describe('getUserEnterpriseList', () => {
        it('should call get with correct params and return data', async () => {
            const mockData = { data: [1, 2, 3] };
            const getSpy = jest.spyOn(service as any, 'get').mockResolvedValue(mockData);
            const setQueryParamsSpy = jest.spyOn(service as any, 'setQueryParams');
            const reqObj = { foo: 'bar' };
            const result = await service.getUserEnterpriseList(reqObj);
            expect(getSpy).toHaveBeenCalledWith('get-user-enterprise-list');
            expect(setQueryParamsSpy).toHaveBeenCalledWith({ ...reqObj, spUrl: 'tets' });
            expect(result).toEqual([1, 2, 3]);
        });

        it('should return [] if response.data is undefined', async () => {
            jest.spyOn(service as any, 'get').mockResolvedValue({});
            const result = await service.getUserEnterpriseList({});
            expect(result).toEqual([]);
        });

        it('should log and throw error on failure', async () => {
            const error = new Error('fail');
            jest.spyOn(service as any, 'get').mockRejectedValue(error);
            const loggerSpy = jest.spyOn(require('../../../../logging/logger').Logger, 'error').mockImplementation();
            await expect(service.getUserEnterpriseList({})).rejects.toThrow('fail');
            expect(loggerSpy).toHaveBeenCalled();
        });
    });

    describe('getGroupDomainByGid', () => {
        it('should call get with correct params and return data', async () => {
            const mockData = { data: [4, 5, 6] };
            const getSpy = jest.spyOn(service as any, 'get').mockResolvedValue(mockData);
            const reqObj = { gid: 123 };
            const result = await service.getGroupDomainByGid(reqObj);
            expect(getSpy).toHaveBeenCalledWith('get-group-domain-by-gid?gid=123');
            expect(result).toEqual([4, 5, 6]);
        });

        it('should return [] if response.data is undefined', async () => {
            jest.spyOn(service as any, 'get').mockResolvedValue({});
            const result = await service.getGroupDomainByGid({ gid: 1 });
            expect(result).toEqual([]);
        });

        it('should log and throw error on failure', async () => {
            const error = new Error('fail');
            jest.spyOn(service as any, 'get').mockRejectedValue(error);
            const loggerSpy = jest.spyOn(require('../../../../logging/logger').Logger, 'error').mockImplementation();
            await expect(service.getGroupDomainByGid({ gid: 1 })).rejects.toThrow('fail');
            expect(loggerSpy).toHaveBeenCalled();
        });
    });

    describe('getGroupByDomain', () => {
        it('should call get with correct params and return data', async () => {
            const mockData = { data: [7, 8, 9] };
            const getSpy = jest.spyOn(service as any, 'get').mockResolvedValue(mockData);
            const reqObj = { domain: 'test.com' };
            const result = await service.getGroupByDomain(reqObj);
            expect(getSpy).toHaveBeenCalledWith('get-group-by-domain');
            expect(result).toEqual([7, 8, 9]);
        });

        it('should return [] if response.data is undefined', async () => {
            jest.spyOn(service as any, 'get').mockResolvedValue({});
            const result = await service.getGroupByDomain({ domain: 'test.com' });
            expect(result).toEqual([]);
        });

        it('should log and throw error on failure', async () => {
            const error = new Error('fail');
            jest.spyOn(service as any, 'get').mockRejectedValue(error);
            const loggerSpy = jest.spyOn(require('../../../../logging/logger').Logger, 'error').mockImplementation();
            await expect(service.getGroupByDomain({ domain: 'test.com' })).rejects.toThrow('fail');
            expect(loggerSpy).toHaveBeenCalled();
        });
    });

    describe('getAffiliatesByUid', () => {
        it('should call get with correct params and return data', async () => {
            const mockData = { data: ['a', 'b'] };
            const getSpy = jest.spyOn(service as any, 'get').mockResolvedValue(mockData);
            const reqObj = { uid: 42 };
            const result = await service.getAffiliatesByUid(reqObj);
            expect(getSpy).toHaveBeenCalledWith('get-affiliates-by-uid');
            expect(result).toEqual(['a', 'b']);
        });

        it('should return [] if response.data is undefined', async () => {
            jest.spyOn(service as any, 'get').mockResolvedValue({});
            const result = await service.getAffiliatesByUid({ uid: 42 });
            expect(result).toEqual([]);
        });
    });

    describe('getGroupByGid', () => {
        it('should call get with correct params and return data', async () => {
            const mockData = { data: [10, 11] };
            const getSpy = jest.spyOn(service as any, 'get').mockResolvedValue(mockData);
            const setQueryParamsSpy = jest.spyOn(service as any, 'setQueryParams');
            const gid = 99;
            const result = await service.getGroupByGid(gid);
            expect(getSpy).toHaveBeenCalledWith('get-group-by-gid');
            expect(setQueryParamsSpy).toHaveBeenCalledWith({ gid });
            expect(result).toEqual([10, 11]);
        });

        it('should return [] if response.data is undefined', async () => {
            jest.spyOn(service as any, 'get').mockResolvedValue({});
            const result = await service.getGroupByGid(99);
            expect(result).toEqual([]);
        });

        it('should log and throw error on failure', async () => {
            const error = new Error('fail');
            jest.spyOn(service as any, 'get').mockRejectedValue(error);
            const loggerSpy = jest.spyOn(require('../../../../logging/logger').Logger, 'error').mockImplementation();
            await expect(service.getGroupByGid(99)).rejects.toThrow('fail');
            expect(loggerSpy).toHaveBeenCalled();
        });
    });
});
