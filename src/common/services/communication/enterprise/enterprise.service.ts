import { ConfigService } from '@nestjs/config';
import { Communication } from '../communication';
import { Inject, Injectable } from '@nestjs/common';
import { Logger } from '../../../../logging/logger';
import { CryptoHelper } from '../../../../helper/helper.crypto';


@Injectable()
export class EnterpriseService extends Communication {
  @Inject('CRYPTO_HELPER') private readonly crypto: CryptoHelper;
  protected url: string;
  constructor(private readonly configService: ConfigService) {
    super();
    this.url = this.configService.get('enterpriseUrl') + '/enterprise/';
  }
  protected resetUri() {
    this.url = this.configService.get('enterpriseUrl') + '/enterprise/';
  }

  public async getUserEnterpriseList(reqObj: any): Promise<any> {
    try {
      this.resetUri();
      this.setCloud6AuthorizationHeader();
      this.setQueryParams({...reqObj,spUrl:'tets'});
      const response = await this.get('get-user-enterprise-list');
      return response?.data || [];
    } catch (error: any) {
      Logger.error('getUserEnterpriseList', {
        METHOD: this.constructor.name + '@' + this.getUserEnterpriseList.name,
        MESSAGE: error.message,
        REQUEST: { reqObj },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
}

  public async getGroupDomainByGid(reqObj: any): Promise<any> {
    try{
    this.resetUri();
    this.setCloud6AuthorizationHeader();
    this.setQueryParams(reqObj);
    const response =  await this.get('get-group-domain-by-gid?gid=' + reqObj.gid); 
    return response?.data || [];
    }catch(error:any){
      Logger.error('getGroupDomainByGid', {
        METHOD: this.constructor.name + '@' + this.getGroupDomainByGid.name,
        MESSAGE: error.message,
        REQUEST: { reqObj },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  public async getGroupByDomain(reqObj: any): Promise<any> {
    try{
    this.resetUri();
    this.setCloud6AuthorizationHeader();
    this.setQueryParams(reqObj);
    const response =  await this.get('get-group-by-domain');
    return response?.data || [];
    }catch(error:any){
      Logger.error('getGroupByDomain', {
        METHOD: this.constructor.name + '@' + this.getGroupByDomain.name,
        MESSAGE: error.message,
        REQUEST: { reqObj },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
  
  async getAffiliatesByUid(reqObj: any): Promise<any> {
    this.resetUri();
    this.setCloud6AuthorizationHeader();
    this.setQueryParams(reqObj);
    const response =  await this.get('get-affiliates-by-uid');
    return response?.data || [];
  }

  private setCloud6AuthorizationHeader() {
    const reqTimestamp = Math.round(new Date().getTime() / 1000);
    const authorization = this.crypto.createHmac(
      reqTimestamp.toString(),
      this.configService.get<string>('cloud6ApiAuthSecretSalt'),
      this.configService.get('hmacAlgo'),
      this.configService.get('hmacEncoding'),
    );
    this.setRequestHeaders({
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: authorization,
      timestamp: reqTimestamp,
    });
  }
  public async getGroupByGid(gid: number) {
      try {
        this.resetUri();
        this.setCloud6AuthorizationHeader();
        this.setQueryParams({ gid: gid });
        const response = await this.get('get-group-by-gid');
        return response?.data || [];
      } catch (error: any) {
        Logger.error('getGroupByGid', {
          METHOD: this.constructor.name + '@' + this.getGroupByGid.name,
          MESSAGE: error.message,
          REQUEST: gid,
          RESPONSE: error.stack,
          TIMESTAMP: new Date().getTime(),
        });
        throw error;
      }
    }
}
