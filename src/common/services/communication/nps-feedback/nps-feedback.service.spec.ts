import { Test, TestingModule } from '@nestjs/testing';
import { NpsFeedbackService } from './nps-feedback.service';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../../../helper/helper.service';
import { AuthTokenHelper } from '../../../../auth/helper/auth.tokenhelper';
import { Logger } from '@nestjs/common';

jest.mock('../communication');

jest.spyOn(Logger, 'error').mockImplementation(jest.fn());


describe('NpsFeedbackService', () => {
  let service: NpsFeedbackService;
  let helperService: any;
  let configService: any;
  let authTokenHelper: any;

  beforeEach(async () => {
    authTokenHelper = {
      createSignedToken: jest.fn().mockResolvedValue('mocked-token'),
    };

    helperService = {
      get: jest.fn().mockResolvedValue(authTokenHelper),
    };

    configService = {
      get: jest.fn((key: string) => {
        const config = {
          npsFeedbackApiUrl: 'http://mock-nps-api.com',
          npsUsername: 'mockuser',
          npsPassword: 'mockpass',
          client_key_nps: 'mock-client-key',
        };
        return config[key];
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NpsFeedbackService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key) => {
              const map = {
                npsFeedbackApiUrl: 'http://mock-api.com',
                npsUsername: 'user',
                npsPassword: 'pass',
                client_key_nps: 'client-key',
              };
              return map[key];
            }),
          },
        },
        {
          provide: HelperService,
          useValue: {
            get: jest.fn().mockImplementation((cls) => {
              if (cls === AuthTokenHelper) {
                return Promise.resolve({
                  createSignedToken: jest.fn(() => 'mocked-token'),
                });
              }
            }),
          },
        }
      ],
    }).compile();


    service = module.get<NpsFeedbackService>(NpsFeedbackService);
    configService = module.get<ConfigService>(ConfigService);  // ✅ assign here
    helperService = module.get<HelperService>(HelperService);  // ✅ assign here

    Object.assign(service, {
      setQueryParams: jest.fn(),
      setRequestHeaders: jest.fn(),
      setRawBody: jest.fn(),
      get: jest.fn(),
      post: jest.fn(),
    });
  });

  it('getFeedbackForms - invalid form ID', async () => {
    const result = await service.getFeedbackForms('0', 'session');
    expect(result).toEqual({});
    expect(Logger.error).toHaveBeenCalled();
  });

  it('getFeedbackForms - success', async () => {
    (service.get as jest.Mock).mockResolvedValue({ feedback: true });
    const result = await service.getFeedbackForms('1', 'module');
    expect(result).toEqual({ feedback: true });
    expect((service as any).setQueryParams).toHaveBeenCalled();
    expect((service as any).setRequestHeaders).toHaveBeenCalled();
  });

  it('getFeedbackForms - error', async () => {
    (service.get as jest.Mock).mockRejectedValue(new Error('fail'));
    const result = await service.getFeedbackForms('1', 'module');
    expect(result).toEqual({});
    expect(Logger.error).toHaveBeenCalled();
  });

  it('saveUserFeedback - invalid JSON', async () => {
    const result = await service.saveUserFeedback(JSON.stringify([{}]));
    expect(result).toEqual({});
    expect(Logger.error).toHaveBeenCalled();
  });

  it('saveUserFeedback - invalid formId config', async () => {
    const payload = JSON.stringify([{ form_id: '999', formModule: 'abc' }]);
    const result = await service.saveUserFeedback(payload);
    expect(result).toEqual({});
    expect(Logger.error).toHaveBeenCalled();
  });

  it('saveUserFeedback - success', async () => {
    (service.post as jest.Mock).mockResolvedValue({ status: 'ok' });
    const payload = JSON.stringify([{ form_id: '1', formModule: 'abc' }]);
    const result = await service.saveUserFeedback(payload);
    expect(result).toEqual({ status: 'ok' });
    expect(service.setRawBody).toHaveBeenCalled();
    expect((service as any).setRequestHeaders).toHaveBeenCalled();
    expect(service.post).toHaveBeenCalled();
  });

  it('saveUserFeedback - error', async () => {
    (service.post as jest.Mock).mockRejectedValue(new Error('fail'));
    const payload = JSON.stringify([{ form_id: '1', formModule: 'abc' }]);
    const result = await service.saveUserFeedback(payload);
    expect(result).toEqual({});
    expect(Logger.error).toHaveBeenCalled();
  });

  it('should call configService.get with correct key', async () => {
    await service.getFeedbackForms('1', 'session');
    expect(configService.get).toHaveBeenCalledWith('npsFeedbackApiUrl');
  });

  it('should call helperService.get for AuthTokenHelper', async () => {
    await service.getFeedbackForms('1', 'session');
    expect(helperService.get).toHaveBeenCalledWith(expect.any(Function)); // or AuthTokenHelper if imported
  });

});
