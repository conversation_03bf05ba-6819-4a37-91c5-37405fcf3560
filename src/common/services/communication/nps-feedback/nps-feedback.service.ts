import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Communication } from '../communication';
import { HelperService } from '../../../../helper/helper.service';
import { AuthTokenHelper } from '../../../../auth/helper/auth.tokenhelper';

@Injectable()
export class NpsFeedbackService extends Communication {
  protected url: string;
  @Inject() private helperService: HelperService;

  // NPS API endpoints mapping
  private readonly npsEndpoints = {
    '1': { url: 'get-feedback-form', entityType: 'session' },
    '2': { url: 'get-feedback-form', entityType: 'course' },
    '3': { url: 'get-project-feedback-form', entityType: 'project' },
    '4': { url: 'get-project-feedback-form', entityType: 'project' },
    '5': { url: 'get-feedback-form', entityType: 'program' },
    '6': { url: 'get-feedback-form', entityType: 'help-and-support' },
    '7': { url: 'get-feedback-form', entityType: 'help-and-support' },
    '8': { url: 'get-feedback-form', entityType: 'help-and-support' }
  };

  constructor(private readonly configService: ConfigService) {
    super();
  }

  protected resetUri(): void {
    this.url = this.configService.get<string>('npsFeedbackApiUrl');
  }

  /**
   * Generate JWT token for NPS API authentication
   * @returns JWT token string
   */
  private async generateTokenForNPS() {
    const authHelperInstance: AuthTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
    const tokenData = {
      userName: this.configService.get<string>('npsUsername'),
      password: this.configService.get<string>('npsPassword')
    };
    const token = await authHelperInstance.createSignedToken(
        tokenData,
        this.configService.get('client_key_nps'),
      );
    return token;
  }

  /**
   * Get feedback form details
   * @param formId Form identifier
   * @param formModule Module name for the form
   * @returns Feedback form data or empty object on error
   */
  async getFeedbackForms(formId: string, formModule: string): Promise<any> {
    try {
      if (!this.npsEndpoints[formId]?.url) {
        Logger.error('getFeedbackForms', {
          METHOD: `${this.constructor.name}@${this.getFeedbackForms.name}`,
          REQUEST: [formId, formModule],
          RESPONSE: 'INCORRECT FORM ID PASSED',
          TIMESTAMP: new Date().getTime(),
        });
        return {};
      }
      const token = await this.generateTokenForNPS();
      this.resetUri();
      this.setQueryParams({ form_id: formId });
      this.setRequestHeaders({
        'Authorization': `Bearer ${token}`
      });
      const result = await this.get(`${formModule}/${this.npsEndpoints[formId].url}`);
      return result;
    } catch (error: any) {
        console.log("error", error);
      Logger.error('getFeedbackForms', {
        METHOD: `${this.constructor.name}@${this.getFeedbackForms.name}`,
        MESSAGE: error.message,
        REQUEST: [formId, formModule],
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return {};
    }
  }

  /**
   * Save user feedback responses
   * @param userFeedbacks JSON string of feedback data
   * @returns Response from the feedback save operation or empty object on error
   */
  async saveUserFeedback(userFeedbacks: string): Promise<any> {
    try {
      this.resetUri();
      this.setRawBody(userFeedbacks);
      const requestParam = JSON.parse(userFeedbacks);
      if (!requestParam?.[0]?.form_id || !requestParam?.[0]?.formModule) {
        Logger.error('saveUserFeedback', {
          METHOD: `${this.constructor.name}@${this.saveUserFeedback.name}`,
          REQUEST: userFeedbacks,
          RESPONSE: 'INCORRECT PARAM PASSED',
          TIMESTAMP: new Date().getTime(),
        });
        return {};
      }

      const formId = requestParam[0].form_id;
      
      if (!this.npsEndpoints[formId] || !this.npsEndpoints[formId].entityType) {
        Logger.error('saveUserFeedback', {
          METHOD: `${this.constructor.name}@${this.saveUserFeedback.name}`,
          REQUEST: userFeedbacks,
          RESPONSE: 'INCORRECT CONFIGURATION FOR FORM ID',
          TIMESTAMP: new Date().getTime(),
        });
        return {};
      }
      const token = await this.generateTokenForNPS();
      const formModule = this.npsEndpoints[formId].entityType;
      
      this.setRequestHeaders({
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      });
      const response = await this.post(`${formModule}/save-user-feedback`);
      return response;
    } catch (error: any) {
      Logger.error('saveUserFeedback', {
        METHOD: `${this.constructor.name}@${this.saveUserFeedback.name}`,
        MESSAGE: error.message,
        REQUEST: userFeedbacks,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return {};
    }
  }
}
