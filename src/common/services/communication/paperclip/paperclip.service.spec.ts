import { Test, TestingModule } from '@nestjs/testing';
import { PaperclipService } from './paperclip.service';
import { ConfigService } from '@nestjs/config';
import { CryptoHelper } from '../../../../helper/helper.crypto';
import { Logger } from '../../../../logging/logger';

jest.mock('../communication');

jest.mock('../../../../logging/logger', () => ({
  Logger: {
    error: jest.fn(),
  },
}));

describe('PaperclipService', () => {
  let service: PaperclipService;

  const configServiceMock = {
    get: jest.fn((key: string) => {
      const config = {
        paperclipApiEndpoint: 'http://api.paperclip.com',
        paperclipApiAuthSecretSalt: 'secret',
        hmacAlgo: 'sha256',
        hmacEncoding: 'hex',
      };
      return config[key];
    }),
  } as Partial<ConfigService> as ConfigService;

  const cryptoHelperMock = {
    createHmac: jest.fn(() => 'mocked-auth-header'),
  } as Partial<CryptoHelper> as CryptoHelper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaperclipService,
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: cryptoHelperMock,
        },
      ],
    }).compile();

    service = module.get<PaperclipService>(PaperclipService);

    jest.clearAllMocks();

    Object.assign(service, {
      post: jest.fn(),
      setRequestHeaders: jest.fn(),
      setFormData: jest.fn(),
      setQueryParams: jest.fn(),
    });
  });

  it('getSkillupReferralRewardInfo - success', async () => {
    jest.spyOn(service, 'cleanupResult').mockResolvedValue({ status: 'ok' });
    (service.post as jest.Mock).mockResolvedValue('{"status":"ok"}');
    const result = await service.getSkillupReferralRewardInfo({ user_id: 1 });
    expect(result).toEqual({ status: 'ok' });
  });

  it('getSkillupReferralRewardInfo - error', async () => {
    (service.post as jest.Mock).mockRejectedValue(new Error('fail'));
    const result = await service.getSkillupReferralRewardInfo({ user_id: 1 });
    expect(result).toBeUndefined(); // Correct expectation
    expect(Logger.error).toHaveBeenCalled();
  });

  it('generateUserTokenData - success', async () => {
    (service.post as jest.Mock).mockResolvedValue({ token: 'abc' });
    const result = await service.generateUserTokenData({ userId: 1, userName: 'john', userEmail: '<EMAIL>' });
    expect(result).toEqual({ token: 'abc' });
  });

  it('generateUserTokenData - error', async () => {
    (service.post as jest.Mock).mockRejectedValue(new Error('fail'));
    const result = await service.generateUserTokenData({ userId: 1, userName: 'john', userEmail: '<EMAIL>' });
    expect(result).toEqual({ status: 'failed', msg: 'Something went wrong' });
    expect(Logger.error).toHaveBeenCalled();
  });

  it('getAssignedB2cCoursesCount - success', async () => {
    jest.spyOn(service, 'cleanupResult').mockResolvedValue({ status: 'ok' });
    (service.post as jest.Mock).mockResolvedValue('{"status":"ok"}');
    const result = await service.getAssignedB2cCoursesCount(123);
    expect(result).toEqual({ status: 'ok' });
  });

  it('saveSkillupReferral - true', async () => {
    (service.post as jest.Mock).mockResolvedValue(true);
    const result = await service.saveSkillupReferral({ user_id: 1, email: '<EMAIL>', first_name: 'John', refcode: '123' });
    expect(result).toBe(true);
  });

  it('saveSkillupReferral - false', async () => {
    (service.post as jest.Mock).mockResolvedValue(false);
    const result = await service.saveSkillupReferral({ user_id: 1, email: '<EMAIL>', first_name: 'John', refcode: '123' });
    expect(result).toBe(false);
  });

  it('cleanupResult - multiline string', async () => {
    const multiLine = '{"status":"ok"}\n{"status":"error"}';
    const result = await service.cleanupResult(multiLine);
    expect(result).toEqual({ status: 'error' }); // correct
  });

  it('cleanupResult - json object', async () => {
    const json = { status: 'ok' };
    const result = await service.cleanupResult(json);
    expect(result).toEqual(json);
  });

  it('getSkillupUserInfo - with 200 code and data', async () => {
    const res = { code: 200, data: 'yes' };
    (service.post as jest.Mock).mockResolvedValue(res);
    jest.spyOn(service, 'cleanupResult').mockResolvedValue({ status: 'ok' });
    const result = await service.getSkillupUserInfo({ userId: 1 });
    expect(result).toEqual({ status: 'ok' });
  });

  it('getSkillupUserInfo - with no 200 code', async () => {
    (service.post as jest.Mock).mockResolvedValue({ code: 500 });
    const result = await service.getSkillupUserInfo({ userId: 1 });
    expect(result).toEqual({ status: 'failed', msg: 'Something went wrong', data: '' });
  });

  it('invalidateTokenForAppUser - success', async () => {
    (service.post as jest.Mock).mockResolvedValue({ success: true });
    const result = await service.invalidateTokenForAppUser(1);
    expect(result).toEqual({ success: true });
  });

  it('invalidateTokenForAppUser - error', async () => {
    (service.post as jest.Mock).mockRejectedValue(new Error('fail'));
    const result = await service.invalidateTokenForAppUser(1);
    expect(result).toBeUndefined();
    expect(Logger.error).toHaveBeenCalled();
  });

  it('logClientError - invalid params', async () => {
    const result = await service.logClientError({ user_id: '', user_email: '', group_id: '', error_type: '' });
    expect(result).toEqual({ status: false, message: 'Invalid parameters' });
    expect(Logger.error).toHaveBeenCalled();
  });

  it('logClientError - success', async () => {
    (service.post as jest.Mock).mockResolvedValue({});
    jest.spyOn(service, 'cleanupResult').mockResolvedValue({ status: true });
    const result = await service.logClientError({ user_id: '1', user_email: '<EMAIL>', group_id: '1', error_type: 'ui' });
    expect(result).toEqual({ status: true });
  });

  it('logClientError - failure', async () => {
    (service.post as jest.Mock).mockRejectedValue(new Error('fail'));
    const result = await service.logClientError({ user_id: '1', user_email: '<EMAIL>', group_id: '1', error_type: 'ui' });
    expect(result).toEqual({ status: false, message: 'Something went wrong' });
    expect(Logger.error).toHaveBeenCalled();
  });

  it('expireAppUsertoken - success', async () => {
    (service.post as jest.Mock).mockResolvedValue({ status: 'ok' });
    const result = await service.expireAppUsertoken(1);
    expect(result).toEqual({ status: 'ok' });
  });

  it('expireAppUsertoken - error', async () => {
    (service.post as jest.Mock).mockRejectedValue(new Error('fail'));
    const result = await service.expireAppUsertoken(1);
    expect(result).toEqual({ status: 'failed', msg: 'Something went wrong' });
    expect(Logger.error).toHaveBeenCalled();
  });
});
