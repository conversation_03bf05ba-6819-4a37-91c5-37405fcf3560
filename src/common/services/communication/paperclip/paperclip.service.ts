import { ConfigService } from '@nestjs/config';
import { Communication } from '../communication';
import { Inject, Injectable } from '@nestjs/common';
import { Logger } from '../../../../logging/logger';
import { CryptoHelper } from '../../../../helper/helper.crypto';

@Injectable()
export class PaperclipService extends Communication {
  @Inject('CRYPTO_HELPER') private readonly crypto: CryptoHelper;
  protected url: string;
  constructor(private readonly configService: ConfigService) {
    super();
  }

  /**
   * Login request
   * @param params
   */

  public async getSkillupReferralRewardInfo(params: { user_id: number }): Promise<any> {
    try {
      let resToPass = { status: 'failed', msg: 'Something went wrong' };
      this._resetUri();
      this.setPaperclipAuthorizationHeader();
      this.setFormData(params);
      const result = await this.post('get-skillup-referral-reward-info');
      if (result) {
        resToPass = await this.cleanupResult(result);
      }
      return resToPass;
    } catch (error: any) {
      Logger.error('getSkillupReferralRewardInfo', {
        METHOD: this.constructor.name + '@' + this.getSkillupReferralRewardInfo.name,
        MESSAGE: error.message,
        REQUEST: params,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  public async generateUserTokenData(params: { userId: number; userName: string; userEmail: string }): Promise<any> {
    try {
      this._resetUri();
      this.setPaperclipAuthorizationHeader();
      this.setFormData(params);
      return await this.post('generate-user-token-data');
    } catch (error: any) {
      Logger.error('generate-user-token-data', {
        METHOD: this.constructor.name + '@' + this.generateUserTokenData.name,
        MESSAGE: error.message,
        REQUEST: params,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { status: 'failed', msg: 'Something went wrong' };
    }
  }

  private _resetUri() {
    this.url = this.configService.get<string>('paperclipApiEndpoint');
  }

  async getAssignedB2cCoursesCount(data: any): Promise<any> {
    try {
      let resToPass = { status: 'failed', msg: 'Something went wrong' };
      this._resetUri();
      this.setPaperclipAuthorizationHeader();
      this.setFormData({ userId: data });
      const result = await this.post('get-assigned-courses');
      if (result) {
        resToPass = await this.cleanupResult(result);
      }
      return resToPass;
    } catch (error: any) {
      Logger.error('getAssignedB2cCoursesCount', {
        METHOD: this.constructor.name + '@' + this.getAssignedB2cCoursesCount.name,
        MESSAGE: error.message,
        REQUEST: { userId: data },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async saveSkillupReferral(data: {
    user_id: number;
    email: string;
    first_name: string;
    refcode: string;
  }): Promise<boolean> {
    try {
      this._resetUri();
      this.setPaperclipAuthorizationHeader();
      this.setFormData(data);
      const result = await this.post('save-skillup-referral');
      return result ? true : false;
    } catch (error: any) {
      Logger.error('saveSkillupReferral', {
        METHOD: this.constructor.name + '@' + this.saveSkillupReferral.name,
        MESSAGE: error.message,
        REQUEST: { data },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  private setPaperclipAuthorizationHeader() {
    const reqTimestamp = Math.round(new Date().getTime() / 1000);
    const authorization = this.crypto.createHmac(
      reqTimestamp.toString(),
      this.configService.get<string>('paperclipApiAuthSecretSalt'),
      this.configService.get('hmacAlgo'),
      this.configService.get('hmacEncoding'),
    );
    this.setRequestHeaders({
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: authorization,
      timestamp: reqTimestamp,
    });
  }

  async cleanupResult(result: any): Promise<any> {
    try {
      let resultString = result;
      if (typeof result === 'string') {
        resultString = result;
      } else {
        resultString = JSON.stringify(result);
      }

      // Split the log into lines
      if (resultString.includes('\n')) {
        let resToPass = { status: 'failed', msg: 'Something went wrong' };
        const lines = result.split('\n');

        // Loop through each line and parse JSON objects
        lines.forEach((line) => {
          if (line.includes('{')) {
            try {
              let linenew = line;
              if (line.includes('<br />')) {
                linenew = line.replace('<br />', '');
              }
              resToPass = JSON.parse(linenew);
              return resToPass;
            } catch (error: any) {
              Logger.error('cleanupResult', {
                METHOD: this.constructor.name + '@' + this.cleanupResult.name,
                MESSAGE: error.message,
                REQUEST: { result: result },
                RESPONSE: error.stack,
                TIMESTAMP: new Date().getTime(),
              });
            }
          }
        });
        return resToPass;
      }
      return result;
    } catch (error: any) {
      Logger.error('cleanupResult', {
        METHOD: this.constructor.name + '@' + this.cleanupResult.name,
        MESSAGE: error.message,
        REQUEST: { result: result },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  public async getSkillupUserInfo(params: { userId: number }) {
    try {
      let resToPass = { status: 'failed', msg: 'Something went wrong' , data : '' };
      this._resetUri();
      this.setPaperclipAuthorizationHeader();
      this.setQueryParams(params);
      const result = await this.post('get-skillup-user-info');
      if (result && result?.code === 200 && result?.data) {
        resToPass = await this.cleanupResult(result);
      }
      return resToPass;
    } catch (error: any) {
      Logger.error('getSkillupUserInfo', {
        METHOD: this.constructor.name + '@' + this.getSkillupUserInfo.name,
        MESSAGE: error.message,
        REQUEST: params,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return;
    }
  }

  public async invalidateTokenForAppUser(userid: number): Promise<any> {
    this._resetUri();
    this.setPaperclipAuthorizationHeader();
    this.setFormData({userId: userid});
    return this.post('invalidate-token-for-app-user')
      .then((response) => {
        return response;
      })
      .catch((error: any) => {
        Logger.error('invalidateTokenForAppUser', {
          METHOD: this.constructor.name + '@' + this.getSkillupUserInfo.name,
          MESSAGE: error.message,
          RESPONSE: error.stack,
          TIMESTAMP: new Date().getTime(),
        });
        return;
      });
  }

  /**
   * Log client-side errors from help and support
   * @param params Error information
   */
  public async logClientError(params: { 
    user_id: string; 
    user_email: string; 
    group_id: string; 
    error_type: string;
  }): Promise<any> {
    try {
      // Validate required parameters
      if (!params.user_id || !params.user_email || !params.error_type) {
        Logger.error('logClientError', {
          METHOD: this.constructor.name + '@' + this.logClientError.name,
          MESSAGE: 'Invalid parameters',
          REQUEST: params,
          TIMESTAMP: new Date().getTime(),
        });
        return { status: false, message: 'Invalid parameters' };
      }

      // Set up request
      this._resetUri();
      this.setPaperclipAuthorizationHeader();
      this.setFormData(params);
      
      // Make API call
      const result = await this.post('help-and-support');
      
      // Process and return result
      return result ? await this.cleanupResult(result) : { status: false, message: 'Something went wrong' };
    } catch (error: any) {
      Logger.error('logClientError', {
        METHOD: this.constructor.name + '@' + this.logClientError.name,
        MESSAGE: error.message,
        REQUEST: params,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { status: false, message: 'Something went wrong' };
    }
  }

  async expireAppUsertoken(userid: number): Promise<any>{
    try {
      this._resetUri();
      this.setPaperclipAuthorizationHeader();
      this.setFormData({user_id: userid});
      const response = await this.post('expire-app-user-token');
      return response;
    } catch (error:any) {
      Logger.error('expireAppUsertoken', {
        METHOD: this.constructor.name + '@' + this.expireAppUsertoken.name,
        MESSAGE: error.message,
        REQUEST: { userId: userid },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { status: 'failed', msg: 'Something went wrong' };
    }
  }
}
