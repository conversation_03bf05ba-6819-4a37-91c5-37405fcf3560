import { Test, TestingModule } from '@nestjs/testing';
import { KafkaService } from './kafka.service';
import { ConfigService } from '@nestjs/config';
import { Logger } from '../../../../logging/logger';

jest.mock('../communication'); // Mock base class

describe('KafkaService', () => {
  let kafkaService: KafkaService;

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KafkaService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    kafkaService = module.get<KafkaService>(KafkaService);

    // Default for allowedTopics
    mockConfigService.get.mockImplementation((key: string) => {
      if (key === 'allowedTopics') return ['topic1', 'topic2'];
      if (key === 'kafkaUrl') return 'http://kafka.example.com';
      if (key === 'topicPrefix') return 'prefix-';
      return null;
    });

    jest.spyOn(Logger, 'error').mockClear();
    jest.spyOn(Logger, 'log').mockClear();
  });

  it('should be defined', () => {
    expect(kafkaService).toBeDefined();
  });

  describe('setUri', () => {
    it('should set the URL property', () => {
      mockConfigService.get.mockReturnValue('http://kafka.example.com');
      kafkaService['setUri']();
      expect(kafkaService['url']).toBe('http://kafka.example.com/topics/');
    });

    it('should throw an error if the Uri is invalid', () => {
      mockConfigService.get.mockReturnValue(undefined);
      expect(() => kafkaService['setUri']()).toThrowError('Invalid Uri');
    });
  });

  describe('publish', () => {
    it('should return false for invalid topic and log an error', async () => {
      const errorSpy = jest.spyOn(Logger, 'error');
      const result = await kafkaService.publish('bad-topic', {}, 'key1');
      expect(result).toBe(false);
      expect(errorSpy).toHaveBeenCalledWith('Invalid topic name', { topic: 'bad-topic' });
    });

    it('should publish successfully and log the response', async () => {
      const postSpy = jest.spyOn(kafkaService as any, 'post').mockResolvedValue({ success: true });
      const logSpy = jest.spyOn(Logger, 'log');

      const topic = 'topic1';
      const payload = { foo: 'bar' };
      const key = 'key1';

      const result = await kafkaService.publish(topic, payload, key);

      expect(postSpy).toHaveBeenCalledWith('prefix-topic1');
      expect(result).toEqual({ success: true });
      expect(logSpy).toHaveBeenCalledWith('Kafka sent successfully', { data: { success: true } });
    });

    it('should handle error during post and log it', async () => {
      const error = new Error('Kafka post failed');
      jest.spyOn(kafkaService as any, 'post').mockRejectedValue(error);

      const errorSpy = jest.spyOn(Logger, 'error');

      const result = await kafkaService.publish('topic1', { foo: 'bar' }, 'key1');

      expect(errorSpy).toHaveBeenCalledWith('Failed to send to Kafka', { error });
      expect(result).toBe(error); // returns error object
    });
  });
});
