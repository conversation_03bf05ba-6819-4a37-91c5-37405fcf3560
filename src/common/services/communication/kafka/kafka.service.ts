import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Logger } from '../../../../logging/logger';
import { Communication } from '../communication';

@Injectable()
export class KafkaService extends Communication {
  private _allowedTopics: string[];

  constructor(private readonly configService: ConfigService) {
    super();
    this._allowedTopics = this.configService.get('allowedTopics');
  }

  private setUri() {
    const kafkaUri = this.configService.get<string>('kafkaUrl');
    if (!kafkaUri) {
      throw new Error('Invalid Uri');
    }
    this.url = `${kafkaUri}/topics/`;
  }

  async publish(topic: string, payload: any, key: string) {
    if (!this._allowedTopics?.includes(topic)) {
      Logger.error('Invalid topic name', { topic: topic });
      return false;
    }
    const topicprefix = this.configService.get<string>('topicPrefix');
    const topicName = `${topicprefix}${topic}`;

    this.setUri();
    this.setRequestHeaders({
      'Content-Type': 'application/vnd.kafka.json.v2+json',
      Connection: 'keep-alive',
      'Keep-Alive': 'timeout=5, max=100',
    });

    const payloadData = {
      records: [
        {
          key: key,
          value: payload,
        },
      ],
    };
    this.setRawBody(JSON.stringify(payloadData));

    try {
      const response = await this.post(topicName);
      Logger.log('Kafka sent successfully', { data: response });
      return response;
    } catch (error) {
      Logger.error('Failed to send to Kafka', { error: error });
      return error;
    }
  }
}
