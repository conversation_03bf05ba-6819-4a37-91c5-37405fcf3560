import { ExceptionFilter, Catch, ArgumentsHost, HttpException, UnauthorizedException, Inject } from '@nestjs/common';
import { Request, Response } from 'express';
import { VIEW_PAGES } from '../../auth/config/view.constants';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import { Utility } from '../util/utility';
@Catch(UnauthorizedException)
export class UnauthorizedExceptionFilter implements ExceptionFilter {
  @Inject(HelperService) private readonly helperService: HelperService;
  @Inject(ConfigService) private readonly configService: ConfigService;

  async catch(exception: UnauthorizedException, host: ArgumentsHost) {
    if (exception.getStatus() === 401) {
      const ctx = host.switchToHttp();
      const response = ctx.getResponse<Response>();
      const cookieHelper = await this.helperService.getHelper('CookieHelper');
      cookieHelper.clearCookie(response, this.configService.get('ssoCookie'));
      return response.redirect(VIEW_PAGES.VIEW_ROUTES.USERS.LOGIN);
    }
  }
}
@Catch(HttpException)
export class ViewValidationFilter implements ExceptionFilter {
  private readonly renderUrl: string;

  constructor(renderUrl: string) {
    this.renderUrl = renderUrl;
  }
  async catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    // Check if the exception is a validation error

    const res = exception.getResponse();
    const route = Utility.getRoute(request?.route);

    // Get the validation errors from the exception message
    const validationErrors = Array.isArray(res['message']) ? res['message'] : [res['message']];
    const viewInfo =
      res['options'] != undefined && res['options']['description'] ? JSON.parse(res['options']['description']) : {};
    // Render the signup page again with the validation errors and form data
    response.render(this.renderUrl, {
      params: { ...request.params },
      validationErrors,
      formData: request.body,
      route: route,
      viewInfo: viewInfo,
    });
  }
}
