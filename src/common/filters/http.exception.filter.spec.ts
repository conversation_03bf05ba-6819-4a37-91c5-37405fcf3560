import { HttpExceptionFilter } from './http.exception.filter';
import { HttpException, HttpStatus, ArgumentsHost } from '@nestjs/common';

describe('HttpExceptionFilter', () => {
  let filter: HttpExceptionFilter;
  let mockResponse: any;
  let mockArgumentsHost: ArgumentsHost;

  beforeEach(() => {
    filter = new HttpExceptionFilter();
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnValue({
        getResponse: () => mockResponse,
      }),
    } as any;
  });

  it('should be defined', () => {
    expect(filter).toBeDefined();
  });

  it('should handle a generic HttpException', () => {
    const exception = new HttpException({ message: 'error' }, HttpStatus.BAD_REQUEST);
    filter.catch(exception, mockArgumentsHost);
    expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
    expect(mockResponse.json).toHaveBeenCalledWith({
      type: 'error',
      msg: 'error',
    });
  });

  it('should handle an array of messages', () => {
    const exception = new HttpException({ message: ['error1', 'error2'] }, HttpStatus.BAD_REQUEST);
    filter.catch(exception, mockArgumentsHost);
    expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
    expect(mockResponse.json).toHaveBeenCalledWith({
      type: 'error',
      msg: 'error1',
    });
  });

  it('should handle an internal server error', () => {
    const exception = new HttpException({ message: 'error' }, HttpStatus.INTERNAL_SERVER_ERROR);
    filter.catch(exception, mockArgumentsHost);
    expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
    expect(mockResponse.json).toHaveBeenCalledWith({
      type: 'error',
      msg: 'Something went wrong',
    });
  });

  it('should handle a forbidden error', () => {
    const exception = new HttpException({ message: 'error' }, HttpStatus.FORBIDDEN);
    filter.catch(exception, mockArgumentsHost);
    expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.FORBIDDEN);
    expect(mockResponse.json).toHaveBeenCalledWith({
      type: 'error',
      msg: 'Invalid Accounts API Authentication',
    });
  });

  it('should handle a successful response', () => {
    const exception = new HttpException({ message: 'success' }, HttpStatus.OK);
    filter.catch(exception, mockArgumentsHost);
    expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
    expect(mockResponse.json).toHaveBeenCalledWith({
      type: 'success',
      msg: 'success',
    });
  });

  it('should handle a response with data', () => {
    const exception = new HttpException({ message: 'success', isData: { data: 'test' } }, HttpStatus.OK);
    filter.catch(exception, mockArgumentsHost);
    expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
    expect(mockResponse.json).toHaveBeenCalledWith({
      type: 'success',
      msg: 'success',
      data: 'test',
    });
  });
});