import { ViewValidationFilter, UnauthorizedExceptionFilter } from './view-validation.filter';
import { HttpException, HttpStatus, ArgumentsHost, UnauthorizedException } from '@nestjs/common';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';

describe('ViewValidationFilter', () => {
  let filter: ViewValidationFilter;
  let mockResponse: any;
  let mockRequest: any;
  let mockArgumentsHost: ArgumentsHost;

  beforeEach(() => {
    filter = new ViewValidationFilter('test-render-url');
    mockResponse = {
      render: jest.fn(),
    };
    mockRequest = {
      params: { id: '123' },
      body: { name: 'test' },
      route: { path: '/test' },
    };
    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnValue({
        getResponse: () => mockResponse,
        getRequest: () => mockRequest,
      }),
    } as any;
  });

  it('should be defined', () => {
    expect(filter).toBeDefined();
  });

  it('should render the view with validation errors', () => {
    const exception = new HttpException({ message: ['error1', 'error2'] }, HttpStatus.BAD_REQUEST);
    filter.catch(exception, mockArgumentsHost);
    expect(mockResponse.render).toHaveBeenCalledWith('test-render-url', {
      params: { id: '123' },
      validationErrors: ['error1', 'error2'],
      formData: { name: 'test' },
      route: { action: '/test', page: 'test' },
      viewInfo: {},
    });
  });
});

describe('UnauthorizedExceptionFilter', () => {
  let filter: UnauthorizedExceptionFilter;
  let mockResponse: any;
  let mockArgumentsHost: ArgumentsHost;
  let helperService: HelperService;
  let configService: ConfigService;

  beforeEach(() => {
    helperService = { getHelper: jest.fn() } as any;
    configService = { get: jest.fn() } as any;
    filter = new UnauthorizedExceptionFilter();
    (filter as any).helperService = helperService;
    (filter as any).configService = configService;

    mockResponse = {
      redirect: jest.fn(),
    };
    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnValue({
        getResponse: () => mockResponse,
      }),
    } as any;
  });

  it('should be defined', () => {
    expect(filter).toBeDefined();
  });

  it('should clear the SSO cookie and redirect to the login page', async () => {
    const exception = new UnauthorizedException();
    const mockCookieHelper = { clearCookie: jest.fn() };
    (helperService.getHelper as jest.Mock).mockResolvedValue(mockCookieHelper);
    await filter.catch(exception, mockArgumentsHost);
    expect(mockCookieHelper.clearCookie).toHaveBeenCalled();
    expect(mockResponse.redirect).toHaveBeenCalled();
  });
});
