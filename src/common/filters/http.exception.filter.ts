import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
interface ResponseData {
  type: string;
  msg: string;
  data?: any;
}

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private static INTERNAL_ERROR_MSG = 'Internal server error';

  private static STATUS_PREFIXES = {
    [HttpStatus.OK]: 'success',
    [HttpStatus.CREATED]: 'success',
    [HttpStatus.ACCEPTED]: 'success',
    [HttpStatus.UNAUTHORIZED]: 'failed',
    [HttpStatus.FORBIDDEN]: 'failed',
    [HttpStatus.BAD_REQUEST]: 'failed',
    [HttpStatus.NOT_FOUND]: 404,
  };

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const statusCode = exception.getStatus();
    const res = exception.getResponse();
    const msg = res['message']
      ? Array.isArray(res['message'])
        ? res['message'][0]
        : res['message'].toString()
      : HttpExceptionFilter.INTERNAL_ERROR_MSG;

    const status = HttpExceptionFilter.STATUS_PREFIXES[statusCode] || 'failed';

    let errorMsg = msg;
    if (status === 'failed') {
      errorMsg =
        statusCode === HttpStatus.INTERNAL_SERVER_ERROR || statusCode === HttpStatus.FORBIDDEN
          ? statusCode === HttpStatus.FORBIDDEN
            ? 'Invalid Accounts API Authentication'
            : 'Something went wrong'
          : `${msg}`;
    }

    const responseData: ResponseData = {
      type: status === 'success' ? 'success' : 'error',
      msg: errorMsg,
      ...(res['isData'] && { data: res['isData'].data }),
    };
    response.status(statusCode).json(responseData);
  }
}
