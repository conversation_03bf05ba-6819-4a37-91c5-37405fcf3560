import { UnauthorizedException, Res } from '@nestjs/common';
import { Logger } from '../../logging/logger';
import { UpdateUserProfileDto } from '../../internal/dto/update-user-profile.dto';
import { UpdateSkillUpUserDTO } from '../../internal/dto/update-skillip-user.dto';

export class Utility {
  /**
   * isEmpty — Determine whether a variable is empty
   * @param data
   * @returns boolean
   */
  static isEmpty(data: any): boolean {
    if (data !== undefined && data !== null && data !== '') {
      return false;
    }
    return true;
  }

  /**
   * ctypeAlpha — Check for alphabetic character(s)
   * @param str
   * @returns
   */
  static ctypeAlpha(str: string): boolean {
    return /^[a-zA-Z]+$/.test(str);
  }

  /**
   * strReplace — Replace all occurrences of the search string with the replacement string
   * @param str
   * @param search
   * @param replace
   * @returns
   */
  static strReplace(str: string, search: string, replace: string): string {
    return str.replace(new RegExp(search, 'g'), replace);
  }

  /**
   * isValidEmail — Check if the given string is a valid email address
   * @param email - The email address to validate
   * @returns Whether the email is valid
   */
  static isValidEmail(email: string): boolean {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  }

  static checkZendUri(url: string): boolean {
    // Regular expression pattern for URI validation
    const uriPattern =
      /^(?:(?:(?:https?|ftp):)\/\/)(?:\S+(?::\S*)?@)?(?:((?!10(?:\.\d{1,3}){3})(?!127(?:\.\d{1,3}){3})(?!169\.254(?:\.\d{1,3}){2})(?!192\.168(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2}))\d{1,3}(?:\.\d{1,3}){3}|(?:[a-z\u00a1-\uffff0-9]+-?)*[a-z\u00a1-\uffff0-9]+(?:\.[a-z\u00a1-\uffff0-9]+-?)*\.[a-z\u00a1-\uffff]{2,}(?::\d{2,5})?)(?:\/[^\s]*)?$/iu;

    return uriPattern.test(url);
  }

  /**
   * validatePasswd — Validate the strength of a password
   * @param passwd - The password to validate
   * @param length - Minimum length required for the password
   * @param maxLength - Maximum length allowed for the password
   * @returns Object with type and message indicating password validity
   */
  static validatePasswd(passwd: string, length: number, maxLength: number): { type: string; msg: string } {
    const result = {
      type: 'error',
      msg: `Your password should be minimum ${length} characters long, with at least one uppercase, one lowercase letter, and one number. Length cannot be longer than ${maxLength} characters.`,
    };

    if (passwd && passwd.length >= length && passwd.length <= maxLength) {
      if (/(?=.*[a-z])(?=.*[A-Z])(?=.*\d).*$/.test(passwd)) {
        result.type = 'success';
        result.msg = 'Password is valid and strong.';
      }
    }
    return result;
  }

  /**
   * validateRedirectUrl — Validate and sanitize a redirect URL
   * @param redirectUrl - The URL to validate and sanitize
   * @returns Validated and sanitized redirect URL
   */
  static validateRedirectUrl(redirectUrl: string, isSimplilearn = true): string {
    try {
      const regxURI = isSimplilearn
        ? /^(http|https):\/\/([a-zA-Z0-9-.])*\.(simplilearn)\.(com)/i
        : /^(http|https):\/\/([a-zA-Z0-9-.])*\.[a-zA-Z]{2,}$/i;
      const decodedUrl = decodeURIComponent(redirectUrl);
      return redirectUrl && regxURI.test(decodedUrl) ? redirectUrl : '';
    } catch (error: any) {
      Logger.error('Error validating redirect URL:', error);
      return '';
    }
  }

  static validateNpsSessionUrl(url: string): boolean {
    return !!url && url.includes('/nps/session/');
  }
  static validateCommunityUrl(urlData: {
    url: string;
    communityBaseUrl: string;
    tribeCommunityBaseUrl: string;
  }): boolean {
    return (
      !!urlData?.url &&
      (urlData?.url.includes(urlData?.communityBaseUrl) || urlData?.url.includes(urlData?.tribeCommunityBaseUrl))
    );
  }

  static isTribeCommunityUrl(url: string, tribeCommunityBaseUrl: string): boolean {
    return url && url.includes(tribeCommunityBaseUrl) ? true : false;
  }

  /**
   * isValidRedirectUrl — Validate and return a valid redirect URL
   * @param redirectUrl - The URL to validate
   * @param lmsSiteUrl - The default URL to return if validation fails
   * @returns Validated redirect URL or the default LMS site URL
   */
  static getValidRedirectUrl(redirectUrl: string, lmsSiteUrl: string): string {
    const isValidUrl = redirectUrl && this.validateRedirectUrl(redirectUrl);
    return isValidUrl ? redirectUrl : lmsSiteUrl;
  }

  static getRoute(route: { path: string }): {
    action: string;
    page: string;
  } {
    let path = route?.path.includes('/:') ? route?.path.split('/:')[0] : route?.path;
    let page = '';
    let parts = path?.split('/');
    parts = parts.filter((val) => {
      return val !== '';
    });
    if (parts.length > 0) {
      page = parts[parts.length - 1];
      path = path;
    } else {
      page = 'login';
      path = '/auth/login';
    }
    return { action: path, page: page };
  }

  static sanitizeRedirectUrl(redirectUrl: string): string | null {
    if (!redirectUrl) return null;

    // Remove protocol, optional 'www.', and trailing slashes/backslashes
    const sanitizedUrl = redirectUrl.replace(/^https?:\/\/(?:www\.)?/i, '').replace(/[\/\\]+$/, '');

    return sanitizedUrl ? redirectUrl : null;
  }

  static validateClientRequest(clientId: string, clientSecret: Record<string, any>, redirectUrl = ''): boolean {
    if (!clientSecret?.[clientId]) {
      throw new UnauthorizedException('Unauthorized access request.');
    }

    if (redirectUrl && !this.validateRedirectUrl(redirectUrl, false)) {
      throw new UnauthorizedException('Unauthorized access request.');
    }

    return true;
  }

  static getUnixTimeStamp(date: string): number {
    const timestamp = new Date(date).getTime();
    return Math.floor(timestamp / 1000);
  }

  /**
   * ucfirst - Make a string's first character uppercase
   * @param str
   * @returns
   */
  static ucfirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  static doEmptyCheck(data: Partial<UpdateUserProfileDto | UpdateSkillUpUserDTO>, fields: Array<string>) {
    const response = {
      status: true,
      type: 'success',
      field: '',
      msg: '',
      comn_msg: '',
    };

    for (const element of fields) {
      if (Utility.isEmpty(data[element])) {
        response.status = false;
        response.type = 'error';
        response.field = element;
        response.msg = 'This field is required';
        response.comn_msg = 'All fields are required';
        break;
      }
    }

    return response;
  }

  /**
   *
   * @param utmDetails
   * @returns
   */
  static getFrsUtm(utmDetails) {
    try {
      let frsUtmData = '';
      utmDetails.forEach((value, key) => {
        if (key === 0) {
          const temp = value.split('=');
          frsUtmData += `${temp[0]}=frs|`;
        } else {
          frsUtmData += `${value}|`;
        }
      });
      frsUtmData = frsUtmData.replace(/(\|)+$/, ''); // Remove trailing pipes
      return frsUtmData;
    } catch (error: any) {
      Logger.log(error.message);
      throw error;
    }
  }

 static isThisCourseCatalogUrl(courseCatalogUrl: string | null | undefined): boolean {
  const subject = (courseCatalogUrl && typeof courseCatalogUrl === 'string') 
    ? courseCatalogUrl.replace(/\/+$/, '') 
    : '';

  // Updated regex: allows optional extra segments after course_id or library
  const pattern = /\/catalog\/course\/course_type\/(osl|mp|lvc|clp|category)\/course_id\/\d+(\/library\/\d+)?(\/.*)?$/i;
  return pattern.test(subject);
}


  static urlsafeB64Encode(input) {
    const encoded = btoa(input);
    return encoded.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  }
  static convertToTimestampUnix(dateStr) {
    if (!dateStr) return null; // Handle invalid input
    const [day, month, year] = dateStr.split('/').map(Number);
    // Convert two-digit years correctly
    const fullYear = year < 100 ? (year < 50 ? 2000 + year : 1900 + year) : year;
    // Convert to Unix timestamp (seconds)
    return Math.floor(new Date(fullYear, month - 1, day).getTime() / 1000);
  }
  static emailDomainExtractor(email) {
    return typeof email === 'string' && email.includes('@') ? email.split('@')[1] : null;
  }
  static validatePhoneNumber(phoneNumber: string): boolean {
    const phoneRegex = /^\d{7,15}$/;

    return phoneRegex.test(phoneNumber);
  }

  static validateCountryCode(country_code: string): boolean {
    const countryCodeRegex = /^[A-Za-z]{2,3}$/;
    return countryCodeRegex.test(country_code);
  }

  static returnResponse(
    response: Record<string, any>,
    responseType: 'json' | 'redirect' = 'json',
    redirectUrl: string | null = null,
    @Res() res, // Assuming `res` is the NestJS response object
  ): void {
    if (responseType.toLowerCase() === 'redirect' && redirectUrl && this.isValidUrl(redirectUrl)) {
      this.redirectRequest(redirectUrl, response, res);
    } else {
      return res.json(response);
    }
  }

  static redirectRequest(
    redirectUrl: string,
    params: Record<string, any> = {},
    @Res() res, // Assuming `res` is the NestJS response object
  ): void {
    if (this.isValidUrl(redirectUrl)) {
      const parsedUrl = new URL(redirectUrl);
      if (params && Object.keys(params).length > 0) {
        const queryString = new URLSearchParams(params).toString();
        const separator = parsedUrl.search ? '&' : '?';
        parsedUrl.search += separator + queryString;
      }
      res.redirect(parsedUrl.toString());
    }
  }

  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch (error: any) {
      Logger.error('isValidUrl', {
        METHOD: this.constructor.name + '@' + this.isValidUrl.name,
        MESSAGE: error.message,
        REQUEST: url,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return false;
    }
  }

  static normalizeIp(ip: string): string {
    // Strip IPv6 prefix if it's a mapped IPv4 address
    if (ip.startsWith('::ffff:')) {
      return ip.replace('::ffff:', '');
    }
    return ip;
  }

  static getIpAddress(req: any): string {
    const fallbackIp = '127.0.0.1';

    const headerKeys = [
      'x-client-ip',
      'x-forwarded-for',
      'x-forwarded',
      'x-cluster-client-ip',
      'forwarded-for',
      'forwarded',
    ];

    for (const key of headerKeys) {
      const value = req.headers[key];
      if (value) {
        const ips = value.split(',');
        for (let ip of ips) {
          ip = ip.trim();
          if (ip && /^\d{1,3}(\.\d{1,3}){3}$/.test(ip)) {
            return this.normalizeIp(ip);
          }
        }
      }
    }

    // Fallback to direct connection address
    const remoteAddr = req.connection?.remoteAddress || fallbackIp;
    return this.normalizeIp(remoteAddr);
  }


}
