import { UnauthorizedException } from '@nestjs/common';
import { UpdateUserProfileDto } from '../../internal/dto/update-user-profile.dto';
import { Utility } from './utility';

describe('Utility', () => {
  describe('isEmpty', () => {
    it('should return true for empty values', () => {
      expect(Utility.isEmpty(undefined)).toBe(true);
      expect(Utility.isEmpty(null)).toBe(true);
      expect(Utility.isEmpty('')).toBe(true);
    });

    it('should return false for non-empty values', () => {
      expect(Utility.isEmpty('someValue')).toBe(false);
      expect(Utility.isEmpty(0)).toBe(false);
      expect(Utility.isEmpty({ key: 'value' })).toBe(false);
    });
  });

  describe('ctypeAlpha', () => {
    it('should return true for alphabetic characters', () => {
      expect(Utility.ctypeAlpha('abc')).toBe(true);
      expect(Utility.ctypeAlpha('XYZ')).toBe(true);
    });

    it('should return false for non-alphabetic characters', () => {
      expect(Utility.ctypeAlpha('123')).toBe(false);
      expect(Utility.ctypeAlpha('abc123')).toBe(false);
    });
  });

  // Add similar test cases for strReplace, htmlEntityDecode, isValidEmail, checkZendUri, validatePasswd, validateRedirectUrl, validateNpsSessionUrl, validateCommunityUrl, isTribeCommunityUrl, getValidRedirectUrl, getRoute, validateClientRequest, getUnixTimeStamp, ucfirst, and doEmptyCheck

  describe('validateClientRequest', () => {
    it('should throw UnauthorizedException for invalid client request', () => {
      const clientId = 'invalidClientId';
      const clientSecret = {};
      expect(() => Utility.validateClientRequest(clientId, clientSecret)).toThrowError(UnauthorizedException);
    });

    it('should not throw an exception for valid client request', () => {
      const clientId = 'validClientId';
      const clientSecret = { validClientId: 'secretKey' };
      expect(() => Utility.validateClientRequest(clientId, clientSecret)).not.toThrowError();
    });
  });

  describe('getUnixTimeStamp', () => {
    it('should return the correct Unix timestamp', () => {
      const date = '2023-01-01T00:00:00Z';
      const expectedTimestamp = 1672531200; // Unix timestamp for 2023-01-01
      expect(Utility.getUnixTimeStamp(date)).toBe(expectedTimestamp);
    });
  });

  // Add additional tests for the remaining methods in the Utility class

  // ... (other test cases)

  describe('doEmptyCheck', () => {
    enum Visibility {
      Simplilearn = 0,
      Public = 1,
    }
    enum CareerType {
      Professional = 'professional',
      Student = 'student',
    }
    it('should return success for all fields filled', () => {
      const data: Partial<UpdateUserProfileDto> = {
        email: '<EMAIL>',
        username: 'testUser',
        visibility: Visibility.Simplilearn,
        user_id: '123456',
        dob: '1990-01-01',
        country_code: '+1',
        phone: '1234567890',
        city: 'TestCity',
        user_career_type: CareerType.Professional,
        objective: 'TestObjective',
        funded_by: 'TestFunding',
        qualification: 'TestQualification',
        industry: {
          name: 'TestIndustry',
          tid: '1',
        },
        job_function: {
          name: 'TestJobFunction',
          tid: '2',
        },
        designation: 'TestDesignation',
        company: 'TestCompany',
        institute: 'TestInstitute',
        specialization: 'TestSpecialization',
        total_work_experience: 5,
        is_enterprise_learner: true,
        linkedin_url: 'https://www.linkedin.com/testuser',
      };

      const fields = ['email', 'username'];
      const result = Utility.doEmptyCheck(data, fields);
      expect(result.status).toBe(true);
      expect(result.type).toBe('success');
    });

    it('should return error for empty fields', () => {
      const data: Partial<UpdateUserProfileDto> = {
        email: '<EMAIL>',
        username: 'testUser',
        visibility: Visibility.Simplilearn,
        user_id: '123456',
        dob: '1990-01-01',
        country_code: '+1',
        phone: '1234567890',
        city: 'TestCity',
        user_career_type: CareerType.Professional,
        objective: 'TestObjective',
        funded_by: 'TestFunding',
        qualification: 'TestQualification',
        industry: {
          name: 'TestIndustry',
          tid: '1',
        },
        job_function: {
          name: 'TestJobFunction',
          tid: '2',
        },
        designation: 'TestDesignation',
        company: 'TestCompany',
        institute: 'TestInstitute',
        specialization: 'TestSpecialization',
        total_work_experience: 5,
        is_enterprise_learner: true,
        linkedin_url: 'https://www.linkedin.com/testuser',
      };
      const fields = ['field1', 'field2'];
      const result = Utility.doEmptyCheck(data, fields);
      expect(result.status).toBe(false);
      expect(result.type).toBe('error');
      expect(result.field).toBe('field1');
      expect(result.msg).toBe('This field is required');
      expect(result.comn_msg).toBe('All fields are required');
    });
  });
  describe('ucfirst', () => {
    it('should capitalize the first letter of a string', () => {
      // Arrange
      const inputString = 'testString';
      const expectedOutput = 'TestString';

      // Act
      const result = Utility.ucfirst(inputString);

      // Assert
      expect(result).toEqual(expectedOutput);
    });

    it('should handle an empty string', () => {
      // Arrange
      const inputString = '';
      const expectedOutput = '';

      // Act
      const result = Utility.ucfirst(inputString);

      // Assert
      expect(result).toEqual(expectedOutput);
    });

    it('should handle a single-character string', () => {
      // Arrange
      const inputString = 'a';
      const expectedOutput = 'A';

      // Act
      const result = Utility.ucfirst(inputString);

      // Assert
      expect(result).toEqual(expectedOutput);
    });

    it('should handle a string with already capitalized first letter', () => {
      // Arrange
      const inputString = 'Test';
      const expectedOutput = 'Test';

      // Act
      const result = Utility.ucfirst(inputString);

      // Assert
      expect(result).toEqual(expectedOutput);
    });
  });
  describe('strReplace', () => {
    it('should replace all occurrences of the search string with the replace string', () => {
      // Arrange
      const inputString = 'This is a test string for testing.';
      const searchString = 'test';
      const replaceString = 'example';
      const expectedOutput = 'This is a example string for exampleing.';

      // Act
      const result = Utility.strReplace(inputString, searchString, replaceString);

      // Assert
      expect(result).toEqual(expectedOutput);
    });

    it('should handle an empty string', () => {
      // Arrange
      const inputString = '';
      const searchString = 'test';
      const replaceString = 'example';
      const expectedOutput = '';

      // Act
      const result = Utility.strReplace(inputString, searchString, replaceString);

      // Assert
      expect(result).toEqual(expectedOutput);
    });

    it('should handle a search string not found in the input string', () => {
      // Arrange
      const inputString = 'This is a test string for testing.';
      const searchString = 'missing';
      const replaceString = 'example';
      const expectedOutput = 'This is a test string for testing.';

      // Act
      const result = Utility.strReplace(inputString, searchString, replaceString);

      // Assert
      expect(result).toEqual(expectedOutput);
    });

    it('should handle special characters in the search and replace strings', () => {
      // Arrange
      const inputString = 'Replace all spaces with underscores.';
      const searchString = ' ';
      const replaceString = '_';
      const expectedOutput = 'Replace_all_spaces_with_underscores.';

      // Act
      const result = Utility.strReplace(inputString, searchString, replaceString);

      // Assert
      expect(result).toEqual(expectedOutput);
    });
  });

  describe('isValidEmail', () => {
    it('should return true for a valid email address', () => {
      const validEmail = '<EMAIL>';
      const result = Utility.isValidEmail(validEmail);
      expect(result).toBe(true);
    });

    it('should return false for an invalid email address', () => {
      const invalidEmail = 'invalid-email';
      const result = Utility.isValidEmail(invalidEmail);
      expect(result).toBe(false);
    });

    it('should return false for an empty email address', () => {
      const emptyEmail = '';
      const result = Utility.isValidEmail(emptyEmail);
      expect(result).toBe(false);
    });
  });
  describe('checkZendUri', () => {
    it('should return true for a valid Zend URI', () => {
      const validZendUri = 'https://www.simplilearn.com';
      const result = Utility.checkZendUri(validZendUri);
      expect(result).toBe(true);
    });

    it('should return false for an invalid URI', () => {
      const invalidUri = 'invalid-uri';
      const result = Utility.checkZendUri(invalidUri);
      expect(result).toBe(false);
    });

    it('should return false for an empty URI', () => {
      const emptyUri = '';
      const result = Utility.checkZendUri(emptyUri);
      expect(result).toBe(false);
    });
  });

  describe('validatePasswd', () => {
    it('should return success for a valid and strong password', () => {
      const validPasswd = 'ValidPass123';
      const length = 8;
      const maxLength = 20;
      const result = Utility.validatePasswd(validPasswd, length, maxLength);
      expect(result.type).toBe('success');
      expect(result.msg).toBe('Password is valid and strong.');
    });

    it('should return error for a password with insufficient length', () => {
      const shortPasswd = 'Short1';
      const length = 8;
      const maxLength = 20;
      const result = Utility.validatePasswd(shortPasswd, length, maxLength);
      expect(result.type).toBe('error');
      expect(result.msg).toContain(`should be minimum ${length} characters long`);
    });

    it('should return error for a password with excessive length', () => {
      const longPasswd = 'LongPassword1234567890';
      const length = 8;
      const maxLength = 20;
      const result = Utility.validatePasswd(longPasswd, length, maxLength);
      expect(result.type).toBe('error');
      expect(result.msg).toContain(`cannot be longer than ${maxLength} characters`);
    });

    it('should return error for a weak password without uppercase, lowercase, and number', () => {
      const weakPasswd = 'weakpassword';
      const length = 8;
      const maxLength = 20;
      const result = Utility.validatePasswd(weakPasswd, length, maxLength);
      expect(result.type).toBe('error');
      expect(result.msg).toContain('at least one uppercase, one lowercase letter, and one number');
    });

    it('should return error for an empty password', () => {
      const emptyPasswd = '';
      const length = 8;
      const maxLength = 20;
      const result = Utility.validatePasswd(emptyPasswd, length, maxLength);
      expect(result.type).toBe('error');
      expect(result.msg).toContain('should be minimum');
    });
  });
  describe('validateRedirectUrl', () => {
    it('should return the same URL for a valid Simplilearn redirect URL', () => {
      const validRedirectUrl = 'https://www.simplilearn.com/some-path';
      const result = Utility.validateRedirectUrl(validRedirectUrl);
      expect(result).toBe(validRedirectUrl);
    });

    it('should return an empty string for an invalid URL', () => {
      const invalidUrl = 'invalid-url';
      const result = Utility.validateRedirectUrl(invalidUrl);
      expect(result).toBe('');
    });

    it('should return an empty string for an empty URL', () => {
      const emptyUrl = '';
      const result = Utility.validateRedirectUrl(emptyUrl);
      expect(result).toBe('');
    });

    it('should return an empty string for a URL with decoding error', () => {
      const urlWithDecodingError = 'https://www.simplilearn.com/%%30%30';
      const result = Utility.validateRedirectUrl(urlWithDecodingError);
      expect(result).toBe('');
    });
  });

  describe('validateNpsSessionUrl', () => {
    it('should return true for a valid NPS session URL', () => {
      const validNpsSessionUrl = 'https://www.simplilearn.com/nps/session/some-id';
      const result = Utility.validateNpsSessionUrl(validNpsSessionUrl);
      expect(result).toBe(true);
    });

    it('should return false for an invalid URL', () => {
      const invalidUrl = 'https://www.simplilearn.com/some-path';
      const result = Utility.validateNpsSessionUrl(invalidUrl);
      expect(result).toBe(false);
    });

    it('should return false for an empty URL', () => {
      const emptyUrl = '';
      const result = Utility.validateNpsSessionUrl(emptyUrl);
      expect(result).toBe(false);
    });
  });

  describe('validateCommunityUrl', () => {
    it('should return true for a valid community URL within the community base URL', () => {
      const validCommunityUrl = 'https://community.simplilearn.com/some-path';
      const urlData = {
        url: validCommunityUrl,
        communityBaseUrl: 'https://community.simplilearn.com',
        tribeCommunityBaseUrl: 'https://tribe.simplilearn.com',
      };
      const result = Utility.validateCommunityUrl(urlData);
      expect(result).toBe(true);
    });

    it('should return true for a valid community URL within the tribe community base URL', () => {
      const validTribeCommunityUrl = 'https://tribe.simplilearn.com/some-path';
      const urlData = {
        url: validTribeCommunityUrl,
        communityBaseUrl: 'https://community.simplilearn.com',
        tribeCommunityBaseUrl: 'https://tribe.simplilearn.com',
      };
      const result = Utility.validateCommunityUrl(urlData);
      expect(result).toBe(true);
    });

    it('should return false for an invalid URL', () => {
      const invalidUrl = 'https://www.simplilearn.com/some-path';
      const urlData = {
        url: invalidUrl,
        communityBaseUrl: 'https://community.simplilearn.com',
        tribeCommunityBaseUrl: 'https://tribe.simplilearn.com',
      };
      const result = Utility.validateCommunityUrl(urlData);
      expect(result).toBe(false);
    });

    it('should return false for an empty URL', () => {
      const emptyUrl = '';
      const urlData = {
        url: emptyUrl,
        communityBaseUrl: 'https://community.simplilearn.com',
        tribeCommunityBaseUrl: 'https://tribe.simplilearn.com',
      };
      const result = Utility.validateCommunityUrl(urlData);
      expect(result).toBe(false);
    });
  });

  describe('isTribeCommunityUrl', () => {
    it('should return true for a valid Tribe community URL', () => {
      const validTribeCommunityUrl = 'https://tribe.simplilearn.com/some-path';
      const tribeCommunityBaseUrl = 'https://tribe.simplilearn.com';
      const result = Utility.isTribeCommunityUrl(validTribeCommunityUrl, tribeCommunityBaseUrl);
      expect(result).toBe(true);
    });

    it('should return false for an invalid URL', () => {
      const invalidUrl = 'https://www.simplilearn.com/some-path';
      const tribeCommunityBaseUrl = 'https://tribe.simplilearn.com';
      const result = Utility.isTribeCommunityUrl(invalidUrl, tribeCommunityBaseUrl);
      expect(result).toBe(false);
    });

    it('should return false for an empty URL', () => {
      const emptyUrl = '';
      const tribeCommunityBaseUrl = 'https://tribe.simplilearn.com';
      const result = Utility.isTribeCommunityUrl(emptyUrl, tribeCommunityBaseUrl);
      expect(result).toBe(false);
    });
  });
  describe('getValidRedirectUrl', () => {
    it('should return the same URL for a valid redirect URL', () => {
      const validRedirectUrl = 'https://www.simplilearn.com/some-path';
      const lmsSiteUrl = 'https://www.simplilearn.com';
      const result = Utility.getValidRedirectUrl(validRedirectUrl, lmsSiteUrl);
      expect(result).toBe(validRedirectUrl);
    });

    it('should return the LMS site URL for an invalid redirect URL', () => {
      const invalidRedirectUrl = 'invalid-url';
      const lmsSiteUrl = 'https://www.simplilearn.com';
      const result = Utility.getValidRedirectUrl(invalidRedirectUrl, lmsSiteUrl);
      expect(result).toBe(lmsSiteUrl);
    });

    it('should return the LMS site URL for an empty redirect URL', () => {
      const emptyRedirectUrl = '';
      const lmsSiteUrl = 'https://www.simplilearn.com';
      const result = Utility.getValidRedirectUrl(emptyRedirectUrl, lmsSiteUrl);
      expect(result).toBe(lmsSiteUrl);
    });
  });

  describe('getRoute', () => {
    it('should return the correct action and page for a valid route with parameters', () => {
      const validRouteWithParams = { path: '/some-path/:id' };
      const result = Utility.getRoute(validRouteWithParams);
      expect(result).toEqual({ action: '/some-path', page: 'some-path' });
    });

    it('should return the correct action and page for a valid route without parameters', () => {
      const validRouteWithoutParams = { path: '/some-path' };
      const result = Utility.getRoute(validRouteWithoutParams);
      expect(result).toEqual({ action: '/some-path', page: 'some-path' });
    });

    it('should return the default action and page for an empty route', () => {
      const emptyRoute = { path: '' };
      const result = Utility.getRoute(emptyRoute);
      expect(result).toEqual({ action: '/auth/login', page: 'login' });
    });
  });
});
