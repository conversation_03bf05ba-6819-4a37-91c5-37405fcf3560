export type UserType = 'FSA' | '';

export enum USERCATEGORY {
  PAID = 'Paid',
  FREEMIUM = 'Freemium',
}
export type ForgotPasswordB2B = {
  client_id: string;
  gid: string;
  affiliateLmsUrl: string;
  affiliateLogoUrl: string;
  providerName: string;
  affiliateName: string;
  isWhiteLabelingEnabled: string;
  fromEmailAddress: string;
  email: string;
  planType: 'standard';
  displayName?: string;
  uid?: number;
};

export type ResetPassword = {
  uid: string;
  token: string;
  password: string;
};

export type TribeUserType = {
  id: string;
  email: string;
  name: string;
};

export type RouteType = {
  action: string;
  page: string;
};

export type ViewData = {
  showPhone: boolean;
  isPhoneMandatory: boolean;
  groupId: number;
  accountSetupValidateUrl: string;
  formAction: string;
  userEmail: string;
  userName: string;
  firstName: string;
  lastName: string;
  varCountryId: string;
  phoneNo: string;
  pageSource: string;
};

export type UserSignupToken = {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  authType: string;
  loginMethodType: string;
  redirectUrl: string;
};

export type CommunityUserSignup = {
  email: string;
  name: string;
  password: string;
  userGroupId: string;
  user_agent: string;
  ip_address: string;
  logFailedUser?: boolean;
  roles?: [];
};

export type FailedCommunityUserType = {
  email: string;
  user_data: string;
  is_processed: number;
  status: number;
  creation_time: number;
  modified_time: number;
};
export type CurrentUser = {
  email: string;
  uid: string;
  name: string;
  first_name: string;
  last_name: string;
  phone_no: string;
};

export type UseSocialLinkData = {
  type: string;
  email: string;
  sub?: string;
  source?: string;
  created_on?: string;
  status?: number;
};

export type ValidationResponse = {
  type: 'error' | 'success';
  userAccountStatus?: boolean;
  socialAccountStatus?: boolean;
  accountSetupStatus?: string;
  errorCode?: number;
  msg?: string;
};

export type CustomResponse = {
  status?: boolean;
  msg?: string;
  message?: string;
  data?: any;
};

export type ProcessSocialAuthResponse = {
  status: boolean;
  setCookie: boolean;
  cookieName: string;
  cookieValue: any;
  returnResponse: {
    type: string;
    message: string;
    userAccountStatus: boolean;
    socialAccountStatus: boolean;
    accountSetupStatus: boolean;
    referer: string;
    redirectUrl: string;
  };
};

export type ResponseCookieType = {
  name: string;
  value: any;
  options?: any;
};

export type CommunityResponse = {
  url: string | '';
  setCookie: boolean | false;
  cookieValue?: ResponseCookieType;
};

export type NpsCookieType = {
  status: boolean | false;
  redirectUrl: string | '';
  setCookie: boolean | false;
  cookieValue?: ResponseCookieType;
};

export type RegisterByEmailParams = {
  client_id: string;
  user_email: string;
  user_name?: string;
  phone_no?: string;
  country_id?: string | number;
  country_code?: string;
  city_id?: string | number;
  user_roles?: string[] | null;
  user_type?: string;
  overwrite?: string | number;
  lms_url?: string;
  gid?: number;
  sso_request?: string;
  email_block?: string;
};
