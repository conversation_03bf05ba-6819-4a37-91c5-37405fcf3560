import { Modu<PERSON> } from '@nestjs/common';
import { AuthController } from './controller/auth.controller';
import { AuthService } from './services/auth/auth.service';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { CustomJwtOptionsFactory } from './guards/auth.jwt.optionsfactory';
import { CachingService } from '../caching/caching.service';
import { JwtStrategy } from './guards/auth.jwt.strategy';
import { AuthTokenHelper } from './helper/auth.tokenhelper';
import { UserToken } from '../db/mongo/schema/user-token/user.token.schema';
import { UserTokenSchema } from '../db/mongo/schema/user-token/user.token.schema';
import { MongooseModule } from '@nestjs/mongoose';
import { PasswordHistory, PasswordHistorySchema } from '../db/mongo/schema/password-history/password.histroy.schema';
import { LinkedInStrategy } from './guards/socialAuthStrategies/linkedin/linkedInStrategy';
import { GoogleStrategy } from './guards/socialAuthStrategies/google/googleStrategy';
import { AppleStrategy } from './guards/socialAuthStrategies/apple/appleStrategy';
import { FacebookStrategy } from './guards/socialAuthStrategies/facebook/facebookStrategy';
import { SocialLoginService } from './services/social-login/social-login.service';
import { UserModule } from '../user/user.module';
@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: UserToken.name,
          schema: UserTokenSchema,
          collection: 'usertokens',
        },
      ],
      'secure',
    ),
    MongooseModule.forFeature(
      [
        {
          name: PasswordHistory.name,
          schema: PasswordHistorySchema,
          collection: 'passwordhistories',
        },
      ],
      'secure',
    ),
    PassportModule,
    JwtModule.registerAsync({
      global: true,
      useClass: CustomJwtOptionsFactory,
      inject: [ConfigService],
    }),
    UserModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtStrategy,
    AuthTokenHelper,
    CachingService,
    ConfigService,
    LinkedInStrategy,
    FacebookStrategy,
    GoogleStrategy,
    AppleStrategy,
    SocialLoginService,
  ],
  exports: [AuthService],
})
export class AuthModule {}
