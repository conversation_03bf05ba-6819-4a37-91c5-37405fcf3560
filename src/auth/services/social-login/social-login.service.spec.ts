import { Test, TestingModule } from '@nestjs/testing';
import { SocialLoginService } from './social-login.service';
import { ConfigService } from '@nestjs/config';
import { Logger } from '../../../logging/logger';

describe('SocialLoginService', () => {
  let service: SocialLoginService;
  let configService: ConfigService;

  // Mock the Communication class methods we use
  const mockCommunication = {
    get: jest.fn(),
    setRequestHeader: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SocialLoginService,
        {
          provide: ConfigService,
          useValue: { get: jest.fn() },
        },
      ],
    }).compile();

    service = module.get<SocialLoginService>(SocialLoginService);
    configService = module.get<ConfigService>(ConfigService);
    // Manually inject the mock communication methods into the service instance
    (service as any).get = mockCommunication.get;
    (service as any).setRequestHeader = mockCommunication.setRequestHeader;

    jest.spyOn(Logger, 'log').mockImplementation(() => ({} as any));
    jest.spyOn(Logger, 'error').mockImplementation(() => ({} as any));
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('fetchSocialUserProfileDetailsFromToken', () => {
    it('should return failed status if token is empty', async () => {
      const result = await service.fetchSocialUserProfileDetailsFromToken({ token: '', type: 'facebook' });
      expect(result).toEqual({ status: false, msg: 'failed' });
    });

    describe('facebook', () => {
      beforeEach(() => {
        (configService.get as jest.Mock).mockReturnValue('http://facebook.api/');
      });

      it('should return user profile on successful api call', async () => {
        const mockResponse = { data: { id: '123', name: 'Test User' } };
        mockCommunication.get.mockResolvedValue(mockResponse);
        const result = await service.fetchSocialUserProfileDetailsFromToken({ token: 'fb-token', type: 'facebook' });
        expect(result).toEqual({ status: true, msg: 'success', data: { sub: '123', ...mockResponse.data } });
      });

      it('should return failed status if facebook response does not have id', async () => {
        const mockResponse = { data: { name: 'Test User' } };
        mockCommunication.get.mockResolvedValue(mockResponse);
        const result = await service.fetchSocialUserProfileDetailsFromToken({ token: 'fb-token', type: 'facebook' });
        expect(result).toEqual({ status: false, msg: 'Something went wrong, please try again.' });
      });

      it('should handle facebook api errors gracefully', async () => {
        mockCommunication.get.mockRejectedValue(new Error('API Error'));
        const result = await service.fetchSocialUserProfileDetailsFromToken({ token: 'fb-token', type: 'facebook' });
        expect(result).toEqual({ status: false, msg: 'something went wrong while getting the token details' });
      });
    });

    describe('linkedin', () => {
      beforeEach(() => {
        (configService.get as jest.Mock).mockReturnValue('http://linkedin.api/');
      });

      it('should return user profile on successful api call', async () => {
        const mockResponse = { status: 200, data: { id: '123', localizedFirstName: 'Test' } };
        mockCommunication.get.mockResolvedValue(mockResponse);
        const result = await service.fetchSocialUserProfileDetailsFromToken({ token: 'li-token', type: 'linkedin' });
        expect(result).toEqual({ status: true, msg: 'success', data: mockResponse.data });
      });

      it('should return failed status if linkedin api call is not successful', async () => {
        const mockResponse = { status: 401, data: {} };
        mockCommunication.get.mockResolvedValue(mockResponse);
        const result = await service.fetchSocialUserProfileDetailsFromToken({ token: 'li-token', type: 'linkedin' });
        expect(result).toEqual({ status: false, msg: 'Something went wrong, please try again.' });
      });
    });

    describe('google', () => {
      beforeEach(() => {
        (configService.get as jest.Mock).mockReturnValue('http://google.api/');
      });

      it('should return user profile on successful api call', async () => {
        const mockResponse = { status: 200, data: { sub: '123', name: 'Test User' } };
        mockCommunication.get.mockResolvedValue(mockResponse);
        const result = await service.fetchSocialUserProfileDetailsFromToken({ token: 'gg-token', type: 'google' });
        expect(result).toEqual({ status: true, msg: 'success', data: mockResponse });
      });

      it('should return failed status if google api call is not successful', async () => {
        const mockResponse = { status: 401, data: {} };
        mockCommunication.get.mockResolvedValue(mockResponse);
        const result = await service.fetchSocialUserProfileDetailsFromToken({ token: 'gg-token', type: 'google' });
        expect(result).toEqual({ status: false, msg: 'Something went wrong, please try again.' });
      });
    });

    it('should return undefined for unsupported type', async () => {
      const result = await service.fetchSocialUserProfileDetailsFromToken({ token: 'some-token', type: 'unknown' });
      expect(result).toBeUndefined();
    });

    it('should handle general errors gracefully', async () => {
      (configService.get as jest.Mock).mockImplementation(() => {
        throw new Error('General Error');
      });
      const result = await service.fetchSocialUserProfileDetailsFromToken({ token: 'some-token', type: 'facebook' });
      expect(result).toEqual({ status: false, msg: 'something went wrong while getting the token details' });
    });
  });
});
