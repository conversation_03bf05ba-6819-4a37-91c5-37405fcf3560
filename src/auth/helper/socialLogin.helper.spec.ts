import { Test, TestingModule } from '@nestjs/testing';
import { SocialLoginHelper } from './socialLogin.helper';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import { IUserRepository, UserRepository } from '../../user/repositories/user/user.repository';
import { AuthTokenHelper } from './auth.tokenhelper';
import { SocialLoginService } from '../services/social-login/social-login.service';
import { User } from '../../db/mongo/schema/user/user.schema';
import { Logger } from '../../logging/logger';
import { Utility } from '../../common/util/utility';
import { UserService } from '../../user/services/user.service';

jest.mock('../../logging/logger');
jest.mock('../../common/util/utility');

describe('SocialLoginHelper', () => {
  let socialLoginHelper: SocialLoginHelper;
  let helperService: HelperService;
  let userRepository: IUserRepository;
  let authTokenHelper: AuthTokenHelper;
  let socialLoginService: SocialLoginService;
  let userService: UserService;
  let configService: ConfigService;

  const mockUser: Partial<User> = {
    uid: 1,
    email: '<EMAIL>',
    user_social_data: [],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SocialLoginHelper,
        { provide: ConfigService, useValue: { get: jest.fn((key) => key) } },
        { provide: HelperService, useValue: { get: jest.fn(), getHelper: jest.fn() } },
        { provide: UserRepository, useValue: { findOneAndUpdate: jest.fn().mockResolvedValue(mockUser), getUserByEmail: jest.fn().mockResolvedValue(mockUser) } },
        { provide: AuthTokenHelper, useValue: { generateSessionTokens: jest.fn().mockResolvedValue({ idToken: 'test-token' }), decodeJWTToken: jest.fn(), createSignedToken: jest.fn() } },
        { provide: SocialLoginService, useValue: { fetchSocialUserProfileDetailsFromToken: jest.fn() } },
        { provide: UserService, useValue: { userRegistration: jest.fn().mockResolvedValue(mockUser) } },
      ],
    }).compile();

    socialLoginHelper = module.get<SocialLoginHelper>(SocialLoginHelper);
    helperService = module.get<HelperService>(HelperService);
    userRepository = module.get<IUserRepository>(UserRepository);
    authTokenHelper = module.get<AuthTokenHelper>(AuthTokenHelper);
    socialLoginService = module.get<SocialLoginService>(SocialLoginService);
    userService = module.get<UserService>(UserService);
    configService = module.get<ConfigService>(ConfigService);

    (helperService.get as jest.Mock).mockImplementation(service => {
        if (service === UserRepository) return userRepository;
        if (service === AuthTokenHelper) return authTokenHelper;
        if (service === SocialLoginService) return socialLoginService;
        if (service === UserService) return userService;
        return {};
    });

    const mockGetHelper = {
        getUserPassword: jest.fn().mockReturnValue('password'),
        getTokenRedirectUrl: jest.fn().mockResolvedValue({ status: true, redirectUrl: 'token-redirect', setCookie: true, cookieValue: { name: 'token', value: 'token' } }),
        getNpsRedirectUrl: jest.fn().mockResolvedValue({ status: true, redirectUrl: 'nps-redirect', setCookie: true, cookieValue: { name: 'nps', value: 'nps' } }),
        getCommunityRedirectUrl: jest.fn().mockResolvedValue({ url: 'community-redirect', setCookie: true, cookieValue: { name: 'comm', value: 'test' } }),
        generateRedirectLinkToManageRedirect: jest.fn().mockResolvedValue('manage-redirect'),
        getTimezoneFromCountryCode: jest.fn().mockResolvedValue('UTC'),
        handleUserSignup: jest.fn(),
        updateUserLoginTime: jest.fn(),
        sendDataToLrs: jest.fn(),
    };
    (helperService.getHelper as jest.Mock).mockResolvedValue(mockGetHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getUserSocialRedirectUrl', () => {
    it('should cover all branches of getUserSocialRedirectUrl', async () => {
        (Utility.isEmpty as jest.Mock).mockImplementation(key => !key);
        await socialLoginHelper.getUserSocialRedirectUrl({ calendarRedirectCookie: 'cal-url' }, {}, {});
        await socialLoginHelper.getUserSocialRedirectUrl({ freemiumAssignmentToken: 'token' }, {}, {});
        await socialLoginHelper.getUserSocialRedirectUrl({ npsRedirectCookie: 'nps-cookie' }, {}, {});
        await socialLoginHelper.getUserSocialRedirectUrl({ communityCookie: 'comm-cookie' }, {}, {});
        (Utility.isEmpty as jest.Mock).mockReturnValue(true);
        await socialLoginHelper.getUserSocialRedirectUrl({}, {}, {});
        (helperService.getHelper as jest.Mock).mockRejectedValue(new Error('Redirect error'));
        await socialLoginHelper.getUserSocialRedirectUrl({}, {}, {});
        expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('userSocialStatus', () => {
    it('should cover all branches of userSocialStatus', async () => {
        await socialLoginHelper.userSocialStatus('<EMAIL>', 'type', { user_social_data: [] });
        await socialLoginHelper.userSocialStatus('<EMAIL>', 'type', { user_social_data: [{ email: '<EMAIL>', type: 'type' }] });
        await socialLoginHelper.userSocialStatus('<EMAIL>', 'type', { user_social_data: [{ email: '<EMAIL>', type: 'type' }] });
        await socialLoginHelper.userSocialStatus('<EMAIL>', 'type', { user_social_data: [{ email: '<EMAIL>', type: 'other-type' }] });
    });
  });

  describe('linkUserSocialData', () => {
    it('should cover all branches of linkUserSocialData', async () => {
        jest.spyOn(socialLoginHelper, 'checkSocialLoginStatus').mockReturnValue({ status: false, isActive: false, index: -1, data: {} });
        await socialLoginHelper.linkUserSocialData({ email: '<EMAIL>', type: 't' }, { user_social_data: [] });
        jest.spyOn(socialLoginHelper, 'checkSocialLoginStatus').mockReturnValue({ status: true, isActive: false, index: 0, data: {} });
        await socialLoginHelper.linkUserSocialData({ email: '<EMAIL>', type: 't' }, { user_social_data: [{ email: '<EMAIL>', type: 't', status: 0 }] });
        jest.spyOn(socialLoginHelper, 'checkSocialLoginStatus').mockReturnValue({ status: true, isActive: true, index: 0, data: {} });
        await socialLoginHelper.linkUserSocialData({ email: '<EMAIL>', type: 't' }, { user_social_data: [{ email: '<EMAIL>', type: 't', status: 1 }] });
        (userRepository.findOneAndUpdate as jest.Mock).mockRejectedValue(new Error('DB Error'));
        jest.spyOn(socialLoginHelper, 'checkSocialLoginStatus').mockReturnValue({ status: false, isActive: false, index: -1, data: {} });
        await socialLoginHelper.linkUserSocialData({ email: '<EMAIL>', type: 't' }, { user_social_data: [] });
        expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('processUserLink', () => {
    it('should cover all branches of processUserLink', async () => {
        jest.spyOn(socialLoginHelper, 'linkUserSocialData').mockResolvedValue(true);
        await socialLoginHelper.processUserLink({ email: '<EMAIL>' }, {});
        jest.spyOn(socialLoginHelper, 'linkUserSocialData').mockResolvedValue(false);
        await socialLoginHelper.processUserLink({ email: '<EMAIL>' }, {});
        (userRepository.getUserByEmail as jest.Mock).mockRejectedValue(new Error('DB Error'));
        await socialLoginHelper.processUserLink({email: '<EMAIL>'}, {});
        expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('processSocialAuthResponse', () => {
    const data = { email: '<EMAIL>', token: 't', type: 'google', authRespType: 'code', referer: 'login', requestSource: 'web' };

    it('should cover all branches of processSocialAuthResponse', async () => {
        (configService.get as jest.Mock).mockReturnValue({ allowedSocialLoginPlatforms: ['facebook'] });
        await socialLoginHelper.processSocialAuthResponse(data, {});
        (configService.get as jest.Mock).mockReturnValue({ allowedSocialLoginPlatforms: ['google'] });
        (Utility.isEmpty as jest.Mock).mockReturnValue(true);
        await socialLoginHelper.processSocialAuthResponse({ ...data, token: '' }, {});
        (Utility.isEmpty as jest.Mock).mockReturnValue(false);
        jest.spyOn(socialLoginHelper, 'createUserSocialData').mockResolvedValue({ status: false, data: {type: 'google'} as any, msg:'' });
        await socialLoginHelper.processSocialAuthResponse(data, {});
        jest.spyOn(socialLoginHelper, 'createUserSocialData').mockResolvedValue({ status: true, data: { email: '<EMAIL>' }, msg:'' });
        jest.spyOn(socialLoginHelper, 'checkSocialLoginStatus').mockReturnValue({ status: true, isActive: true, index: 0, data: { email: '<EMAIL>' } });
        await socialLoginHelper.processSocialAuthResponse({ ...data, requestSource: 'mobile' }, { user_social_data: [{ email: '<EMAIL>', type: 'google', status: 1 }] });
        await socialLoginHelper.processSocialAuthResponse(data, { user_social_data: [{ email: '<EMAIL>', type: 'google', status: 1 }] });
        jest.spyOn(socialLoginHelper, 'validateSocialUserStatus').mockResolvedValue({ status: true, setCookie: true, cookieName: '', cookieValue: '' as any, returnResponse: {} as any });
        await socialLoginHelper.processSocialAuthResponse({ ...data, authRespType: '' }, mockUser);
        await socialLoginHelper.processSocialAuthResponse({ ...data, authRespType: '' }, null);
        (configService.get as jest.Mock).mockImplementation(() => { 
            throw new Error('Config Error');
        });
        await socialLoginHelper.processSocialAuthResponse(data, {});
        expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('createUserSocialData', () => {
    it('should cover all branches of createUserSocialData', async () => {
        (authTokenHelper.decodeJWTToken as jest.Mock).mockResolvedValue({ sub: 'sub', email: '<EMAIL>' });
        await socialLoginHelper.createUserSocialData({ token: 't', type: 'google', email: '<EMAIL>', source: 's' });
        (socialLoginService.fetchSocialUserProfileDetailsFromToken as jest.Mock).mockResolvedValue({ status: true, data: { sub: 'sub', email: '<EMAIL>' } });
        await socialLoginHelper.createUserSocialData({ token: 't', type: 'facebook', email: '<EMAIL>', source: 's' });
        (socialLoginService.fetchSocialUserProfileDetailsFromToken as jest.Mock).mockResolvedValue({ status: false, msg: 'error' });
        await socialLoginHelper.createUserSocialData({ token: 't', type: 'facebook', email: '<EMAIL>', source: 's' });
        (authTokenHelper.decodeJWTToken as jest.Mock).mockRejectedValue(new Error('JWT Error'));
        await socialLoginHelper.createUserSocialData({ token: 't', type: 'google', email: '<EMAIL>', source: 's' });
        expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('validateSocialUserStatus', () => {
    it('should cover all branches of validateSocialUserStatus', async () => {
        jest.spyOn(socialLoginHelper, 'checkSocialLoginStatus').mockReturnValue({ status: true, isActive: true, index: 0, data: {} });
        await socialLoginHelper.validateSocialUserStatus(mockUser, { type: 't', referer: 'r', email: '<EMAIL>', source: 's' });
        jest.spyOn(socialLoginHelper, 'checkSocialLoginStatus').mockReturnValue({ status: false, isActive: false, index: -1, data: {} });
        await socialLoginHelper.validateSocialUserStatus(mockUser, { type: 't', referer: 'r', email: '<EMAIL>', source: 's' });
        (authTokenHelper.generateSessionTokens as jest.Mock).mockResolvedValue({ idToken: null });
        await socialLoginHelper.validateSocialUserStatus(mockUser, { type: 't', referer: 'r', email: '<EMAIL>', source: 's' });
        jest.spyOn(socialLoginHelper, 'checkSocialLoginStatus').mockImplementation(() => { throw new Error('Check Error'); });
        await socialLoginHelper.validateSocialUserStatus(mockUser, { type: 't', referer: 'r', email: '<EMAIL>', source: 's' });
        expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('createUserAccount', () => {
    it('should cover all branches of createUserAccount', async () => {
        (Utility.ctypeAlpha as jest.Mock).mockReturnValue(true);
        await socialLoginHelper.createUserAccount({ country_code: 'US' }, 'source');
        (userService.userRegistration as jest.Mock).mockResolvedValue(null);
        await socialLoginHelper.createUserAccount({}, 'source');
        (helperService.getHelper as jest.Mock).mockRejectedValue(new Error('Create Error'));
        await socialLoginHelper.createUserAccount({}, 'source');
        expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('checkSocialLoginStatus', () => {
    it('should cover all branches of checkSocialLoginStatus', () => {
        socialLoginHelper.checkSocialLoginStatus({ email: '<EMAIL>', type: 't' }, [{ email: '<EMAIL>', type: 't', status: 1 }]);
        socialLoginHelper.checkSocialLoginStatus({ email: '<EMAIL>', type: 't' }, [{ email: '<EMAIL>', type: 't', status: 0 }]);
        socialLoginHelper.checkSocialLoginStatus({ email: '<EMAIL>', type: 't' }, null);
        expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('updateUserSocialAccountDetails', () => {
    it('should cover all branches of updateUserSocialAccountDetails', async () => {
        await socialLoginHelper.updateUserSocialAccountDetails({ user_social_data: [{ type: 't', status: 1 }] }, 't');
        await socialLoginHelper.updateUserSocialAccountDetails({ user_social_data: [{ type: 't', status: 0 }] }, 't');
        (userRepository.findOneAndUpdate as jest.Mock).mockRejectedValue(new Error('DB Error'));
        await expect(socialLoginHelper.updateUserSocialAccountDetails({ user_social_data: [{ type: 't', status: 1 }] }, 't')).rejects.toThrow('DB Error');
    });
  });

  describe('createUserSocialAccountDetails', () => {
    it('should cover all branches of createUserSocialAccountDetails', async () => {
        await socialLoginHelper.createUserSocialAccountDetails({});
        (userRepository.findOneAndUpdate as jest.Mock).mockRejectedValue(new Error('DB Error'));
        await expect(socialLoginHelper.createUserSocialAccountDetails({})).rejects.toThrow('DB Error');
    });
  });

  describe('registerFrsUser', () => {
    it('should cover all branches of registerFrsUser', async () => {
        jest.spyOn(socialLoginHelper, 'createUserAccount').mockResolvedValue({ type: 'success', msg:'success', data: mockUser });
        jest.spyOn(socialLoginHelper, 'linkUserSocialData').mockResolvedValue(true);
        await socialLoginHelper.registerFrsUser({}, {});
        jest.spyOn(socialLoginHelper, 'createUserAccount').mockResolvedValue({ type: 'error', msg: 'error', data: {} });
        await socialLoginHelper.registerFrsUser({}, {});
        jest.spyOn(socialLoginHelper, 'createUserAccount').mockResolvedValue({ type: 'success', msg: 'success', data: mockUser });
        (authTokenHelper.generateSessionTokens as jest.Mock).mockResolvedValue({ idToken: null });
        await socialLoginHelper.registerFrsUser({}, {});
    });
  });
});