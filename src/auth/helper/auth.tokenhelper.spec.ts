import { Test, TestingModule } from '@nestjs/testing';
import { AuthTokenHelper } from './auth.tokenhelper';
import { JwtService } from '@nestjs/jwt';
import { CryptoHelper } from '../../helper/helper.crypto';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
import { CachingService } from '../../caching/caching.service';
import { User } from '../../db/mongo/schema/user/user.schema';
import { Logger } from '../../logging/logger';

jest.mock('../../logging/logger');

describe('AuthTokenHelper', () => {
  let authTokenHelper: AuthTokenHelper;
  let jwtService: JwtService;
  let cryptoHelper: CryptoHelper;
  let configService: ConfigService;
  let helperService: HelperService;
  let paperclipService: PaperclipService;
  let enterpriseService: EnterpriseService;
  let cachingService: CachingService;

  const mockUser: Partial<User> = {
    uid: 1,
    roles: [{ rid: 1, roleName: 'user' }],
    user_groups: [1, 2, 3],
    display_name: 'Test User',
    first_name: 'Test',
    last_name: 'User',
    email: '<EMAIL>',
    name: 'testuser',
    country_code: 'US',
    user_options: 0,
    timezone: 'UTC',
    user_type: 'Freemium' as any,
    profile_pic: { filename: 'pic.jpg' },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthTokenHelper,
        { provide: JwtService, useValue: { signAsync: jest.fn(), verify: jest.fn(), decode: jest.fn() } },
        { provide: 'CRYPTO_HELPER', useValue: { encrypt: jest.fn(), decrypt: jest.fn(), createHmac: jest.fn() } },
        { provide: ConfigService, useValue: { get: jest.fn() } },
        { provide: HelperService, useValue: { get: jest.fn(), getHelper: jest.fn() } },
        { provide: PaperclipService, useValue: { getSkillupUserInfo: jest.fn() } },
        { provide: EnterpriseService, useValue: { getGroupByGid: jest.fn() } },
        { provide: CachingService, useValue: { get: jest.fn(), set: jest.fn() } },
      ],
    }).compile();

    authTokenHelper = module.get<AuthTokenHelper>(AuthTokenHelper);
    jwtService = module.get<JwtService>(JwtService);
    cryptoHelper = module.get<CryptoHelper>('CRYPTO_HELPER');
    configService = module.get<ConfigService>(ConfigService);
    helperService = module.get<HelperService>(HelperService);
    paperclipService = module.get<PaperclipService>(PaperclipService);
    enterpriseService = module.get<EnterpriseService>(EnterpriseService);
    cachingService = module.get<CachingService>(CachingService);

    (helperService.get as jest.Mock).mockImplementation(service => {
        if (service === PaperclipService) return paperclipService;
        if (service === EnterpriseService) return enterpriseService;
        if (service === CachingService) return cachingService;
        return {};
    });
    (helperService.getHelper as jest.Mock).mockResolvedValue({ updateUserLoginTime: jest.fn() });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('decodeToken', () => {
    it('should decode a token and decrypt attributes', () => {
      const mockPayload = { data: { id: 'enc_id', email: 'enc_email', uname: 'enc_uname', user_type: 'test' } };
      (jwtService.verify as jest.Mock).mockReturnValue({ payload: mockPayload });
      (cryptoHelper.decrypt as jest.Mock).mockImplementation(v => v.replace('enc_', ''));
      const result = authTokenHelper.decodeToken('some-token');
      expect(result.id).toBe('id');
      expect(result.email).toBe('email');
      expect(result.uname).toBe('uname');
      expect(result.user_type).toBe('test');
    });

    it('should return undefined if verification fails', () => {
      (jwtService.verify as jest.Mock).mockImplementation(() => { throw new Error('Verify Error'); });
      const result = authTokenHelper.decodeToken('bad-token');
      expect(result).toBeUndefined();
    });

    it('should handle missing user_type in payload', () => {
        const mockPayload = { data: { id: 'enc_id', email: 'enc_email', uname: 'enc_uname' } };
        (jwtService.verify as jest.Mock).mockReturnValue({ payload: mockPayload });
        const result = authTokenHelper.decodeToken('some-token');
        expect(result.user_type).toBe('');
    });
  });

  describe('createSignedToken', () => {
    it('should create a signed token with sign options', async () => {
      (jwtService.signAsync as jest.Mock).mockResolvedValue('signed-token');
      const result = await authTokenHelper.createSignedToken({ data: 'd' }, { expiresIn: '1h' });
      expect(jwtService.signAsync).toHaveBeenCalledWith({ data: 'd' }, { expiresIn: '1h' });
      expect(result).toBe('signed-token');
    });

    it('should create a signed token without sign options', async () => {
        (jwtService.signAsync as jest.Mock).mockResolvedValue('signed-token-no-opts');
        const result = await authTokenHelper.createSignedToken({ data: 'd' });
        expect(jwtService.signAsync).toHaveBeenCalledWith({ data: 'd' });
        expect(result).toBe('signed-token-no-opts');
      });
  });

  describe('decryptAttribute', () => {
    it('should decrypt data', () => {
      (cryptoHelper.decrypt as jest.Mock).mockReturnValue('decrypted');
      expect(authTokenHelper.decryptAttribute('encrypted')).toBe('decrypted');
    });

    it('should return original data on decryption error', () => {
      (cryptoHelper.decrypt as jest.Mock).mockImplementation(() => { throw new Error('Decrypt Error'); });
      expect(authTokenHelper.decryptAttribute('bad-data')).toBe('bad-data');
      expect(Logger.log).toHaveBeenCalled();
    });
  });

  describe('getUserInfoForJwt', () => {
    beforeEach(() => {
        (paperclipService.getSkillupUserInfo as jest.Mock).mockResolvedValue({ data: { userType: 'Premium' } });
        (enterpriseService.getGroupByGid as jest.Mock).mockResolvedValue({ data: [{ displayName: 'Test Group' }] });
    });

    it('should get user info for JWT', async () => {
      const result = await authTokenHelper.getUserInfoForJwt(mockUser, 'client-key');
      expect(result.data.userType).toBe('Premium');
      expect(result.data.lgid).toBe('3');
    });

    it('should handle various optional user data fields', async () => {
        const extendedUser = {
            ...mockUser,
            lgid: 4,
            user_groups: [5], // To test lgid override and single group case
            b2b_ssp: { data: 'ssp' },
            sfSsoUserAuthToken: { client_id: 'sf', token: 'token' },
            course_catalog: { data: 'catalog' },
            futurex_data: [{ data: 'futurex' }],
            vendor_data: [{ data: 'vendor' }]
        };
        const result:any = await authTokenHelper.getUserInfoForJwt(extendedUser, 'key');
        expect(result.data.lgid).toBe('4');
        expect(result.data.b2b_ssp).toBeDefined();
        expect(result.data.sfSsoUserAuthToken).toBeDefined();
        expect(result.data.catalogActivation).toBeDefined();
        expect(result.data.futurexData).toBeDefined();
        expect(result.data.vendorData).toBeDefined();
        expect(enterpriseService.getGroupByGid).toHaveBeenCalledWith(4);
    });

    it('should use default role if present', async () => {
        (configService.get as jest.Mock).mockReturnValue({ '10': 'default-role' });
        const result = await authTokenHelper.getUserInfoForJwt({ ...mockUser, roles: [] }, 'key');
        expect(result.data.roles['10']).toBe('default-role');
    });

    it('should handle null userTokenData', async () => {
        (paperclipService.getSkillupUserInfo as jest.Mock).mockResolvedValue({ data: null });
        const result = await authTokenHelper.getUserInfoForJwt({ user_groups: [] } as any, 'key');
        expect(result.data.userType).toBe('Freemium');
    });

    it('should handle single user group', async () => {
        const result = await authTokenHelper.getUserInfoForJwt({ ...mockUser, user_groups: [10] }, 'key');
        expect(result.data.lgid).toBe('10');
    });

    it('should handle no user groups', async () => {
        const result = await authTokenHelper.getUserInfoForJwt({ ...mockUser, user_groups: [] }, 'key');
        expect(result.data.lgid).toBe('');
        expect(result.data.gid).toBe('');
    });

    it('should handle no profile picture', async () => {
        const result = await authTokenHelper.getUserInfoForJwt({ ...mockUser, profile_pic: null }, 'key');
        expect(result.data.profile_pic).toBe('');
    });

    it('should handle lgid <= 2', async () => {
        await authTokenHelper.getUserInfoForJwt({ ...mockUser, user_groups: [2] }, 'key');
        expect(enterpriseService.getGroupByGid).not.toHaveBeenCalled();
    });

    it('should handle no group details from enterprise service', async () => {
        (enterpriseService.getGroupByGid as jest.Mock).mockResolvedValue({ data: [] });
        const result:any = await authTokenHelper.getUserInfoForJwt({ ...mockUser, user_groups: [4] }, 'key');
        expect(result.data.groupName).toBe('');
    });

    it('should handle actor_email', async () => {
        const result = await authTokenHelper.getUserInfoForJwt({ ...mockUser, actor_email: '<EMAIL>' } as any, 'key');
        expect(result.actor_emails).toBe('<EMAIL>');
    });
  });

  describe('generateSessionTokens', () => {
    beforeEach(() => {
        authTokenHelper.getUserInfoForJwt = jest.fn().mockResolvedValue({ data: { id: '1' } });
        authTokenHelper.decodeJWTToken = jest.fn().mockResolvedValue({ exp: Date.now() });
        authTokenHelper.createSignedToken = jest.fn().mockResolvedValue('signed-token');
    });

    it('should generate session tokens', async () => {
      (cachingService.get as jest.Mock).mockResolvedValue(null);
      const result = await authTokenHelper.generateSessionTokens(mockUser, 'key');
      expect(result.idToken).toBe('signed-token');
      expect(cachingService.set).toHaveBeenCalled();
    });

    it('should handle existing sessions in cache', async () => {
        const existingSession = JSON.stringify({ [Date.now() - 1000]: Date.now() + 3600 });
        (cachingService.get as jest.Mock).mockResolvedValue(existingSession);
        await authTokenHelper.generateSessionTokens(mockUser, 'key');
        expect(cachingService.set).toHaveBeenCalled();
    });

    it('should handle empty existing session data in cache', async () => {
        const existingSession = JSON.stringify(null);
        (cachingService.get as jest.Mock).mockResolvedValue(existingSession);
        await authTokenHelper.generateSessionTokens(mockUser, 'key');
        expect(cachingService.set).toHaveBeenCalled();
    });

    it('should handle expired sessions in cache', async () => {
        const existingSession = JSON.stringify({ [Date.now() - 4000]: Date.now() - 3600 });
        (cachingService.get as jest.Mock).mockResolvedValue(existingSession);
        await authTokenHelper.generateSessionTokens(mockUser, 'key');
        expect(cachingService.set).toHaveBeenCalled();
    });

    it('should handle generation errors', async () => {
        (helperService.getHelper as jest.Mock).mockRejectedValue(new Error('Session Gen Error'));
        await authTokenHelper.generateSessionTokens(mockUser, 'key');
        expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('encodeOriginalToken', () => {
    it('should encode a token', async () => {
      authTokenHelper.decodeToken = jest.fn().mockResolvedValue({ data: 'payload' });
      authTokenHelper.createSignedToken = jest.fn().mockResolvedValue('signed-token');
      const result = await authTokenHelper.encodeOriginalToken('token');
      expect(result).toBe('signed-token');
    });

    it('should handle encoding errors', async () => {
        authTokenHelper.decodeToken = jest.fn().mockRejectedValue(new Error('Encode Error'));
        await authTokenHelper.encodeOriginalToken('bad-token');
        expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('getSignHash', () => {
    it('should generate a signed hash', () => {
      (cryptoHelper.createHmac as jest.Mock).mockReturnValue('hash+/=');
      const result = authTokenHelper.getSignHash('input', 'secret');
      expect(result).toBe('hash-_');
    });

    it('should generate a signed hash with provided algo', () => {
        (cryptoHelper.createHmac as jest.Mock).mockReturnValue('hash+/=');
        const result = authTokenHelper.getSignHash('input', 'secret', 'sha512');
        expect(result).toBe('hash-_');
      });
  });

  describe('drupalHmacBase64', () => {
    it('should generate a drupal-compatible hmac', () => {
        (cryptoHelper.createHmac as jest.Mock).mockReturnValue('hmac+/=');
        const result = authTokenHelper.drupalHmacBase64('data', 'key');
        expect(result).toBe('hmac-_');
    });

    it('should handle hmac generation errors', () => {
        (cryptoHelper.createHmac as jest.Mock).mockImplementation(() => { throw new Error('HMAC error'); });
        expect(() => authTokenHelper.drupalHmacBase64('data', 'key')).toThrow('HMAC error');
        expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('learnerAuthenticationRehash', () => {
    it('should rehash for learner authentication', () => {
      authTokenHelper.drupalHmacBase64 = jest.fn();
      authTokenHelper.learnerAuthenticationRehash('gid', 'ts', 'email', 'secret');
      expect(authTokenHelper.drupalHmacBase64).toHaveBeenCalledWith('tsemail', 'secretgid');
    });
  });

  describe('teamLearnerAuthenticationRehash', () => {
    it('should rehash for team learner authentication', () => {
        authTokenHelper.drupalHmacBase64 = jest.fn();
        authTokenHelper.teamLearnerAuthenticationRehash('gid', 'sgid', 'reqid', 'ts', 'email', 'secret');
        expect(authTokenHelper.drupalHmacBase64).toHaveBeenCalledWith('tsemail', 'secretgid#sgid#reqid');
    });
  });
});