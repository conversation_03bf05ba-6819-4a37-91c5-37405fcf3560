import { Inject, Injectable } from '@nestjs/common';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Crypto<PERSON>elper } from '../../helper/helper.crypto';
import { Logger } from '../../logging/logger';
import { User } from '../../db/mongo/schema/user/user.schema';
import { Utility } from '../../common/util/utility';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';
import { CachingService } from '../../caching/caching.service';
@Injectable()
export class AuthTokenHelper {
  @Inject(JwtService) private readonly jwtService: JwtService;
  @Inject('CRYPTO_HELPER') private readonly crypto: CryptoHelper;
  @Inject(ConfigService) private readonly configService: ConfigService;
  @Inject() private readonly helperService: HelperService;

  // Decodes a JWT token and extracts user-related information from its payload.
  decodeToken = (token: string) => {
    try {
      const result = this.jwtService.verify(token, { complete: true });
      const {
        id,
        email,
        roles,
        rid,
        ckey,
        country,
        first_name,
        last_name,
        gid,
        lgid,
        name,
        npsSD,
        opts,
        timezone,
        uname,
        user_type,
        userType,
      } = result.payload.data;
      Logger.log('decodeToken', { user_type, userType });
      return {
        ckey,
        country,
        email: this.decryptAttribute(email),
        first_name,
        gid,
        id: this.decryptAttribute(id),
        last_name,
        lgid,
        name,
        npsSD,
        opts,
        roles,
        timezone,
        uname: this.decryptAttribute(uname),
        user_type: user_type || '',
        userType,
        uid: this.decryptAttribute(id),
        sessionId: rid,
      };
    } catch (e) {}
    return undefined;
  };

  /**
   * Creates a signed JSON Web Token (JWT) asynchronously using the provided payload and optional signing options.
   *
   * @param payload - The data to be encoded into the JWT.
   * @param signOptions - Optional signing options to customize the token generation (e.g., expiration, algorithm).
   * @returns A promise that resolves to the signed JWT as a string.
   */
  createSignedToken = async (payload, signOptions = {}) => {
    if (Object.keys(signOptions).length > 0) {
      return await this.jwtService.signAsync(payload, signOptions);
    } else {
      return await this.jwtService.signAsync(payload);
    }
  };

  /**
   * Decrypts an encrypted attribute using the CryptoHelper service.
   *
   * @param data - The encrypted data to be decrypted.
   * @returns The decrypted data, or the original data if decryption fails.
   */
  decryptAttribute = (data) => {
    try {
      const decrypted = this.crypto.decrypt(data);
      return decrypted;
    } catch (error: any) {
      Logger.log('decrypt attributes error', error);
    }
    return data;
  };

  /**
   * Retrieves user information for JWT token generation.
   *
   * @param userTokenData - Partial user data containing information such as roles, user groups, and other user-specific details.
   * @param clientKey - A unique client key associated with the request.
   * @returns A promise that resolves to an object containing user data, user type, and actor emails.
   *
   * The returned user data includes:
   * - `ckey`: The client key.
   * - `id`: The user ID.
   * - `name`: The display name of the user.
   * - `first_name`: The first name of the user.
   * - `last_name`: The last name of the user.
   * - `uname`: The username (email).
   * - `email`: The email address of the user.
   * - `roles`: An object mapping role IDs to role names.
   * - `gid`: Encrypted group IDs.
   * - `lgid`: The largest group ID or a specific group ID if provided.
   * - `country`: The country code of the user.
   * - `opts`: User options.
   * - `timezone`: The user's timezone.
   * - `userType`: The user type, defaulting to 'Freemium' if not provided.
   * - `user_type`: Additional user type information.
   * - `profile_pic`: The URL of the user's profile picture.
   * - `b2b_ssp`: B2B SSP data if available.
   * - `sfSsoUserAuthToken`: Salesforce SSO user authentication token if available.
   * - `catalogActivation`: Course catalog activation data if available.
   * - `futurexData`: FutureX data if available.
   * - `vendorData`: Vendor data if available.
   * - `groupName`: The name of the group associated with the largest group ID, if applicable.
   *
   * @throws Will throw an error if required services (e.g., PaperclipService, EnterpriseService) fail to respond.
   */
  getUserInfoForJwt = async (userTokenData: Partial<User>, clientKey: string) => {
    const defaultRole = this.configService.get<object>('defaultRole') || {};
    const roles = {
      ...(Object.keys(defaultRole).length
      ? { [Object.keys(defaultRole)[0]]: Object.values(defaultRole)[0] }
      : {}),
      ...(userTokenData?.roles?.reduce(
      (acc, role) => ({ ...acc, [role.rid]: role.roleName }),
      {},
      ) || {}),
    };

    const {
      uid='' ,
      display_name = '',
      first_name,
      last_name,
      email = '',
      name = '',
      country_code = '',
      user_options = 0,
      timezone,
      user_type,
      profile_pic,
    } = userTokenData || {};

    let lgid =userTokenData?.user_groups.length === 1 ? userTokenData.user_groups[0] : (userTokenData?.user_groups.length > 1 ? Math.max(...userTokenData?.user_groups):0);
    lgid = userTokenData['lgid'] ? userTokenData['lgid'] : lgid;

    const gid = userTokenData?.user_groups.length > 0 ? this.crypto.encrypt(userTokenData.user_groups.toString()) : '';

    const paperclipService = await this.helperService.get<PaperclipService>(PaperclipService);

    const getSkillupUserInfoResponse = await paperclipService.getSkillupUserInfo({ userId: Number(uid) });
    let userType = (getSkillupUserInfoResponse?.data)?.['userType'];
    const userData = {
      ckey: clientKey,
      id: String(uid),
      name: display_name,
      first_name,
      last_name,
      uname: name,
      email: email,
      roles,
      gid,
      lgid: lgid ? lgid.toString() : '',
      country: country_code,
      opts: user_options,
      timezone,
      userType: userType || 'Freemium',
      user_type: user_type || '',
      profile_pic: profile_pic?.filename || '',
    };

    if (userTokenData['b2b_ssp']) {
      userData['b2b_ssp'] = userTokenData['b2b_ssp'];
    }
    if (userTokenData['sfSsoUserAuthToken']) {
      userData['sfSsoUserAuthToken'] = {
        client_id: userTokenData['sfSsoUserAuthToken']['client_id'],
        token: `${userTokenData['sfSsoUserAuthToken']['token']}`,
      };
    }
    // token: userTokenData ['sfSsoUserAuthToken']['token'],client_id: userTokenData['sfSsoUserAuthToken']['client_id']}
    if (userTokenData['course_catalog']) {
      userData['catalogActivation'] = userTokenData['course_catalog'];
    }
    if (userTokenData['futurex_data'] && Array.isArray(userTokenData['futurex_data'])) {
      userData['futurexData'] = userTokenData['futurex_data'];
    }
    if (userTokenData['vendor_data'] && Array.isArray(userTokenData['vendor_data'])) {
      userData['vendorData'] = userTokenData['vendor_data'];
    }
    if (lgid && lgid > 2) {
      const enterpriseService: EnterpriseService = await this.helperService.get<EnterpriseService>(EnterpriseService);
      const groupDetails = await enterpriseService.getGroupByGid(lgid);

      userData['groupName'] = groupDetails?.data[0] ? groupDetails?.data[0]?.displayName : '';
    }

    return {
      data: userData,
      actor_emails: userTokenData['actor_email'] || null,
    };
  };

  /**
   * Decodes a JWT token from the provided cookie value.
   *
   * @param cookieValue - The JWT token string extracted from the cookie.
   * @returns A promise that resolves to an object containing the decoded token data,
   *          including the following properties:
   *          - `data`: The payload data of the token.
   *          - `exp`: The expiration timestamp of the token.
   *          - `iat`: The issued-at timestamp of the token.
   *          - `iss`: The issuer of the token.
   *          - `nbf`: The "not before" timestamp of the token.
   * @throws Logs an error if the decoding process fails, including the method name,
   *         error message, stack trace, and the provided cookie value.
   */
  async decodeJWTToken(
    cookieValue: string,
  ): Promise<{ data: any; exp: number; iat: number; iss: string; nbf: string }> {
    try {
      const jwtService = new JwtService();
      const cookieDecodeValue: any = await jwtService.decode(cookieValue);
      return cookieDecodeValue;
    } catch (error: any) {
      Logger.error('decodeJWTToken', {
        METHOD: this.constructor.name + '@' + this.decodeJWTToken.name,
        MESSAGE: error.message,
        REQUEST: { cookieValue },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  /**
   * Generates session tokens for a user, including an ID token and user data.
   *
   * @param user - A partial representation of the user object containing user details.
   * @param clientKey - A unique key associated with the client making the request.
   * @param signOptions - Optional signing options for the JWT token.
   * @returns A promise that resolves to an object containing the ID token and user data.
   *
   * @throws Logs an error and handles exceptions if token generation fails.
   *
   * The function performs the following steps:
   * 1. Retrieves user information for the JWT payload and a helper service instance.
   * 2. Encrypts sensitive user information (e.g., ID, username, email) for the JWT payload.
   * 3. Creates a signed JWT token using the payload and optional signing options.
   * 4. Updates the user's login time using the helper service.
   * 5. Returns the generated ID token and user data.
   */
  generateSessionTokens = async (
    user: Partial<User>,
    clientKey: string,
    signOptions?: {},
  ): Promise<{ idToken: string; userData: object }> => {
    try {
      const [payload, UserHelper, cacheService] = await Promise.all([
        this.getUserInfoForJwt(user, clientKey),
        this.helperService.getHelper('UserHelper'),
        this.helperService.get<CachingService>(CachingService)
      ]);

      const userData = { ...payload.data };
      //Encrypt user info for jwt token
      payload['data']['id'] = this.crypto.encrypt(payload['data']['id']);
      payload['data']['uname'] = this.crypto.encrypt(payload['data']['uname']);
      payload['data']['email'] = this.crypto.encrypt(payload['data']['email']);
      const token = await this.createSignedToken(payload, signOptions);

      //Sessions
      const decodedToken = await this.decodeJWTToken(token);
      const time = Math.floor(Date.now() / 1000);
      const cacheKey = `${'sc_'}${userData?.id}`;
      const existingSession : any = await cacheService.get(cacheKey);
      const newSession = {};
      newSession['issuedAt'] = decodedToken?.exp;

      if(existingSession) {
        const existingSessionData = JSON.parse(existingSession);
        if(existingSessionData) {
          for (const iat in existingSessionData) {
            const exp = existingSessionData[iat];
            if (exp > time) {
              newSession[iat] = exp;
            }
          }
        }
      }
      //store the token in memcache for expiry
      await cacheService.set(cacheKey, JSON.stringify(newSession), this.configService.get('cacheTtl'));
      UserHelper.updateUserLoginTime(user);
      
      return { idToken: token, userData: userData };
    } catch (error: any) {
      Logger.error('generateSessionTokens', {
        METHOD: this.constructor.name + '@' + this.generateSessionTokens.name,
        MESSAGE: error.message,
        REQUEST: { user, clientKey },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  };

  /**
   * Encodes an original token by first decoding it to extract the payload
   * and then creating a signed token with the payload data.
   *
   * @param token - The original token to be encoded.
   * @returns A promise that resolves to the signed token.
   * @throws Logs an error if the token decoding or signing process fails.
   *
   * The error log includes:
   * - METHOD: The class and method name where the error occurred.
   * - MESSAGE: The error message.
   * - REQUEST: The input token that caused the error.
   * - RESPONSE: The error stack trace.
   * - TIMESTAMP: The timestamp when the error occurred.
   */
  encodeOriginalToken = async (token: string) => {
    try {
      const payload = await this.decodeToken(token);
      return this.createSignedToken({ data: payload });
    } catch (error: any) {
      Logger.error('encodeOriginalToken', {
        METHOD: this.constructor.name + '@' + this.encodeOriginalToken.name,
        MESSAGE: error.message,
        REQUEST: { token },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  };

  /**
   * Generates a signed hash for the given input using the specified algorithm and client secret.
   *
   * @param input - The input data to be signed.
   * @param clientSecret - The secret key used for signing the input.
   * @param algo - (Optional) The hashing algorithm to use. If not provided, the default algorithm
   *               is retrieved from the configuration service.
   * @returns The signed hash, encoded in a URL-safe Base64 format.
   *
   * @remarks
   * The method replaces `+` with `-`, `/` with `_`, and removes trailing `=` characters
   * to ensure the hash is URL-safe.
   */
  getSignHash(input, clientSecret, algo = '') {
    const hashAlgo = this.configService.get<string>('shaAlgorithm');
    const signingInput = Utility.urlsafeB64Encode(input);
    const sign = this.crypto.createHmac(signingInput, clientSecret, algo || hashAlgo, 'base64');
    return sign.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  }

  /**
   * Generates a Drupal-compatible HMAC (Hash-based Message Authentication Code) in Base64 format.
   * The resulting HMAC is URL-safe, with '+' replaced by '-', '/' replaced by '_', and '=' removed.
   *
   * @param data - The input string to be hashed.
   * @param key - The secret key used for hashing.
   * @returns A URL-safe Base64-encoded HMAC string.
   * @throws Will log an error and rethrow it if an exception occurs during HMAC generation.
   */
  drupalHmacBase64(data: string, key: string): string {
    try {
      const hmac = this.crypto.createHmac(data, key, 'sha256', 'base64');
      // Modify the hmac so it's safe to use in URLs.
      return hmac.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
    } catch (error: any) {
      Logger.error('drupalHmacBase64', {
        METHOD: this.constructor.name + '@' + this.drupalHmacBase64.name,
        MESSAGE: error.message,
        REQUEST: { data, key },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  /**
   * Generates a rehashed authentication token for a learner.
   *
   * @param gid - The unique identifier for the learner.
   * @param timestamp - The timestamp used for the rehashing process.
   * @param email - The email address of the learner.
   * @param clientSecrectCloud6 - The client secret key used for hashing.
   * @returns A Base64-encoded HMAC string generated using the input and client secret.
   */
  learnerAuthenticationRehash(gid, timestamp, email, clientSecrectCloud6) {
    const input = timestamp + email;
    const clientSecret = clientSecrectCloud6 + gid;

    return this.drupalHmacBase64(input, clientSecret);
  }

  /**
   * Generates a rehashed authentication token for a team learner.
   *
   * @param gid - The group ID associated with the team learner.
   * @param sgid - The subgroup ID associated with the team learner.
   * @param reqid - The request ID for the authentication process.
   * @param timestamp - The timestamp of the authentication request.
   * @param email - The email address of the team learner.
   * @param clientSecrectCloud6 - The client secret key for cloud authentication.
   * @returns A Base64-encoded HMAC string generated using the provided inputs.
   */
  public teamLearnerAuthenticationRehash(
    gid: string,
    sgid: string,
    reqid: string,
    timestamp: string,
    email: string,
    clientSecrectCloud6,
  ): string {
    const input = timestamp + email;
    const separator = '#';
    const clientSecret = clientSecrectCloud6 + gid + separator + sgid + separator + reqid;

    return this.drupalHmacBase64(input, clientSecret);
  }
}
