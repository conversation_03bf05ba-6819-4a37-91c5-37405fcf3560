import { Test, TestingModule } from '@nestjs/testing';
import { AuthHelper } from './auth.helper';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';

import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';
import { AuthTokenHelper } from './auth.tokenhelper';
import { CachingService } from '../../caching/caching.service';
import { IUserRepository, UserRepository } from '../../user/repositories/user/user.repository';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
import { UserMgmtCommunityService } from '../../user/services/communication/usermgmt.community.service';
import { BadRequestException } from '@nestjs/common';
import * as drupalHash from 'drupal-hash';
import { User } from '../../db/mongo/schema/user/user.schema';
import { LoginDto } from '../dtos/login.dto';
import { Utility } from '../../common/util/utility';

jest.mock('drupal-hash');
jest.mock('../../common/util/utility');
jest.mock('../../logging/logger');

describe('AuthHelper', () => {
  let authHelper: AuthHelper;
  let helperService: HelperService;
  let userRepository: IUserRepository;
  let authTokenHelper: AuthTokenHelper;
  let paperclipService: PaperclipService;
  let enterpriseService: EnterpriseService;
  
  let cachingService: CachingService;
  let configService: ConfigService;
  

  const mockUser: Partial<User> = {
    uid: 1,
    email: '<EMAIL>',
    password: 'hashedpassword',
    status: 1,
    country_code: 'US',
    first_name: 'Test',
    last_name: 'User',
    phone_no: '**********',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthHelper,
        { provide: ConfigService, useValue: { get: jest.fn((key) => key) } },
        { provide: HelperService, useValue: { get: jest.fn(), getHelper: jest.fn() } },
        { provide: 'CRYPTO_HELPER', useValue: { encrypt: jest.fn().mockResolvedValue('encrypted'), decrypt: jest.fn() } },
        { provide: PaperclipService, useValue: { getSkillupReferralRewardInfo: jest.fn(), saveSkillupReferral: jest.fn() } },
        { provide: AuthTokenHelper, useValue: { createSignedToken: jest.fn().mockResolvedValue('signed-token'), decodeJWTToken: jest.fn(), generateSessionTokens: jest.fn() } },
        { provide: UserRepository, useValue: { getUserByEmail: jest.fn().mockResolvedValue(mockUser) } },
        { provide: CachingService, useValue: { get: jest.fn(), set: jest.fn() } },
        { provide: EnterpriseService, useValue: { getGroupDomainByGid: jest.fn() } },
        { provide: UserMgmtCommunityService, useValue: { signUpNewUser: jest.fn() } },
      ],
    }).compile();

    authHelper = module.get<AuthHelper>(AuthHelper);
    helperService = module.get<HelperService>(HelperService);
    userRepository = module.get<IUserRepository>(UserRepository);
    authTokenHelper = module.get<AuthTokenHelper>(AuthTokenHelper);
    paperclipService = module.get<PaperclipService>(PaperclipService);
    enterpriseService = module.get<EnterpriseService>(EnterpriseService);
    cachingService = module.get<CachingService>(CachingService);
    configService = module.get<ConfigService>(ConfigService);
    

    (helperService.get as jest.Mock).mockImplementation(service => {
        if (service === PaperclipService) return paperclipService;
        if (service === UserRepository) return userRepository;
        if (service === CachingService) return cachingService;
        if (service === EnterpriseService) return enterpriseService;
        if (service === AuthTokenHelper) return authTokenHelper;
        return {
            updateUserTimezone: jest.fn(),
            updateUserLoginTime: jest.fn(),
            getFreemiumRedirectUrl: jest.fn().mockResolvedValue('freemium-redirect-url'),
            getCommunityRedirectUrl: jest.fn().mockResolvedValue({ url: 'community-redirect-url', cookieValue: { name: 'comm', value: 'test' } }),
            liveClassRedirectUrl: jest.fn().mockResolvedValue('live-class-redirect'),
            saveUserSignup: jest.fn().mockResolvedValue({ status: 'success' })
        };
    });
    (helperService.getHelper as jest.Mock).mockResolvedValue({
        updateUserTimezone: jest.fn(),
        updateUserLoginTime: jest.fn(),
        getFreemiumRedirectUrl: jest.fn().mockResolvedValue('freemium-redirect-url'),
        getCommunityRedirectUrl: jest.fn().mockResolvedValue({ url: 'community-redirect-url', cookieValue: { name: 'comm', value: 'test' } }),
        liveClassRedirectUrl: jest.fn().mockResolvedValue('live-class-redirect'),
        saveUserSignup: jest.fn().mockResolvedValue({ status: 'success' })
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getTokenRedirectUrl', () => {
    it('should cover all branches of getTokenRedirectUrl', async () => {
        (paperclipService.getSkillupReferralRewardInfo as jest.Mock).mockResolvedValue({ respData: { status: 'success', skillUpReferralInfo: { enrolmentRestricted: true, userRefCode: 'REF123' } } });
        await authHelper.getTokenRedirectUrl({ skillupEnrollmentCampaign: 'test-campaign' }, 'test-token', mockUser, 'http://calendar.url');
        (paperclipService.getSkillupReferralRewardInfo as jest.Mock).mockResolvedValue({});
        await authHelper.getTokenRedirectUrl({}, 'test-token', mockUser, '');
        (paperclipService.getSkillupReferralRewardInfo as jest.Mock).mockRejectedValue(new Error('Paperclip Error'));
        await authHelper.getTokenRedirectUrl({}, 'test-token', mockUser, '');
    });
  });

  describe('getNpsRedirectUrl', () => {
    it('should cover all branches of getNpsRedirectUrl', async () => {
        (authTokenHelper.decodeJWTToken as jest.Mock).mockResolvedValue({ url: 'decoded-nps-url' });
        await authHelper.getNpsRedirectUrl({ npsRedirectCookie: 'nps-token' }, 'nps-cookie', mockUser, 'redirect-url');
        (authTokenHelper.decodeJWTToken as jest.Mock).mockResolvedValue(null);
        await authHelper.getNpsRedirectUrl({ npsRedirectCookie: 'nps-token' }, 'nps-cookie', mockUser, 'redirect-url');
        (authTokenHelper.decodeJWTToken as jest.Mock).mockRejectedValue(new Error('NPS Error'));
        await authHelper.getNpsRedirectUrl({}, 'nps-cookie', mockUser, 'redirect-url');
    });
  });

  describe('handleUserSignup', () => {
    it('should cover all branches of handleUserSignup', async () => {
        await authHelper.handleUserSignup('source', 'ref123', { email: '<EMAIL>', uid: 2, first_name: 'New' });
        (helperService.getHelper as jest.Mock).mockResolvedValue({ saveUserSignup: jest.fn().mockResolvedValue({ status: 'failed' }) });
        await authHelper.handleUserSignup('source', 'ref123', {});
        (helperService.getHelper as jest.Mock).mockRejectedValue(new Error('Signup Error'));
        await authHelper.handleUserSignup('source', 'ref123', {});
    });
  });

  describe('getSignupRedirect', () => {
    it('should cover all branches of getSignupRedirect', async () => {
        (Utility.isEmpty as jest.Mock).mockReturnValue(false);
        await authHelper.getSignupRedirect({}, { email: '<EMAIL>' }, 'redirect-url');
        (Utility.isEmpty as jest.Mock).mockReturnValue(true);
        await authHelper.getSignupRedirect({}, { email: '<EMAIL>' }, '');
        (Utility.isEmpty as jest.Mock).mockReturnValue(false);
        (paperclipService.getSkillupReferralRewardInfo as jest.Mock).mockResolvedValue(null);
        await authHelper.getSignupRedirect({ freemiumAssignmentToken: 'assign-token' }, { uid: 1 }, '');
        (paperclipService.getSkillupReferralRewardInfo as jest.Mock).mockResolvedValue({});
        await authHelper.getSignupRedirect({ freemiumAssignmentToken: 'assign-token' }, { uid: 1 }, '');
        (Utility.isEmpty as jest.Mock).mockReturnValue(false);
        await authHelper.getSignupRedirect({ communityCookie: 'comm-cookie' }, {}, '');
        (helperService.get as jest.Mock).mockRejectedValue(new Error('Redirect Error'));
        await expect(authHelper.getSignupRedirect({}, {}, '')).rejects.toThrow('Redirect Error');
    });
  });

  describe('authenticateUser', () => {
    it('should cover all branches of authenticateUser', async () => {
        const loginDto: LoginDto = { email: '<EMAIL>', password: 'password123', redirect_url: '', calendar_url: '' };
        (drupalHash.checkPassword as jest.Mock).mockResolvedValue(true);
        await authHelper.authenticateUser(loginDto);
        (userRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
        await expect(authHelper.authenticateUser(loginDto)).rejects.toThrow(BadRequestException);
        (userRepository.getUserByEmail as jest.Mock).mockResolvedValue({ ...mockUser, password: null });
        await authHelper.authenticateUser(loginDto);
        (userRepository.getUserByEmail as jest.Mock).mockResolvedValue(mockUser);
        (drupalHash.checkPassword as jest.Mock).mockResolvedValue(false);
        await expect(authHelper.authenticateUser(loginDto)).rejects.toThrow(BadRequestException);
        (userRepository.getUserByEmail as jest.Mock).mockResolvedValue({ ...mockUser, status: 0 });
        (drupalHash.checkPassword as jest.Mock).mockResolvedValue(true);
        await expect(authHelper.authenticateUser(loginDto)).rejects.toThrow(BadRequestException);
        (userRepository.getUserByEmail as jest.Mock).mockRejectedValue(new Error('DB Error'));
        await expect(authHelper.authenticateUser(loginDto)).rejects.toThrow('DB Error');
    });
  });

  describe('getLoginRedirectUrl', () => {
    const loginDto: LoginDto = { email: '<EMAIL>', password: 'pw', redirect_url: ' ', calendar_url: ' ' };
    it('should cover all branches of getLoginRedirectUrl', async () => {
        await authHelper.getLoginRedirectUrl({}, { ...loginDto, redirect_url: 'some-url' }, mockUser);
        await authHelper.getLoginRedirectUrl({}, loginDto, mockUser);
        await authHelper.getLoginRedirectUrl({ freemiumAssignmentToken: 'token' }, loginDto, mockUser);
        await authHelper.getLoginRedirectUrl({ npsRedirectCookie: 'nps-token' }, loginDto, mockUser);
        await authHelper.getLoginRedirectUrl({}, { ...loginDto, isB2BAndB2C: true, domainGid: 'gid', urlDomain: 'domain' }, mockUser);
        await authHelper.getLoginRedirectUrl({}, { ...loginDto, calendar_url: 'cal-url' }, mockUser);
        (helperService.getHelper as jest.Mock).mockRejectedValue(new Error('Login Redirect Error'));
        await expect(authHelper.getLoginRedirectUrl({}, loginDto, mockUser)).rejects.toThrow('Login Redirect Error');
    });
  });

  describe('generateRedirectLinkToManageRedirect', () => {
    it('should cover all branches of generateRedirectLinkToManageRedirect', async () => {
        await authHelper.generateRedirectLinkToManageRedirect(mockUser, 'login', 'email', 'redirect', 'calendar');
        await authHelper.generateRedirectLinkToManageRedirect(mockUser, 'signup', 'email', 'redirect', 'calendar');
        (helperService.get as jest.Mock).mockRejectedValue(new Error('JWT Error'));
        await authHelper.generateRedirectLinkToManageRedirect(mockUser, 'login', 'email');
    });
  });

  describe('forgotPasswordAttemptLimit', () => {
    it('should cover all branches of forgotPasswordAttemptLimit', async () => {
      (cachingService.get as jest.Mock).mockResolvedValue(2);
      await authHelper.forgotPasswordAttemptLimit('<EMAIL>');
      (configService.get as jest.Mock).mockImplementation(key => (key === 'fpResetCount' ? 3 : 10));
      (cachingService.get as jest.Mock).mockResolvedValue(3);
      await authHelper.forgotPasswordAttemptLimit('<EMAIL>');
    });
  });

  describe('getTokenByEmail', () => {
    it('should cover all branches of getTokenByEmail', async () => {
      await authHelper.getTokenByEmail('<EMAIL>', 'client-key');
      (userRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
      await expect(authHelper.getTokenByEmail('<EMAIL>', 'key')).rejects.toThrow(BadRequestException);
      (userRepository.getUserByEmail as jest.Mock).mockResolvedValue({ ...mockUser, status: 0 });
      await expect(authHelper.getTokenByEmail('<EMAIL>', 'key')).rejects.toThrow(BadRequestException);
      (userRepository.getUserByEmail as jest.Mock).mockRejectedValue(new Error('DB Error'));
      await expect(authHelper.getTokenByEmail('<EMAIL>', 'key')).rejects.toThrow(BadRequestException);
    });
  });

  describe('getUserPassResetUrl', () => {
    it('should cover all branches of getUserPassResetUrl', async () => {
      await authHelper.getUserPassResetUrl(123);
    });
  });

  describe('getSubDomain', () => {
    it('should cover all branches of getSubDomain', async () => {
      (enterpriseService.getGroupDomainByGid as jest.Mock).mockResolvedValue({ data: [{ lmsSiteUrl: 'sub.domain.com' }] });
      await authHelper.getSubDomain('gid');
      (enterpriseService.getGroupDomainByGid as jest.Mock).mockResolvedValue({ data: [{ lmsSiteUrl: '' }] });
      await authHelper.getSubDomain('gid');
      (enterpriseService.getGroupDomainByGid as jest.Mock).mockRejectedValue(new Error('Subdomain Error'));
      await expect(authHelper.getSubDomain('gid')).rejects.toThrow(BadRequestException);
    });
  });
});
