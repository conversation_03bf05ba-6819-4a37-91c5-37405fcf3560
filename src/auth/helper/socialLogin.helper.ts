import { Inject, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import {
  CustomResponse,
  ProcessSocialAuthResponse,
  ResponseCookieType,
  UseSocialLinkData,
} from '../../common/typeDef/auth.type';
import { IUserRepository, UserRepository } from '../../user/repositories/user/user.repository';
import { AuthTokenHelper } from './auth.tokenhelper';
import { SocialLoginService } from '../services/social-login/social-login.service';
import { User } from '../../db/mongo/schema/user/user.schema';
import { SignupDto } from '../../user-api/dto/signup.dto';
import { Utility } from './../../common/util/utility';
import { UserSocial } from '../../db/mongo/schema/user/social.schema';
import { VIEW_PAGES } from '../config/view.constants';
import { UserService } from '../../user/services/user.service';

export class SocialLoginHelper {
  @Inject() private configService: ConfigService;
  @Inject(HelperService) helperService: HelperService;

  async getUserSocialRedirectUrl(
    cookieData,
    linkData,
    userObj,
  ): Promise<CustomResponse & { setCookie: boolean; cookieValue?: ResponseCookieType[] }> {
    let response: CustomResponse & { setCookie: boolean; cookieValue?: ResponseCookieType[] } = {
      status: false,
      data: '',
      msg: '',
      setCookie: false,
    };
    try {
      const userHelper = await this.helperService.getHelper('UserHelper');
      const authHelper = await this.helperService.getHelper('AuthHelper');
      const calendarRedirectCookieName = this.configService.get('calendarRedirectCookie');
      const npsRedirectUrl = this.configService.get('npsRedirectCookie');
      const assignmentToken = this.configService.get('freemiumAssignmentToken');
      const loginInfo = { email: linkData?.email, password: userHelper.getUserPassword() };
      if (!Utility.isEmpty(cookieData[calendarRedirectCookieName])) {
        const encodedRedirect: string =
          this.configService.get('calendarUrl') + encodeURIComponent(cookieData[calendarRedirectCookieName]);
        response = { status: true, msg: 'success', data: encodedRedirect, setCookie: true };
        response.cookieValue.push({
          name: 'calendarRedirectCookie',
          value: encodedRedirect,
          options: { expires: new Date(new Date().getTime() / 1000 - 3600) },
        });
        return response;
      }
      if (!Utility.isEmpty(cookieData[assignmentToken])) {
        const tokenRedirectData = await authHelper.getTokenRedirectUrl(
          cookieData,
          userObj,
          loginInfo,
          cookieData[calendarRedirectCookieName],
        );
        if (tokenRedirectData?.status) {
          response = {
            status: true,
            msg: 'success',
            data: tokenRedirectData.redirectUrl,
            setCookie: tokenRedirectData.setCookie,
          };
          response.cookieValue.push(tokenRedirectData.cookieValue);
        }
        return response;
      }
      if (!Utility.isEmpty(cookieData[npsRedirectUrl])) {
        const npsRedirectData = await authHelper.getNpsRedirectUrl(
          cookieData,
          cookieData[npsRedirectUrl],
          userObj,
          cookieData[npsRedirectUrl],
        );
        if (npsRedirectData?.status) {
          response = {
            status: true,
            msg: 'success',
            data: npsRedirectData.redirectUrl,
            setCookie: npsRedirectData.setCookie,
          };
          response.cookieValue.push(npsRedirectData.cookieValue);
        }
        return response;
      }
      if (!Utility.isEmpty(cookieData[this.configService.get('communityCookie')])) {
        const usermgmtCommunityHelper = await this.helperService.getHelper('UsermgmtCommunityHelper');
        const communityData = await usermgmtCommunityHelper.getCommunityRedirectUrl(
          cookieData,
          VIEW_PAGES.VIEW_ROUTES.USERS.MULTIACCOUNT,
          userObj,
          loginInfo,
          cookieData[calendarRedirectCookieName],
        );
        response = { status: true, msg: 'success', data: communityData.url, setCookie: communityData.setCookie };
        response.cookieValue.push(communityData.cookieValue);
        return response;
      }
      const redirectUrl = await authHelper.generateRedirectLinkToManageRedirect(userObj, 'login', 'email');
      response = { ...response, status: true, data: redirectUrl, msg: 'success' };
      return response;
    } catch (error: any) {
      Logger.error('getUserSocialRedirectUrl', {
        METHOD: `${this.constructor.name}@${this.getUserSocialRedirectUrl.name}`,
        MESSAGE: error.message,
        REQUEST: linkData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return response;
    }
  }

  async userSocialStatus(
    email: string,
    type: string,
    userInfo: any,
  ): Promise<{
    status: boolean;
    code: number;
    msg: string;
  }> {
    if (!userInfo?.user_social_data?.length) {
      return { status: false, code: 3, msg: 'show link user button' };
    }
  
    let emailMatch = false;
    let typeMatch = false;
  
    for (const socialData of userInfo.user_social_data) {
      if (socialData?.email === email) emailMatch = true;
      if (socialData?.type === type) typeMatch = true;
  
      if (emailMatch && typeMatch) {
        return { status: true, code: 1, msg: 'do login operation' };
      }
    }
  
    return emailMatch
      ? { status: false, code: 3, msg: 'show link user button' }
      : { status: false, code: 2, msg: 'incorrect mapping show error' };
  }

  async linkUserSocialData(linkData: UseSocialLinkData, userData) {
    try {
      const userSocialStatus = this.checkSocialLoginStatus(linkData, userData?.user_social_data);
      let updateValue = false;
      linkData = { ...linkData, status: 1, created_on: Math.ceil(new Date().getTime() / 1000).toString() };
      if (!userSocialStatus?.status) {
        userData?.user_social_data.push(linkData);
        updateValue = true;
      } else if (!userSocialStatus?.isActive && userSocialStatus?.status) {
        userData.user_social_data[userSocialStatus?.index].status = 1;
        updateValue = true;
      }
      if (updateValue) {
        const userRepository: IUserRepository = await this.helperService.get<IUserRepository>(UserRepository);
        // TODO: synch data with cloud6

        await userRepository.findOneAndUpdate(
          { email: linkData?.email },
          { user_social_data: userData?.user_social_data },
        );
      }
      return true;
    } catch (error: any) {
      Logger.error('linkUserSocialData', {
        METHOD: this.constructor.name + '@' + this.linkUserSocialData.name,
        MESSAGE: error.message,
        REQUEST: {
          linkData: linkData,
          userData: userData,
        },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return false;
    }
  }

  async processUserLink(
    linkData,
    cookieData,
  ): Promise<CustomResponse & { setCookie: boolean; cookieValue?: ResponseCookieType[] }> {
    const response: CustomResponse & { setCookie: boolean; cookieValue?: ResponseCookieType[] } = {
      status: false,
      data: '',
      msg: 'Something went wrong, please try again.',
      setCookie: false,
    };
    try {
      const userRepository: IUserRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const userData: Partial<User> = await userRepository.getUserByEmail(linkData?.email);
      const isUpdated: boolean = await this.linkUserSocialData(linkData, userData);
      if (!isUpdated) {
        return {
          ...response,
          data: VIEW_PAGES.VIEW_ROUTES.USERS.LOGIN,
          msg: 'Something went wrong, please try again.',
        };
      }
      const redirectResponse: CustomResponse & { setCookie: boolean; cookieValue?: ResponseCookieType[] } =
        await this.getUserSocialRedirectUrl(cookieData, linkData, userData);
      const authTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
      const userTokenDetail = await authTokenHelper.generateSessionTokens(
        userData,
        this.configService.get('clientKey'),
      );
      redirectResponse.cookieValue = redirectResponse.cookieValue || [];
      redirectResponse.cookieValue.push({
        name: 'ssoCookie',
        value: userTokenDetail?.idToken,
        options: { expires: this.configService.get('maxAge') },
      });
      return { ...redirectResponse, setCookie: true };
    } catch (error: any) {
      Logger.error('processUserLink', {
        METHOD: this.constructor.name + '@' + this.processUserLink.name,
        MESSAGE: error.message,
        REQUEST: { linkData, cookieData },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return response;
    }
  }

  async processSocialAuthResponse(
    data: {
      email: string;
      token: string;
      type: string;
      authRespType: string;
      referer: string;
      requestSource: string;
    }, userData: Partial<User>,
  ) {
    try {
      const linkData = { type: data?.type, email: data?.email, source: data?.requestSource, sub: '', code: 0 };
      const linkDataCookieName = this.configService.get('linkData');
      const socialAuth = await this.configService.get('socialAuth');
      const UserHelper = await this.helperService.getHelper('UserHelper');

      const response: ProcessSocialAuthResponse = {
        status: true,
        setCookie: true,
        cookieName: linkDataCookieName,
        cookieValue: {},
        returnResponse: {
          type: 'success',
          message: '',
          userAccountStatus: false,
          socialAccountStatus: false,
          accountSetupStatus: false,
          referer: 'login',
          redirectUrl: VIEW_PAGES.VIEW_ROUTES.USERS.LOGIN,
        },
      };
      const failedResponse = {
        status: false,
        returnResponse: { type: 'error', message: 'Unrecognized request type!' },
      };
      let isPlatformAllowed = false;
      isPlatformAllowed = socialAuth?.allowedSocialLoginPlatforms?.includes(data?.type);
      if (!isPlatformAllowed ) {
        return failedResponse;
      }
      const userSocialData = {
        token: data?.token,
        type: data?.type,
        email: data?.email,
        source: data?.requestSource,
      };
      if(data?.authRespType){
        if(Utility.isEmpty(data?.token)){
          return failedResponse;
        }
        const userSocialLinkData: any = await this.createUserSocialData(userSocialData);
        if (!userSocialLinkData?.status && ['google', 'facebook'].includes(userSocialLinkData?.data?.type)) {
          return {
            ...failedResponse,
            returnResponse: { type: 'error', message: 'Unrecognized request type!' },
          };
        } else if (!userSocialLinkData?.status) {
            return { ...failedResponse, returnResponse: { type: 'error', msg: 'Invalid token!' } };
        }
        const userSocialLoginData = this.checkSocialLoginStatus(
          { type: data?.type, email: data?.email },
          userData?.user_social_data,
        );
        if (userSocialLoginData?.status && userSocialLoginData?.data?.email !== userSocialLinkData?.data?.email) {
          if (data?.requestSource === 'mobile') {
            return { ...failedResponse, returnResponse: { type: 'error', msg: 'Invalid token!' } };
          }
          response.returnResponse = {
            ...response.returnResponse, 
            type: 'success',
            message: 'Existing Account',
            userAccountStatus: true,
            accountSetupStatus: !Utility.isEmpty(userData?.user_social_data),
            redirectUrl: VIEW_PAGES.VIEW_ROUTES.USERS.SOCIAL_LINK,
          };
          UserHelper.updateUserLoginTime({email : data?.email});
          return { ...response, cookieValue: linkData };
        }
      }
     
      if (!Utility.isEmpty(userData) && userData != null) {
        return await this.validateSocialUserStatus(userData, { ...userSocialData, referer: data?.referer });
      }
    
      const redirectUrl =
        data?.referer === 'login'
          ? VIEW_PAGES.VIEW_ROUTES.USERS.SOCIAL_LINK
          : VIEW_PAGES.VIEW_ROUTES.USERS.REGISTER_COMPLETE;
      response.returnResponse = {
        ...response.returnResponse,
        message: 'No Existing Account',
        accountSetupStatus: !Utility.isEmpty(userData?.user_social_data),
        referer: 'register',
        redirectUrl: redirectUrl,
      };
      UserHelper.updateUserLoginTime({email : data?.email});
      return { ...response, cookieValue: linkData };
    } catch (error: any) {
      Logger.error(this.processSocialAuthResponse.name, {
        METHOD: this.constructor.name + '@' + this.processSocialAuthResponse.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return {
        type: 'error',
        message: 'Something went wrong, please try again.',
      };
    }
  }

  async createUserSocialData(params: {
    token: string;
    type: string;
    email: string;
    source: string;
  }): Promise<CustomResponse> {
    try {
      const isJwt = !['linkedin', 'facebook'].includes(params?.type?.toLowerCase());
      const authHelperInstance: AuthTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
      const data: { sub: string; email: string; type: string; source: string } = {
        sub: '',
        email: '',
        type: params?.type,
        source: params?.source,
      };
      if (isJwt) {
        const payload: any = await authHelperInstance.decodeJWTToken(params?.token);
        if (!Utility.isEmpty(payload)) {
          return { status: true, msg: 'success', data: { ...data, sub: payload?.sub, email: payload?.email } };
        }
      }
      const socialLoginService: SocialLoginService = await this.helperService.get<SocialLoginService>(
        SocialLoginService,
      );
      const socialUserInfo: CustomResponse = await socialLoginService.fetchSocialUserProfileDetailsFromToken({
        token: params?.token,
        type: params?.type,
      });
      if (socialUserInfo?.status) {
        return {
          status: true,
          msg: 'success',
          data: { ...data, sub: socialUserInfo?.data?.sub, email: socialUserInfo?.data?.email },
        };
      }
      return { status: false, msg: socialUserInfo.msg };
    } catch (error: any) {
      Logger.error(this.createUserSocialData.name, {
        METHOD: this.constructor.name + '@' + this.createUserSocialData.name,
        MESSAGE: error.message,
        REQUEST: params,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { status: false, msg: 'Something went wrong, please try again.' };
    }
  }

  async validateSocialUserStatus(
    userData: Partial<User>,
    data: { type: string; referer: string; email: string; source: string },
  ) {
    try {
      const linkData = { type: data?.type, email: data?.email, source: data?.source, sub: '', code: 0 };
      const response = {
        status: true,
        setCookie: true,
        cookieName: '',
        cookieValue: {},
        returnResponse: {
          type: 'success',
          message: 'Something went wrong, please try again.',
          userAccountStatus: true,
          socialAccountStatus: false,
          accountSetupStatus: false,
          referer: 'login',
          redirectUrl: VIEW_PAGES.VIEW_ROUTES.USERS.LOGIN,
        },
      };
      const userSocialLoginData = this.checkSocialLoginStatus(
        { type: data?.type, email: data?.email },
        userData?.user_social_data,
      );
      userSocialLoginData;

      const accountSetupFlag = userData?.user_social_data.length ? true : false;
      if (userSocialLoginData?.status && userSocialLoginData?.isActive) {
        response.returnResponse = {
          ...response.returnResponse,
          message: 'Existing Account',
          socialAccountStatus: true,
          accountSetupStatus: accountSetupFlag,
          redirectUrl: VIEW_PAGES.VIEW_ROUTES.USERS.SOCIAL_LINK,
        };
        return { ...response, cookieName: this.configService.get('linkData'), cookieValue: linkData };
      }
      const userMgmtUtilityHelper = await this.helperService.getHelper('UserMgmtUtilityHelper');
      userMgmtUtilityHelper.updateUserTimezone({ uid: userData?.uid, country: userData?.country_code });
      const tokenService = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
      const userTokenDetail = await tokenService.generateSessionTokens(userData, this.configService.get('clientKey'));
      if (!Utility.isEmpty(userTokenDetail?.idToken)) {
        const redirectUrl = `/user/validate-login?token=${userTokenDetail?.idToken}`;
        response.returnResponse = {
          ...response.returnResponse,
          message: 'Existing Account',
          accountSetupStatus: accountSetupFlag,
          redirectUrl: redirectUrl,
        };
        return { ...response, cookieName: this.configService.get('ssoCookie'), cookieValue: userTokenDetail?.idToken };
      } else {
        return { status: false };
      }
    } catch (error: any) {
      Logger.error(this.validateSocialUserStatus.name, {
        METHOD: this.constructor.name + '@' + this.validateSocialUserStatus.name,
        MESSAGE: error.message,
        REQUEST: userData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { status: false };
    }
  }

  async createUserAccount(params: Partial<SignupDto>, utmSource) {
    let response = { type: 'error', msg: 'Something went wrong, please try again.', data: {} };
    try {
      const userMgmtUtilityHelperInstance = await this.helperService.getHelper('UserMgmtUtilityHelper');
      const countryCode =
        !Utility.isEmpty(params?.country_code) && Utility.ctypeAlpha(params?.country_code)
          ? params?.country_code.trim().toUpperCase()
          : 'IN';
      const timeZone =
        params?.time_zone || (await userMgmtUtilityHelperInstance.getTimezoneFromCountryCode(countryCode));
      const userType: any = params?.user_type || this.configService.get('freeUserType');
      const userHelper = await this.helperService.getHelper('UserHelper');
      const signupDetail: Partial<User> = {
        first_name: params.first_name,
        last_name: params.last_name,
        email: params.user_email,
        password: params.user_pwd || userHelper.getUserPassword(),
        phone_no: params.phone_no || '********',
        timezone: timeZone,
        country_code: countryCode,
        accept_agreement: true,
        name: params.user_email,
        display_name: params.first_name + ' ' + params.last_name,
        language: 'en',
        status: 1,
        user_category: userType,
        account_setup: this.configService.get('userOptionsAccountSetupComplete'),
        password_created: this.configService.get('userOptionsAccountSetupComplete'),
      };

      const userService = await this.helperService.get<UserService>(UserService);
      const user = await userService.userRegistration(signupDetail);
      const authHelper = await this.helperService.getHelper('AuthHelper');
      authHelper.handleUserSignup(utmSource, params?.referral_code, user);

      if (user != null) {
        response = { type: 'success', msg: 'User Saved!', data: user };
      }
      return response;
    } catch (error: any) {
      Logger.error(this.createUserAccount.name, {
        METHOD: this.constructor.name + '@' + this.createUserAccount.name,
        MESSAGE: error.message,
        REQUEST: params,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return response;
    }
  }

  checkSocialLoginStatus(
    linkData: UseSocialLinkData,
    userSocialData,
  ): {
    status: boolean;
    isActive: boolean;
    index: any;
    data: any;
  } {
    try {
      const index = userSocialData?.findIndex((val) => val?.email === linkData?.email && val?.type === linkData?.type);
      if (index >= 0) {
        const existingSocialData: UseSocialLinkData = userSocialData[index];
        const isActive: boolean = existingSocialData?.status === 1 ? true : false;
        return { status: true, isActive: isActive, index: index, data: existingSocialData };
      } else {
        return { status: false, isActive: false, index: index, data: {} };
      }
    } catch (error: any) {
      Logger.error(this.checkSocialLoginStatus.name, {
        METHOD: this.constructor.name + '@' + this.checkSocialLoginStatus.name,
        MESSAGE: error.message,
        REQUEST: linkData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { status: false, isActive: false, index: -1, data: {} };
    }
  }

  async updateUserSocialAccountDetails(
    user: Partial<User>,
    method: string,
  ): Promise<{
    type: string;
    message: string;
    socialAccountDetails?: UserSocial[];
    redirectUrl?: string;
  }> {
    try {
      let isUpdateStatus = false;
      const socialData = user?.user_social_data.map((data) => {
        const resData = { ...data, updated_on: new Date() };
        if (method === data['type']) {
          if (data['status'] === 0) isUpdateStatus = true;
          resData['status'] = 0;
        }
        return resData;
      });
      if (isUpdateStatus || user?.user_social_data.length === 0) {
        return {
          type: 'error',
          message: 'User deletion failed!',
        };
      }
      user['user_social_data'] = socialData;
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      // TODO: synch data with cloud6
      const updatedResponse = await userRepository.findOneAndUpdate({ email: user?.email }, user);
      return {
        type: 'success',
        message: 'Details stored successfully',
        socialAccountDetails: updatedResponse?.user_social_data,
        redirectUrl: `${this.configService.get('lmsSiteUrl')}/user/login`,
      };
    } catch (error: any) {
      Logger.error(this.updateUserSocialAccountDetails.name, {
        METHOD: this.constructor.name + '@' + this.updateUserSocialAccountDetails.name,
        MESSAGE: error.message,
        REQUEST: { user, method },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async createUserSocialAccountDetails(user: Partial<User>) {
    try {
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      // TODO: synch data with cloud6
      const updatedResponse = await userRepository.findOneAndUpdate(
        { email: user?.email },
        { ...user, account_setup: this.configService.get('userOptionsAccountSetupComplete') },
      );
      return {
        type: 'success',
        message: 'Details stored successfully',
        socialAccountDetails: updatedResponse?.user_social_data,
        redirectUrl: `${this.configService.get('lmsSiteUrl')}/user/login`,
      };
    } catch (error: any) {
      Logger.error(this.createUserSocialAccountDetails.name, {
        METHOD: this.constructor.name + '@' + this.createUserSocialAccountDetails.name,
        MESSAGE: error.message,
        REQUEST: { user },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
  async registerFrsUser(userParams, cookieBody) {
    const oneTapCookie = cookieBody[this.configService.get('skillUpOneTapRedirectCookie')] || null;
    const tokenRedirectUrl = oneTapCookie
      ? `${this.configService.get('sheldonSiteUrl')}.${encodeURIComponent(oneTapCookie)}`
      : this.configService.get('lmsSiteUrl');

    const params = {
      type: userParams?.type,
      source: userParams?.source,
      first_name: userParams?.firstName,
      last_name: userParams?.lastName,
      email: userParams?.emailAddress,
      phone_no: userParams?.phone_no,
      country_code: userParams?.country_code,
      user_email: userParams?.emailAddress,
    };

    const userAccounts = await this.createUserAccount(params, params?.source);
    if (userAccounts?.type == 'error') {
      return { ...userAccounts, tokenRedirectUrl };
    }
    if (userAccounts?.type == 'success') {
      const userLinkData: UseSocialLinkData = { status: 1, type: params.type, source: 'web', email: params?.email };
      this.linkUserSocialData(userLinkData, userAccounts?.data);
      const user: Partial<User> = userAccounts?.data;
      const authTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
      const userTokenDetail = await authTokenHelper.generateSessionTokens(user, this.configService.get('clientKey'));
      const lrsData = {
        verb: 'register',
        objectType: 'accounts',
        objectId: user?.uid,
        dataVals: {
          client_id: this.configService.get('clientKey'),
          redirect_url: this.configService.get('lmsSiteURl'),
        },
      };
      const lrsInstance = await this.helperService.getHelper('lrsHelper');
      lrsInstance.sendDataToLrs(userTokenDetail['userData'], lrsData);
      if (userTokenDetail?.idToken) {
        return {
          userAccounts: userAccounts,
          cookieValue: userTokenDetail?.idToken,
          setCookie: true,
          user: user,
          cookieName: this.configService.get('ssoCookie'),
        };
      }
    }
    return { userAccounts };
  }
}
