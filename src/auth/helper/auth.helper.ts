import { BadRequestException, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import { Logger } from '../../logging/logger';
import { <PERSON>pt<PERSON><PERSON>elper } from '../../helper/helper.crypto';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';
import { AuthTokenHelper } from './auth.tokenhelper';
import { User } from '../../db/mongo/schema/user/user.schema';
import { LoginDto } from '../dtos/login.dto';
import { UserMgmtCommunityService } from '../../user/services/communication/usermgmt.community.service';
import { IUserRepository, UserRepository } from '../../user/repositories/user/user.repository';
import * as drupalHash from 'drupal-hash';
import { CachingService } from '../../caching/caching.service';
import { Utility } from '../../common/util/utility';
import { ResponseCookieType, NpsCookieType, UserSignupToken } from '../../common/typeDef/auth.type';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
export class AuthHelper {
  @Inject() private configService: ConfigService;
  @Inject(HelperService) private readonly helperService: HelperService;
  @Inject('CRYPTO_HELPER') private readonly cryptoHelper: CryptoHelper;
  async getTokenRedirectUrl(
    cookieData,
    assignmentToken: string,
    user: Partial<User>,
    calendarUrl: string,
  ): Promise<{ status: boolean; redirectUrl: string; setCookie: boolean; cookieValue?: ResponseCookieType }> {
    try {
      const refRewardData = { user_id: user?.uid };
      const paperclipService = await this.helperService.get<PaperclipService>(PaperclipService);
      const rewardData = await paperclipService.getSkillupReferralRewardInfo(refRewardData);
      let multiAccountUrl = '';
      const cookieValue: ResponseCookieType = { name: '', value: '' };
      if (
        !Utility.isEmpty(rewardData?.respData) &&
        rewardData.respData?.status === 'success' &&
        !Utility.isEmpty(rewardData.respData?.skillUpReferralInfo?.enrolmentRestricted)
      ) {
        const userRefCode = rewardData?.respData?.skillUpReferralInfo?.userRefCode;
        multiAccountUrl = this.configService.get('freemiumAssignmentBlockRedirectUrl') + userRefCode;
        const skillupCampaignIdentifier = cookieData[this.configService.get('skillupEnrollmentCampaign')] || 0;
        if (skillupCampaignIdentifier) {
          cookieValue.name = 'skillupEnrollmentCampaign';
          cookieValue.value = skillupCampaignIdentifier;
        }
      }
      cookieValue.name = 'freemiumAssignmentToken';
      cookieValue.value = assignmentToken;
      multiAccountUrl = this.configService.get('freemiumAssignmentRedirectUrl') + assignmentToken;
      const tokenRedirectUrl: string = await this.generateRedirectLinkToManageRedirect(
        user,
        'login',
        'email',
        multiAccountUrl,
        calendarUrl,
      );
      return { status: true, redirectUrl: tokenRedirectUrl, setCookie: true, cookieValue: cookieValue };
    } catch (error: any) {
      return { status: false, redirectUrl: '', setCookie: false };
    }
  }

  async getNpsRedirectUrl(cookieData, npsCookie, user: Partial<User>, redirectUrl: string): Promise<NpsCookieType> {
    const response: NpsCookieType = { status: false, redirectUrl: '', setCookie: false };
    try {
      const npsTokenCookie = cookieData[this.configService.get('npsRedirectCookie')] || '';
      const authTokenHelper: AuthTokenHelper = await this.helperService.getHelper<AuthTokenHelper>(AuthTokenHelper);
      const npsTokenDecode: any = await authTokenHelper.decodeJWTToken(npsTokenCookie);
      const multiAccountUrl = npsTokenDecode ? npsTokenDecode?.url : '';
      const tokenRedirectUrl: string = await this.generateRedirectLinkToManageRedirect(
        user,
        'login',
        'email',
        multiAccountUrl,
        redirectUrl,
      );
      response.cookieValue = { name: 'npsRedirectCookie', value: npsCookie };
      return { ...response, status: true, redirectUrl: tokenRedirectUrl, setCookie: true };
    } catch (error: any) {
      Logger.error('getNpsRedirectUrl', {
        METHOD: this.constructor?.name + '@' + this.getNpsRedirectUrl?.name,
        MESSAGE: error.message,
        REQUEST: {
          npsCookie,
          user,
          redirectUrl,
        },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return response;
    }
  }

  // Don't cleanup this function handle user signup

  async handleUserSignup(utm_source: string, referralCode: string, signupDetail) {
    try {
      const userCommunity = await this.helperService.getHelper('UsermgmtCommunityHelper');
      const userSignup = await userCommunity.saveUserSignup({ ...signupDetail, utm_source });

      const paperclipService = await this.helperService.get<PaperclipService>(PaperclipService);
      await paperclipService.saveSkillupReferral({
        email: signupDetail?.email,
        user_id: signupDetail?.uid,
        first_name: signupDetail?.first_name,
        refcode: referralCode,
      });

      await this.helperService.get<UserMgmtCommunityService>(UserMgmtCommunityService);

      const constructorName = this.constructor?.name + '@' + this.handleUserSignup?.name;
      if (!Utility.isEmpty(userSignup?.status) && userSignup?.status !== 'success') {
        this.logError(constructorName, signupDetail, 'Failed to save signup logs');
      }
      // userMgmtCommunityService.signUpNewUser(signupDetail);
      // if (!utilFunc.empty(communityUser?.status) && !utilFunc.empty(communityUser?.status)) {
      //   this.logFailedSignup(constructorName, req?.body, signupDetail);
      // }
    } catch (error: any) {
      this.logError(this.constructor.name, signupDetail, error.message);
      // throw new BadRequestException(error.message);
    }
  }

  logError(methodName, signupDetail, message: string) {
    Logger.log('handleUserSignup', {
      METHOD: methodName,
      MESSAGE: message,
      REQUEST: {
        signupDetail,
      },
      TIMESTAMP: new Date().getTime(),
    });
  }

  async getSignupRedirect(
    cookieData,
    signupDetail,
    redirectUrl: string,
  ): Promise<{ redirectUrl: string; setCookie: boolean; cookieValue: ResponseCookieType[] }> {
    try {
      const response: {
        redirectUrl: string;
        setCookie: boolean;
        cookieValue: ResponseCookieType[];
      } = {
        redirectUrl: !Utility.isEmpty(redirectUrl)
          ? Utility.validateRedirectUrl(redirectUrl)
          : this.configService.get('lmsSiteUrl'),
        setCookie: false,
        cookieValue: [],
      };
      const tokenPayload: UserSignupToken = {
        email: this.cryptoHelper.encrypt(signupDetail?.email),
        firstName: signupDetail?.first_name,
        lastName: signupDetail?.last_name,
        phoneNumber: signupDetail?.phone_no,
        authType: 'Signup',
        loginMethodType: 'email',
        redirectUrl: redirectUrl,
      };

      // Create a signed token
      const authTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
      const token: string = await authTokenHelper.createSignedToken(
        { data: tokenPayload },
        { secret: this.configService.get('jwtSecret') },
      );
      redirectUrl = this.configService.get('manageAuthRedirectUrl') + token;
      const assignmentToken: string = cookieData[this.configService.get('freemiumAssignmentToken')] || '';
      if (!Utility.isEmpty(assignmentToken)) {
        const paperclipService = await this.helperService.get<PaperclipService>(PaperclipService);
        const rewardData = await paperclipService.getSkillupReferralRewardInfo({ user_id: signupDetail?.uid });
        if (Utility.isEmpty(rewardData)) {
          response.cookieValue.push({ name: 'freemiumAssignmentToken', value: assignmentToken });
          return { ...response, redirectUrl: redirectUrl, setCookie: true };
        }
        const userHelper = await this.helperService.getHelper('UserHelper');
        redirectUrl = await userHelper.getFreemiumRedirectUrl(
          rewardData,
          signupDetail,
          assignmentToken,
          'Signup',
          redirectUrl,
        );
      }
      // Redirecting for community
      const communityCookie = cookieData[this.configService.get('communityCookie')] || '';
      if (Utility.isEmpty(communityCookie)) {
        return { ...response, redirectUrl: redirectUrl };
      }
      const usermgmtCommunityHelper = await this.helperService.getHelper('UsermgmtCommunityHelper');
      const communityRedirectData = await usermgmtCommunityHelper.getCommunityRedirectUrl(
        cookieData,
        redirectUrl,
        signupDetail,
      );
      response.cookieValue.push(communityRedirectData.cookieValue);
      response.cookieValue.push({ name: 'communityCookie', value: communityCookie });
      return { ...response, redirectUrl: communityRedirectData.url, setCookie: true };
    } catch (error: any) {
      Logger.error('getSignupRedirect', {
        METHOD: `${this.constructor.name}@${this.getSignupRedirect.name}`,
        MESSAGE: error.message,
        REQUEST: { signupDetail: signupDetail },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
  async authenticateUser(loginDto: LoginDto): Promise<Partial<User>> {
    try {
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user: Partial<User> = await userRepository.getUserByEmail(loginDto?.email);

      if (!user || user === null) {
        throw new BadRequestException('UserNotFoundException');
      }
      const userPassword = user.password ? user.password : '';
      const isPassValid = await drupalHash.checkPassword(loginDto?.password, userPassword);

      if (!isPassValid || user.status !== 1) {
        throw new BadRequestException('InvalidCredentials');
      }
      const userMgmtUtilityHelper = await this.helperService.getHelper('UserMgmtUtilityHelper');
      await userMgmtUtilityHelper.updateUserTimezone({ uid: user?.uid, country: user?.country_code });
      return user;
    } catch (error: any) {
      Logger.error('authenticateUser', {
        METHOD: `${this.constructor.name}@${this.authenticateUser?.name}`,
        MESSAGE: error.message,
        REQUEST: { loginDto: loginDto },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async getLoginRedirectUrl(cookieData, loginDto: LoginDto, user: Partial<User>) {
    try {
      const response: {
        url: string;
        setCookie: boolean;
        cookieValue?: ResponseCookieType;
      } = { url: '', setCookie: false };
      const calendarUrl: string = loginDto?.calendar_url.trim() || '';
      let multiAccountUrl = '/auth/multi-account';
      let redirectUrl: string | null = loginDto?.redirect_url.trim() || null; // Initialize as null
      const isB2BAndB2C: boolean = loginDto?.isB2BAndB2C || false;
      const domainGid: string = loginDto?.domainGid || '';
      const domainUrl: string = loginDto?.urlDomain || '';

      const userHelper = await this.helperService.getHelper('UserHelper');
      //TODO:should be part of auth method
      await userHelper.updateUserLoginTime(user);

      let defaultRequestUrl = '/auth/login';
      if (redirectUrl) {
        redirectUrl = Utility.validateRedirectUrl(redirectUrl); // Reassign if redirectUrl is not null
        defaultRequestUrl += '?' + new URLSearchParams({ redirect_url: redirectUrl }).toString();
      } else {
        redirectUrl = multiAccountUrl; // Assign the default value if redirectUrl is null
      }

      const assignmentToken = cookieData[this.configService.get('freemiumAssignmentToken')] || '';
      if (assignmentToken) {
        const tokenUrl = await this.getTokenRedirectUrl(cookieData, assignmentToken, user, calendarUrl);
        multiAccountUrl = tokenUrl?.redirectUrl;
      }

      const npsTokenCookie = cookieData[this.configService.get('npsRedirectCookie')] || '';
      if (npsTokenCookie) {
        const npsResponse = await this.getNpsRedirectUrl(cookieData, npsTokenCookie, user, redirectUrl);
        multiAccountUrl = npsResponse?.redirectUrl;
      }

      /* DISABLE TRIBE COMMUNITY START
      if (
        Utility.validateCommunityUrl({
          url: redirectUrl,
          communityBaseUrl: this.configService.get('communityBaseUrl'),
          tribeCommunityBaseUrl: this.configService.get('tribeCommunityBaseUrl'),
        })
      ) {
        const usermgmtCommunityHelper = await this.helperService.getHelper('UsermgmtCommunityHelper');
        const communityRedirectData: CommunityResponse = await usermgmtCommunityHelper.getCommunityRedirectUrl(
          cookieData,
          multiAccountUrl,
          user,
          loginDto,
          redirectUrl,
        );
        communityRedirectData.url = redirectUrl;
        return communityRedirectData;
      }
      DISABLE TRIBE COMMUNITY STOP */

      if (isB2BAndB2C) {
        multiAccountUrl = `${multiAccountUrl}?gid=${domainGid}&url=${domainUrl}`;
      }

      if (calendarUrl) {
        const userMgmtUtilityHelperInstance = await this.helperService.getHelper('UserMgmtUtilityHelper');
        multiAccountUrl = await userMgmtUtilityHelperInstance.liveClassRedirectUrl(defaultRequestUrl, 1, calendarUrl);
      }

      response.url = multiAccountUrl;
      return response;
    } catch (error: any) {
      Logger.error('getLoginRedirectUrl', {
        METHOD: `${this.constructor?.name}@${this.getLoginRedirectUrl?.name}`,
        MESSAGE: error.message,
        REQUEST: { loginInfo: loginDto },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async generateRedirectLinkToManageRedirect(
    user: Partial<User>,
    referer: string,
    type: string,
    redirectUrl = null,
    calendarUrl = null,
  ): Promise<any> {
    try {
      const authTypeUser = referer === 'login' ? 'Login' : 'Signup';
      const payload = {
        email: user?.email,
        firstName: user?.first_name,
        lastName: user?.last_name,
        phoneNumber: user?.phone_no,
        authType: authTypeUser,
        loginMethodType: type,
        redirectUrl: redirectUrl,
        calendar_url: calendarUrl,
      };

      //jwt token
      const authHelperInstance: AuthTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
      const token = await authHelperInstance.createSignedToken(
        { data: payload },
        {
          secret: this.configService.get<string>('jwtSecret'),
        },
      );
      const generatedRedirectUrl = this.configService.get('manageAuthRedirectUrl') + token;
      return generatedRedirectUrl;
    } catch (error: any) {
      Logger.error('generateRedirectLinkToManageRedirect', {
        METHOD: this.constructor.name + '@' + this.generateRedirectLinkToManageRedirect.name,
        MESSAGE: error.message,
        REQUEST: {},
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async forgotPasswordAttemptLimit(userEmail: string): Promise<{ status: string; msg: string }> {
    const fpResetCount = this.configService.get<number>('fpResetCount');
    const fpResetWaitTime = this.configService.get<number>('fpResetWaitTime');
    const fpEmailInCacheTime = this.configService.get<number>('fpEmailInCacheTime');

    const cacheService = await this.helperService.get<CachingService>(CachingService);
    const cacheKey = `${userEmail}_reset`;
    const email_cache_count: number = (await cacheService.get(cacheKey)) || 0;

    if (email_cache_count >= fpResetCount) {
      cacheService.set(`${userEmail}_reset_timeout`, Date.now(), 60 * fpResetWaitTime);
      return {
        status: 'limit_exceeded',
        msg: `Attempt limit exceeded. Please try again after  ${fpResetWaitTime} minutes.`,
      };
    }
    cacheService.set(cacheKey, email_cache_count + 1, 60 * fpEmailInCacheTime);
  }
  async getTokenByEmail(email: string, client_key: string , addData : object = {}): Promise<{ idToken: string; userData: object }> {
    try {
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user = await userRepository.getUserByEmail(email);
      if (!user || user === null) {
        throw new BadRequestException('No active account associated with this email address.');
      }
      if (!user || !user.status) {
        throw new BadRequestException('Try using a different email address to create an account.');
      }
      const authTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
      return await authTokenHelper.generateSessionTokens({...user,...addData}, client_key);
    } catch (error: any) {
      throw new BadRequestException(error.message);
    }
  }

  async getUserPassResetUrl(uid): Promise<{ url: string; token: string }> {
    const timestamp: number = Math.floor(Date.now() / 1000); // Get the current timestamp in seconds
    const tokenDetail = `${uid},${timestamp}`;
    const token = await this.cryptoHelper.encrypt(tokenDetail);
    return {
      url: `${this.configService.get('baseUrl')}/auth/reset/${await this.cryptoHelper.encrypt(uid.toString())}/${token}`,
      token: token,
    };
  }

  async getSubDomain(groupId) {
    try {
      const enterpriseService = await this.helperService.get<EnterpriseService>(EnterpriseService);
      const enterpriseResponse = await enterpriseService.getGroupDomainByGid({gid: groupId});
      const domain = enterpriseResponse?.data?.[0]?.lmsSiteUrl || '';
      
      return domain ? domain.split('.')[0] : '';
    } catch(error:any) {
      Logger.error('getSubDomain', {
        METHOD: `${this.constructor.name}@${this.getSubDomain?.name}`,
        MESSAGE: error.message,
        REQUEST: { groupId },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException(error.message);
    }
  }
}
