import { ExecutionContext } from '@nestjs/common';
import { Request } from 'express';
import { getCurrentUser } from './auth.current.user';

describe('GetUser Decorator (simulated behavior)', () => {
  // simulate the same logic as the original GetUser decorator's factory function
  const simulateGetUserFactory = (_data: unknown, context: ExecutionContext) => {
    const request = context.switchToHttp().getRequest();
    return request?.user;
  };

  it('should return user from ExecutionContext', () => {
    const mockUser = { id: 'user123', email: '<EMAIL>' };

    const context: ExecutionContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          user: mockUser,
        }),
      }),
    } as any;

    const result = simulateGetUserFactory(null, context);
    expect(result).toEqual(mockUser);
  });

  it('should return undefined if user is missing', () => {
    const context: ExecutionContext = {
      switchToHttp: () => ({
        getRequest: () => ({}),
      }),
    } as any;

    const result = simulateGetUserFactory(null, context);
    expect(result).toBeUndefined();
  });

  it('should return undefined if getRequest returns undefined', () => {
    const context: ExecutionContext = {
      switchToHttp: () => ({
        getRequest: () => undefined,
      }),
    } as any;

    const result = simulateGetUserFactory(null, context);
    expect(result).toBeUndefined();
  });
});

describe('getCurrentUser function', () => {
  it('should return the user from the express request object', () => {
    const req = {
      user: { id: 'abc', name: 'John Doe' },
    } as unknown as Request;

    const result = getCurrentUser(req);
    expect(result).toEqual({ id: 'abc', name: 'John Doe' });
  });

  it('should return undefined if user is not defined in request', () => {
    const req = {} as unknown as Request;
    const result = getCurrentUser(req);
    expect(result).toBeUndefined();
  });
});