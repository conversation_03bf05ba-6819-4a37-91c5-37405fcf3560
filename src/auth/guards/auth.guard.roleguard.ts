import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { getCurrentUser } from './auth.current.user';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user: any = getCurrentUser(request);
    if (!user) throw new UnauthorizedException('Invalid user');
    const roles: string[] = this.reflector.get<string[]>('roles', context.getHandler());
    if (!roles) return true;
    return this.checkRoles(user, roles);
  }
  checkRoles(user, requestedRoles: string[]) {
    //TODO: review this implementation
    const userRoles: [] = user.roles;
    if (!userRoles) return false;
    return userRoles.find((r) => requestedRoles.includes(r)) !== undefined;
  }
}
