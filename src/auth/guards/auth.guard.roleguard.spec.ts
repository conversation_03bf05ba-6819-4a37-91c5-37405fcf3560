import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RolesGuard } from './auth.guard.roleguard';
import * as authUserModule from './auth.current.user';

describe('RolesGuard', () => {
  let guard: RolesGuard;
  let reflector: Reflector;

  const mockContext = (user: any = null): ExecutionContext => {
    return {
      switchToHttp: () => ({
        getRequest: () => ({ user }),
      }),
      getHandler: () => 'testHandler',
    } as unknown as ExecutionContext;
  };

  beforeEach(() => {
    reflector = {
      get: jest.fn(),
    } as unknown as Reflector;

    guard = new RolesGuard(reflector);

    // Reset getCurrentUser mock
    jest.spyOn(authUserModule, 'getCurrentUser');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should throw UnauthorizedException if user is missing', () => {
    (authUserModule.getCurrentUser as jest.Mock).mockReturnValue(null);
    const ctx = mockContext(null);

    expect(() => guard.canActivate(ctx)).toThrow(UnauthorizedException);
  });

  it('should return true if no roles are defined in metadata', () => {
    (authUserModule.getCurrentUser as jest.Mock).mockReturnValue({ id: '123', roles: ['user'] });
    jest.spyOn(reflector, 'get').mockReturnValue(undefined);

    const ctx = mockContext({ id: '123', roles: ['user'] });

    expect(guard.canActivate(ctx)).toBe(true);
  });

  it('should return true if user has one of the required roles', () => {
    (authUserModule.getCurrentUser as jest.Mock).mockReturnValue({ id: '123', roles: ['admin', 'editor'] });
    jest.spyOn(reflector, 'get').mockReturnValue(['editor', 'viewer']);

    const ctx = mockContext({ id: '123', roles: ['admin', 'editor'] });

    expect(guard.canActivate(ctx)).toBe(true);
  });

  it('should return false if user has no matching roles', () => {
    (authUserModule.getCurrentUser as jest.Mock).mockReturnValue({ id: '123', roles: ['guest'] });
    jest.spyOn(reflector, 'get').mockReturnValue(['editor', 'admin']);

    const ctx = mockContext({ id: '123', roles: ['guest'] });

    expect(guard.canActivate(ctx)).toBe(false);
  });

  it('should return false if user.roles is undefined', () => {
    (authUserModule.getCurrentUser as jest.Mock).mockReturnValue({ id: '123' });
    jest.spyOn(reflector, 'get').mockReturnValue(['admin']);

    const ctx = mockContext({ id: '123' });

    expect(guard.canActivate(ctx)).toBe(false);
  });
});
