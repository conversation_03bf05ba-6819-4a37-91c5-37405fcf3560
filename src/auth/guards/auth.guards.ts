import { AuthGuard } from '@nestjs/passport';
import { ExecutionContext, Injectable, UseGuards, applyDecorators } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { SetMetadata } from '@nestjs/common';
import { APIAuthGuard } from './auth.guard.apikey.authentication';
import { RolesGuard } from './auth.guard.roleguard';

export const AUTH_BYPASS_META_KEY = 'bypass';
@Injectable()
export class JwtAuthenticationGuard extends AuthGuard('jwt') {
  constructor(private readonly reflector: Reflector) {
    super();
  }
  canActivate(context: ExecutionContext) {
    const shouldBypass = this.reflector.get<boolean>(AUTH_BYPASS_META_KEY, context.getHandler());
    if (shouldBypass == true) {
      return true;
    }
    return super.canActivate(context);
  }
}

@Injectable()
export class JwtRefreshTokenGuard extends AuthGuard('refresh-token') {}

/*
 * Checks the resource against AWS JWT ID token (optionally Legacy JWT ID token)
 */
export const UseAuthGuard = () => {
  return applyDecorators(UseGuards(JwtAuthenticationGuard));
};

/*
 * Checks the resource against AWS JWT Refresh token
 */
export const UseRefreshGuard = () => {
  return applyDecorators(UseGuards(JwtRefreshTokenGuard));
};

/*
 * Checks the resource against Simplilearn's bearer token
 */
export const UseAPIGuard = () => {
  return applyDecorators(UseGuards(APIAuthGuard));
};

/*
 * Allows to check for role.
 */
export const UserRoleGuard = () => {
  return applyDecorators(UseGuards(RolesGuard));
};

/*
 * Allows to bypass JWT token checks
 */
export const BypassAuth = () => {
  return applyDecorators(SetMetadata(AUTH_BYPASS_META_KEY, true));
};

/*
 * Allows to check for specific role.
 */
export const RequireRoles = (...roles: string[]) => {
  return applyDecorators(SetMetadata('roles', roles));
};
