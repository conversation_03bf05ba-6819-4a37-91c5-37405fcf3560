import { APIAuthGuard } from './auth.guard.apikey.authentication';
import { UnauthorizedException, BadRequestException } from '@nestjs/common';
import { ExecutionContext } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';

const createCryptoHelperMock = {
  createHmac: jest.fn(),
};

describe('APIAuthGuard', () => {
  let apiAuthGuard: APIAuthGuard;
  let mockConfigService: any;

  beforeEach(async () => {
    mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        APIAuthGuard,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: createCryptoHelperMock,
        },
      ],
    }).compile();

    apiAuthGuard = module.get<APIAuthGuard>(APIAuthGuard);
  });

  describe('canActivate', () => {
    it('should return true if apiAuthHeader is false', async () => {
      mockConfigService.get.mockReturnValue(false);

      const context: ExecutionContext = {
        switchToHttp: () => ({
          getRequest: () => ({}),
        }),
      } as any;

      const result = await apiAuthGuard.canActivate(context);

      expect(result).toBe(true);
    });

    it('should throw UnauthorizedException if validateRequest fails', async () => {
      mockConfigService.get.mockReturnValue(true);

      const context: ExecutionContext = {
        switchToHttp: () => ({
          getRequest: () => ({}),
        }),
        getHandler: jest.fn(() => ({ name: 'HandlerName' })),
        getClass: jest.fn(() => ({ name: 'ClassName' })),
      } as any;

      jest.spyOn(apiAuthGuard, 'validateRequest').mockImplementation(() => {
        throw new UnauthorizedException('Test error');
      });

      const result = await apiAuthGuard.canActivate(context);

      expect(result).toBe(false);
    });
  });

  describe('validateRequest', () => {
    it('should throw BadRequestException for missing timestamp', () => {
      const request: any = {
        get: jest.fn().mockReturnValueOnce(undefined),
      };

      expect(() => apiAuthGuard.validateRequest(request)).toThrow(BadRequestException);
    });

    it('should throw BadRequestException for missing Authorization header', () => {
      const request: any = {
        get: jest
          .fn()
          .mockReturnValueOnce('123456') // Mock timestamp
          .mockReturnValueOnce(undefined), // Mock missing Authorization
      };

      expect(() => apiAuthGuard.validateRequest(request)).toThrow(BadRequestException);
    });

    it('should throw UnauthorizedException for expired timestamp', () => {
      const request: any = {
        get: jest
          .fn()
          .mockReturnValueOnce((Math.round(new Date().getTime() / 1000) - 1000).toString()) // Expired timestamp
          .mockReturnValueOnce('ValidAuthorizationHeader'),
      };

      mockConfigService.get.mockReturnValue(60); // Set time limit to 60 seconds

      expect(() => apiAuthGuard.validateRequest(request)).toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for invalid authorization', () => {
      const request: any = {
        get: jest
          .fn()
          .mockReturnValueOnce(Math.round(new Date().getTime() / 1000).toString()) // Valid timestamp
          .mockReturnValueOnce('InvalidAuthorizationHeader'),
      };

      mockConfigService.get.mockReturnValue(60); // Set time limit to 60 seconds
      jest.spyOn(createCryptoHelperMock, 'createHmac').mockReturnValue('ExpectedAuthorizationHeader');

      expect(() => apiAuthGuard.validateRequest(request)).toThrow(UnauthorizedException);
    });

    it('should return true for valid request', () => {
      const timestamp = Math.round(new Date().getTime() / 1000);
      const authorizationHeader = 'ValidAuthorizationHeader';

      const request: any = {
        get: jest
          .fn()
          .mockReturnValueOnce(timestamp.toString()) // Valid timestamp
          .mockReturnValueOnce(authorizationHeader), // Valid Authorization header
      };

      mockConfigService.get.mockReturnValue(60); // Set time limit to 60 seconds
      jest.spyOn(createCryptoHelperMock, 'createHmac').mockReturnValue(authorizationHeader);

      const result = apiAuthGuard.validateRequest(request);

      expect(result).toBe(true);
    });
  });

  describe('verifyAuthorization', () => {
    it('should return true for matching authorization', () => {
      const timestamp = 123456;
      const authorizationHeader = 'ValidAuthorizationHeader';

      mockConfigService.get.mockReturnValue('SecretSalt');
      jest.spyOn(createCryptoHelperMock, 'createHmac').mockReturnValue(authorizationHeader);

      const result = apiAuthGuard['verifyAuthorization'](timestamp, authorizationHeader);

      expect(result).toBe(true);
    });

    it('should return false for non-matching authorization', () => {
      const timestamp = 123456;
      const authorizationHeader = 'InvalidAuthorizationHeader';

      mockConfigService.get.mockReturnValue('SecretSalt');
      jest.spyOn(createCryptoHelperMock, 'createHmac').mockReturnValue('ExpectedAuthorizationHeader');

      const result = apiAuthGuard['verifyAuthorization'](timestamp, authorizationHeader);

      expect(result).toBe(false);
    });
  });
});
