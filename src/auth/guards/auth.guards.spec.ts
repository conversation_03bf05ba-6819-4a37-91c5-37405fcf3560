import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext } from '@nestjs/common';
import {
  JwtAuthenticationGuard,
  UseAuthGuard,
  UseAPIGuard,
  BypassAuth,
  RequireRoles,
  UserRoleGuard,
  UseRefreshGuard,
} from './auth.guards';
import { Reflector } from '@nestjs/core';

describe('JwtAuthenticationGuard', () => {
  let guard: JwtAuthenticationGuard;
  let reflector: Reflector;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [JwtAuthenticationGuard, Reflector],
    }).compile();

    guard = module.get<JwtAuthenticationGuard>(JwtAuthenticationGuard);
    reflector = module.get<Reflector>(Reflector);
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  it('should return true when bypass is set', () => {
    const context: ExecutionContext = {
      getHandler: jest.fn().mockResolvedValue({ name: 'uday' }),
      getClass: jest.fn().mockResolvedValue({ name: 'uday' }),
      switchToHttp: () => ({
        getRequest: () => ({}),
      }),
    } as any;
    jest.spyOn(reflector, 'get').mockReturnValue(true);

    const result = guard.canActivate(context);
    expect(result).toBe(true);
  });

  // Add more test cases for JwtAuthenticationGuard as needed
});

describe('UseAPIGuard', () => {
  it('should create a decorator', () => {
    const decorator = UseAPIGuard();
    expect(decorator).toBeDefined();
  });

  // Add more test cases for decorators as needed
});

describe('BypassAuth', () => {
  it('should create a decorator', () => {
    const decorator = BypassAuth();
    expect(decorator).toBeDefined();
  });

  // Add more test cases for decorators as needed
});

describe('RequireRoles', () => {
  it('should create a decorator with roles', () => {
    const decorator = RequireRoles('admin', 'user');
    expect(decorator).toBeDefined();
  });

  describe('UserRoleGuard', () => {
    it('should create a decorator with roles', () => {
      const decorator = UserRoleGuard();
      expect(decorator).toBeDefined();
    });
  });
  describe('UseRefreshGuard', () => {
    it('should create a decorator with roles', () => {
      const decorator = UseRefreshGuard();
      expect(decorator).toBeDefined();
    });
  });

  describe('UseAuthGuard', () => {
    it('should return JwtAuthenticationGuard decorator when includeLegacyCheck is false', () => {
      const decorator = UseAuthGuard();
      expect(decorator).toBeDefined();
      // expect(decorator).toEqual(applyDecorators(UseGuards(JwtAuthenticationGuard)));
    });

    it('should return MultiJwtAuthenticationGuard decorator when includeLegacyCheck is true', () => {
      const decorator = UseAuthGuard();
      expect(decorator).toBeDefined();
      // expect(decorator).toEqual(applyDecorators(UseGuards(MultiJwtAuthenticationGuard)));
    });
  });
  // Add more test cases for decorators as needed
});
