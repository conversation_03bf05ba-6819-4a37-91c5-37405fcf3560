import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  BadRequestException,
  Inject,
} from '@nestjs/common';
import { Request } from 'express';
import { ConfigService } from '@nestjs/config';
import { Logger } from './../../logging/logger';
import { Crypto<PERSON>elper } from './../../helper/helper.crypto';

@Injectable()
export class APIAuthGuard implements CanActivate {
  constructor(
    @Inject(ConfigService) private readonly config: ConfigService,
    @Inject('CRYPTO_HELPER') private readonly crypto: CryptoHelper,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const apiAuthHeader = this.config.get<boolean>('apiAuthHeader');
    if (apiAuthHeader === false) {
      return true;
    }
    const request = context.switchToHttp().getRequest();
    try {
      return this.validateRequest(request);
    } catch (err: any) {
      const functionName = context.getHandler().name; // Get function name
      const className = context.getClass().name; // Get class name
      Logger.error(`${className} -> ${functionName}`, err);
    }
    return false;
  }

  validateRequest(request: Request): boolean {
    const timestamp = Number.parseInt(request.get('timestamp'));
    const authorization = request.get('Authorization');
    if (Number.isNaN(timestamp)) {
      throw new BadRequestException('Missing/Invalid authorization header');
    }
    if (authorization === undefined) {
      throw new BadRequestException('Missing authorization header');
    }

    const receivedTime = Math.round(new Date().getTime() / 1000);
    const timeDiff = receivedTime - timestamp;
    const reqTimeLimit = this.config.get<number>('apiAuthReqTimeLimit'); // Default to 60 seconds

    if (!(timeDiff >= 0 && timeDiff <= reqTimeLimit)) {
      throw new UnauthorizedException('API Auth timed out');
    }

    if (!this.verifyAuthorization(timestamp, authorization)) {
      throw new UnauthorizedException('API Authorization failed');
    }

    return true;
  }

  private verifyAuthorization(timestamp: number, authorizationHeader: string): boolean {
    const expectedAuthorization = this.crypto.createHmac(
      timestamp.toString(),
      this.config.get<string>('cloud6ApiAuthSecretSalt'),
      'SHA256',
      'base64',
    );

    return expectedAuthorization === authorizationHeader;
  }
}
