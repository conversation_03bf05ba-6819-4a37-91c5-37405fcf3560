import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtModuleOptions, JwtOptionsFactory } from '@nestjs/jwt';

@Injectable()
export class CustomJwtOptionsFactory implements JwtOptionsFactory {
  @Inject(ConfigService)
  private readonly configService: ConfigService;

  // Method to create JWT module options
  createJwtOptions(): Promise<JwtModuleOptions> | JwtModuleOptions {
    // const issuedAt = (new Date().getTime()) - this.configService.get<number>('ssoLeewayTime');
    return {
      global: true, // Enable JWT module globally
      secret: this.configService.get<string>('jwtSecret'), // JWT secret key
      signOptions: {
        algorithm: this.configService.get('hashAlgo'),
        issuer: this.configService.get('issuer'), // JWT issuer
        expiresIn: this.configService.get<number>('expiresIn'), // JWT expiration time
        notBefore: -3000, // Uncomment this line if you want to set a not-before time for JWTs
      },
    };
  }
}
