import { UnauthorizedException, Inject } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, ExtractJwt } from 'passport-jwt';
import { Request } from 'express';
import { AuthTokenHelper } from '../helper/auth.tokenhelper';
import { Logger } from '../../logging/logger';
import { CachingService } from '../../caching/caching.service';
import { CryptoHelper } from '../../helper/helper.crypto';

export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    @Inject(JwtService) private readonly jwtService: JwtService,
    @Inject(ConfigService) private readonly configService: ConfigService,
    @Inject(CachingService) private readonly cachingService: CachingService,
    @Inject('CRYPTO_HELPER') private readonly crypto: CryptoHelper,
    private readonly authHelper: AuthTokenHelper,
  ) {
    super({
      cache: true,
      rateLimit: configService.get<number>('jwksRequestsPerMinute') > 0,
      jwksRequestsPerMinute: configService.get<number>('jwksRequestsPerMinute'),
      jwksUri: `${configService.get<string>('authority')}/.well-known/jwks.json`,
      secretOrKey: configService.get('jwtSecret'),
      passReqToCallback: true,
      jwtFromRequest: ExtractJwt.fromExtractors([
        (req: Request) => {
          let token = null;
          if (req && req.cookies) {
            token = req.cookies[configService.get('ssoCookie')];
          }
          Logger.log(`JWT token: ${token}`);
          return token;
        },
      ]),
      ignoreExpiration: false,
    });

    // ✅ Explicitly reference the services to avoid TS6138 error
    console.log(this.jwtService ? 'JwtService initialized' : 'JwtService not found');
    console.log(this.configService ? 'ConfigService initialized' : 'ConfigService not found');
  }

  async validate(request: Request, payload: any) {
    Logger.info('JWT Validation Request', {
      ip: request.ip,
      headers: request.headers,
    });
    const decodedData = this.crypto.decrypt(payload?.data?.id);
    const checkSession = await this.cachingService.get(`sc_${decodedData}`);
    console.log('Check session:', checkSession);
    if (!payload?.data?.id || !payload?.data?.email 
      // || checkSession === null
    ) {
      throw new UnauthorizedException('Invalid JWT token');
    }
    const uid = this.authHelper.decryptAttribute(payload?.data?.id)
    return {
      uid: isNaN(uid) ? uid : parseInt(uid, 10),
      email: this.authHelper.decryptAttribute(payload?.data?.email),
      name: payload?.data?.name,
      first_name: payload?.data?.first_name,
      last_name: payload?.data?.last_name,
    };
  }
}
