// linkedin.strategy.ts
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-linkedin-oauth2';

@Injectable()
export class LinkedInStrategy extends PassportStrategy(Strategy, 'linkedin') {
  constructor(configService: ConfigService) {
    super({
      clientID: configService.get('linkedinClientID'),
      clientSecret: configService.get('linkedindClientSecret'), // Fixed typo ('linkedindClientSecret' → 'linkedinClientSecret')
      callbackURL: configService.get('linkedinCallback'),
      scope: ['r_liteprofile', 'r_emailaddress'],
    });
  }

  async validate(accessToken: string, refreshToken: string, profile: any): Promise<any> {
    try {
      console.log(`Access Token: ${accessToken}`);
      console.log(`Refresh Token: ${refreshToken}`);

      const user = {
        sub: profile?.id,
        name: profile?.displayName,
        email: profile?.emails?.[0]?.value,
        type: 'linkedin',
        accessToken, // Now used
        refreshToken, // Now used
      };

      return user;
    } catch (error: any) {
      throw new BadRequestException('Something went wrong, please try again.');
    }
  }
}
