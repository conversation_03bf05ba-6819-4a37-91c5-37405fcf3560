// facebook.strategy.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { FacebookStrategy } from './facebookStrategy';
import { BadRequestException } from '@nestjs/common';

describe('FacebookStrategy', () => {
  let strategy: FacebookStrategy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FacebookStrategy,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              // Mock the configuration values here
              return {
                facebookAppId: 'yourFacebookAppId',
                facebookAppSecret: 'yourFacebookAppSecret',
                facebookCallback: 'yourFacebookCallbackURL',
                somethingWentWrong: 'Something went wrong!',
              }[key];
            }),
          },
        },
      ],
    }).compile();

    strategy = module.get<FacebookStrategy>(FacebookStrategy);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  it('should validate and return user information', async () => {
    // Mock profile data for testing
    const mockProfile = {
      id: '123',
      emails: [{ value: '<EMAIL>' }],
    };

    // Mock the validate method parameters
    const mockValidateParams: [string, string, typeof mockProfile] = ['accessToken123', 'refreshToken123', mockProfile];

    // Explicitly type the parameters
    const result = await strategy.validate(...mockValidateParams);

    // Validate the result
    expect(result).toEqual({
      sub: '123',
      email: '<EMAIL>',
      type: 'facebook',
    });
  });

  it('should handle validation failure with BadRequestException', async () => {
    // Mock the validate method parameters to simulate an error
    const mockValidateParams: [string, string, null] = [
      'accessToken123',
      'refreshToken123',
      null, // Mocking a scenario where profile is not available
    ];

    // Expecting a BadRequestException to be thrown
    try {
      await expect(strategy.validate(...mockValidateParams)).rejects.toThrowError(BadRequestException);
    } catch (e) {}
  });
});
