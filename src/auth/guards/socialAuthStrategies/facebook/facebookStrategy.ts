import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-facebook';

@Injectable()
export class FacebookStrategy extends PassportStrategy(Strategy, 'facebook') {
  constructor(configService: ConfigService) {
    super({
      clientID: configService.get<string>('facebookAppId'),
      clientSecret: configService.get<string>('facebookAppSecret'),
      callbackURL: configService.get<string>('facebookCallback'),
      scope: ['email'],
      profileFields: ['id', 'displayName', 'email'], // Ensure 'id' and 'displayName' are included
    });
  }

  async validate(
    _accessToken: string, // ✅ Prefix with `_` to indicate it's unused
    _refreshToken: string, // ✅ Prefix with `_` to indicate it's unused
    profile: any,
  ): Promise<any> {
    try {
      const user = {
        sub: profile?.id,
        name: profile?.displayName || '', // Ensure name is available
        email: profile?.emails?.[0]?.value || '', // Safe optional chaining
        type: 'facebook',
      };

      return user;
    } catch (error) {
      throw new BadRequestException('Something went wrong, please try again.');
    }
  }
}
