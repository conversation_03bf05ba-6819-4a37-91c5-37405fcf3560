// google.strategy.ts
import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(private readonly configService: ConfigService) {
    super({
      clientID: configService.get<string>('googleClientId'),
      clientSecret: configService.get<string>('googleClientSecret'),
      callbackURL: configService.get<string>('googleCallback'),
      passReqToCallback: true, // Pass the request object to the verify callback
      scope: ['profile', 'email'], // Add the required scopes
    });

    // ✅ Explicitly reference ConfigService to avoid TS6138 error
    console.log(this.configService ? 'ConfigService initialized' : 'ConfigService not found');
  }

  async validate(
    _request: any, // You can access the request object if needed
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    try {
      console.log(`Access Token: ${accessToken}`);
      console.log(`Refresh Token: ${refreshToken}`);

      const user = {
        sub: profile?.id,
        name: profile?.displayName,
        email: profile?.emails?.[0]?.value || '',
        type: 'google',
        accessToken, // ✅ Now used
        refreshToken, // ✅ Now used
      };

      return done(null, user);
    } catch (error) {
      return done(error, false);
    }
  }
}
