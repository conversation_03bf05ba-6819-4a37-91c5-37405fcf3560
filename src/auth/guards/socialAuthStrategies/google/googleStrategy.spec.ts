import { GoogleStrategy } from './googleStrategy';
import { ConfigService } from '@nestjs/config';
import { Strategy } from 'passport-google-oauth20';

// Mock the Strategy from the passport-google-oauth20 library
jest.mock('passport-google-oauth20', () => ({
  Strategy: jest.fn(),
}));

const MockedStrategy = Strategy as jest.Mock;

describe('GoogleStrategy', () => {
  let configService: ConfigService;

  beforeEach(() => {
    MockedStrategy.mockClear(); // Reset the mock before each test

    configService = {
      get: jest.fn((key: string) => {
        switch (key) {
          case 'googleClientId':
            return 'test-client-id';
          case 'googleClientSecret':
            return 'test-client-secret';
          case 'googleCallback':
            return 'http://localhost/auth/google/callback';
          default:
            return '';
        }
      }),
    } as any;
  });

  // Test for constructor logic
  describe('constructor', () => {
    it('should call super with correct config when configService is provided', () => {
      new GoogleStrategy(configService);
      expect(MockedStrategy).toHaveBeenCalledWith(
        {
          clientID: 'test-client-id',
          clientSecret: 'test-client-secret',
          callbackURL: 'http://localhost/auth/google/callback',
          passReqToCallback: true,
          scope: ['profile', 'email'],
        },
        expect.any(Function) // The 'validate' function is passed to super
      );
    });

    it('should log "ConfigService initialized" when configService is provided', () => {
      const logSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
      new GoogleStrategy(configService);
      expect(logSpy).toHaveBeenCalledWith('ConfigService initialized');
      logSpy.mockRestore();
    });

    it('should log "ConfigService not found" when configService is null', () => {
      const logSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
      // Because we mocked the Strategy, super() won't crash
      new GoogleStrategy(null);
      expect(logSpy).toHaveBeenCalledWith('ConfigService not found');
      logSpy.mockRestore();
    });
  });

  // Test for the validate method
  describe('validate', () => {
    let strategy: GoogleStrategy;

    beforeEach(() => {
      // We need an instance to call .validate on.
      // The constructor will call the mocked super, which is fine.
      strategy = new GoogleStrategy(configService);
    });

    it('should return user object with all details', async () => {
      const done = jest.fn();
      const profile = {
        id: '123',
        displayName: 'Test User',
        emails: [{ value: '<EMAIL>' }],
      };
      await strategy.validate({}, 'access-token', 'refresh-token', profile, done);
      expect(done).toHaveBeenCalledWith(null, {
        sub: '123',
        name: 'Test User',
        email: '<EMAIL>',
        type: 'google',
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
      });
    });

    it('should handle profile with no emails array', async () => {
        const done = jest.fn();
        const profile = {
          id: '123',
          displayName: 'Test User',
          // no emails property
        };
        await strategy.validate({}, 'access-token', 'refresh-token', profile, done);
        expect(done).toHaveBeenCalledWith(null, expect.objectContaining({ email: '' }));
    });

    it('should handle profile with empty emails array', async () => {
        const done = jest.fn();
        const profile = {
          id: '123',
          displayName: 'Test User',
          emails: [],
        };
        await strategy.validate({}, 'access-token', 'refresh-token', profile, done);
        expect(done).toHaveBeenCalledWith(null, expect.objectContaining({ email: '' }));
    });

    it('should handle null profile', async () => {
        const done = jest.fn();
        await strategy.validate({}, 'access-token', 'refresh-token', null, done);
        expect(done).toHaveBeenCalledWith(null, expect.objectContaining({
            sub: undefined,
            name: undefined,
            email: '',
        }));
    });

    it('should call done with an error if an exception occurs', async () => {
        const done = jest.fn();
        const error = new Error('Something went wrong');
        const profile = {};
        // Make profile access throw an error
        Object.defineProperty(profile, 'id', { get: () => { throw error; } });

        await strategy.validate({}, 'access-token', 'refresh-token', profile, done);
        expect(done).toHaveBeenCalledWith(error, false);
    });
  });
});