// apple.strategy.ts
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, Profile } from '@arendajaelu/nestjs-passport-apple';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppleStrategy extends PassportStrategy(Strategy, 'apple') {
  constructor(configService: ConfigService) {
    super({
      clientID: configService.get('appleClientId'),
      teamID: configService.get('appleTeamId'),
      callbackURL: configService.get('appleCallback'),
      keyID: configService.get('appleKeyId'),
      passReqToCallback: true,
      // response_type: 'code id_token',
      scope: ['name', 'email'],
      keyFilePath: 'appleKey.p8',
    });
  }

  async validate(
    _request: any,
    _accessToken: string,
    _refreshToken: string,
    idToken: any,
    profile: Profile,
  ): Promise<any> {
    try {
      const name = profile?.name?.firstName + profile?.name?.lastName || profile?.email;
      const user = {
        sub: profile?.id || idToken?.id,
        name: name || profile?.email,
        email: profile?.email || idToken?.email,
        type: profile?.provider || idToken?.provider,
      };
      return user;
    } catch (error) {
      new UnauthorizedException('Apple authentication failed');
    }
  }
}
