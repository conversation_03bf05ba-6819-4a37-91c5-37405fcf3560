import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { AppleStrategy } from './appleStrategy';
import { UnauthorizedException } from '@nestjs/common';

describe('AppleStrategy', () => {
  let strategy: AppleStrategy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AppleStrategy,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(
              (key: string) =>
                ({
                  appleClientId: 'yourClientId',
                  appleTeamId: 'yourTeamId',
                  appleCallback: 'yourCallbackURL',
                  appleKeyId: 'yourKeyId',
                })[key],
            ),
          },
        },
      ],
    }).compile();

    strategy = module.get<AppleStrategy>(AppleStrategy);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  it('should validate and return user information', async () => {
    // Mock profile data for testing
    const mockProfile = {
      id: '123',
      name: {
        firstName: '<PERSON>',
        lastName: 'Doe',
      },
      email: '<EMAIL>',
      provider: 'apple',
      emailVerified: true,
    };

    // Mock the validate method parameters
    const mockValidateParams: Parameters<AppleStrategy['validate']> = [
      null, // request
      'accessToken123',
      'refreshToken123',
      { id: 'idToken123' }, // idToken can be a string or an object with an id property
      mockProfile,
    ];

    // Explicitly type the result
    const result: any = await strategy.validate(...mockValidateParams);

    // Validate the result
    expect(result).toEqual({
      sub: '123',
      name: 'JohnDoe', // Concatenation of firstName and lastName
      email: '<EMAIL>',
      type: 'apple',
    });
  });

  it('should handle validation failure with UnauthorizedException', async () => {
    // Mock the validate method parameters
    const mockValidateParams: Parameters<AppleStrategy['validate']> = [
      null, // request
      'accessToken123',
      'refreshToken123',
      { id: 'idToken123' }, // idToken can be a string or an object with an id property
      null, // Mocking a scenario where profile is not available
    ];

    // Expecting an UnauthorizedException to be thrown
    try {
      await expect(strategy.validate(...mockValidateParams)).rejects.toThrowError(UnauthorizedException);
    } catch (e) {}
  });
});
