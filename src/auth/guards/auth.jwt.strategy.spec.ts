import { UnauthorizedException } from '@nestjs/common';
import { JwtStrategy } from './auth.jwt.strategy';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { CachingService } from '../../caching/caching.service';
import { AuthTokenHelper } from '../helper/auth.tokenhelper';
import { CryptoHelper } from '../../helper/helper.crypto';
import { Request } from 'express';
import { Logger } from '../../logging/logger';
import { ExtractJwt } from 'passport-jwt';

jest.mock('../../logging/logger', () => ({
  Logger: {
    log: jest.fn(),
    info: jest.fn(),
  },
}));

describe('JwtStrategy', () => {
  let strategy: JwtStrategy;
  let jwtService: JwtService;
  let configService: ConfigService;
  let cachingService: CachingService;
  let cryptoHelper: CryptoHelper;
  let authHelper: AuthTokenHelper;

  beforeEach(() => {
    jwtService = {} as JwtService;

    configService = {
      get: jest.fn((key: string) => {
        const values = {
          jwksRequestsPerMinute: 5,
          authority: 'https://auth.example.com',
          jwtSecret: 'test-secret',
          ssoCookie: 'sso_token',
        };
        return values[key];
      }),
    } as unknown as ConfigService;

    cachingService = {
      get: jest.fn(),
    } as unknown as CachingService;

    cryptoHelper = {
      decrypt: jest.fn(),
    } as unknown as CryptoHelper;

    authHelper = {
      decryptAttribute: jest.fn(),
    } as unknown as AuthTokenHelper;

    strategy = new JwtStrategy(jwtService, configService, cachingService, cryptoHelper, authHelper);
  });

  describe('jwtFromRequest extractor', () => {
    it('should extract token from request cookies and log it', () => {
      const req = {
        cookies: {
          sso_token: 'jwt_token_here',
        },
      } as Request;

      const mockConfigService = {
        get: jest.fn((key: string) => {
          if (key === 'ssoCookie') return 'sso_token';
          return null;
        }),
      } as unknown as ConfigService;

      const extractor = ExtractJwt.fromExtractors([
        (req: Request) => {
          let token = null;
          if (req && req.cookies) {
            token = req.cookies[mockConfigService.get('ssoCookie')];
          }
          Logger.log(`JWT token: ${token}`);
          return token;
        },
      ]);

      const token = extractor(req);
      expect(token).toBe('jwt_token_here');
      expect(Logger.log).toHaveBeenCalledWith('JWT token: jwt_token_here');
    });

    it('should return null if request or cookies are missing', () => {
      const req = {} as Request;

      const mockConfigService = {
        get: jest.fn((key: string) => {
          if (key === 'ssoCookie') return 'sso_token';
          return null;
        }),
      } as unknown as ConfigService;

      const extractor = ExtractJwt.fromExtractors([
        (req: Request) => {
          let token = null;
          if (req && req.cookies) {
            token = req.cookies[mockConfigService.get('ssoCookie')];
          }
          Logger.log(`JWT token: ${token}`);
          return token;
        },
      ]);

      const token = extractor(req);
      expect(token).toBeNull();
      expect(Logger.log).toHaveBeenCalledWith('JWT token: null');
    });
  });

  describe('validate', () => {
    const mockRequest = {
      ip: '127.0.0.1',
      headers: { 'user-agent': 'jest' },
    } as Request;

    const payload = {
      data: {
        id: 'encrypted_id',
        email: 'encrypted_email',
        name: 'Test Name',
        first_name: 'Test',
        last_name: 'User',
      },
    };

    it('should return user object after successful validation', async () => {
      (cryptoHelper.decrypt as jest.Mock).mockReturnValue('decrypted_id');
      (cachingService.get as jest.Mock).mockResolvedValue('active');
      (authHelper.decryptAttribute as jest.Mock).mockImplementation((val) =>
        val === 'encrypted_id' ? '123' : '<EMAIL>',
      );

      const result = await strategy.validate(mockRequest, payload);

      expect(Logger.info).toHaveBeenCalledWith('JWT Validation Request', {
        ip: '127.0.0.1',
        headers: { 'user-agent': 'jest' },
      });

      expect(result).toEqual({
        uid: 123,
        email: '<EMAIL>',
        name: 'Test Name',
        first_name: 'Test',
        last_name: 'User',
      });
    });

    it('should throw UnauthorizedException if id or email is missing in payload', async () => {
      const badPayload = {
        data: {
          id: null,
          email: null,
        },
      };

      await expect(strategy.validate(mockRequest, badPayload)).rejects.toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException if session is missing', async () => {
      (cryptoHelper.decrypt as jest.Mock).mockReturnValue('decrypted_id');
      (cachingService.get as jest.Mock).mockResolvedValue(null);

      await expect(strategy.validate(mockRequest, payload)).rejects.toThrow(UnauthorizedException);
    });

    it('should parse numeric uid and return as number', async () => {
      const mockRequest = {
        ip: '127.0.0.1',
        headers: { 'user-agent': 'jest' },
      } as Request;

      const payload = {
        data: {
          id: 'encrypted_id',
          email: 'encrypted_email',
          name: 'Jane Doe',
          first_name: 'Jane',
          last_name: 'Doe',
        },
      };

      jest.spyOn(cryptoHelper, 'decrypt').mockReturnValue('987');
      jest.spyOn(cachingService, 'get').mockResolvedValue('active');
      jest.spyOn(authHelper, 'decryptAttribute').mockImplementation((value) => {
        if (value === 'encrypted_id') return '987';
        if (value === 'encrypted_email') return '<EMAIL>';
        return value;
      });

      const result = await strategy.validate(mockRequest, payload);

      expect(result.uid).toBe(987);
      expect(typeof result.uid).toBe('number');
      expect(result.email).toBe('<EMAIL>');
    });

    it('should return uid as string if not numeric', async () => {
      const payload = {
        data: {
          id: 'encrypted_id',
          email: 'encrypted_email',
          name: 'John Doe',
          first_name: 'John',
          last_name: 'Doe',
        },
      };

      const mockRequest = {
        ip: '127.0.0.1',
        headers: {},
      } as any;

      jest.spyOn(cryptoHelper, 'decrypt').mockReturnValue('abc_uid');
      jest.spyOn(cachingService, 'get').mockResolvedValue('active');

      jest.spyOn(authHelper, 'decryptAttribute').mockImplementation((value) => {
        if (value === 'encrypted_id') return 'abc_uid';
        if (value === 'encrypted_email') return '<EMAIL>';
        return value;
      });

      const result = await strategy.validate(mockRequest, payload);

      expect(result.uid).toBe('abc_uid');
      expect(result.email).toBe('<EMAIL>');
      expect(result.name).toBe('John Doe');
      expect(result.first_name).toBe('John');
      expect(result.last_name).toBe('Doe');
    });

  });
});