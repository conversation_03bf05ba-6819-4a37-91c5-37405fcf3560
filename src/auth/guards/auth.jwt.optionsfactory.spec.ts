import { Test, TestingModule } from '@nestjs/testing';
import { CustomJwtOptionsFactory } from './auth.jwt.optionsfactory';
import { ConfigService } from '@nestjs/config';

describe('CustomJwtOptionsFactory', () => {
  let customJwtOptionsFactory: CustomJwtOptionsFactory;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomJwtOptionsFactory,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
            // Add a mockReturnValueOnce method with any type
            mockReturnValueOnce: jest.fn(),
          },
        },
      ],
    }).compile();

    customJwtOptionsFactory = module.get<CustomJwtOptionsFactory>(CustomJwtOptionsFactory);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(customJwtOptionsFactory).toBeDefined();
  });

  it('should create JWT module options', async () => {
    // Arrange
    (configService.get as jest.Mock).mockReturnValueOnce(true); // Mock configuration values
    (configService.get as jest.Mock).mockReturnValueOnce('your_hash_algorithm');
    (configService.get as jest.Mock).mockReturnValueOnce('your_issuer');
    (configService.get as jest.Mock).mockReturnValueOnce(3600); // JWT expiration time

    // Act
    const jwtOptions = await customJwtOptionsFactory.createJwtOptions();

    // Assert
    expect(jwtOptions.global).toBe(true);
    expect(jwtOptions.secret).toBe(true);
    expect(jwtOptions.signOptions.algorithm).toBe('your_hash_algorithm');
    expect(jwtOptions.signOptions.issuer).toBe('your_issuer');
    expect(jwtOptions.signOptions.expiresIn).toBe(3600);
    expect(jwtOptions.signOptions.notBefore).toBe(-3000);
  });
});
