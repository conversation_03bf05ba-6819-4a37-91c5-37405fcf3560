import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  Param,
  Post,
  Render,
  Req,
  Res,
  UseFilters,
  UsePipes,
  ValidationPipe,
  Query,
  BadRequestException,
  UseGuards,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { AuthService } from '../services/auth/auth.service';
import { ConfigService } from '@nestjs/config';
import { VIEW_PAGES } from '../config/view.constants';
import { SignUpDto } from '../dtos/signup.dto';
import { HelperService } from '../../helper/helper.service';
import { UseAuthGuard } from '../guards/auth.guards';
import { ForgotPasswordDto } from '../dtos/forgot-password.dto';
import { CryptoHelper } from '../../helper/helper.crypto';
import { Logger } from '../../logging/logger';
import { CachingService } from '../../caching/caching.service';
import { RegisterCompleteDto } from '../dtos/regester-complete.dto';
import { UnauthorizedExceptionFilter, ViewValidationFilter } from '../../common/filters/view-validation.filter';
import { LoginDto } from '../dtos/login.dto';
import { CurrentUser, RouteType } from '../../common/typeDef/auth.type';
import { AuthGuard } from '@nestjs/passport';
import { ApiExcludeController } from '@nestjs/swagger';
import { Utility } from '../../common/util/utility';
import { IUserRepository, UserRepository } from '../../user/repositories/user/user.repository';
import { AuthTokenHelper } from '../helper/auth.tokenhelper';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
@UseFilters(UnauthorizedExceptionFilter)
@Controller('auth')
@ApiExcludeController(true)
export class AuthController {
  @Inject('CRYPTO_HELPER') private readonly cryptoHelper: CryptoHelper;
  @Inject() private authService: AuthService;
  @Inject() private configService: ConfigService;
  @Inject() private helperService: HelperService;

  @Get('/login')
  @Render(VIEW_PAGES.MAIN)
  async loginRender(@Query() queryParam, @Req() req, @Res({ passthrough: true }) res: Response) {
    try {
      const ssoCookie = req?.cookies[this.configService.get('ssoCookie')] || '';
      if (!Utility.isEmpty(ssoCookie)) {
        const redirectUrl = await this.authService.handleUserSsoCookie(queryParam, ssoCookie);
        return res.redirect(redirectUrl);
      }
      const route = Utility.getRoute(req?.route);
      const { redirectUrl, calendarUrl, isB2BAndB2C, domainGid, domainUrl } = await this.authService.getRedirectUrl(
        res,
        queryParam,
      );
      const cookieHelper = await this.helperService.getHelper('CookieHelper');
      await cookieHelper.setCookie(res, this.configService.get('page'), route.page);
      return {
        token: '',
        validationErrors: [],
        route: route,
        redirect_url: redirectUrl,
        calendar_url: calendarUrl,
        domainGid: domainGid,
        isB2BAndB2C: isB2BAndB2C,
        domainUrl: domainUrl,
      };
    } catch (error: any) {
      Logger.error('Get loginRender', {
        METHOD: this.constructor.name + '@' + this.loginRender.name,
        MESSAGE: error.message,
        REQUEST: queryParam,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException('Some error occurred while user login.');
    }
  }

  /**
   * Handles the login request.
   * @param loginInfo - Login information
   * @param res - Express response object
   * @returns Success message if login is successful
   */
  @Post('/login')
  @UsePipes(new ValidationPipe())
  @UseFilters(new ViewValidationFilter(VIEW_PAGES.MAIN))
  async login(@Body() loginInfo: LoginDto, @Req() req, @Res({ passthrough: true }) res) {
    try {
      const userResponse = await this.authService.authenticateUser(
        res,
        loginInfo,
        this.configService.get<string>('clientKey'),
      );
      if (userResponse instanceof Error) {
        if (userResponse?.message === 'UserNotFoundException' || userResponse?.message === 'InvalidCredentials') {
          throw new BadRequestException('The email or password you have entered is invalid.');
        } else {
          throw new BadRequestException('Some error occurred while user login.');
        }
      }
      const redirectUrl = await this.authService.getLoginRedirectUrl(req.cookies, res, loginInfo, userResponse);
      res.redirect(redirectUrl);
    } catch (error: any) {
      Logger.error('login', {
        METHOD: this.constructor.name + '@' + this.login.name,
        MESSAGE: error.message,
        REQUEST: LoginDto,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Renders the register view.
   * @param req
   * @returns
   */
  @Get('/register')
  @Render(VIEW_PAGES.MAIN)
  async register(@Query() queryParam, @Req() req, @Res() res) {
    const route: {
      action: string;
      page: string;
    } = Utility.getRoute(req?.route);

    const assignmentToken: string = queryParam?.assignmentToken || '';
    const redirectUrl: string = queryParam?.redirect_url || '';

    const cookieHelper = await this.helperService.getHelper('CookieHelper');
    await cookieHelper.setCookie(res, this.configService.get('page'), route.page);
    await cookieHelper.setFermiumCookie(assignmentToken, res);
    await cookieHelper.setCommunityCookie(redirectUrl, res);
    return { token: '', validationErrors: [], route: route };
  }

  /**
   * Renders the register email view.
   * @returns Rendered register email view
   */
  @Get('/register-email')
  @Render(VIEW_PAGES.MAIN)
  async registerEmail(@Req() req, @Res() res) {
    // check whether user is already login, if yes then signout first and redirect
    if (req?.cookies[this.configService.get('ssoCookie')]) {
      const cookieHelper = await this.helperService.getHelper('CookieHelper');
      cookieHelper.clearCookie(res, this.configService.get('ssoCookie'));
      res.redirect('/auth/register');
      return;
    }
    // Get terms and conditions content
    const termsContent = await this.authService.getTermsAndConditions();
    const termsAndConditions = termsContent?.termsAndConditions || '';
    const termsStyles = termsContent?.styles || '';

    const messageConfig = this.configService.get('registerEmail');
    const route: {
      action: string;
      page: string;
    } = Utility.getRoute(req?.route);
    return {
      token: '',
      validationErrors: [],
      route: route,
      registerEmailMessages: messageConfig,
      dataPassToView: {
        ...req.query,
      },
      termsAndConditions: termsAndConditions,
      termsStyles: termsStyles,
    };
  }

  /**
   * Handles the signup request.
   * @param signupDetail - Signup details
   * @param res - Express response object
   */
  @Post('/register-email')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe())
  @UseFilters(new ViewValidationFilter(VIEW_PAGES.MAIN))
  async signup(@Body() signupDetail: SignUpDto, @Res({ passthrough: true }) res, @Req() req) {
    try {
      const redirectUrl = signupDetail?.redirect_url;
      const countryData = JSON.parse(signupDetail['country_data']);
      const signupDto = {
        country_code: countryData?.code,
        timezone: countryData?.timeZone,
        email: signupDetail?.email,
        password: signupDetail?.password,
        first_name: signupDetail?.first_name,
        last_name: signupDetail?.last_name,
        accept_agreement: signupDetail?.termCondition ? true : false,
        phone_no: signupDetail?.phone_no,
        name: signupDetail?.email,
        display_name: (signupDetail?.first_name || '') + ' ' + (signupDetail?.last_name || ''),
        language: 'en',
        status: 1,
        account_setup: this.configService.get('userOptionsAccountSetupComplete'),
        password_created: this.configService.get('userOptionsAccountSetupComplete'),
      };
      const response = await this.authService.signUp(signupDto, redirectUrl, req.cookies, res);
      res.redirect(response);
    } catch (error: any) {
      Logger.error('signup', {
        METHOD: this.constructor?.name + '@' + this.signup?.name,
        MESSAGE: error.message,
        REQUEST: { signupDetail },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException(
        error.message === 'UserAlreadyExist'
          ? 'Email address already exists, please choose a different one'
          : 'Some error occurred while user registration.',
      );
    }
  }

  /**
   * Handle the registration completion page.
   *
   * @param req Express request object
   * @returns Rendered page with registration completion details
   *
   * @description This method handles the registration completion page and performs the following steps:
   */
  @Get('/register-complete')
  @Render(VIEW_PAGES.MAIN)
  async registerComplete(@Req() req) {
    if(!req?.cookies[this.configService.get('linkData')]) {
      throw new BadRequestException('Link data cookie is not present in the request cookies.');
    }
    const linkData: any = JSON.parse(req?.cookies[this.configService.get('linkData')]);

    if (Utility.isEmpty(linkData)) {
      Logger.error('registerComplete', {
        METHOD: this.constructor?.name + '@' + this.registerComplete?.name,
        MESSAGE: '_linkData cookie is not present in the req.cookies.',
        REQUEST: req,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException('Something went wrong, please try again.');
    }
    const route: RouteType = Utility.getRoute(req?.route);
    
    // Get terms and conditions content
    const termsContent = await this.authService.getTermsAndConditions();
    const termsAndConditions = termsContent?.termsAndConditions || '';
    const termsStyles = termsContent?.styles || '';
    const termsSource = termsContent?.source || 'unknown';
    
    return {
      token: '',
      route: route,
      validationErrors: [],
      email: linkData?.email,
      type: linkData?.type || '',
      termsAndConditions: termsAndConditions,
      termsStyles: termsStyles,
      termsSource: termsSource
    };
  }

  /**
   * Handle the registration completion POST request.
   *
   * @param formData Registration completion form data
   * @param res Express response object (for setting cookies and sending responses)
   * @param req Express request object
   * @returns Response data based on the success or failure of the registration completion process
   *
   * @description This method handles the registration completion POST request and performs the following steps:
   */
  @Post('/register-complete')
  @UsePipes(new ValidationPipe())
  async registerCompletePost(
    @Body(new ValidationPipe()) formData: RegisterCompleteDto,
    @Res({ passthrough: true }) res,
    @Req() req,
  ) {
    try {
      const linkDataCookieName = this.configService.get('linkData');
      const linkData: any = JSON.parse(req?.cookies[linkDataCookieName]);
      const pageCookieName = this.configService.get('page');
      const origin = req.cookies[pageCookieName];
      const cookieHelper = await this.helperService.getHelper('CookieHelper');
      if (!Utility.isEmpty(origin) && origin === 'register') {
        cookieHelper.clearCookie(res, pageCookieName);
      }
      if (!Utility.isEmpty(req?.cookies[linkDataCookieName])) {
        cookieHelper.clearCookie(res, linkDataCookieName);
      }
      if (Utility.isEmpty(linkData)) {
        const defaultMessage: string = 'Something went wrong, please try again.';
        Logger.error('registerComplete', {
          METHOD: this.constructor?.name + '@' + this.registerComplete?.name,
          MESSAGE: '_linkData cookie is not present in the req.cookies.',
          REQUEST: req,
          TIMESTAMP: new Date().getTime(),
        });
        throw new BadRequestException(defaultMessage);
      }
      const response = await this.authService.registerCompletePost(req.cookies, res, formData, linkData);
      res.redirect(response?.data);
    } catch (error: any) {
      const defaultMessage: string = 'Something went wrong, please try again.';
      Logger.error('registerCompletePost', {
        METHOD: this.constructor?.name + '@' + this.registerCompletePost?.name,
        MESSAGE: error.message,
        REQUEST: formData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException(defaultMessage);
    }
  }

  @Post('/multi-account')
  @UseAuthGuard()
  async multiAccountPost(
    @Body() lmsInfo: { url: string; gid: string },
    @Res({ passthrough: true }) res: Response,
    @Req() req,
  ) {
    const user: CurrentUser = req.user;
    const redirectUrl = await this.authService.redirectMultiAccount(lmsInfo, user);
    res.redirect(redirectUrl);
  }

  @UseAuthGuard()
  @Get('/multi-account')
  @UseAuthGuard()
  @Render(VIEW_PAGES.MAIN)
  async multiAccount(
    @Query() queryParam: { url: string; gid: string; calendar_url: string },
    @Res({ passthrough: true }) res,
    @Req() req,
  ) {
    try {
      const user: CurrentUser = req.user;
      const route = Utility.getRoute(req?.route);
      const response = await this.authService.getMultiAccountRedirectionList(
        queryParam,
        req?.user?.name,
        route,
        res,
        user,
      );
      if (response?.items.length === 0) {
        res.redirect(response?.redirectUrl);
      }
      return response;
    } catch (error: any) {
      Logger.error('multiAccount', {
        METHOD: this.constructor.name + '@' + this.multiAccount.name,
        MESSAGE: error.message,
        REQUEST: { req: req },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  @Get('/forgot-password')
  @Render(VIEW_PAGES.MAIN)
  async forgotPasswordRender(@Req() req, @Res() res: Response) {
    const route: { action: string; page: string } = Utility.getRoute(req?.route);
    const forgotPasswordDetails = { params: req.params, ...req.cookies['forgotPasswordInfo'] };
    const forgotPasswordInfo = { email: '', isSent: false };
    const token = req?.cookies['token'];
    const cookieHelper = await this.helperService.getHelper('CookieHelper');
    await cookieHelper.setCookie(res, 'forgotPasswordInfo', forgotPasswordInfo);
    cookieHelper.clearCookie(res, 'token');
  
    return {
      token: '',
      validationErrors: [token ? 'Invalid password reset URL OR it is expired.' : ''],
      route: route,
      params: forgotPasswordDetails,
    };
  }

  @Post('/forgot-password')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe())
  @UseFilters(new ViewValidationFilter(VIEW_PAGES.MAIN))
  async forgotPassword(@Body() forgotPasswordDetails: ForgotPasswordDto, @Res({ passthrough: true }) res: Response) {
    try {
      const response = await this.authService.forgetPassword(forgotPasswordDetails.email);
      if (response instanceof Error) {
        if (response?.message === 'UserNotFoundException') {
          throw new BadRequestException('Please make sure the email address entered is correct.', {
            description: JSON.stringify({ userNotFound: true }),
          });
        }
        if (response?.message === 'AttemptLimitExceed') {
          throw new BadRequestException(`Attempt limit exceeded. Please try again after ${this.configService.get('fpResetWaitTime')} minutes.'`, {
            description: JSON.stringify({
              attemptLimitExceed: true,
              limit: this.configService.get('fpResetCount'),
              waitTime: this.configService.get('fpResetWaitTime'),
            }),
          });
        } else if (response?.message === 'UserDisabled') {
          throw new BadRequestException('Try using a different email address to create an account.');
        } else {
          throw new BadRequestException('Invalid Request.');
        }
      }
      if (response) {
        const cookieHelper = await this.helperService.getHelper('CookieHelper');
        await cookieHelper.setCookie(res, 'forgotPasswordInfo', { email: forgotPasswordDetails?.email, isSent: true });
      }
        // LRS logging via  - START // TODO lrs events
        // const lrsData = {
        //   verb: 'visit',
        //   objectType: 'forgot-password',
        //   dataVals: {
        //     client_id: this.configService.get('ssoClientId'),
        //   },
        // };
        // const lrsInstanse = await this.helperService.getHelper('lrsHelper');
        // lrsInstanse.sendDataToLrs(null, lrsData);
        // LRS logging via  - END
      return res.redirect('/auth/forgot-password');
    } catch (error: any) {
      throw new BadRequestException(error);
    }
  }

  @Get('/reset/:uid/:token')
  @Render(VIEW_PAGES.MAIN)
  async reset(@Param() params: any, @Req() req, @Res() res: Response) {
    const cacheService = await this.helperService.get<CachingService>(CachingService);
    const userId = this.cryptoHelper.decrypt(params?.uid);
    let resetStatus = req?.cookies[userId] || false;
    const token: string = await cacheService.get(userId);
    const cookieHelper = await this.helperService.getHelper('CookieHelper');
    if (params?.token !== token) {
      resetStatus = false;

      cookieHelper.clearCookie(res, userId);
      await cookieHelper.setCookie(res, 'token', token);
      res.redirect(`/auth/forgot-password`);
    }
    const key = `token_${params?.uid}`;
    resetStatus = req?.cookies[key] ? true : false;
    req?.cookies[key] ? await cacheService.del(userId) : '';
    cookieHelper.clearCookie(res, key);
    const route: {
      action: string;
      page: string;
    } = Utility.getRoute(req?.route);
    return {
      params: { ...req?.params, token },
      validationErrors: [],
      route: route,
      status: resetStatus,
    };
  }

  @Post('/reset/:uid/:token')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe())
  @UseFilters(new ViewValidationFilter(VIEW_PAGES.MAIN))
  async resetPost(@Body() body: { password: string }, @Param() params: any, @Req() req, @Res() res) {
    try {
      const passResetResponse = await this.authService.resetPassword(body, params);
      if (passResetResponse) {
        const cookieHelper = await this.helperService.getHelper('CookieHelper');
        cookieHelper.setCookie(res, `token_${params['uid']}`, true);
      }
      res.redirect(`/auth/reset/${req?.params?.uid}/${req?.params?.token}`);
    } catch (error: any) {
      throw new BadRequestException('Invalid Request.');
    }
  }

  @Get('/reset-password/:userId/:requestTime/:requestToken')
  @Render(VIEW_PAGES.RESET_PASSWORD)
  resetPassword() {
    Logger.log('resetPassword');
  }

  @Get('/process-apple-auth-response-register')
  @Render(VIEW_PAGES.PROCESS_APPLE_AUTH_RESPONSE_REGISTER)
  processAppleAuthResponseRegister() {
    Logger.log('processAppleAuthResponseRegister');
  }

  @Get('/process-apple-auth-response-login')
  @Render(VIEW_PAGES.PROCESS_APPLE_AUTH_RESPONSE_LOGIN)
  processAppleAuthResponseLogin() {
    Logger.log('processAppleAuthResponseLogin');
  }

  @Get('/process-social-auth-response')
  @Render('pages/process-social-auth-response')
  processSocialAuthResponse() {
    Logger.log('processSocialAuthResponse');
  }

  @Get('/create-user-social-account-details')
  @Render(VIEW_PAGES.CREATE_USER_SOCIAL_ACCOUNT_DETAILS)
  createUserSocialAccountDetails() {
    Logger.log('createUserSocialAccountDetails');
  }

  @Get('/create-user-account')
  @Render(VIEW_PAGES.CREATE_USER_ACCOUNT)
  createUserAccount() {
    Logger.log('createUserAccount');
  }

  /**
   * Render Social Link Page.
   *
   * @param req Express request object
   * @returns Rendered view object containing user information and validation errors
   *
   * @description This endpoint renders the social link page.
   */

  @Get('/social-link')
  @Render(VIEW_PAGES.MAIN)
  // @UseGuards(JwtAuthenticationGuard)
  async socialLinkRender(@Req() req) {
    const route: RouteType = Utility.getRoute(req?.route);
    const defaultMessage: string = 'Something went wrong, please try again.';
    const response: {
      route: RouteType;
      validationErrors: string[];
      user: {
        email: string;
        type: string;
      };
      code: number;
    } = {
      route: route,
      validationErrors: [defaultMessage],
      user: { email: '', type: '' },
      code: 0,
    };

    let linkData;
    try {
      if(!req?.cookies[this.configService.get('linkData')]){
        Logger.error('socialLinkRender', {
          METHOD: this.constructor?.name + '@' + this.socialLinkRender?.name,
          MESSAGE: '_linkData cookie is not present in the req.cookies.',
          REQUEST: req,
          TIMESTAMP: new Date().getTime(),
        });        
        throw new BadRequestException(defaultMessage);
      }
      linkData = JSON.parse(req?.cookies[this.configService.get('linkData')]);
      if (Utility.isEmpty(linkData) || Utility.isEmpty(linkData?.type) || Utility.isEmpty(linkData?.email)) {
        Logger.error('socialLinkRender', {
          METHOD: this.constructor?.name + '@' + this.socialLinkRender?.name,
          MESSAGE: '_linkData cookie is not present in the req.cookies.',
          REQUEST: req,
          TIMESTAMP: new Date().getTime(),
        });
        throw new BadRequestException(defaultMessage);
      }
      const email: string = linkData?.email;
      const type: string = linkData?.type;
      response.validationErrors = [];
      response.user = { email, type };
      response.code = linkData?.code;
      return response;
    } catch (error: any) {
      Logger.error('socialLinkRender', {
        METHOD: this.constructor?.name + '@' + this.socialLinkRender?.name,
        MESSAGE: error.message,
        REQUEST: req,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      response.code = linkData?.code;
      return response;
    }
  }

  /**
   * Handles social link processing and redirects the user based on the response.
   *
   * @param req Express request object
   * @param res Express response object
   *
   * @returns {void} No direct return value; redirects the user based on the response
   *
   * @description This endpoint handles social link processing and redirects the user based on the response.
   * The process involves the following steps:
   */
  @Post('/social-link')
  @UseFilters(new ViewValidationFilter(VIEW_PAGES.MAIN))
  async socialLink(@Req() req, @Res() res) {
    const defaultMessage: string = 'Something went wrong, please try again.';
    let linkData;
    try {
      if (!req?.cookies[this.configService.get('linkData')]) {
        Logger.error('socialLinkRender', {
          METHOD: this.constructor?.name + '@' + this.socialLinkRender?.name,
          MESSAGE: '_linkData cookie is not present in the req.cookies.',
          REQUEST: req,
          TIMESTAMP: new Date().getTime(),
        });
        throw new BadRequestException(defaultMessage);
      }
      linkData = JSON.parse(req?.cookies[this.configService.get('linkData')]);
      if (Utility.isEmpty(linkData) || !Utility.isValidEmail(linkData?.email)) {
        Logger.error('socialLinkRender', {
          METHOD: this.constructor?.name + '@' + this.socialLinkRender?.name,
          MESSAGE: '_linkData cookie is not present in the req.cookies.',
          REQUEST: req,
          TIMESTAMP: new Date().getTime(),
        });
        throw new BadRequestException(defaultMessage);
      }
      const response = await this.authService.socialLink(req.cookies, res, linkData);
      if (!response?.status) {
        throw new BadRequestException(defaultMessage);
      }
      return res.redirect(response?.data?.redirectUrl || response?.data);
    } catch (error: any) {
      Logger.error('socialLink', {
        METHOD: this.constructor?.name + '@' + this.socialLink?.name,
        MESSAGE: error.message,
        REQUEST: { body: req?.body, linkData: linkData },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException(defaultMessage);
    }
  }

  @Get('/validate-login')
  @Render(VIEW_PAGES.VALIDATE_LOGIN)
  validateLogin() {
    Logger.log('validateLogin');
  }

  // @UseAuthGuard()
  @Get('/manage-auth-redirect')
  async manageAuthRedirect(
    @Req() req: Request,
    @Query() queryParam,
    @Query('gid') gid: string,
    @Query('url') url: string,
    @Res({ passthrough: true }) res: Response,
  ) {
    //call decode function for decoding manage auth token
    try {
      const token = queryParam?.token;
      const redirectUrl = await this.authService.manageAuthRedirect(req.cookies, res, token, gid);
      res.redirect(redirectUrl);
    } catch (error: any) {
      Logger.error('manageAuthRedirect', {
        METHOD: this.constructor?.name + '@' + this.manageAuthRedirect?.name,
        MESSAGE: error.message,
        REQUEST: { queryParam, gid, url },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  @Get('/logout')
  @UseAuthGuard()
  async logout(@Res({passthrough: true}) res: Response, @Req() req: Request) {
    try { 
      const currentUser = req?.user;
      const [
        userRepository,
        authTokenHelper,
        enterpriseService,
        authHelper,
        cacheService,
        cookieHelper,
      ] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.getHelper<AuthTokenHelper>(AuthTokenHelper),
        this.helperService.get<EnterpriseService>(EnterpriseService),
        this.helperService.getHelper('AuthHelper'),
        this.helperService.get<CachingService>(CachingService),
        this.helperService.getHelper('CookieHelper'),
      ]);

      const userObj = await userRepository.findByUID(currentUser['uid']);
      const cacheKey = `sc_${userObj?.uid}`;

      const clearCacheAndCookies = async () => {
        await cacheService.invalidateCache(cacheKey, null, 0);
        await cookieHelper.clearCookie(res, this.configService.get('ssoCookie'));
      };

      //LRS logging starts
       const lrsData = {
        verb: 'logout',
        objectType: 'accounts',
        objectId: userObj?.uid,
        dataVals: {
          client_id: this.configService.get('ssoClientId')
        }
      };
      const lrsInstanse = await this.helperService.getHelper('lrsHelper');
      lrsInstanse.sendDataToLrs(userObj, lrsData);
      //LRS ends
      if(userObj?.user_groups.length > 0) {
        const userToken =  await authTokenHelper.decodeJWTToken(req?.cookies?.[this.configService.get('ssoCookie')]);
        // Check if the groupId contains multiple IDs separated by a comma,
        // indicating that the user belongs to multiple groups.
        const resolvedGroupId = Array.isArray(userObj?.user_groups) && userObj?.user_groups.length > 0
        ? userToken?.data?.lgid || userObj?.user_groups[0]
        : userObj?.user_groups[0];
        const enterpriseResponse = await enterpriseService.getGroupByGid(Number(resolvedGroupId));
        let logoutUrl = enterpriseResponse?.data[0].homePageUrl || '';

        if (logoutUrl !== '') {
          if (Utility.isValidUrl(logoutUrl)) {
            const hasSchema = logoutUrl.startsWith('http://') || logoutUrl.startsWith('https://');
            if (!hasSchema) {
            logoutUrl = `https://${logoutUrl}`;
            }
            await this.userSignout(res);
            await clearCacheAndCookies();
            return res.redirect(logoutUrl);
          }
        }

        if (enterpriseResponse?.data[0].saml_sp_initiated === 1) {
          const subDomain = await authHelper.getSubDomain(resolvedGroupId);
          const siteUrl = this.configService.get('issuer');
          const logoutURL = `${siteUrl}/saml/service-provider/sp-initiated-sls/title/${subDomain}`;
          await clearCacheAndCookies();
          await this.userSignout(res);
          return this.logoutSimpleSAML(req.cookies, res, logoutURL);
        } else {
          await clearCacheAndCookies();
          await this.userSignout(res);
          res.redirect(this.configService.get('ic9SiteUrl'));
        }
      }
      
      // If the user does not belong to any group, simply sign them out
      await clearCacheAndCookies();
      await this.userSignout(res);
      res.redirect(this.configService.get('ic9SiteUrl'));
    } catch (error: any) {
      Logger.error('logout', {
        METHOD: this.constructor?.name + '@' + this.logout?.name,
        MESSAGE: error.message,
        REQUEST: { cookies: req?.cookies },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  // userSignout is used by /logout to invalidate the cookies
  async userSignout(@Res() res) {
    try {
      const cookieHelper = await this.helperService.getHelper('CookieHelper');
      await cookieHelper.setBulkCookie(res, [this.configService.get('ssoCookie'), this.configService.get('slCookie')], {
      domain: null,
      expires: -1,
      path: '/',
      });
    } catch (error: any) {
      Logger.error('userSignout', {
      METHOD: this.constructor?.name + '@' + this.userSignout?.name,
      MESSAGE: error.message,
      RESPONSE: error.stack,
      TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException('Failed to clear cookies during signout.');
    }
  }

  // logoutSimpleSAML is used by /logout to invalidate the SimpleSAML cookies
  async logoutSimpleSAML(userCookies, res, logoutURL) {
    try {
      const cookie = userCookies;
      const cookieHelper = await this.helperService.getHelper('CookieHelper');
      if(cookie['SimpleSAMLAuthToken']){
        await cookieHelper.setCookie(res,'SimpleSAMLAuthToken', null, -1, '/');
      }
  
      if(cookie['SimpleSAML']){
        await cookieHelper.setCookie(res,'SimpleSAML', null, -1, '/');
      }
      res.redirect(logoutURL);
    } catch (error: any) {
      Logger.error('logoutSimpleSAML', {
        METHOD: this.constructor?.name + '@' + this.logoutSimpleSAML?.name,
        MESSAGE: error.message,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
        });
        throw new BadRequestException('Failed to Invalidate simpleSAML cookies.');
    }
    
  }

  /**
   * Renders the account setup page based on query parameters.
   *
   * @param queryParam Query parameters
   * @param req Express request object
   * @param res Express response object
   *
   * @returns {object} Rendered account setup page data
   *
   * @description This endpoint renders the account setup page based on the provided query parameters.
   * The process involves the following steps:
   */

  @Get('/account-setup')
  @Render(VIEW_PAGES.MAIN)
  async accountSetUp(@Query() queryParam, @Req() req, @Res() res) {
    try {
      const route: {
        action: string;
        page: string;
      } = Utility.getRoute(req?.route);
      const userEmail: string = queryParam?.userEmail;
      if (!Utility.isValidEmail(userEmail)) {
        return {
          token: '',
          validationErrors: ['Unauthorized email address'],
          route: route,
          dataPassToView: {},
        };
      }
      
      // Get terms and conditions content
      const termsContent = await this.authService.getTermsAndConditions();
      const termsAndConditions = termsContent?.termsAndConditions || '';
      const termsStyles = termsContent?.styles || '';
      
      const accountSetupResponse: any = await this.authService.accountSetUp(req.cookies, queryParam, userEmail);
      if (accountSetupResponse?.status) {
        res.redirect(accountSetupResponse?.redirectUrl);
      } else {
        return {
          token: '',
          validationErrors:
            accountSetupResponse?.msg != 'success' ? [this.configService.get(accountSetupResponse?.msg)] : [],
          route: route,
          dataPassToView: {
            ...accountSetupResponse?.data,
            userEmail: userEmail  // Explicitly adding userEmail to make the conditional work
          },
          termsAndConditions: termsAndConditions,
          termsStyles: termsStyles,
        };
      }
    } catch (error: any) {
      Logger.error('accountSetUp', {
        METHOD: this.constructor?.name + '@' + this.accountSetUp?.name,
        MESSAGE: error.message,
        REQUEST: { queryParam },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException('Something went wrong, please try again.');
    }
  }

  /**
   * Submit account setup information.
   *
   * @param accountSetupInfo Account setup data
   * @param req Express request object
   * @param res Express response object (passthrough)
   *
   * @returns {object} Submission result and data
   *
   * @description This endpoint submits account setup information and handles the process accordingly.
   * The process involves the following steps:
   */
  @Post('/account-setup')
  @UsePipes(new ValidationPipe())
  @UseFilters(new ViewValidationFilter(VIEW_PAGES.MAIN))
  async submitAccountSetUp(
    @Body(new ValidationPipe()) accountSetupInfo: SignUpDto,
    @Req() req,
    @Res({ passthrough: true }) res,
  ) {
    try {
      const route: {
        action: string;
        page: string;
      } = Utility.getRoute(req?.route);
      const result = await this.authService.accountSetupPostOperations(res, accountSetupInfo);
      if (result?.status) {
        res.redirect(result?.data?.redirect_url);
      } else {
        return {
          token: '',
          route: route,
          validationErrors: [result?.msg],
        };
      }
    } catch (error: any) {
      Logger.error('submitAccountSetUp', {
        METHOD: this.constructor?.name + '@' + this.submitAccountSetUp?.name,
        MESSAGE: error.message,
        REQUEST: { accountSetupInfo },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException(error.message);
    }
  }

  @Get('/linkedin/callback')
  @UseGuards(AuthGuard('linkedin'))
  async linkedinCallback(@Req() req, @Res() res) {
    res.setHeader('Content-Type', 'text/html');
    try {
      const origin = req.cookies[this.configService.get('page')];
      const user = req?.user;
      const response = await this.authService.getUserIdpInfo(user, origin, res);
      return res.send(response);
    } catch (error: any) {
      Logger.error('linkedinCallback', {
        METHOD: `${this.constructor?.name}@${this.linkedinCallback?.name}`,
        MESSAGE: error.message,
        REQUEST: req.user,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return res.send(this.authService.socialLoginRedirect('login'));
    }
  }

  @Get('/google/callback')
  @UseGuards(AuthGuard('google'))
  async googleCallback(@Req() req, @Res() res) {
    res.setHeader('Content-Type', 'text/html');
    try {
      const origin = req.cookies[this.configService.get('page')];
      const user = req?.user;
      const response = await this.authService.getUserIdpInfo(user, origin, res);
      res.send(response);
    } catch (error: any) {
      Logger.error('googleCallback', {
        METHOD: `${this.constructor?.name}@${this.googleCallback?.name}`,
        MESSAGE: error.message,
        REQUEST: req?.user,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      await this.helperService.getHelper('SocialLoginHelper');
      const redirectUrl = this.authService.socialLoginRedirect('login');
      res.send(redirectUrl);
    }
  }

  @Get('/facebook/callback')
  @UseGuards(AuthGuard('facebook'))
  async facebookCallback(@Req() req, @Res() res) {
    res.setHeader('Content-Type', 'text/html');
    try {
      const origin = req.cookies[this.configService.get('page')];
      const user = req?.user;
      const response = await this.authService.getUserIdpInfo(user, origin, res);
      return res.send(response);
    } catch (error: any) {
      Logger.error('facebookCallback', {
        METHOD: `${this.constructor?.name}@${this.facebookCallback?.name}`,
        MESSAGE: error.message,
        REQUEST: req?.user,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return res.send(this.authService.socialLoginRedirect('login'));
    }
  }

  @Post('/apple/callback')
  @UseGuards(AuthGuard('apple'))
  async appleCallback(@Req() req, @Res() res) {
    res.setHeader('Content-Type', 'text/html');
    try {
      const origin = req.cookies[this.configService.get('page')];
      const user = req?.user;
      const response = await this.authService.getUserIdpInfo(user, origin, res);
      return res.send(response);
    } catch (error: any) {
      Logger.error('appleCallback', {
        METHOD: `${this.constructor?.name}@${this.appleCallback?.name}`,
        MESSAGE: error.message,
        REQUEST: req.user,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return res.send(this.authService.socialLoginRedirect('login'));
    }
  }

  @Get('/linkedin')
  @UseGuards(AuthGuard('linkedin'))
  async linkedinLogin() {
    // The LinkedIn OAuth2 login flow will be initiated automatically by Passport
    Logger.info('Initiating LinkedIn login');
  }

  @Get('/facebook')
  @UseGuards(AuthGuard('facebook'))
  async facebookLogin() {
    // The facebook OAuth2 login flow will be initiated automatically by Passport
    Logger.info('Initiating facebook login');
  }

  @Get('/google')
  @UseGuards(AuthGuard('google'))
  async googleLogin() {
    // The Google OAuth2 login flow will be initiated automatically by Passport
    Logger.info('Initiating google login');
  }

  @Get('/apple')
  @UseGuards(AuthGuard('apple'))
  async appleLogin() {
    // The "Sign in with Apple" OAuth2 login flow will be initiated automatically by Passport
    Logger.info('Initiating apple login');
  }
}