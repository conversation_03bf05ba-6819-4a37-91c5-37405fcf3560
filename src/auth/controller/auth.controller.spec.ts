import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from '../services/auth/auth.service';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import { EmailService } from '../../common/services/email/email.service';
import { JwtService } from '@nestjs/jwt';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
import { CachingService } from '../../caching/caching.service';
import { UserRepository } from '../../user/repositories/user/user.repository';
import { AuthTokenHelper } from '../helper/auth.tokenhelper';
import { UserService } from '../../user/services/user.service';
import { BadRequestException } from '@nestjs/common';
import { Utility } from '../../common/util/utility';
import { Logger } from '../../logging/logger';

jest.mock('../../common/util/utility', () => ({
  Utility: {
    isEmpty: jest.fn(),
    getRoute: jest.fn().mockReturnValue({ action: 'login', page: 'loginPage' }),
    isValidEmail: jest.fn(),
    isValidUrl: jest.fn(),
  },
}));

describe('AuthController', () => {
  let controller: AuthController;
  let configService: ConfigService;
  let authService: AuthService;
  let helperService: HelperService;

  const mockUser = {
    uid: 123,
    email: '<EMAIL>',
    user_groups: [10],
  };

  const mockRequest = {
    user: mockUser,
    cookies: {
      ssoCookieName: 'sso-token',
      SimpleSAMLAuthToken: 'auth-token',
      SimpleSAML: 'saml-token',
    },
    route: { path: '/login' },
    query: {},
  } as any;

  const mockResponse = {
    redirect: jest.fn(),
    cookie: jest.fn(),
  } as any;

  const mockCacheService = { invalidateCache: jest.fn() };
  const mockCookieHelper = {
    clearCookie: jest.fn(),
    setBulkCookie: jest.fn(),
    setCookie: jest.fn(),
    setFermiumCookie: jest.fn(),
    setCommunityCookie: jest.fn(),
  };

  const mockTokenHelper = {
    decodeJWTToken: jest.fn().mockResolvedValue({ data: { lgid: 11 } }),
  };

  const mockEnterpriseService = {
    getGroupByGid: jest.fn().mockResolvedValue({
      data: [{ homePageUrl: 'https://homepage.com', saml_sp_initiated: 0 }],
    }),
  };

  const mockAuthHelper = {
    getSubDomain: jest.fn().mockResolvedValue('subdomain'),
  };

  const mockLrsHelper = {
    sendDataToLrs: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        UserService,
        AuthService,
        HelperService,
        EmailService,
        UserRepository,
        {
          provide: 'secureConnection/UserModel',
          useValue: {},
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              switch (key) {
                case 'ssoCookie': return 'ssoCookieName';
                case 'slCookie': return 'slCookieName';
                case 'issuer': return 'https://auth.domain.com';
                case 'ssoClientId': return 'sso-client';
                case 'ic9SiteUrl': return 'https://ic9.com';
                case 'page': return 'page';
                case 'clientKey': return 'client-key';
                case 'linkData': return 'linkData';
                case 'userOptionsAccountSetupComplete': return 'complete';
                case 'fpResetWaitTime': return 15;
                case 'fpResetCount': return 5;
                case 'invalidLink': return 'Invalid link provided.';
                case 'registerEmail': return { message: 'Register email message' };
                default: return '';
              }
            }),
          },
        },
        { provide: JwtService, useValue: { sign: jest.fn() } },
        {
          provide: 'CRYPTO_HELPER', useValue: {
            encrypt: jest.fn(),
            decrypt: jest.fn(),
          }
        },
      ],
    }).compile();

    controller = module.get(AuthController);
    authService = module.get(AuthService);
    configService = module.get(ConfigService);
    helperService = module.get(HelperService);

    jest.spyOn(helperService, 'get').mockImplementation(token => {
      if (token === EnterpriseService) return Promise.resolve(mockEnterpriseService);
      if (token === CachingService) return Promise.resolve(mockCacheService);
      if (token === UserRepository) return Promise.resolve({
        findByUID: jest.fn().mockResolvedValue(mockUser),
      });
      return Promise.resolve(undefined);
    });

    jest.spyOn(helperService, 'getHelper').mockImplementation(token => {
      if (token === AuthTokenHelper) return Promise.resolve(mockTokenHelper);
      if (token === 'AuthHelper') return Promise.resolve(mockAuthHelper);
      if (token === 'lrsHelper') return Promise.resolve(mockLrsHelper);
      if (token === 'CookieHelper') return Promise.resolve(mockCookieHelper);
      return Promise.resolve(undefined);
    });

  });

  describe('GET /login - loginRender', () => {
    it('should redirect if ssoCookie is present', async () => {
      const redirectUrl = 'https://redirect-url.com';
      const ssoCookieValue = 'sso-cookie-token';
      mockRequest.cookies = { ssoCookieName: ssoCookieValue };

      (Utility.isEmpty as jest.Mock).mockReturnValue(false);
      jest.spyOn(authService, 'handleUserSsoCookie').mockResolvedValue(redirectUrl);

      const result = await controller.loginRender({}, mockRequest, mockResponse);

      expect(authService.handleUserSsoCookie).toHaveBeenCalledWith({}, ssoCookieValue);
      expect(mockResponse.redirect).toHaveBeenCalledWith(redirectUrl);
      expect(result).toBeUndefined();
    });

    it('should render login page if ssoCookie is not present', async () => {
      mockRequest.cookies = { ssoCookieName: '' };
      (Utility.isEmpty as jest.Mock).mockReturnValue(true);

      const query = {};
      const mockRoute = { page: 'login', action: '/login' };
      const mockRedirectData: any = {
        redirectUrl: 'https://redirect.com',
        calendarUrl: 'https://calendar.com',
        isB2BAndB2C: true,
        domainGid: 101,
        domainUrl: 'https://domain.com',
      };

      (Utility.getRoute as jest.Mock).mockReturnValue(mockRoute);
      jest.spyOn(authService, 'getRedirectUrl').mockResolvedValue(mockRedirectData);

      const result = await controller.loginRender(query, mockRequest, mockResponse);

      expect(authService.getRedirectUrl).toHaveBeenCalledWith(mockResponse, query);
      expect(mockCookieHelper.setCookie).toHaveBeenCalledWith(mockResponse, 'page', 'login');

      expect(result).toEqual({
        token: '',
        validationErrors: [],
        route: mockRoute,
        redirect_url: mockRedirectData.redirectUrl,
        calendar_url: mockRedirectData.calendarUrl,
        domainGid: mockRedirectData.domainGid,
        isB2BAndB2C: mockRedirectData.isB2BAndB2C,
        domainUrl: mockRedirectData.domainUrl,
      });
    });

    it('should throw BadRequestException if error occurs', async () => {
      (Utility.isEmpty as jest.Mock).mockImplementation(() => {
        throw new Error('Unexpected failure');
      });

      await expect(controller.loginRender({}, mockRequest, mockResponse)).rejects.toThrow(BadRequestException);
    });
  });

  describe('POST /login - login', () => {
    const loginInfo = { email: '<EMAIL>', password: 'password123' };

    it('should redirect on successful login', async () => {
      const mockUserResponse = { uid: 123 };
      const redirectUrl = 'https://redirect-url.com';

      jest.spyOn(authService, 'authenticateUser').mockResolvedValue(mockUserResponse);
      jest.spyOn(authService, 'getLoginRedirectUrl').mockResolvedValue(redirectUrl);

      await controller.login(loginInfo, mockRequest, mockResponse);

      expect(authService.authenticateUser).toHaveBeenCalledWith(
        mockResponse,
        loginInfo,
        configService.get('clientKey')
      );

      expect(authService.getLoginRedirectUrl).toHaveBeenCalledWith(
        mockRequest.cookies,
        mockResponse,
        loginInfo,
        mockUserResponse
      );

      expect(mockResponse.redirect).toHaveBeenCalledWith(redirectUrl);
    });

    it('should throw BadRequestException for UserNotFoundException', async () => {
      jest.spyOn(authService, 'authenticateUser').mockResolvedValue(new Error('UserNotFoundException'));

      await expect(controller.login(loginInfo, mockRequest, mockResponse))
        .rejects
        .toThrow(new BadRequestException('The email or password you have entered is invalid.'));
    });

    it('should throw BadRequestException for InvalidCredentials', async () => {
      jest.spyOn(authService, 'authenticateUser').mockResolvedValue(new Error('InvalidCredentials'));

      await expect(controller.login(loginInfo, mockRequest, mockResponse))
        .rejects
        .toThrow(new BadRequestException('The email or password you have entered is invalid.'));
    });

    it('should throw BadRequestException for unknown error', async () => {
      jest.spyOn(authService, 'authenticateUser').mockResolvedValue(new Error('UnexpectedError'));

      await expect(controller.login(loginInfo, mockRequest, mockResponse))
        .rejects
        .toThrow(new BadRequestException('Some error occurred while user login.'));
    });

    it('should throw BadRequestException if an exception is thrown', async () => {
      jest.spyOn(authService, 'authenticateUser').mockRejectedValue(new Error('Unhandled error'));

      await expect(controller.login(loginInfo, mockRequest, mockResponse))
        .rejects
        .toThrow(BadRequestException);
    });
  });

  describe('GET /register - register', () => {
    it('should render the register page and set cookies', async () => {
      const queryParam = {
        assignmentToken: 'assign-123',
        redirect_url: 'https://redirect.com',
      };

      (Utility.getRoute as jest.Mock).mockReturnValue({
        action: 'register',
        page: 'registerPage',
      });

      const result = await controller.register(queryParam, mockRequest, mockResponse);

      expect(Utility.getRoute).toHaveBeenCalledWith(mockRequest.route);
      expect(mockCookieHelper.setCookie).toHaveBeenCalledWith(mockResponse, 'page', 'registerPage');
      expect(mockCookieHelper.setFermiumCookie).toHaveBeenCalledWith('assign-123', mockResponse);
      expect(mockCookieHelper.setCommunityCookie).toHaveBeenCalledWith('https://redirect.com', mockResponse);

      expect(result).toEqual({
        token: '',
        validationErrors: [],
        route: { action: 'register', page: 'registerPage' },
      });
    });

    it('should handle missing query params gracefully', async () => {
      const queryParam = {};

      (Utility.getRoute as jest.Mock).mockReturnValue({
        action: 'register',
        page: 'registerPage',
      });

      const result = await controller.register(queryParam, mockRequest, mockResponse);

      expect(mockCookieHelper.setFermiumCookie).toHaveBeenCalledWith('', mockResponse);
      expect(mockCookieHelper.setCommunityCookie).toHaveBeenCalledWith('', mockResponse);

      expect(result).toEqual({
        token: '',
        validationErrors: [],
        route: { action: 'register', page: 'registerPage' },
      });
    });
  });

  describe('GET /register-email - registerEmail', () => {
    let req: any;
    let res: any;

    beforeEach(() => {
      req = {
        cookies: {},
        route: { path: '/register-email' },
      };
      res = {
        redirect: jest.fn(),
      };
      (Utility.getRoute as jest.Mock).mockReturnValue({ action: 'register-email', page: 'registerEmailPage' });
      jest.spyOn(authService, 'getTermsAndConditions').mockResolvedValue({
        termsAndConditions: '<p>Terms</p>',
        styles: '.css',
        source: 'test-source',
      });
    });

    it('should redirect if ssoCookie is present', async () => {
      req.cookies.ssoCookieName = 'sso-token';
      await controller.registerEmail(req, res);
      expect(mockCookieHelper.clearCookie).toHaveBeenCalledWith(res, 'ssoCookieName');
      expect(res.redirect).toHaveBeenCalledWith('/auth/register');
    });

    it('should render register email page if ssoCookie is not present', async () => {
      const result = await controller.registerEmail(req, res);
      expect(mockCookieHelper.clearCookie).not.toHaveBeenCalled();
      expect(res.redirect).not.toHaveBeenCalled();
      expect(result).toEqual({
        token: '',
        validationErrors: [],
        route: { action: 'register-email', page: 'registerEmailPage' },
        registerEmailMessages: { message: 'Register email message' },
        dataPassToView: {},
        termsAndConditions: '<p>Terms</p>',
        termsStyles: '.css',
      });
    });

    it('should handle error when fetching terms and conditions', async () => {
      jest.spyOn(authService, 'getTermsAndConditions').mockResolvedValue(null);
      const result = await controller.registerEmail(req, res);
      expect(result.termsAndConditions).toBe('');
      expect(result.termsStyles).toBe('');
    });
  });

  describe('POST /register-email - signup', () => {
    let req: any;
    let res: any;
    let signupDetail: any;

    beforeEach(() => {
      req = { cookies: {} };
      res = { redirect: jest.fn() };
      signupDetail = {
        redirect_url: 'https://redirect.com',
        country_data: JSON.stringify({ code: 'US', timeZone: 'America/New_York' }),
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'John',
        last_name: 'Doe',
        termCondition: true,
        phone_no: '1234567890',
      };
      jest.spyOn(authService, 'signUp').mockResolvedValue('https://signup-success.com');
    });

    it('should successfully sign up and redirect', async () => {
      await controller.signup(signupDetail, res, req);
      expect(authService.signUp).toHaveBeenCalledWith(
        expect.objectContaining({
          email: '<EMAIL>',
          password: 'password123',
        }),
        'https://redirect.com',
        req.cookies,
        res,
      );
      expect(res.redirect).toHaveBeenCalledWith('https://signup-success.com');
    });

    it('should throw BadRequestException for UserAlreadyExist', async () => {
      jest.spyOn(authService, 'signUp').mockRejectedValue(new Error('UserAlreadyExist'));
      await expect(controller.signup(signupDetail, res, req)).rejects.toThrow(
        'Email address already exists, please choose a different one',
      );
    });

    it('should throw BadRequestException for other errors', async () => {
      jest.spyOn(authService, 'signUp').mockRejectedValue(new Error('SomeOtherError'));
      await expect(controller.signup(signupDetail, res, req)).rejects.toThrow(
        'Some error occurred while user registration.',
      );
    });

    it('should throw BadRequestException if country_data is invalid', async () => {
      signupDetail.country_data = 'invalid-json';
      await expect(controller.signup(signupDetail, res, req)).rejects.toThrow(BadRequestException);
    });

    it('should log error and throw BadRequestException on general failure', async () => {
      jest.spyOn(authService, 'signUp').mockRejectedValue(new Error('General failure'));
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation();

      await expect(controller.signup(signupDetail, res, req)).rejects.toThrow(BadRequestException);
      expect(loggerSpy).toHaveBeenCalledWith(
        'signup',
        expect.objectContaining({
          METHOD: expect.stringContaining('signup'),
          MESSAGE: 'General failure',
          REQUEST: { signupDetail },
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        }),
      );
      loggerSpy.mockRestore();
    });
  });

  describe('GET /register-complete - registerComplete', () => {
    const mockLinkData = {
      email: '<EMAIL>',
      type: 'b2c'
    };

    beforeEach(() => {
      // Set configService to return cookie key
      (configService.get as jest.Mock).mockImplementation(key => {
        if (key === 'linkData') return 'linkData';
        return '';
      });

      // Always return valid route
      (Utility.getRoute as jest.Mock).mockReturnValue({ action: 'register-complete', page: 'registerCompletePage' });
    });

    it('should return route, linkData, and terms when linkData cookie is present', async () => {
      const encodedLinkData = JSON.stringify(mockLinkData);

      mockRequest.cookies = {
        linkData: encodedLinkData,
      };

      const mockTermsResponse = {
        termsAndConditions: '<p>Terms</p>',
        styles: '.css',
        source: 'test-source',
      };

      jest.spyOn(authService, 'getTermsAndConditions').mockResolvedValue(mockTermsResponse);
      (Utility.isEmpty as jest.Mock).mockReturnValue(false);

      const result = await controller.registerComplete(mockRequest);

      expect(result).toEqual({
        token: '',
        route: { action: 'register-complete', page: 'registerCompletePage' },
        validationErrors: [],
        email: mockLinkData.email,
        type: mockLinkData.type,
        termsAndConditions: '<p>Terms</p>',
        termsStyles: '.css',
        termsSource: 'test-source',
      });
    });

    it('should default to empty string when terms content is missing', async () => {
      mockRequest.cookies = {
        linkData: JSON.stringify(mockLinkData),
      };

      jest.spyOn(authService, 'getTermsAndConditions').mockResolvedValue(null);
      (Utility.isEmpty as jest.Mock).mockReturnValue(false);

      const result = await controller.registerComplete(mockRequest);

      expect(result.termsAndConditions).toBe('');
      expect(result.termsStyles).toBe('');
      expect(result.termsSource).toBe('unknown');
    });

    it('should throw BadRequestException if linkData is empty or invalid', async () => {
      mockRequest.cookies = {
        linkData: '{}',
      };

      (Utility.isEmpty as jest.Mock).mockReturnValue(true);

      await expect(controller.registerComplete(mockRequest))
        .rejects
        .toThrow(BadRequestException);
    });

    it('should throw BadRequestException if cookie is not present at all', async () => {
      mockRequest.cookies = {}; // Missing cookie

      await expect(controller.registerComplete(mockRequest))
        .rejects
        .toThrow(BadRequestException);
    });
  });

  describe('POST /register-complete - registerCompletePost', () => {
    const formData = {
      email: '<EMAIL>',
      password: 'testpass123',
    } as any;

    const parsedLinkData = {
      email: '<EMAIL>',
      type: 'b2c',
    };

    beforeEach(() => {
      (configService.get as jest.Mock).mockImplementation((key) => {
        if (key === 'linkData') return 'linkData';
        if (key === 'page') return 'page';
        return '';
      });

      (Utility.isEmpty as jest.Mock).mockImplementation((val) => {
        return (
          val === undefined ||
          val === null ||
          val === '' ||
          (Array.isArray(val) && val.length === 0) ||
          (typeof val === 'object' && Object.keys(val).length === 0)
        );
      });

      mockRequest.cookies = {
        linkData: JSON.stringify(parsedLinkData),
        page: 'register',
      };

      jest.spyOn(mockCookieHelper, 'clearCookie').mockImplementation(() => { });
      const registerCompletePost: any = {
        data: 'https://redirect.com',
      }
      jest.spyOn(authService, 'registerCompletePost').mockResolvedValue(registerCompletePost);
    });

    it('should clear cookies and redirect on successful registration', async () => {
      await controller.registerCompletePost(formData, mockResponse, mockRequest);

      expect(mockCookieHelper.clearCookie).toHaveBeenCalledWith(mockResponse, 'page');
      expect(mockCookieHelper.clearCookie).toHaveBeenCalledWith(mockResponse, 'linkData');

      expect(authService.registerCompletePost).toHaveBeenCalledWith(
        mockRequest.cookies,
        mockResponse,
        formData,
        parsedLinkData,
      );

      expect(mockResponse.redirect).toHaveBeenCalledWith('https://redirect.com');
    });

    it('should not clear page cookie if origin is not "register"', async () => {
      mockRequest.cookies.page = 'login'; // origin is not 'register'

      await controller.registerCompletePost(formData, mockResponse, mockRequest);

      expect(mockCookieHelper.clearCookie).not.toHaveBeenCalledWith(mockResponse, 'page');
      expect(mockCookieHelper.clearCookie).toHaveBeenCalledWith(mockResponse, 'linkData');
    });

    it('should throw BadRequestException if cookie value is empty after parsing', async () => {
      mockRequest.cookies.linkData = JSON.stringify({}); // empty object

      await expect(controller.registerCompletePost(formData, mockResponse, mockRequest))
        .rejects
        .toThrow(BadRequestException);
    });

    it('should throw BadRequestException if cookie is malformed JSON', async () => {
      mockRequest.cookies.linkData = 'not-a-json';

      await expect(controller.registerCompletePost(formData, mockResponse, mockRequest))
        .rejects
        .toThrow(BadRequestException);
    });

    it('should throw BadRequestException if linkData cookie is missing', async () => {
      delete mockRequest.cookies.linkData;

      await expect(controller.registerCompletePost(formData, mockResponse, mockRequest))
        .rejects
        .toThrow(BadRequestException);
    });

    it('should throw BadRequestException if authService.registerCompletePost fails', async () => {
      jest.spyOn(authService, 'registerCompletePost').mockRejectedValue(new Error('service failure'));

      await expect(controller.registerCompletePost(formData, mockResponse, mockRequest))
        .rejects
        .toThrow(BadRequestException);
    });
  });

  describe('POST /multi-account - multiAccountPost', () => {
    const lmsInfo = { url: 'https://lms.com', gid: '42' };
    const mockUser = { uid: 123, email: '<EMAIL>' };
    let req: any;
    let res: any;

    beforeEach(() => {
      req = { user: mockUser };
      res = { redirect: jest.fn() };
    });

    it('should call authService.redirectMultiAccount and redirect', async () => {
      const redirectUrl = 'https://redirect-multi.com';
      jest.spyOn(authService, 'redirectMultiAccount').mockResolvedValue(redirectUrl);

      await controller.multiAccountPost(lmsInfo, res, req);

      expect(authService.redirectMultiAccount).toHaveBeenCalledWith(lmsInfo, mockUser);
      expect(res.redirect).toHaveBeenCalledWith(redirectUrl);
    });

    it('should throw BadRequestException if authService.redirectMultiAccount fails', async () => {
      jest.spyOn(authService, 'redirectMultiAccount').mockRejectedValue(new BadRequestException('fail'));

      await expect(controller.multiAccountPost(lmsInfo, res, req)).rejects.toThrow(BadRequestException);
    });
  });

  describe('GET /multi-account - multiAccount', () => {
    const queryParam = { url: 'https://lms.com', gid: '42', calendar_url: 'https://calendar.com' };
    const mockUser = { uid: 123, email: '<EMAIL>', name: 'Test User' };
    let req: any;
    let res: any;

    beforeEach(() => {
      req = { user: mockUser, route: { path: '/multi-account' } };
      res = { redirect: jest.fn() };
      (Utility.getRoute as jest.Mock).mockReturnValue({ action: 'multi-account', page: 'multiAccountPage' });
    });

    it('should return response from authService.getMultiAccountRedirectionList', async () => {
      const mockResponse: any = { items: [{ id: 1 }], redirectUrl: 'https://redirect.com' };
      jest.spyOn(authService, 'getMultiAccountRedirectionList').mockResolvedValue(mockResponse);

      const result = await controller.multiAccount(queryParam, res, req);

      expect(authService.getMultiAccountRedirectionList).toHaveBeenCalledWith(
        queryParam,
        mockUser.name,
        { action: 'multi-account', page: 'multiAccountPage' },
        res,
        mockUser,
      );
      expect(res.redirect).not.toHaveBeenCalled();
      expect(result).toBe(mockResponse);
    });

    it('should redirect if items array is empty', async () => {
      const mockResponse: any = { items: [], redirectUrl: 'https://redirect.com' };
      jest.spyOn(authService, 'getMultiAccountRedirectionList').mockResolvedValue(mockResponse);

      const result = await controller.multiAccount(queryParam, res, req);

      expect(res.redirect).toHaveBeenCalledWith('https://redirect.com');
      expect(result).toBe(mockResponse);
    });

    it('should log error and not throw if exception occurs', async () => {
      const error = new Error('test error');
      jest.spyOn(authService, 'getMultiAccountRedirectionList').mockRejectedValue(error);
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation();

      const result = await controller.multiAccount(queryParam, res, req);

      expect(result).toBeUndefined();
      loggerSpy.mockRestore();
    });
  });

  describe('GET /forgot-password - forgotPasswordRender', () => {
    it('should render forgot password page and set/clear cookies', async () => {
      const mockRoute = { action: 'forgot-password', page: 'forgotPasswordPage' };
      (Utility.getRoute as jest.Mock).mockReturnValue(mockRoute);

      mockRequest.route = { path: '/forgot-password' };
      mockRequest.params = { some: 'param' };
      mockRequest.cookies = { forgotPasswordInfo: { email: '', isSent: false }, token: 'expired-token' };

      const cookieHelper = await helperService.getHelper('CookieHelper');
      jest.spyOn(cookieHelper, 'setCookie').mockResolvedValue(undefined);
      jest.spyOn(cookieHelper, 'clearCookie').mockImplementation(() => { });

      const result = await controller.forgotPasswordRender(mockRequest, mockResponse);

      expect(Utility.getRoute).toHaveBeenCalledWith(mockRequest.route);
      expect(cookieHelper.setCookie).toHaveBeenCalledWith(mockResponse, 'forgotPasswordInfo', { email: '', isSent: false });
      expect(cookieHelper.clearCookie).toHaveBeenCalledWith(mockResponse, 'token');
      expect(result).toEqual({
        token: '',
        validationErrors: ['Invalid password reset URL OR it is expired.'],
        route: mockRoute,
        params: { params: mockRequest.params, ...mockRequest.cookies['forgotPasswordInfo'] },
      });
    });

    it('should render forgot password page with empty validation error if no token', async () => {
      const mockRoute = { action: 'forgot-password', page: 'forgotPasswordPage' };
      (Utility.getRoute as jest.Mock).mockReturnValue(mockRoute);

      mockRequest.route = { path: '/forgot-password' };
      mockRequest.params = {};
      mockRequest.cookies = { forgotPasswordInfo: { email: '', isSent: false } };

      const cookieHelper = await helperService.getHelper('CookieHelper');
      jest.spyOn(cookieHelper, 'setCookie').mockResolvedValue(undefined);
      jest.spyOn(cookieHelper, 'clearCookie').mockImplementation(() => { });

      const result = await controller.forgotPasswordRender(mockRequest, mockResponse);

      expect(result).toEqual({
        token: '',
        validationErrors: [''],
        route: mockRoute,
        params: { params: mockRequest.params, ...mockRequest.cookies['forgotPasswordInfo'] },
      });
    });
  });

  describe('POST /forgot-password - forgotPassword', () => {
    const forgotPasswordDetails = { email: '<EMAIL>' };
    let cookieHelper: any;

    beforeEach(async () => {
      cookieHelper = await helperService.getHelper('CookieHelper');
      jest.spyOn(cookieHelper, 'setCookie').mockResolvedValue(undefined);
      jest.spyOn(mockLrsHelper, 'sendDataToLrs').mockImplementation(() => { });
      (configService.get as jest.Mock).mockImplementation((key) => {
        if (key === 'fpResetWaitTime') return 15;
        if (key === 'fpResetCount') return 5;
        if (key === 'ssoClientId') return 'sso-client';
        return '';
      });
    });

    it('should set cookie and redirect on successful forgot password', async () => {
      jest.spyOn(authService, 'forgetPassword').mockResolvedValue(true);

      const result = await controller.forgotPassword(forgotPasswordDetails, mockResponse);

      expect(authService.forgetPassword).toHaveBeenCalledWith(forgotPasswordDetails.email);
      expect(cookieHelper.setCookie).toHaveBeenCalledWith(
        mockResponse,
        'forgotPasswordInfo',
        { email: forgotPasswordDetails.email, isSent: true }
      );
      expect(mockResponse.redirect).toHaveBeenCalledWith('/auth/forgot-password');
      expect(result).toBe(mockResponse.redirect());
    });

    it('should throw BadRequestException for UserNotFoundException', async () => {
      jest.spyOn(authService, 'forgetPassword').mockResolvedValue(new Error('UserNotFoundException'));

      await expect(controller.forgotPassword(forgotPasswordDetails, mockResponse))
        .rejects
        .toThrow(new BadRequestException('Please make sure the email address entered is correct.', {
          description: JSON.stringify({ userNotFound: true }),
        }));
    });

    it('should throw BadRequestException for AttemptLimitExceed', async () => {
      jest.spyOn(authService, 'forgetPassword').mockResolvedValue(new Error('AttemptLimitExceed'));

      await expect(controller.forgotPassword(forgotPasswordDetails, mockResponse))
        .rejects
        .toThrow(
          new BadRequestException(
            `Attempt limit exceeded. Please try again after 15 minutes.'`,
            {
              description: JSON.stringify({
                attemptLimitExceed: true,
                limit: 5,
                waitTime: 15,
              }),
            }
          )
        );
    });

    it('should throw BadRequestException for UserDisabled', async () => {
      jest.spyOn(authService, 'forgetPassword').mockResolvedValue(new Error('UserDisabled'));

      await expect(controller.forgotPassword(forgotPasswordDetails, mockResponse))
        .rejects
        .toThrow(new BadRequestException('Try using a different email address to create an account.'));
    });

    it('should throw BadRequestException for unknown error', async () => {
      jest.spyOn(authService, 'forgetPassword').mockResolvedValue(new Error('SomeOtherError'));

      await expect(controller.forgotPassword(forgotPasswordDetails, mockResponse))
        .rejects
        .toThrow(new BadRequestException('Invalid Request.'));
    });

    it('should throw BadRequestException if exception is thrown', async () => {
      jest.spyOn(authService, 'forgetPassword').mockRejectedValue(new Error('Unexpected'));

      await expect(controller.forgotPassword(forgotPasswordDetails, mockResponse))
        .rejects
        .toThrow(BadRequestException);
    });
  });

  describe('GET /reset/:uid/:token - reset', () => {
    let cacheService: any;
    let cookieHelper: any;
    let cryptoHelper: any;
    let req: any;
    let res: any;
    let params: any;

    beforeEach(async () => {
      cacheService = { get: jest.fn(), del: jest.fn() };
      cookieHelper = {
        clearCookie: jest.fn(),
        setCookie: jest.fn(),
      };
      cryptoHelper = { decrypt: jest.fn() };

      req = {
        cookies: {},
        route: { path: '/reset/:uid/:token' },
        params: { uid: 'encryptedUid', token: 'reset-token' },
      };
      res = { redirect: jest.fn() };
      params = { uid: 'encryptedUid', token: 'reset-token' };

      jest.spyOn(helperService, 'get').mockImplementation(token => {
        if (token === CachingService) return Promise.resolve(cacheService);
        return Promise.resolve(undefined);
      });
      jest.spyOn(helperService, 'getHelper').mockImplementation(token => {
        if (token === 'CookieHelper') return Promise.resolve(cookieHelper);
        return Promise.resolve(undefined);
      });

      // Assign the mock cryptoHelper to the controller so it is used in the reset method
      (controller as any).cryptoHelper = cryptoHelper;

      (Utility.getRoute as jest.Mock).mockReturnValue({ action: 'reset', page: 'resetPage' });
    });

    it('should redirect to forgot-password if token does not match', async () => {
      cryptoHelper.decrypt.mockReturnValue('decryptedUid');
      cacheService.get.mockResolvedValue('expected-token');
      req.cookies = { decryptedUid: true };
      params.token = 'wrong-token';

      await controller.reset(params, req, res);

      expect(cryptoHelper.decrypt).toHaveBeenCalledWith('encryptedUid');
      expect(cacheService.get).toHaveBeenCalledWith('decryptedUid');
      expect(cookieHelper.clearCookie).toHaveBeenCalledWith(res, 'decryptedUid');
      expect(cookieHelper.setCookie).toHaveBeenCalledWith(res, 'token', 'expected-token');
      expect(res.redirect).toHaveBeenCalledWith('/auth/forgot-password');
    });

    it('should return reset page data if token matches and key cookie exists', async () => {
      cryptoHelper.decrypt.mockReturnValue('decryptedUid');
      cacheService.get.mockResolvedValue('reset-token');
      req.cookies = { token_encryptedUid: true };
      params.token = 'reset-token';

      const result = await controller.reset(params, req, res);

      expect(cryptoHelper.decrypt).toHaveBeenCalledWith('encryptedUid');
      expect(cacheService.get).toHaveBeenCalledWith('decryptedUid');
      expect(cookieHelper.clearCookie).toHaveBeenCalledWith(res, 'token_encryptedUid');
      expect(cacheService.del).toHaveBeenCalledWith('decryptedUid');
      expect(result).toEqual({
        params: { ...req.params, token: 'reset-token' },
        validationErrors: [],
        route: { action: 'reset', page: 'resetPage' },
        status: true,
      });
    });

    it('should return reset page data with status false if key cookie does not exist', async () => {
      cryptoHelper.decrypt.mockReturnValue('decryptedUid');
      cacheService.get.mockResolvedValue('reset-token');
      req.cookies = {};
      params.token = 'reset-token';

      const result = await controller.reset(params, req, res);

      expect(cryptoHelper.decrypt).toHaveBeenCalledWith('encryptedUid');
      expect(cacheService.get).toHaveBeenCalledWith('decryptedUid');
      expect(cookieHelper.clearCookie).toHaveBeenCalledWith(res, 'token_encryptedUid');
      expect(result).toEqual({
        params: { ...req.params, token: 'reset-token' },
        validationErrors: [],
        route: { action: 'reset', page: 'resetPage' },
        status: false,
      });
    });
  });

  describe('POST /reset/:uid/:token - resetPost', () => {
    let cookieHelper: any;
    let req: any;
    let res: any;
    let params: any;
    let body: any;

    beforeEach(async () => {
      cookieHelper = {
        setCookie: jest.fn(),
      };
      req = { params: { uid: 'encryptedUid', token: 'reset-token' } };
      res = { redirect: jest.fn() };
      params = { uid: 'encryptedUid', token: 'reset-token' };
      body = { password: 'newPassword123' };

      jest.spyOn(helperService, 'getHelper').mockImplementation(token => {
        if (token === 'CookieHelper') return Promise.resolve(cookieHelper);
        return Promise.resolve(undefined);
      });
    });

    it('should set cookie and redirect on successful password reset', async () => {
      jest.spyOn(authService, 'resetPassword').mockResolvedValue(true);

      await controller.resetPost(body, params, req, res);

      expect(authService.resetPassword).toHaveBeenCalledWith(body, params);
      expect(cookieHelper.setCookie).toHaveBeenCalledWith(res, 'token_encryptedUid', true);
      expect(res.redirect).toHaveBeenCalledWith('/auth/reset/encryptedUid/reset-token');
    });

    it('should redirect even if resetPassword returns falsy', async () => {
      jest.spyOn(authService, 'resetPassword').mockResolvedValue(false);

      await controller.resetPost(body, params, req, res);

      expect(cookieHelper.setCookie).not.toHaveBeenCalled();
      expect(res.redirect).toHaveBeenCalledWith('/auth/reset/encryptedUid/reset-token');
    });

    it('should throw BadRequestException if resetPassword throws', async () => {
      jest.spyOn(authService, 'resetPassword').mockRejectedValue(new Error('fail'));

      await expect(controller.resetPost(body, params, req, res))
        .rejects
        .toThrow(BadRequestException);
    });
  });

  describe('GET /reset-password/:userId/:requestTime/:requestToken - resetPassword', () => {
    it('should log and render reset password page', async () => {
      const loggerSpy = jest.spyOn(Logger, 'log').mockImplementation();
      // Simulate calling the controller method
      if (controller.resetPassword) {
        const result = await controller.resetPassword();
        expect(result).toBeUndefined();
      }
      loggerSpy.mockRestore();
    });
  });

  describe('GET /process-apple-auth-response-register - processAppleAuthResponseRegister', () => {
    it('should log and render process apple auth response register page', async () => {
      const loggerSpy = jest.spyOn(Logger, 'log').mockImplementation();
      if (controller.processAppleAuthResponseRegister) {
        const result = await controller.processAppleAuthResponseRegister();
        expect(result).toBeUndefined();
      }
      loggerSpy.mockRestore();
    });
  });

  describe('GET /process-apple-auth-response-login - processAppleAuthResponseLogin', () => {
    it('should log and render process apple auth response login page', async () => {
      const loggerSpy = jest.spyOn(Logger, 'log').mockImplementation();
      if (controller.processAppleAuthResponseLogin) {
        const result = await controller.processAppleAuthResponseLogin();
        expect(result).toBeUndefined();
      }
      loggerSpy.mockRestore();
    });
  });

  describe('GET /process-social-auth-response - processSocialAuthResponse', () => {
    it('should log and render process social auth response page', async () => {
      const loggerSpy = jest.spyOn(Logger, 'log').mockImplementation();
      if (controller.processSocialAuthResponse) {
        const result = await controller.processSocialAuthResponse();
        expect(result).toBeUndefined();
      }
      loggerSpy.mockRestore();
    });
  });

  describe('GET /create-user-social-account-details - createUserSocialAccountDetails', () => {
    it('should log and render create user social account details page', async () => {
      const loggerSpy = jest.spyOn(Logger, 'log').mockImplementation();
      if (controller.createUserSocialAccountDetails) {
        const result = await controller.createUserSocialAccountDetails();
        expect(result).toBeUndefined();
      }
      loggerSpy.mockRestore();
    });
  });

  describe('GET /create-user-account - createUserAccount', () => {
    it('should log and render create user account page', async () => {
      const loggerSpy = jest.spyOn(Logger, 'log').mockImplementation();
      if (controller.createUserAccount) {
        const result = await controller.createUserAccount();
        expect(result).toBeUndefined();
      }
      loggerSpy.mockRestore();
    });
  });

  describe('GET /social-link - socialLinkRender', () => {
    let req: any;

    beforeEach(() => {
      req = {
        route: { path: '/social-link' },
        cookies: {},
      };
      (configService.get as jest.Mock).mockImplementation((key) => {
        if (key === 'linkData') return 'linkData';
        return '';
      });
      (Utility.getRoute as jest.Mock).mockReturnValue({ action: 'social-link', page: 'socialLinkPage' });
    });

    it('should return user info if linkData cookie is valid', async () => {
      const linkData = { email: '<EMAIL>', type: 'social', code: 123 };
      req.cookies.linkData = JSON.stringify(linkData);
      (Utility.isEmpty as jest.Mock).mockReturnValue(false);

      const result = await controller.socialLinkRender(req);

      expect(result).toEqual({
        route: { action: 'social-link', page: 'socialLinkPage' },
        validationErrors: [],
        user: { email: '<EMAIL>', type: 'social' },
        code: 123,
      });
    });

    it('should return default error if linkData is empty', async () => {
      req.cookies.linkData = JSON.stringify({});
      (Utility.isEmpty as jest.Mock).mockReturnValue(true);

      const result = await controller.socialLinkRender(req);

      expect(result).toEqual({
        route: { action: 'social-link', page: 'socialLinkPage' },
        validationErrors: ['Something went wrong, please try again.'],
        user: { email: '', type: '' },
        code: undefined,
      });
    });

    it('should return default error if linkData is missing', async () => {
      req.cookies = {};
      (Utility.isEmpty as jest.Mock).mockReturnValue(true);

      const result = await controller.socialLinkRender(req);

      expect(result).toEqual({
        route: { action: 'social-link', page: 'socialLinkPage' },
        validationErrors: ['Something went wrong, please try again.'],
        user: { email: '', type: '' },
        code: undefined,
      });
    });

    it('should return default error if linkData is malformed', async () => {
      req.cookies.linkData = 'not-a-json';

      const result = await controller.socialLinkRender(req);

      expect(result).toEqual({
        route: { action: 'social-link', page: 'socialLinkPage' },
        validationErrors: ['Something went wrong, please try again.'],
        user: { email: '', type: '' },
        code: undefined,
      });
    });
  });

  describe('POST /social-link - socialLink', () => {
    let req: any;
    let res: any;
    let linkData: any;

    beforeEach(() => {
      req = {
        cookies: {},
        body: {},
      };
      res = {
        redirect: jest.fn(),
      };
      linkData = { email: '<EMAIL>', type: 'social', code: 123 };
      (configService.get as jest.Mock).mockImplementation((key) => {
        if (key === 'linkData') return 'linkData';
        return '';
      });
    });

    it('should redirect on successful social link', async () => {
      req.cookies.linkData = JSON.stringify(linkData);
      (Utility.isEmpty as jest.Mock).mockReturnValue(false);
      (Utility.isValidEmail as jest.Mock).mockReturnValue(true);

      const mockResponse: any = { status: true, data: { redirectUrl: 'https://redirect.com' } };
      jest.spyOn(authService, 'socialLink').mockResolvedValue(mockResponse);

      await controller.socialLink(req, res);

      expect(authService.socialLink).toHaveBeenCalledWith(req.cookies, res, linkData);
      expect(res.redirect).toHaveBeenCalledWith('https://redirect.com');
    });

    it('should throw BadRequestException if linkData is empty', async () => {
      req.cookies.linkData = JSON.stringify({});
      (Utility.isEmpty as jest.Mock).mockReturnValue(true);

      await expect(controller.socialLink(req, res)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if email is invalid', async () => {
      req.cookies.linkData = JSON.stringify(linkData);
      (Utility.isEmpty as jest.Mock).mockReturnValue(false);
      (Utility.isValidEmail as jest.Mock).mockReturnValue(false);

      await expect(controller.socialLink(req, res)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if socialLink returns status false', async () => {
      req.cookies.linkData = JSON.stringify(linkData);
      (Utility.isEmpty as jest.Mock).mockReturnValue(false);
      (Utility.isValidEmail as jest.Mock).mockReturnValue(true);

      const mockResponse: any = { status: false, data: {} };
      jest.spyOn(authService, 'socialLink').mockResolvedValue(mockResponse);

      await expect(controller.socialLink(req, res)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if linkData is malformed', async () => {
      req.cookies.linkData = 'not-a-json';

      await expect(controller.socialLink(req, res)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if exception occurs', async () => {
      req.cookies.linkData = JSON.stringify(linkData);
      (Utility.isEmpty as jest.Mock).mockReturnValue(false);
      (Utility.isValidEmail as jest.Mock).mockReturnValue(true);

      jest.spyOn(authService, 'socialLink').mockRejectedValue(new Error('fail'));

      await expect(controller.socialLink(req, res)).rejects.toThrow(BadRequestException);
    });
  });

  describe('GET /validate-login - validateLogin', () => {
    it('should log and render validate login page', async () => {
      const loggerSpy = jest.spyOn(Logger, 'log').mockImplementation();
      if (controller.validateLogin) {
        const result = await controller.validateLogin();
        expect(result).toBeUndefined();
      }
      loggerSpy.mockRestore();
    });
  });

  describe('GET /manage-auth-redirect - manageAuthRedirect', () => {
    let req: any;
    let res: any;
    let queryParam: any;

    beforeEach(() => {
      req = { cookies: {} };
      res = { redirect: jest.fn() };
      queryParam = { token: 'test-token' };
    });

    it('should call authService.manageAuthRedirect and redirect', async () => {
      const redirectUrl = 'https://redirect.com';
      jest.spyOn(authService, 'manageAuthRedirect').mockResolvedValue(redirectUrl);

      await controller.manageAuthRedirect(req, queryParam, '123', 'https://url.com', res);

      expect(authService.manageAuthRedirect).toHaveBeenCalledWith(req.cookies, res, 'test-token', '123');
      expect(res.redirect).toHaveBeenCalledWith(redirectUrl);
    });

    it('should log error if exception occurs', async () => {
      const error = new Error('fail');
      jest.spyOn(authService, 'manageAuthRedirect').mockRejectedValue(error);

      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation();

      await controller.manageAuthRedirect(req, queryParam, '123', 'https://url.com', res);

      expect(loggerSpy).toHaveBeenCalledWith(
        'manageAuthRedirect',
        expect.objectContaining({
          METHOD: expect.stringContaining('manageAuthRedirect'),
          MESSAGE: 'fail',
          REQUEST: expect.any(Object),
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        }),
      );

      loggerSpy.mockRestore();
    });
  });

  describe('GET /logout - logout', () => {
    let req: any;
    let res: any;
    let userRepository: any;
    let authTokenHelper: any;
    let enterpriseService: any;
    let authHelper: any;
    let cacheService: any;
    let cookieHelper: any;
    let lrsHelper: any;
    let userObj: any;

    beforeEach(async () => {
      userObj = { uid: 123, user_groups: [10] };
      req = {
        user: { uid: 123 },
        cookies: { ssoCookieName: 'sso-token' },
      };
      res = { redirect: jest.fn() };

      userRepository = { findByUID: jest.fn().mockResolvedValue(userObj) };
      authTokenHelper = { decodeJWTToken: jest.fn().mockResolvedValue({ data: { lgid: 10 } }) };
      enterpriseService = { getGroupByGid: jest.fn().mockResolvedValue({ data: [{ homePageUrl: 'https://homepage.com', saml_sp_initiated: 0 }] }) };
      authHelper = { getSubDomain: jest.fn().mockResolvedValue('subdomain') };
      cacheService = { invalidateCache: jest.fn() };
      cookieHelper = { clearCookie: jest.fn() };
      lrsHelper = { sendDataToLrs: jest.fn() };

      jest.spyOn(helperService, 'get').mockImplementation(token => {
        if (token === UserRepository) return Promise.resolve(userRepository);
        if (token === EnterpriseService) return Promise.resolve(enterpriseService);
        if (token === CachingService) return Promise.resolve(cacheService);
        return Promise.resolve(undefined);
      });
      jest.spyOn(helperService, 'getHelper').mockImplementation(token => {
        if (token === AuthTokenHelper) return Promise.resolve(authTokenHelper);
        if (token === 'AuthHelper') return Promise.resolve(authHelper);
        if (token === 'CookieHelper') return Promise.resolve(cookieHelper);
        if (token === 'lrsHelper') return Promise.resolve(lrsHelper);
        return Promise.resolve(undefined);
      });
      jest.spyOn(controller, 'userSignout').mockResolvedValue(undefined);
      jest.spyOn(controller, 'logoutSimpleSAML').mockResolvedValue(undefined);
      (configService.get as jest.Mock).mockImplementation((key) => {
        if (key === 'ssoCookie') return 'ssoCookieName';
        if (key === 'ssoClientId') return 'sso-client';
        if (key === 'issuer') return 'https://auth.domain.com';
        if (key === 'ic9SiteUrl') return 'https://ic9.com';
        return '';
      });
      (Utility.isValidUrl as jest.Mock).mockReturnValue(true);
    });

    it('should clear cache, cookies, log LRS, and redirect to homePageUrl if valid', async () => {
      await controller.logout(res, req);

      expect(userRepository.findByUID).toHaveBeenCalledWith(123);
      expect(authTokenHelper.decodeJWTToken).toHaveBeenCalledWith('sso-token');
      expect(enterpriseService.getGroupByGid).toHaveBeenCalledWith(10);
      expect(lrsHelper.sendDataToLrs).toHaveBeenCalled();
      expect(cacheService.invalidateCache).toHaveBeenCalledWith('sc_123', null, 0);
      expect(cookieHelper.clearCookie).toHaveBeenCalledWith(res, 'ssoCookieName');
      expect(controller.userSignout).toHaveBeenCalledWith(res);
      expect(res.redirect).toHaveBeenCalledWith('https://homepage.com');
    });

    it('should redirect to SAML logout if saml_sp_initiated is 1', async () => {
      enterpriseService.getGroupByGid.mockResolvedValue({ data: [{ homePageUrl: '', saml_sp_initiated: 1 }] });
      await controller.logout(res, req);

      expect(authHelper.getSubDomain).toHaveBeenCalledWith(10);
      expect(controller.logoutSimpleSAML).toHaveBeenCalledWith(
        req.cookies,
        res,
        'https://auth.domain.com/saml/service-provider/sp-initiated-sls/title/subdomain'
      );
    });

    it('should redirect to ic9SiteUrl if no valid homePageUrl and not SAML', async () => {
      enterpriseService.getGroupByGid.mockResolvedValue({ data: [{ homePageUrl: '', saml_sp_initiated: 0 }] });
      await controller.logout(res, req);

      expect(res.redirect).toHaveBeenCalledWith('https://ic9.com');
    });

    it('should handle user with no groups', async () => {
      userObj.user_groups = [];
      await controller.logout(res, req);

      expect(res.redirect).toHaveBeenCalledWith('https://ic9.com');
    });

    it('should log error and throw if exception occurs', async () => {
      userRepository.findByUID.mockRejectedValue(new Error('fail'));
      let LoggerMock: any = () => { };
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation(LoggerMock);

      await expect(controller.logout(res, req)).rejects.toThrow('fail');
      expect(loggerSpy).toHaveBeenCalledWith(
        'logout',
        expect.objectContaining({
          METHOD: expect.stringContaining('logout'),
          MESSAGE: 'fail',
          REQUEST: expect.any(Object),
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        }),
      );
      loggerSpy.mockRestore();
    });
  });

  describe('userSignout', () => {
    let res: any;
    let cookieHelper: any;

    beforeEach(async () => {
      res = {};
      cookieHelper = {
        setBulkCookie: jest.fn().mockResolvedValue(undefined),
      };
      jest.spyOn(helperService, 'getHelper').mockImplementation(token => {
        if (token === 'CookieHelper') return Promise.resolve(cookieHelper);
        return Promise.resolve(undefined);
      });
      (configService.get as jest.Mock).mockImplementation((key) => {
        if (key === 'ssoCookie') return 'ssoCookieName';
        if (key === 'slCookie') return 'slCookieName';
        return '';
      });
    });

    it('should call setBulkCookie with correct cookies', async () => {
      await controller.userSignout(res);
      expect(cookieHelper.setBulkCookie).toHaveBeenCalledWith(
        res,
        ['ssoCookieName', 'slCookieName'],
        { domain: null, expires: -1, path: '/' }
      );
    });

    it('should log error and throw BadRequestException if setBulkCookie fails', async () => {
      cookieHelper.setBulkCookie.mockRejectedValue(new Error('fail'));
      let LoggerMock: any = () => { };
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation(LoggerMock);
      await expect(controller.userSignout(res)).rejects.toThrow('Failed to clear cookies during signout.');
      expect(loggerSpy).toHaveBeenCalledWith(
        'userSignout',
        expect.objectContaining({
          METHOD: expect.stringContaining('userSignout'),
          MESSAGE: 'fail',
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        }),
      );
      loggerSpy.mockRestore();
    });
  });

  describe('logoutSimpleSAML', () => {
    const res = { redirect: jest.fn() } as any;

    it('should clear SAML cookies and redirect to logoutURL', async () => {
      const userCookies = {
        SimpleSAMLAuthToken: 'auth-token-value',
        SimpleSAML: 'saml-token-value',
      };

      await controller.logoutSimpleSAML(userCookies, res, 'https://logout.url');

      expect(mockCookieHelper.setCookie).toHaveBeenCalledWith(res, 'SimpleSAMLAuthToken', null, -1, '/');
      expect(mockCookieHelper.setCookie).toHaveBeenCalledWith(res, 'SimpleSAML', null, -1, '/');
      expect(res.redirect).toHaveBeenCalledWith('https://logout.url');
    });

    it('should handle missing cookies gracefully and still redirect', async () => {
      const userCookies = {}; // no cookies

      await controller.logoutSimpleSAML(userCookies, res, 'https://logout.url');

      expect(mockCookieHelper.setCookie).not.toHaveBeenCalled();
      expect(res.redirect).toHaveBeenCalledWith('https://logout.url');
    });

    it('should log error and throw BadRequestException if error occurs', async () => {
      const userCookies = {
        SimpleSAML: 'some-token'
      };

      // Force cookieHelper to throw an error
      mockCookieHelper.setCookie.mockImplementationOnce(() => {
        throw new Error('Mock failure');
      });

      let loggerMock: any = () => { };
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation(loggerMock);

      await expect(
        controller.logoutSimpleSAML(userCookies, res, 'https://logout.url')
      ).rejects.toThrow(BadRequestException);

      expect(loggerSpy).toHaveBeenCalledWith('logoutSimpleSAML', expect.objectContaining({
        METHOD: expect.stringContaining('logoutSimpleSAML'),
        MESSAGE: 'Mock failure',
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));

      loggerSpy.mockRestore();
    });
  });

  describe('GET /account-setup - accountSetUp', () => {
    let req: any;
    let res: any;
    let queryParam: any;

    beforeEach(() => {
      req = {
        route: { path: '/account-setup' },
        cookies: {},
      };

      res = {
        redirect: jest.fn(),
      };

      queryParam = {
        userEmail: '<EMAIL>',
      };

      (Utility.getRoute as jest.Mock).mockReturnValue({ action: 'account-setup', page: 'accountSetupPage' });
    });

    it('should return validation error if email is invalid', async () => {
      (Utility.isValidEmail as jest.Mock).mockReturnValue(false);

      const result = await controller.accountSetUp(queryParam, req, res);

      expect(result).toEqual({
        token: '',
        validationErrors: ['Unauthorized email address'],
        route: { action: 'account-setup', page: 'accountSetupPage' },
        dataPassToView: {},
      });
    });

    it('should redirect if accountSetUp response status is true', async () => {
      (Utility.isValidEmail as jest.Mock).mockReturnValue(true);
      authService.getTermsAndConditions = jest.fn().mockResolvedValue({
        termsAndConditions: '<p>Terms</p>',
        styles: 'some-style',
      });
      authService.accountSetUp = jest.fn().mockResolvedValue({
        status: true,
        redirectUrl: 'https://redirect.url',
      });

      await controller.accountSetUp(queryParam, req, res);

      expect(res.redirect).toHaveBeenCalledWith('https://redirect.url');
    });

    it('should return view with errors if accountSetUp response is false', async () => {
      (Utility.isValidEmail as jest.Mock).mockReturnValue(true);
      authService.getTermsAndConditions = jest.fn().mockResolvedValue({
        termsAndConditions: '<p>Terms</p>',
        styles: 'some-style',
      });
      authService.accountSetUp = jest.fn().mockResolvedValue({
        status: false,
        msg: 'invalidLink',
        data: { name: 'Test User' },
      });

      (configService.get as jest.Mock).mockImplementation((key) => {
        if (key === 'invalidLink') return 'Invalid link provided.';
        return '';
      });

      const result = await controller.accountSetUp(queryParam, req, res);

      expect(result).toEqual({
        token: '',
        validationErrors: ['Invalid link provided.'],
        route: { action: 'account-setup', page: 'accountSetupPage' },
        dataPassToView: {
          name: 'Test User',
          userEmail: '<EMAIL>',
        },
        termsAndConditions: '<p>Terms</p>',
        termsStyles: 'some-style',
      });
    });

    it('should throw BadRequestException on failure', async () => {
      (Utility.isValidEmail as jest.Mock).mockReturnValue(true);
      authService.getTermsAndConditions = jest.fn().mockRejectedValue(new Error('Boom!'));

      let loggerMock: any = () => { };
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation(loggerMock);

      await expect(controller.accountSetUp(queryParam, req, res)).rejects.toThrow(BadRequestException);

      expect(loggerSpy).toHaveBeenCalledWith('accountSetUp', expect.objectContaining({
        METHOD: expect.stringContaining('accountSetUp'),
        MESSAGE: 'Boom!',
        REQUEST: { queryParam },
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));

      loggerSpy.mockRestore();
    });
  });

  describe('POST /account-setup - submitAccountSetUp', () => {
    let req: any;
    let res: any;
    let accountSetupInfo: any;

    beforeEach(() => {
      req = {
        route: { path: '/account-setup' },
      };

      res = {
        redirect: jest.fn(),
      };

      accountSetupInfo = {
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        password: 'StrongP@ssw0rd',
      };

      (Utility.getRoute as jest.Mock).mockReturnValue({
        action: 'account-setup',
        page: 'accountSetupPage',
      });
    });

    it('should call accountSetupPostOperations and redirect on success', async () => {
      authService.accountSetupPostOperations = jest.fn().mockResolvedValue({
        status: true,
        data: { redirect_url: 'https://dashboard.com' },
      });

      await controller.submitAccountSetUp(accountSetupInfo, req, res);

      expect(authService.accountSetupPostOperations).toHaveBeenCalledWith(res, accountSetupInfo);
      expect(res.redirect).toHaveBeenCalledWith('https://dashboard.com');
    });

    it('should return validationErrors if status is false', async () => {
      authService.accountSetupPostOperations = jest.fn().mockResolvedValue({
        status: false,
        msg: 'Invalid referral code',
      });

      const result = await controller.submitAccountSetUp(accountSetupInfo, req, res);

      expect(result).toEqual({
        token: '',
        route: { action: 'account-setup', page: 'accountSetupPage' },
        validationErrors: ['Invalid referral code'],
      });
    });

    it('should log error and throw BadRequestException if exception is thrown', async () => {
      const error = new Error('DB connection failed');

      authService.accountSetupPostOperations = jest.fn().mockRejectedValue(error);

      let LoggerMock: any = () => { };
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation(LoggerMock);

      await expect(controller.submitAccountSetUp(accountSetupInfo, req, res)).rejects.toThrow(BadRequestException);

      expect(loggerSpy).toHaveBeenCalledWith('submitAccountSetUp', expect.objectContaining({
        METHOD: expect.stringContaining('submitAccountSetUp'),
        MESSAGE: 'DB connection failed',
        REQUEST: { accountSetupInfo },
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));

      loggerSpy.mockRestore();
    });
  });

  describe('Social Callback Handlers', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    const mockUser = { id: 1, email: '<EMAIL>' };
    const mockResponseText = '<html>Success</html>';
    const mockError = new Error('Something went wrong');

    const setupMocks = (method: string) => {
      mockRequest.user = mockUser;
      mockRequest.cookies = { page: 'originValue' };
      mockResponse.send = jest.fn();
      mockResponse.setHeader = jest.fn();

      jest.spyOn(authService, 'getUserIdpInfo').mockResolvedValue(mockResponseText);
      jest.spyOn(authService, 'socialLoginRedirect').mockReturnValue('redirect-url');
      let mock: any = () => { };
      jest.spyOn(Logger, 'error').mockImplementation(mock);

      if (method === 'google') {
        jest.spyOn(helperService, 'getHelper').mockResolvedValue({}); // SocialLoginHelper
      }
    };

    const testSuccessCase = (method: string, handler: () => Promise<void>) => {
      it(`should return HTML response on successful ${method}Callback`, async () => {
        setupMocks(method);
        await handler();
        expect(mockResponse.setHeader).toHaveBeenCalledWith('Content-Type', 'text/html');
        expect(authService.getUserIdpInfo).toHaveBeenCalledWith(mockUser, 'originValue', mockResponse);
        expect(mockResponse.send).toHaveBeenCalledWith(mockResponseText);
      });
    };

    const testFailureCase = (method: string, handler: () => Promise<void>) => {
      it(`should log error and return redirect URL on ${method}Callback failure`, async () => {
        setupMocks(method);
        jest.spyOn(authService, 'getUserIdpInfo').mockRejectedValue(mockError);
        await handler();
        expect(Logger.error).toHaveBeenCalledWith(`${method}Callback`, expect.objectContaining({
          METHOD: expect.stringContaining(`${method}Callback`),
          MESSAGE: mockError.message,
          REQUEST: mockUser,
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        }));
        expect(mockResponse.send).toHaveBeenCalledWith('redirect-url');
      });
    };

    // LinkedIn
    testSuccessCase('linkedin', () => controller.linkedinCallback(mockRequest, mockResponse));
    testFailureCase('linkedin', () => controller.linkedinCallback(mockRequest, mockResponse));

    // Google
    testSuccessCase('google', () => controller.googleCallback(mockRequest, mockResponse));
    testFailureCase('google', () => controller.googleCallback(mockRequest, mockResponse));

    // Facebook
    testSuccessCase('facebook', () => controller.facebookCallback(mockRequest, mockResponse));
    testFailureCase('facebook', () => controller.facebookCallback(mockRequest, mockResponse));

    // Apple
    testSuccessCase('apple', () => controller.appleCallback(mockRequest, mockResponse));
    testFailureCase('apple', () => controller.appleCallback(mockRequest, mockResponse));
  });

  describe('Social OAuth Login Endpoints', () => {
    let controller: AuthController;

    beforeEach(async () => {
      const moduleRef = await Test.createTestingModule({
        controllers: [AuthController],
        providers: [
          {
            provide: AuthService,
            useValue: {},
          },
          {
            provide: ConfigService,
            useValue: {
              get: jest.fn(),
            },
          },
          {
            provide: HelperService,
            useValue: {},
          },
          {
            provide: 'CRYPTO_HELPER',
            useValue: {},
          },
        ],
      }).compile();

      controller = moduleRef.get(AuthController);
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should log message on LinkedIn login', async () => {
      const loggerSpy = jest.spyOn(Logger, 'info').mockImplementation();
      await controller.linkedinLogin();
      expect(loggerSpy).toHaveBeenCalledWith('Initiating LinkedIn login');
    });

    it('should log message on Facebook login', async () => {
      const loggerSpy = jest.spyOn(Logger, 'info').mockImplementation();
      await controller.facebookLogin();
      expect(loggerSpy).toHaveBeenCalledWith('Initiating facebook login');
    });

    it('should log message on Google login', async () => {
      const loggerSpy = jest.spyOn(Logger, 'info').mockImplementation();
      await controller.googleLogin();
      expect(loggerSpy).toHaveBeenCalledWith('Initiating google login');
    });

    it('should log message on Apple login', async () => {
      const loggerSpy = jest.spyOn(Logger, 'info').mockImplementation();
      await controller.appleLogin();
      expect(loggerSpy).toHaveBeenCalledWith('Initiating apple login');
    });
  });

});
