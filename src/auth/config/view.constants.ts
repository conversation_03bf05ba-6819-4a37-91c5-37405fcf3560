const VIEW_PAGES = {
  REGISTER_EMAIL: 'pages/registerEmail',
  LOGIN: 'pages/login',
  REGISTER_COMPLETE: 'pages/register-complete',
  MULTI_ACCOUNT: 'pages/multi-account',
  FORGET_PASSWORD: 'pages/forgot-password',
  RESET: 'pages/reset',
  RESET_PASSWORD: 'pages/reset-password',
  PROCESS_APPLE_AUTH_RESPONSE_REGISTER: 'pages/process-apple-auth-response-register',
  PROCESS_APPLE_AUTH_RESPONSE_LOGIN: 'pages/process-apple-auth-response-login',
  PROCESS_SOCIAL_AUTH_RESPONSE: 'pages/process-social-auth-response',
  CREATE_USER_SOCIAL_ACCOUNT_DETAILS: 'pages/create-user-social-account-details',
  CREATE_USER_ACCOUNT: 'pages/create-user-account',
  SOCIAL_LINK: 'pages/social-link',
  VALIDATE_LOGIN: 'pages/validate-login',
  MANA<PERSON>_AUTH_REDIRECT: 'pages/manage-auth-redirect',
  MAIN: 'layout/main',
  USER: 'layout/user',
  CHANGEPASSWORD: 'layout/changepassword',
  REDIRECT: '/auth/redirect',
  ERROR: 'pages/saml/error',
  MOBILE_SUCCESS: 'pages/saml/mobileSuccess',
  MOBILE_ERROR: 'pages/saml/mobileError',
  VIEW_ROUTES: {
    USERS: {
      LOGIN: '/auth/login',
      REGISTER_EMAIL: '/auth/register-email',
      REGISTER: '/auth/register',
      FORGOT_PASSWORD: '/auth/forgot-password',
      RESET_PASSWORD: '/auth/reset',
      SOCIAL_LINK: '/auth/social-link',
      REGISTER_COMPLETE: '/auth/register-complete',
      MULTIACCOUNT: '/auth/multi-account',
      SOCIAL_SETUP: '/auth/account-setup',
    },
    PROFILES: {
      PROFILE: '/user/profile',
    },
    CHANGEPASSWORD: {
      CHANGE_PASSWORD: '/user/profile/change-password',
    },
  },
  SIMPLILEARN_DEFAULT_COMPANY_LOGO: '/../frontend/images/default-company.svg',
  SIMPLILEARN_DEFAULT_LOGO: '/../frontend/images/simplilearn-logo.png',
};
export { VIEW_PAGES };
