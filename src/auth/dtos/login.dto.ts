import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEmail } from 'class-validator';

export class LoginDto {
  @ApiProperty({ default: '<EMAIL>' })
  @IsNotEmpty()
  @IsEmail({}, { message: 'Please enter a valid email address.' })
  email: string;

  @ApiProperty({ default: 'Simpli@12345' })
  @IsNotEmpty()
  @IsString()
  password: string;

  calendar_url?: string;
  redirect_url?: string;
  assignmentToken?: string;
  isB2BAndB2C?: boolean;
  domainGid?: string;
  urlDomain?: string;
}
