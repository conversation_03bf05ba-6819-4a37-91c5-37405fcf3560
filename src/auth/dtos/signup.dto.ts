import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEmail } from 'class-validator';
import { IntegerType } from 'mongodb';
import { IsNameValid } from '../../common/validators/name.validator';
import { IsPasswordValid } from '../../common/validators/password.validator';

export class SignUpDto {
  @ApiProperty({ default: 'Test', description: 'First name' })
  @IsNotEmpty({ message: 'First name is required' })
  @IsString({ message: 'First name must be a string' })
  @IsNameValid('First Name')
  readonly first_name: string;

  @ApiProperty({ default: 'user', description: 'Last name' })
  @IsNotEmpty({ message: 'Last name is required' })
  @IsString({ message: 'Last name must be a string' })
  @IsNameValid('Last Name')
  readonly last_name: string;

  @ApiProperty({ default: '<EMAIL>', description: 'Email' })
  @IsNotEmpty({ message: 'Email is required' })
  @IsEmail({}, { message: 'Please enter a valid email address.' })
  email: string;

  @ApiProperty({ default: '9009123453', description: 'Phone number' })
  @IsNotEmpty({ message: 'Phone number is required' })
  readonly phone_no: string;

  @ApiProperty({ default: 'Simpli@12345', description: 'Password' })
  @IsNotEmpty({ message: 'Password is required' })
  @IsString({ message: 'Password must be a string' })
  @IsPasswordValid()
  password: string;

  @ApiProperty({ default: 'Y' })
  @IsNotEmpty()
  termCondition: string;

  @ApiProperty({ default: 'IN' })
  @IsNotEmpty()
  country_code: string;

  @ApiProperty({ default: false })
  isB2bStudent: boolean;

  @ApiProperty({ default: '' })
  b2bLmsUrl: string;

  @ApiProperty({ default: false })
  // @IsNotEmpty()
  isPhoneMandatoryFrontend: boolean;

  @ApiProperty({ default: false })
  // @IsNotEmpty()
  showPhoneFrontend: boolean;

  @ApiProperty({ default: 'AccountSetupEmail' })
  // @IsNotEmpty()
  pageSource: string;

  @ApiProperty({ default: 'http://dockerv2.simplilearn.com:8609/apachedev/git/paperclip/public' })
  // @IsNotEmpty()
  redirect_url: string;

  @ApiProperty({ default: 2 })
  // @IsNotEmpty()
  groupId: IntegerType;
}
