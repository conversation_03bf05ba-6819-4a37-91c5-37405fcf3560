import { IsS<PERSON>, <PERSON>NotEmpty, IsOptional } from 'class-validator';

export class AuthenticationRequestDto {
  @IsNotEmpty({ message: 'Unauthorized access request.' })
  @IsString({ message: 'Unauthorized access request.' })
  client_id: string;

  @IsOptional()
  redirect_url?: string;

  @IsOptional()
  from_mobile?: string | number;

  @IsOptional()
  device_type?: string;

  @IsNotEmpty({ message: 'Please provide user login.' })
  user_login: string;

  @IsNotEmpty({ message: 'Please provide user password.' })
  user_pwd: string;

  @IsOptional()
  app_type?: string;
}
