import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, IsNotEmpty, IsEmail } from 'class-validator';

export class RegisterCompleteDto {
  @ApiProperty({ default: 'Test', description: 'First name' })
  @IsNotEmpty({ message: 'First name is required' })
  @IsString({ message: 'First name must be a string' })
  @Transform(({ value }) => value.trim())
  readonly first_name: string;

  @ApiProperty({ default: 'user', description: 'Last name' })
  @IsNotEmpty({ message: 'Last name is required' })
  @IsString({ message: 'Last name must be a string' })
  @Transform(({ value }) => value.trim())
  readonly last_name: string;

  @ApiProperty({ default: '<EMAIL>', description: 'Email' })
  @IsNotEmpty({ message: 'Email is required' })
  @IsEmail({}, { message: 'Invalid email format' })
  @Transform(({ value }) => value.trim())
  email: string;

  @ApiProperty({ default: '9009123453', description: 'Phone number' })
  @IsNotEmpty({ message: 'Phone number is required' })
  @Transform(({ value }) => value.trim())
  readonly phone_no: string;

  @ApiProperty({ default: 'Y' })
  @IsNotEmpty()
  termCondition: string;

  @ApiProperty({ default: 'IN' })
  @IsNotEmpty()
  @Transform(({ value }) => value.trim())
  country_code: string;
}
