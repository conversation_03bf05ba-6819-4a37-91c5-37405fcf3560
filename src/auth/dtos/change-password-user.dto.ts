import { Validate } from 'class-validator';
import { PasswordsMatchConstraint } from '../../common/validators/passwords-match-validator';
import { IsPasswordValid } from '../../common/validators/password.validator';
import { PasswordNotSameConstraint } from '../../common/validators/password-not-same.validator';

export class ChangePasswordUserDto {
 
  @IsPasswordValid()
  cur_passwd: string;

  @Validate(PasswordNotSameConstraint, ['cur_passwd'])
  @IsPasswordValid()
  new_passwd: string;

  @Validate(PasswordsMatchConstraint, ['new_passwd'])
  confirm_passwd: string;
}
