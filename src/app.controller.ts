import { Controller, Get, Redirect } from '@nestjs/common';
import { AppService } from './app.service';
import { Logger } from './logging/logger';
import { ApiExcludeEndpoint } from '@nestjs/swagger';

@Controller()
export class AppController {
  // Injecting the AppService dependency
  constructor(
    private readonly appService: AppService,
  ) {}

  /**
   * Redirects the root URL to the login page.
   * Logs the redirection for debugging purposes.
   */
  @Get()
  @Redirect('auth/login', 302)
  @ApiExcludeEndpoint()
  redirectTo(): void {
    Logger.info('Redirecting to login page');
  }

  /**
   * Health check endpoint to verify the service is running.
   * Returns a string response from the AppService.
   */
  @Get('/health')
  @ApiExcludeEndpoint()
  healthCheck(): string {
    return this.appService.healthCheck();
  }
}
