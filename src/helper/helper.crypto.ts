import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Logger } from '../logging/logger';
import { createCipheriv, createDecipheriv, createHash, createHmac } from 'crypto';

enum HASHTYPE {
  STANDARD_HASH = 'Standard',
  HMAC = 'HMAC',
}

export class CryptoException extends Error {
  constructor(message: string, private readonly innerEx?: Error) {
    super(message);
    Object.setPrototypeOf(this, new.target.prototype);
    Logger.error(message, this.innerEx);
  }
}

function hash_impl(type: HASHTYPE, input: string, key?: string, algo = 'sha256', encoding?: BufferEncoding): string {
  let hashed;
  try {
    if (type === HASHTYPE.HMAC) {
      hashed = createHmac(algo, key);
    } else {
      hashed = createHash(algo);
    }
    hashed.update(input);
    if (encoding !== undefined) {
      return Buffer.from(hashed.digest()).toString(encoding);
    }
    return hashed.digest().toString();
  } catch (error: any) {
    throw new CryptoException(`Failed to generate ${type} hash - ${error.message}`, error);
  }
}

const encrypt_decrypt_impl = (
  encrypt: boolean,
  input: string,
  algo: string,
  key: string,
  iv: string,
  encoding?: BufferEncoding,
): string => {
  let cipher;
  let sourceEnc;
  let targetEnc;
  let output;
  try {
    if (encrypt) {
      cipher = createCipheriv(algo, key, iv);
      sourceEnc = 'utf-8';
      targetEnc = encoding;
    } else {
      cipher = createDecipheriv(algo, key, iv);
      sourceEnc = encoding;
      targetEnc = 'utf-8';
    }
    if (encoding !== undefined && encrypt) {
      output = cipher.update(input, sourceEnc, targetEnc);
      output += cipher.final(targetEnc);
      output = Buffer.from(output, 'utf-8').toString('base64');
    } else {
      const data = Buffer.from(input, 'base64').toString('utf-8');
      output = cipher.update(data, sourceEnc);
      output += cipher.final('utf-8');
    }
    return output;
  } catch (error: any) {
    const action = encrypt ? 'Encryption' : 'Decryption';
    const message = `${action} failed - ${error.message}`;
    throw new CryptoException(message, error);
  }
};

@Injectable()
export class CryptoHelper implements OnModuleInit {
  private __key: string;
  private __iv: string;
  private __algo: string;

  constructor(private readonly cryptoConfig: any) {}
  onModuleInit() {
    try {
      this.__algo = this.cryptoConfig.algo;
      const hashKey = createHash('sha256');
      hashKey.update(this.cryptoConfig.key);
      this.__key = hashKey.digest('hex').substring(0, 32);
      const hashIv = createHash('sha256');
      hashIv.update(this.cryptoConfig.iv);
      this.__iv = hashIv.digest('hex').substring(0, 16);
    } catch (e) {
      Logger.error('Failed to initialize cryptography keys');
    }
  }

  hash = (input: string, algo: string, encoding?: BufferEncoding): string =>
    hash_impl(HASHTYPE.STANDARD_HASH, input, undefined, algo, encoding);

  createHmac = (input: string, key: string, algo: string, encoding?: BufferEncoding): string =>
    hash_impl(HASHTYPE.HMAC, input, key, algo, encoding);

  encrypt = (data: string, encoding: BufferEncoding = 'base64'): string =>
    encrypt_decrypt_impl(true, data, this.__algo, this.__key, this.__iv, encoding);

  decrypt = (data: string, encoding: BufferEncoding = 'base64'): string =>
    encrypt_decrypt_impl(false, data, this.__algo, this.__key, this.__iv, encoding);

  encryptDecrypt(type: string, input: string) {
    let output = '';
    if (type === 'encrypt') {
      output = this.encrypt(input);
    } else if (type === 'decrypt') {
      output = this.decrypt(input);
    }
    return output;
  }
}

export const CryptoProviders = [
  {
    provide: 'CRYPTO_HELPER',
    useFactory: (configService: ConfigService) => {
      const config = configService.get('crypt');
      return new CryptoHelper(config);
    },
    inject: [ConfigService],
  },
  {
    provide: 'TRIBE_CRYPTO_HELPER',
    useFactory: (configService: ConfigService) => {
      const config = configService.get('tribecrypt');
      return new CryptoHelper(config);
    },
    inject: [ConfigService],
  },
];
