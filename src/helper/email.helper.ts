import { Inject } from '@nestjs/common';
import { EmailService } from '../common/services/email/email.service';
import { HelperService } from './helper.service';
import { Logger } from '../logging/logger';
export class EmailHelper {
  @Inject() private helperService: HelperService;
  async sendEmail(mailTo: string, templateIdentifier: string, mailTokens: object): Promise<boolean> {
    try {
      const mailParams = {
        mailTo: mailTo,
        mailIdentifier: templateIdentifier,
        mailTokens: mailTokens,
      };
      const emailService = await this.helperService.get<EmailService>(EmailService);
      return await emailService.sendEmail(mailParams);
    } catch (error: any) {
      Logger.error('Send Email', {
        METHOD: this.constructor.name + '@' + this.sendEmail.name,
        MESSAGE: error.message,
        REQUEST: { mailTo, templateIdentifier, mailTokens },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
  getMailTokens(param) {
    if (param?.gid) {
      return {
        gid: param?.gid,
        affiliateLmsUrl: param?.affiliateLmsUrl,
        affiliateLogoUrl: param?.affiliateLogoUrl,
        resetPasswordUrl: `${param?.affiliateLmsUrl}/affiliate/user/reset-password-v1`,
        planType: param?.planType,
        providerName: param?.providerName,
        affiliateName: param?.affiliateName,
        isWhiteLabelingEnabled: param.isWhiteLabelingEnabled ? true : false,
        fromEmailAddress: param.fromEmailAddress,
        displayName: param.displayName,
      };
    }
  }
}
