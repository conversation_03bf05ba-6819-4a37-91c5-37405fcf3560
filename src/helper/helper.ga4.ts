import axios from 'axios';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';
import { Logger } from '../logging/logger';

@Injectable()
export class GoogleAnalytics {
  @Inject() private readonly configService: ConfigService;
  async sendEvents(clientID = '', data) {
    if (clientID === '') {
      clientID = this.generateUniqueID(); // Implement your own unique ID generation logic
    }

    if (!data.hasOwnProperty('user_id')) {
      throw new Error('Please provide user_id');
    }
    const { user_id, ...eventsData } = data;
    let sl_freemium_user = false;
    if (eventsData.hasOwnProperty('sl_freemium_user')) {
      sl_freemium_user = eventsData.sl_freemium_user;
      delete eventsData.sl_freemium_user;
    }

    const payload = {
      clientId: clientID,
      user_id: user_id,
      sl_freemium_user: sl_freemium_user,
      events: [eventsData],
    };
    const config = this.configService.get('cdp').ga4;
    try {
      const response = await axios.post(
        `${config.ga4APIUrl}?api_secret=${config.ga4APISecret}&measurement_id=${config.ga4MeasurementId}`,
        payload,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );
      Logger.log('Google Analytics response:', response.data);
      return response.data;
    } catch (error: any) {
      Logger.error('Error sending events:', error);
      throw error;
    }
  }
  generateUniqueID(): string {
    return uuidv4();
  }
}
