import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HelperService } from './helper.service';
import { <PERSON>ieHelper } from './cookie.helper';
import { Logger } from '../logging/logger';
import { Utility } from '../common/util/utility';
import { ResponseCookieType } from '../common/typeDef/auth.type';
import { VIEW_PAGES } from '../auth/config/view.constants';

// Mock dependencies
jest.mock('../logging/logger');
jest.mock('../common/util/utility');

describe('CookieHelper', () => {
  let cookieHelper: CookieHelper;
  let configService: ConfigService;
  let helperService: HelperService;

  const mockResponse = () => {
    const res: any = {};
    res.cookie = jest.fn().mockReturnValue(res);
    res.clearCookie = jest.fn().mockReturnValue(res);
    return res;
  };

  const mockRequest = (cookies: object) => ({
    cookies,
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CookieHelper,
        { provide: ConfigService, useValue: { get: jest.fn() } },
        { provide: HelperService, useValue: { get: jest.fn(), getHelper: jest.fn() } },
      ],
    }).compile();

    cookieHelper = module.get<CookieHelper>(CookieHelper);
    configService = module.get<ConfigService>(ConfigService);
    helperService = module.get<HelperService>(HelperService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(cookieHelper).toBeDefined();
  });

  describe('setCookie', () => {
    it('should set a cookie with merged options', async () => {
      const res = mockResponse();
      (configService.get as jest.Mock).mockReturnValue('example.com');

      await cookieHelper.setCookie(res, 'test', 'value', { path: '/custom' });

      expect(res.cookie).toHaveBeenCalledWith(
        'test',
        'value',
        expect.objectContaining({ path: '/custom', domain: 'example.com' }),
      );
    });
  });

  describe('clearCookieIfExists', () => {
    it('should clear a cookie if it exists', () => {
      const res = mockResponse();
      const req = mockRequest({ testCookie: 'exists' });
      (configService.get as jest.Mock).mockReturnValue('example.com');

      cookieHelper.clearCookieIfExists(res, req, 'testCookie');

      expect(res.clearCookie).toHaveBeenCalledWith('testCookie', {
        httpOnly: true,
        domain: 'example.com',
      });
    });

    it('should not clear a cookie if it does not exist', () => {
      const res = mockResponse();
      const req = mockRequest({});
      cookieHelper.clearCookieIfExists(res, req, 'testCookie');
      expect(res.clearCookie).not.toHaveBeenCalled();
    });
  });

  describe('clearCookie', () => {
    it('should call res.clearCookie with correct parameters', () => {
      const res = mockResponse();
      (configService.get as jest.Mock).mockReturnValue('example.com');
      cookieHelper.clearCookie(res, 'testKey');
      expect(res.clearCookie).toHaveBeenCalledWith('testKey', {
        httpOnly: true,
        domain: 'example.com',
      });
    });
  });

  describe('getCalendarCookie', () => {
    it('should set calendar cookie and return the url', async () => {
      const res = mockResponse();
      const calendarUrl = 'http://test.com';
      (configService.get as jest.Mock).mockImplementation((key) => {
        if (key === 'calendarUrl') return 'http://base.url/?redirect=';
        if (key === 'calendarRedirect') return 'calendar_redirect_cookie';
        if (key === 'cookieMaxAge') return 3600000;
      });

      const result = await cookieHelper.getCalendarCookie(calendarUrl, res);

      const encodedUrl = encodeURIComponent(calendarUrl);
      expect(result).toBe(`http://base.url/?redirect=${encodedUrl}`);
      expect(res.cookie).toHaveBeenCalled();
    });

    it('should log error if setCookie fails', async () => {
      const res = mockResponse();
      (configService.get as jest.Mock).mockImplementation(() => {
        throw new Error('Config error');
      });

      await cookieHelper.getCalendarCookie('http://test.com', res);
      expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('setCalenderCookie', () => {
    it('should set a calendar cookie if url is provided', async () => {
      const res = mockResponse();
      (configService.get as jest.Mock).mockReturnValue('calendar_cookie');
      await cookieHelper.setCalenderCookie('http://test.com', res);
      expect(res.cookie).toHaveBeenCalled();
    });

    it('should not set a calendar cookie if url is empty', async () => {
      const res = mockResponse();
      await cookieHelper.setCalenderCookie('', res);
      expect(res.cookie).not.toHaveBeenCalled();
    });
  });

  describe('setFermiumCookie', () => {
    it('should set a freemium cookie if token is provided', async () => {
      const res = mockResponse();
      (configService.get as jest.Mock).mockReturnValue('freemium_cookie');
      await cookieHelper.setFermiumCookie('token', res);
      expect(res.cookie).toHaveBeenCalled();
    });

    it('should not set a freemium cookie if token is empty', async () => {
      const res = mockResponse();
      await cookieHelper.setFermiumCookie('', res);
      expect(res.cookie).not.toHaveBeenCalled();
    });

    it('should log error if setCookie fails', async () => {
        const res = mockResponse();
        (configService.get as jest.Mock).mockImplementation(() => {
          throw new Error('Config error');
        });
  
        await cookieHelper.setFermiumCookie('token', res);
        expect(Logger.error).toHaveBeenCalled();
      });
  });

  describe('setNpsCookie', () => {
    it('should set NPS cookie for a valid URL', async () => {
      const res = mockResponse();
      const url = 'http://valid.nps.url';
      (Utility.validateNpsSessionUrl as jest.Mock).mockReturnValue(true);
      (Utility.isEmpty as jest.Mock).mockReturnValue(false);
      (helperService.get as jest.Mock).mockResolvedValue({ createSignedToken: jest.fn().mockResolvedValue('token') });

      await cookieHelper.setNpsCookie(url, res);
      expect(res.cookie).toHaveBeenCalled();
    });

    it('should not set NPS cookie for an invalid URL', async () => {
      const res = mockResponse();
      (Utility.validateNpsSessionUrl as jest.Mock).mockReturnValue(false);
      (Utility.isEmpty as jest.Mock).mockReturnValue(false);

      await cookieHelper.setNpsCookie('http://invalid.url', res);
      expect(res.cookie).not.toHaveBeenCalled();
    });
  });

  describe('setCommunityCookie', () => {
    it('should set community cookie for a valid URL', async () => {
      const res = mockResponse();
      const url = 'http://valid.community.url';
      (Utility.validateCommunityUrl as jest.Mock).mockReturnValue(true);
      (Utility.isEmpty as jest.Mock).mockReturnValue(false);
      (helperService.get as jest.Mock).mockResolvedValue({ createSignedToken: jest.fn().mockResolvedValue('token') });

      await cookieHelper.setCommunityCookie(url, res);
      expect(res.cookie).toHaveBeenCalled();
    });

    it('should handle tribe community URL correctly', async () => {
        const res = mockResponse();
        const url = 'http://tribe.community.url';
        (Utility.validateCommunityUrl as jest.Mock).mockReturnValue(true);
        (Utility.isEmpty as jest.Mock).mockReturnValue(false);
        (Utility.isTribeCommunityUrl as jest.Mock).mockReturnValue(true);
        const createSignedToken = jest.fn().mockResolvedValue('token');
        (helperService.get as jest.Mock).mockResolvedValue({ createSignedToken });
  
        await cookieHelper.setCommunityCookie(url, res);
  
        expect(createSignedToken).toHaveBeenCalledWith({ url, isTribeRedirect: true }, expect.any(Object));
        expect(res.cookie).toHaveBeenCalled();
      });

    it('should log error if an exception occurs', async () => {
      const res = mockResponse();
      const url = 'http://valid.community.url';
      (Utility.validateCommunityUrl as jest.Mock).mockReturnValue(true);
      (Utility.isEmpty as jest.Mock).mockReturnValue(false);
      (helperService.get as jest.Mock).mockRejectedValue(new Error('Token error'));

      await cookieHelper.setCommunityCookie(url, res);
      expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('getRedirectUrlFromCookie', () => {
    it('should return tribe SSO link for tribe community URL', async () => {
      const redirectUrls = { redirect_url: 'http://tribe.url', calendar_url: '' };
      const userTokenDetail = { id: 'id', email: 'email', name: 'name' };
      (Utility.isTribeCommunityUrl as jest.Mock).mockReturnValue(true);
      (helperService.getHelper as jest.Mock).mockResolvedValue({ decrypt: (val) => val });
      (helperService.get as jest.Mock).mockResolvedValue({ getTribeSSOLink: jest.fn().mockResolvedValue('http://sso.tribe.url') });

      const result = await cookieHelper.getRedirectUrlFromCookie(redirectUrls, userTokenDetail);
      expect(result).toBe('http://sso.tribe.url');
    });

    it('should return calendar URL if present', async () => {
        const redirectUrls = { redirect_url: '', calendar_url: 'http://calendar.url' };
        (Utility.isTribeCommunityUrl as jest.Mock).mockReturnValue(false);
        (configService.get as jest.Mock).mockReturnValue('http://base.calendar.url?q=');
        const result = await cookieHelper.getRedirectUrlFromCookie(redirectUrls, {} as any);
        expect(result).toContain('http%3A%2F%2Fcalendar.url');
      });

    it('should return profile page as default', async () => {
        (Utility.isTribeCommunityUrl as jest.Mock).mockReturnValue(false);
      const result = await cookieHelper.getRedirectUrlFromCookie({ redirect_url: '', calendar_url: '' }, {} as any);
      expect(result).toBe(VIEW_PAGES.VIEW_ROUTES.PROFILES.PROFILE);
    });

    it('should log error on failure', async () => {
        (Utility.isTribeCommunityUrl as jest.Mock).mockImplementation(() => {
          throw new Error('Test Error');
        });
        await cookieHelper.getRedirectUrlFromCookie({ redirect_url: 'http://tribe.url', calendar_url: '' }, {} as any);
        expect(Logger.error).toHaveBeenCalled();
      });
  });

  describe('setBulkCookie', () => {
    it('should call setCookie for each cookie in the array', () => {
      const res = mockResponse();
      const cookies: ResponseCookieType[] = [{ name: 'c1', value: 'v1' }, { name: 'c2', value: 'v2' }];
      const setCookieSpy = jest.spyOn(cookieHelper, 'setCookie');
      (configService.get as jest.Mock).mockImplementation((key) => key);

      cookieHelper.setBulkCookie(res, cookies, {});

      expect(setCookieSpy).toHaveBeenCalledTimes(2);
      expect(setCookieSpy).toHaveBeenCalledWith(res, 'c1', 'v1', {});
      expect(setCookieSpy).toHaveBeenCalledWith(res, 'c2', 'v2', {});
    });
  });

  describe('clearBulkCookie', () => {
    it('should call clearCookie for each key in the array', () => {
      const res = mockResponse();
      const keys = ['c1', 'c2'];
      const clearCookieSpy = jest.spyOn(cookieHelper, 'clearCookie');

      cookieHelper.clearBulkCookie(res, keys);

      expect(clearCookieSpy).toHaveBeenCalledTimes(2);
      expect(clearCookieSpy).toHaveBeenCalledWith(res, 'c1');
      expect(clearCookieSpy).toHaveBeenCalledWith(res, 'c2');
    });
  });
});