import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Logger } from '../logging/logger';
import * as md5 from 'md5';
import { LrsService } from '../common/services/communication/lrs/lrs.service';
import { KafkaService } from '../common/services/communication/kafka/kafka.service';
import { CryptoHelper } from '../helper/helper.crypto';

interface LrsData {
  verb: string;
  objectType: string;
  objectId: string;
  dataVals: string[];
}
@Injectable()
export class LrsHelper {
  @Inject('CRYPTO_HELPER') private readonly crypto: CryptoHelper;
  protected verbTopicName = {
    'forgot-password': [],
    login: ['_lrs_rest.login'],
    logout: [],
    register: ['_rest.learners'],
    'register-by-email': ['_rest.learners'],
    'reset-password': [],
    'set-learner-password': [],
    unlockcertificate: ['_lrs_rest.cert'],
    visit: ['_lrs_rest.visit'],
    accountsetup: ['_rest.learners'],
  };

  constructor(
    private readonly configService: ConfigService,
    private readonly lrsService: LrsService,
    private readonly kafkaService: KafkaService,
  ) {}

  async sendDataToLrs(userData: any, lrsData: LrsData, groupData?: any): Promise<any> {
    try {
      // Validate input data
      // if (
      //     !Array.isArray(userData) ||
      //     !Array.isArray(lrsData)
      // ) {
      //     return {
      //         type: 'error',
      //         msg: 'Invalid input data for LRS.',
      //     };
      // }

      let appId = '';
      let appName = '';
      if (groupData?.gid && typeof groupData?.gid === 'number') {
        appId = groupData.gid;
        appName = `group_${appId}`;

        if (groupData.groupName && typeof groupData.groupName === 'string') {
          appName = groupData.groupName;
        }
      }
      const varLrsData = this._getLrsData(userData, lrsData, appId, appName);
      /**
       * If LRS enabled
       *      Send data to LRS
       */
      const lrsEnabled = this.configService.get<string>('lrsEnabled');

      if (lrsEnabled && lrsEnabled == '1') {
        // Use the LrsService to send data
        this.lrsService.sendLrsData(varLrsData);
      }

      /**
       * If Topic name set
       *      Send data to kafka topic
       */
      const topics = this.verbTopicName[varLrsData.verb] || [];

      if (topics.length > 0) {
        // Validate and format data
        const formattedLrsData = this._formatLRSData(lrsData);
        // Push raw data to the topic and set key to true to format data in the consumer
        const formattedDataWithFormatKey = {
          ...formattedLrsData,
          formatData: true,
        };

        for (const topicName of topics) {
          const key = this.crypto.createHmac(
            JSON.stringify(formattedLrsData),
            this.configService.get<string>('cloud6ApiAuthSecretSalt'),
            this.configService.get('hmacAlgo'),
            this.configService.get('hmacEncoding'),
          );
          this.kafkaService.publish(topicName, formattedDataWithFormatKey, key);
        }
      }
      return true;
    } catch (error: any) {
      Logger.error('Error sending lrs data', error);
      throw error;
    }
  }

  private _getLrsData(userData: any, data: any, appId: string, appName: string): Record<string, any> {
    const defaultAppId = this.configService.get<string>('defaultAppId');
    const defaultAppName = this.configService.get<string>('defaultAppName');
    const lrsUserSecret = this.configService.get<string>('lrsUserSecret');
    const currentDate = new Date();
    const currentTime = new Date().getTime();
    const futureDate = new Date(currentDate);
    const lrsTTL = futureDate.setDate(futureDate.getDate() + this.configService.get('lrsTtl'));

    const ipAddress = '127.0.0.1';

    data['applicationId'] = appId || defaultAppId;
    data['applicationName'] = appName || defaultAppName;
    data['userId'] = userData?.id || 'anonymous_userid';
    data['userEmail'] = userData?.email || 'anonymous_email';
    data['userName'] = userData?.name || '';
    data['firstName'] = userData?.first_name || '';
    data['lastName'] = userData?.last_name || '';
    data['timezone'] = userData?.timezone || '';
    data['userRole'] = JSON.stringify(userData?.roles || []);
    data['signature'] = userData?.id
      ? md5(lrsUserSecret + userData?.id)
      : md5(lrsUserSecret + 'anonymous_userid');
    data['eventTime'] = Math.floor(currentTime);
    data['ip'] = ipAddress;

    data['referalUrl'] = typeof window !== 'undefined' && window.document.referrer ? window.document.referrer : '';
    data['url'] = 'localhost-cli';
    data['userAgent'] = typeof navigator !== 'undefined' && navigator.userAgent ? navigator.userAgent : 'localhost-cli';

    data['from_server'] = 'true';
    data['ttl'] = lrsTTL;

    if (data['dataVals']) {
      data['dataVals'] = JSON.stringify(data['dataVals']);
    }

    return data;
  }

  private _formatLRSData(data: any): any {
    const allFields = [
      'applicationId',
      'applicationName',
      'userId',
      'userEmail',
      'eventTime',
      'userName',
      'firstName',
      'lastName',
      'userRole',
      'verb',
      'objectType',
      'ip',
      'userAgent',
      'url',
      'referalUrl',
      'objectId',
      'object',
      'value',
      'dataVals',
      'ttl',
      'timezone',
    ];
    const mandatoryFields = [
      'applicationId',
      'applicationName',
      'userEmail',
      'eventTime',
      'verb',
      'objectName',
      'objectType',
      'ip',
      'userAgent',
      'url',
    ];

    const resultArray: Record<string, any> = {};

    let objectType = '';
    let objectId = '';
    let error = false;
    let message = '';

    for (const field of allFields) {
      if (mandatoryFields.includes(field) && !data[field]) {
        error = true;
        message = `Missing field value for ${field}`;
        break;
      } else {
        if (field === 'objectType') {
          objectType = data[field] || '';
        } else if (field === 'objectId') {
          objectId = data[field] || '';
        }

        resultArray[field] = data[field] || '';
      }
    }

    resultArray['objectInfo'] = `${objectType}_${objectId}`;
    resultArray['created'] = String(Date.now());

    if (error) {
      // Log the error message or throw an exception as needed
      console.error({
        METHOD: 'formatLRSData',
        MESSAGE: message,
        REQUEST: { PARAMS: data },
      });
      throw new Error(message);
    }

    return resultArray;
  }

  async sendDataToKafka(topic: string, data: any): Promise<any> {
    try {
      /**
       * If Topic name set
       *      Send data to kafka topic
       */
      if (topic) {
        const key = this.crypto.createHmac(
          JSON.stringify(data),
          this.configService.get<string>('cloud6ApiAuthSecretSalt'),
          this.configService.get('hmacAlgo'),
          this.configService.get('hmacEncoding'),
        );
        this.kafkaService.publish(topic, data, key);
      }
      return true;
    } catch (error: any) {
      Logger.error('Error sending data to kafka', error);
      throw error;
    }
  }
}
