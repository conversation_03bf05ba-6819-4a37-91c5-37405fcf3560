import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON><PERSON><PERSON>elper } from './email.helper';
import { HelperService } from './helper.service';
import { EmailService } from '../common/services/email/email.service';
import { Logger } from '../logging/logger';

// Mock the external dependencies
jest.mock('./helper.service');
jest.mock('../common/services/email/email.service');
jest.mock('../logging/logger');

describe('EmailHelper', () => {
  let emailHelper: EmailHelper;
  let helperService: HelperService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [EmailHelper, HelperService],
    }).compile();

    emailHelper = module.get<EmailHelper>(EmailHelper);
    helperService = module.get<HelperService>(HelperService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendEmail', () => {
    it('should send an email successfully', async () => {
      const mailTo = '<EMAIL>';
      const templateIdentifier = 'test-template';
      const mailTokens = { token: '123' };

      const emailServiceMock = {
        sendEmail: jest.fn().mockResolvedValue(true),
      };

      (helperService.get as jest.Mock).mockResolvedValue(emailServiceMock);

      const result = await emailHelper.sendEmail(mailTo, templateIdentifier, mailTokens);

      expect(helperService.get).toHaveBeenCalledWith(EmailService);
      expect(emailServiceMock.sendEmail).toHaveBeenCalledWith({
        mailTo,
        mailIdentifier: templateIdentifier,
        mailTokens,
      });
      expect(result).toBe(true);
    });

    it('should throw an error and log it when sending email fails', async () => {
      const mailTo = '<EMAIL>';
      const templateIdentifier = 'test-template';
      const mailTokens = { token: '123' };
      const error = new Error('Failed to send email');

      const emailServiceMock = {
        sendEmail: jest.fn().mockRejectedValue(error),
      };

      (helperService.get as jest.Mock).mockResolvedValue(emailServiceMock);
      const loggerErrorSpy = jest.spyOn(Logger, 'error');

      await expect(emailHelper.sendEmail(mailTo, templateIdentifier, mailTokens)).rejects.toThrow(error);

      expect(helperService.get).toHaveBeenCalledWith(EmailService);
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        'Send Email',
        expect.objectContaining({
          MESSAGE: error.message,
          REQUEST: { mailTo, templateIdentifier, mailTokens },
        }),
      );
    });
  });

  describe('getMailTokens', () => {
    it('should return mail tokens when gid is present', () => {
      const param = {
        gid: 'test-gid',
        affiliateLmsUrl: 'http://lms.example.com',
        affiliateLogoUrl: 'http://logo.example.com',
        planType: 'premium',
        providerName: 'Test Provider',
        affiliateName: 'Test Affiliate',
        isWhiteLabelingEnabled: true,
        fromEmailAddress: '<EMAIL>',
        displayName: 'Test User',
      };

      const expectedTokens = {
        gid: 'test-gid',
        affiliateLmsUrl: 'http://lms.example.com',
        affiliateLogoUrl: 'http://logo.example.com',
        resetPasswordUrl: 'http://lms.example.com/affiliate/user/reset-password-v1',
        planType: 'premium',
        providerName: 'Test Provider',
        affiliateName: 'Test Affiliate',
        isWhiteLabelingEnabled: true,
        fromEmailAddress: '<EMAIL>',
        displayName: 'Test User',
      };

      expect(emailHelper.getMailTokens(param)).toEqual(expectedTokens);
    });

    it('should handle isWhiteLabelingEnabled being false', () => {
        const param = {
            gid: 'test-gid',
            isWhiteLabelingEnabled: false,
        };
        const tokens = emailHelper.getMailTokens(param);
        expect(tokens.isWhiteLabelingEnabled).toBe(false);
    });

    it('should return undefined if param is null or undefined', () => {
      expect(emailHelper.getMailTokens(null)).toBeUndefined();
      expect(emailHelper.getMailTokens(undefined)).toBeUndefined();
    });

    it('should return undefined if gid is not in param', () => {
      const param = {
        affiliateLmsUrl: 'http://lms.example.com',
      };
      expect(emailHelper.getMailTokens(param)).toBeUndefined();
    });
  });
});
