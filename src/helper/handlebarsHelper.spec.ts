import { <PERSON><PERSON><PERSON>sHelper } from './handlebarsHelper';
import { VIEW_PAGES } from '../auth/config/view.constants';
import { Logger } from '../logging/logger';

// Mock dependencies
jest.mock('../logging/logger');

describe('HandlebarsHelper', () => {
  let hbsMock;
  let appMock;
  let configServiceMock;
  const helpers = {};

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();

    // Mock Handlebars instance
    hbsMock = {
      registerHelper: jest.fn((name, fn) => {
        helpers[name] = fn;
      }),
      SafeString: jest.fn(str => str), // Mock SafeString constructor
    };

    // Mock ConfigService
    configServiceMock = {
      get: jest.fn(),
    };

    // Mock NestJS app instance
    appMock = {
      select: jest.fn().mockReturnThis(),
      get: jest.fn().mockReturnValue(configServiceMock),
    };

    // Mock VIEW_PAGES routes for the 'route' helper test
    VIEW_PAGES.VIEW_ROUTES = {
      USERS: {
        REGISTER: 'register',
        LOGIN: 'login',
        FORGOT_PASSWORD: 'forgot_password',
        RESET_PASSWORD: 'reset_password',
        SOCIAL_LINK: 'social_link',
        MULTIACCOUNT: 'multiaccount',
        SOCIAL_SETUP: 'social_setup',
      },
      PROFILES: {
        PROFILE: 'profile',
      },
      CHANGEPASSWORD: {
        CHANGE_PASSWORD: 'changepassword',
      },
    } as any;
  });

  it('should register all helpers', () => {
    HandlebarsHelper.registerHelpers(hbsMock, appMock);
    expect(hbsMock.registerHelper).toHaveBeenCalledTimes(23);
  });

  describe('Individual Helpers', () => {
    beforeEach(() => {
      // Register helpers before testing them individually
      HandlebarsHelper.registerHelpers(hbsMock, appMock);
    });


    it('increment: should increment a number', () => {
        expect(helpers['increment'](5)).toBe(6);
        expect(helpers['increment']('10')).toBe(11);
      });

    describe('safeJson', () => {
      it('should return empty object string and log error for invalid context', () => {
        expect(helpers['safeJson'](null)).toBe('{}');
        expect(Logger.error).toHaveBeenCalled();
      });
    });

    describe('isEqual', () => {
        const options = { fn: jest.fn(() => true), inverse: jest.fn(() => false) };
        it('should return true if values are equal', () => {
          expect(helpers['isEqual'](1, 1, options)).toBe(true);
          expect(options.fn).toHaveBeenCalled();
        });
    
        it('should return false if values are not equal', () => {
          expect(helpers['isEqual'](1, 2, options)).toBe(false);
          expect(options.inverse).toHaveBeenCalled();
        });
      });

    it('timestampToDate: should format timestamp correctly', () => {
      const timestamp = 1672531200; // 2023-01-01
      expect(helpers['timestampToDate'](timestamp)).toBe('1/1/2023');
      expect(helpers['timestampToDate'](null)).toBe('');
    });

    it('injectGlobalVariables: should inject correct script', () => {
        (configServiceMock.get as jest.Mock).mockImplementation(key => {
          if (key === 'cdp') return { webEngage: { keyB2B: 'b2b', keyB2C: 'b2c' } };
          if (key === 'baseUrl') return 'http://test.com';
        });
        const script = helpers['injectGlobalVariables']();
        expect(script).toContain('window.baseUrl = \'http://test.com\'');
        expect(script).toContain('window.b2b_webengage_key = "b2b"');
      });

      it('injectGA4: should inject GA4 script', () => {
        (configServiceMock.get as jest.Mock).mockReturnValue({ ga4: { ga4ConfigCode: 'G-123', showGa4InConsole: true, ga4DebugMode: false, ga4SiteSpeedRate: 10 } });
        const script = helpers['injectGA4']();
        expect(script).toContain('https://www.googletagmanager.com/gtag/js?id=G-123');
        expect(script).toContain('gtag("config", "G-123"');
        expect(script).toContain('console.group("GA4 Configure")');
      });

      it('setMessages/getMessage: should set and get messages', () => {
        const messages = { greeting: 'Hello' };
        (configServiceMock.get as jest.Mock).mockReturnValue(messages);
        const context: any = {};
        helpers['setMessages'].call(context, 'auth');
        expect(context.messages).toEqual(messages);
        expect(helpers['getMessage'].call(context, 'greeting')).toBe('Hello');
      });

      describe('route', () => {
        it('should return correct partial for user routes', () => {
          const page = { data: { root: { route: { action: 'login' } } } };
          expect(helpers['route'](page, {})).toBe('users');
        });
    
        it('should return correct partial for profile routes', () => {
          const page = { data: { root: { route: { action: 'profile' } } } };
          expect(helpers['route'](page, {})).toBe('profiles');
        });

        it('should return correct partial for changepassword routes', () => {
            const page = { data: { root: { route: { action: 'changepassword' } } } };
            expect(helpers['route'](page, {})).toBe('changepassword');
          });
    
        it('should handle errors gracefully', () => {
          expect(helpers['route']({data: {root: {route: {action: 'invalid'}}}}, {})).toBe('users');
        });
      });

      it('groupIdIsTwo: should check groupId correctly', () => {
        (configServiceMock.get as jest.Mock).mockReturnValue(2);
        expect(helpers['groupIdIsTwo'](2)).toBe(true);
        expect(helpers['groupIdIsTwo'](3)).toBe(false);
      });

      it('getMultiAccountLogo: should return correct logo URL', () => {
        VIEW_PAGES.SIMPLILEARN_DEFAULT_COMPANY_LOGO = 'default.png';
        expect(helpers['getMultiAccountLogo']('logo.png')).toBe('/../logo.png');
        expect(helpers['getMultiAccountLogo'](null)).toBe('default.png');
      });

      it('getCurrentYear: should return the current year', () => {
        expect(helpers['getCurrentYear']()).toBe(new Date().getFullYear());
      });

      it('fullProfilePicUrl: should return the full profile picture URL', () => {
        (configServiceMock.get as jest.Mock).mockImplementation(key => {
          if (key === 's3BaseUrl') return 'https://s3.com';
          if (key === 'defaultProfilePicUrl') return 'default.jpg';
        });
        expect(helpers['fullProfilePicUrl']('user.jpg')).toBe('https://s3.com/user.jpg');
        expect(helpers['fullProfilePicUrl'](null)).toBe('default.jpg');
      });

      it('equals: should compare two values', () => {
        expect(helpers['equals'](1, '1')).toBe(true);
        expect(helpers['equals'](1, 2)).toBe(false);
      });

      it('or: should perform a logical OR', () => {
        expect(helpers['or'](true, false)).toBe(true);
        expect(helpers['or'](false, false)).toBe(false);
      });

      it('ne: should perform a not-equal comparison', () => {
        expect(helpers['ne'](1, 2)).toBe(true);
        expect(helpers['ne'](1, '1')).toBe(false);
      });

      it('and: should perform a logical AND', () => {
        const options = { fn: jest.fn() }; // Mock options object
        expect(helpers['and'](true, true, options)).toBe(true);
        expect(helpers['and'](true, false, options)).toBe(false);
      });

      it('not: should perform a logical NOT', () => {
        expect(helpers['not'](true)).toBe(false);
        expect(helpers['not'](false)).toBe(true);
      });
  });
});
