import { Logger } from '../logging/logger';
import { CryptoHelper, CryptoException } from './helper.crypto';
import { createHash, createHmac, createCipheriv, createDecipheriv } from 'crypto';

// Mocking crypto and Logger
jest.mock('crypto', () => ({
  createHash: jest.fn(),
  createHmac: jest.fn(),
  createCipheriv: jest.fn(),
  createDecipheriv: jest.fn(),
}));
jest.mock('../logging/logger');

describe('CryptoHelper', () => {
  let cryptoHelper: CryptoHelper;
  const cryptoConfig = {
    algo: 'aes-256-cbc',
    key: 'your_secret_key',
    iv: 'your_initialization_vector',
  };

  beforeEach(() => {
    cryptoHelper = new CryptoHelper(cryptoConfig);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('hash', () => {
    it('should call createHash function and return the hash', () => {
      const input = '<EMAIL>';
      const expectedHash = 'some_hash_value';

      (createHash as jest.Mock).mockReturnValueOnce({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValueOnce(expectedHash),
      });

      const hash = cryptoHelper.hash(input, 'sha256');
      expect(hash).toEqual(expectedHash);
      expect(createHash).toHaveBeenCalledWith('sha256');
    });

    it('should return hash with specified encoding', () => {
      const input = '<EMAIL>';
      const rawHash = 'raw_hash_data';
      const expectedHash = Buffer.from(rawHash).toString('hex');

      (createHash as jest.Mock).mockReturnValueOnce({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValueOnce(Buffer.from(rawHash)),
      });

      const hash = cryptoHelper.hash(input, 'sha256', 'hex');
      expect(hash).toEqual(expectedHash);
    });

    it('should throw CryptoException when createHash throws', () => {
      const input = 'Hello, world!';
      const error = new Error('Error in createHash');
      (createHash as jest.Mock).mockImplementationOnce(() => {
        throw error;
      });
      const loggerErrorSpy = jest.spyOn(Logger, 'error');
      expect(() => cryptoHelper.hash(input, 'sha256')).toThrow(CryptoException);
      expect(loggerErrorSpy).toHaveBeenCalledWith(`Failed to generate Standard hash - ${error.message}`, error);
    });
  });

  describe('createHmac', () => {
    it('should call createHmac function and return the HMAC', () => {
      const input = '<EMAIL>';
      const key = 'your_hmac_key';
      const expectedHmac = 'some_hmac_value';

      (createHmac as jest.Mock).mockReturnValueOnce({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValueOnce(expectedHmac),
      });

      const hmac = cryptoHelper.createHmac(input, key, 'sha256');
      expect(hmac).toEqual(expectedHmac);
      expect(createHmac).toHaveBeenCalledWith('sha256', key);
    });

    it('should return HMAC with specified encoding', () => {
      const input = '<EMAIL>';
      const key = 'your_hmac_key';
      const rawHmac = 'raw_hmac_data';
      const expectedHmac = Buffer.from(rawHmac).toString('hex');

      (createHmac as jest.Mock).mockReturnValueOnce({
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValueOnce(Buffer.from(rawHmac)),
      });

      const hmac = cryptoHelper.createHmac(input, key, 'sha256', 'hex');
      expect(hmac).toEqual(expectedHmac);
    });

    it('should throw CryptoException when createHmac throws', () => {
      const input = '<EMAIL>';
      const key = 'your_hmac_key';
      const error = new Error('Error in createHmac');
      (createHmac as jest.Mock).mockImplementationOnce(() => {
        throw error;
      });
      const loggerErrorSpy = jest.spyOn(Logger, 'error');
      expect(() => cryptoHelper.createHmac(input, key, 'sha256')).toThrow(CryptoException);
      expect(loggerErrorSpy).toHaveBeenCalledWith(`Failed to generate HMAC hash - ${error.message}`, error);
    });
  });

  describe('onModuleInit', () => {
    it('should initialize cryptography keys', () => {
      const hashKeyMock = {
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValueOnce('a'.repeat(32)),
      };
      const hashIvMock = {
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValueOnce('b'.repeat(16)),
      };
      (createHash as jest.Mock).mockReturnValueOnce(hashKeyMock).mockReturnValueOnce(hashIvMock);

      cryptoHelper.onModuleInit();
      expect(cryptoHelper['__algo']).toBe(cryptoConfig.algo);
      expect(cryptoHelper['__key']).toBe('a'.repeat(32));
      expect(cryptoHelper['__iv']).toBe('b'.repeat(16));
      expect(createHash).toHaveBeenCalledTimes(2);
    });

    it('should log error when initializing cryptography keys fails', () => {
      const error = new Error('Init failed');
      (createHash as jest.Mock).mockImplementationOnce(() => {
        throw error;
      });
      const loggerErrorSpy = jest.spyOn(Logger, 'error');
      cryptoHelper.onModuleInit();
      expect(loggerErrorSpy).toHaveBeenCalledWith('Failed to initialize cryptography keys');
    });
  });

  describe('Initialized CryptoHelper', () => {
    beforeEach(() => {
      // Initialize the helper for encryption/decryption tests
      const hashKeyMock = {
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValueOnce('a'.repeat(32)),
      };
      const hashIvMock = {
        update: jest.fn().mockReturnThis(),
        digest: jest.fn().mockReturnValueOnce('b'.repeat(16)),
      };
      (createHash as jest.Mock).mockReturnValueOnce(hashKeyMock).mockReturnValueOnce(hashIvMock);
      cryptoHelper.onModuleInit();
      (createHash as jest.Mock).mockClear();
    });

    describe('encrypt', () => {
      it('should call createCipheriv function and return the encrypted data', () => {
        const data = '<EMAIL>';
        const encryptedPart1 = 'some_encrypted_data';
        const encryptedPart2 = '_final_part';
        const finalEncrypted = Buffer.from(encryptedPart1 + encryptedPart2, 'utf-8').toString('base64');

        (createCipheriv as jest.Mock).mockReturnValueOnce({
          update: jest.fn().mockReturnValueOnce(encryptedPart1),
          final: jest.fn().mockReturnValueOnce(encryptedPart2),
        });

        const encryptedData = cryptoHelper.encrypt(data);
        expect(encryptedData).toEqual(finalEncrypted);
        expect(createCipheriv).toHaveBeenCalledWith(cryptoConfig.algo, 'a'.repeat(32), 'b'.repeat(16));
      });

      it('should throw CryptoException when createCipheriv throws', () => {
        const data = 'Hello, world!';
        const error = new Error('Error in createCipheriv');
        (createCipheriv as jest.Mock).mockImplementationOnce(() => {
          throw error;
        });
        const loggerErrorSpy = jest.spyOn(Logger, 'error');
        expect(() => cryptoHelper.encrypt(data)).toThrow(CryptoException);
        expect(loggerErrorSpy).toHaveBeenCalledWith(`Encryption failed - ${error.message}`, error);
      });
    });

    describe('decrypt', () => {
      it('should call createDecipheriv function and return the decrypted data', () => {
        const decryptedData = '<EMAIL>';
        const encryptedData = Buffer.from('some_encrypted_data').toString('base64');

        (createDecipheriv as jest.Mock).mockReturnValueOnce({
          update: jest.fn().mockReturnValueOnce(decryptedData),
          final: jest.fn().mockReturnValueOnce(''),
        });

        const result = cryptoHelper.decrypt(encryptedData);
        expect(result).toEqual(decryptedData);
        expect(createDecipheriv).toHaveBeenCalledWith(cryptoConfig.algo, 'a'.repeat(32), 'b'.repeat(16));
      });

      it('should throw CryptoException when createDecipheriv throws', () => {
        const encryptedData = Buffer.from('some_encrypted_data').toString('base64');
        const error = new Error('Error in createDecipheriv');
        (createDecipheriv as jest.Mock).mockImplementationOnce(() => {
          throw error;
        });
        const loggerErrorSpy = jest.spyOn(Logger, 'error');
        expect(() => cryptoHelper.decrypt(encryptedData)).toThrow(CryptoException);
        expect(loggerErrorSpy).toHaveBeenCalledWith(`Decryption failed - ${error.message}`, error);
      });
    });

    describe('encryptDecrypt', () => {
      it('should call encrypt when type is "encrypt"', () => {
        const input = 'my_data';
        const encryptedData = 'encrypted_data';
        const encryptSpy = jest.spyOn(cryptoHelper, 'encrypt').mockReturnValue(encryptedData);
        
        const result = cryptoHelper.encryptDecrypt('encrypt', input);
        
        expect(result).toBe(encryptedData);
        expect(encryptSpy).toHaveBeenCalledWith(input);
      });

      it('should call decrypt when type is "decrypt"', () => {
        const input = 'my_encrypted_data';
        const decryptedData = 'decrypted_data';
        const decryptSpy = jest.spyOn(cryptoHelper, 'decrypt').mockReturnValue(decryptedData);

        const result = cryptoHelper.encryptDecrypt('decrypt', input);

        expect(result).toBe(decryptedData);
        expect(decryptSpy).toHaveBeenCalledWith(input);
      });

      it('should return an empty string for an invalid type', () => {
        const input = 'my_data';
        const result = cryptoHelper.encryptDecrypt('invalid_type', input);
        expect(result).toBe('');
      });

      it('should return an empty string for an empty type', () => {
        const input = 'my_data';
        const result = cryptoHelper.encryptDecrypt('', input);
        expect(result).toBe('');
      });
    });
  });

  describe('CryptoException', () => {
    it('should be an instance of Error', () => {
      expect(new CryptoException('test')).toBeInstanceOf(Error);
    });

    it('should log the message and inner exception upon creation', () => {
      const loggerErrorSpy = jest.spyOn(Logger, 'error');
      const message = 'Test Exception';
      const innerError = new Error('Inner Error');
      
      const ex = new CryptoException(message, innerError);

      expect(ex.message).toBe(message);
      expect(loggerErrorSpy).toHaveBeenCalledWith(message, innerError);
    });
  });
});
