import { ConfigModule, ConfigService } from '@nestjs/config';
import { VIEW_PAGES } from '../auth/config/view.constants';
import { ForgotPasswordDto } from '../auth/dtos/forgot-password.dto';
import { Logger } from '../logging/logger';
/**
 * common class to set common functionality of Handlebars
 */

export class HandlebarsHelper {
  static messages: any;
  static globalVariable: any;

  /**
   * Static function to register helper functions for Handlebars
   * @param hbs
   * @param app
   */
  static registerHelpers(hbs, app): void {
    const configService = app.select(ConfigModule).get(ConfigService);
    const routs = VIEW_PAGES.VIEW_ROUTES;
    // Register the json helper
    hbs.registerHelper('json', function (context) {
      return JSON.stringify(context);
    });

    hbs.registerHelper('increment' ,function (context) {
      return Number(context)+1;
    })

    hbs.registerHelper("safeJson", function (context) {
      try {
        if (!context) {
          throw new Error("Invalid context provided to handlebar helper");
        }
        return JSON.stringify(context).replace(/</g, "\\u003c").replace(/>/g, "\\u003e");
      } catch (error: any) {
      Logger.error('safeJSON', {
        METHOD: "HandlebarsHelper.registerHelpers",
        MESSAGE: error.message,
        REQUEST: { context: context },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return "{}";
      }
    });
    // Register the isEqual helper
    hbs.registerHelper('isEqual', function (this: any, value1, value2, options) {
      if (value1 === value2) {
        return options.fn(this);
      } else {
        return options.inverse(this);
      }
    });

    // Register a Handlebars helper to format timestamps to "DD/MM/YYYY"
    hbs.registerHelper("timestampToDate", function (timestamp) {
      if (!timestamp) return "";
      const date = new Date(timestamp * 1000); // Convert from seconds to milliseconds
      return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
    });
  
    // Assign global variable to window object
    // Helper to assign global variables to window object
    hbs.registerHelper('injectGlobalVariables', function () {
      const cdp = configService.get('cdp');
      const baseUrl = configService.get('baseUrl'); 
      return `
        <script>
          window.ENVIRONMENT = '${process.env.NODE_ENV}';
          window.baseUrl = '${baseUrl}';
          window.b2b_webengage_key = ${JSON.stringify(cdp.webEngage.keyB2B)};
          window.b2c_webengage_key = ${JSON.stringify(cdp.webEngage.keyB2C)};
        </script>
      `;
    });


    // Helper to enable or disable logs
 
    // Helper to inject GA4 scripts and configuration
    hbs.registerHelper('injectGA4', function () {
      const cdp = configService.get('cdp');
      const showGA4Logs = cdp.ga4.showGa4InConsole ? true : false;
      return `
      <script async src="https://www.googletagmanager.com/gtag/js?id=${cdp.ga4.ga4ConfigCode}"></script>
      <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag("js", new Date());

        ${showGA4Logs ? 'console.group("GA4 Configure");' : ''}

        var GA4_DEBUG = ${JSON.stringify(cdp.ga4.ga4DebugMode)};
        if (GA4_DEBUG) {
        gtag("config", "${cdp.ga4.ga4ConfigCode}", {
          site_speed_sample_rate: ${JSON.stringify(cdp.ga4.ga4SiteSpeedRate)},
          debug_mode: true
        });
        } else {
        gtag("config", "${cdp.ga4.ga4ConfigCode}", {
          site_speed_sample_rate: ${JSON.stringify(cdp.ga4.ga4SiteSpeedRate)}
        });
        }

        gtag("consent", "default", {
        ad_storage: "allowed",
        analytics_storage: "allowed"
        });

        gtag("consent", "default", {
        ad_storage: "granted",
        analytics_storage: "granted",
        wait_for_update: 500
        });

        ${showGA4Logs ? 'console.log("GA4 Configuration Complete!"); console.groupEnd();' : ''}
      </script>
      `;
    });

    // set messages
    hbs.registerHelper('setMessages', function (this: any, key) {
      this.messages = configService.get(key.toString());
    });
    // get messages asper keys
    hbs.registerHelper('getMessage', function (this: any, key) {
      return this.messages[key.toString()];
    });

    hbs.registerHelper('setDTOValue', function (options) {
      const _forgotPasswordDto = new ForgotPasswordDto();
      _forgotPasswordDto.email = '';
      Logger.log('ForgotPasswordDto', options);
    });

    hbs.registerHelper('printMsg', function (msg) {
      return msg.toString();
    });

    hbs.registerHelper('route', function (page: { data: any; root: { route: any } }, options) {
      try {
        Logger.log('page', options);
        page = { ...page?.data };
        const route = page?.root?.route;
        let partialPage = 'users';
        switch (route?.action) {
          case routs.USERS.REGISTER:
          case routs.USERS.REGISTER_EMAIL:
          case routs.USERS.LOGIN:
          case routs.USERS.FORGOT_PASSWORD:
          case routs.USERS.RESET_PASSWORD:
          case routs.USERS.SOCIAL_LINK:
          case routs.USERS.MULTIACCOUNT:
          case routs.USERS.SOCIAL_SETUP:
            partialPage = 'users';
            break;
          case routs.PROFILES.PROFILE:
            partialPage = 'profiles';
            break;
          case routs.CHANGEPASSWORD.CHANGE_PASSWORD:
              partialPage = 'changepassword';
              break;
          case route.default:
            partialPage = 'users';
            break;
        }
        return partialPage;
      } catch (error) {
        return 'landedOnMars';
      }
    });

    hbs.registerHelper('setDTOValue', function (options) {
      new ForgotPasswordDto().email = '';
      Logger.log('ForgotPasswordDto',options);
    });

    hbs.registerHelper('obfuscateEmail', (email: string) => {
      try {
        const [username, _domain] = email?.split('@');
        const [domain, extention] = _domain?.split('.');
        const obfuscatedUsername = `${username?.charAt(0)}${'*'.repeat(username?.length - 2)}${username.charAt(
          username.length - 1,
        )}`;
        const obfuscatedDomain = `${domain?.charAt(0)}${domain?.charAt(1)}${'*'.repeat(
          domain?.length - 3,
        )}${domain?.charAt(domain?.length - 1)}`;
        return new hbs.SafeString(`${obfuscatedUsername}@${obfuscatedDomain}.${extention}`);
      } catch (error) {
        return 'landedOnMars';
      }
    });
    // Custom helper to check if the groupId is equal to 2
    hbs.registerHelper('groupIdIsTwo', function (groupId) {
      return groupId === configService.get('defaultUserGroupId');
    });

    hbs.registerHelper('getMultiAccountLogo', function (url) {
      return url != null ? '/../' + url : VIEW_PAGES.SIMPLILEARN_DEFAULT_COMPANY_LOGO;
    });

    hbs.registerHelper('getCurrentYear', function () {
      return new Date().getFullYear();
    });
    
    hbs.registerHelper('fullProfilePicUrl', function (key) {
      const baseUrl = configService.get('s3BaseUrl');
      const defaultProfilePicUrl = configService.get('defaultProfilePicUrl');
      return key ? baseUrl +'/'+ key : defaultProfilePicUrl;
    });

    hbs.registerHelper('equals', function(value1 , value2){
      return String(value1) == String(value2)
    })

    hbs.registerHelper('or', (value1, value2) => value1 || value2);
    hbs.registerHelper('ne', function (value1, value2) {
      return String(value1) !== String(value2);
    });

  hbs.registerHelper('and', (...args) => {
    if (args.length && typeof args[args.length - 1] === 'object' && args[args.length - 1].fn) {
      args.pop(); 
    }
    return args.every(Boolean);
  });
    
    hbs.registerHelper('not', function (value) {
      return !value;
    });
    
    // Capitalize helper: Capitalizes the first letter of a string
    hbs.registerHelper('capitalize', function (str) {
      if (typeof str !== 'string' || !str.length) return '';
      return str.charAt(0).toUpperCase() + str.slice(1);
    });
  }
}
