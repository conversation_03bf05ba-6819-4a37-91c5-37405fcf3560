import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ChannelOptions, ICDPInterface } from './helper.cdp.interface';
import { Communication } from '../../common/services/communication/communication';

// Example usage of eventEmitter.emit
// this.eventEmitter.emit('user.login', {
//   email: "<EMAIL>",
//   clientId: "test",
//   gid: 2, 
//   eventName: 'auth-token',
//   payload: {
//     first_name: 'Test',
//     last_name: 'User',
//     email: '<EMAIL>',
//     phone_no: '**********',
//   },
// });

@Injectable()
export class Webengage extends Communication implements ICDPInterface {
  private readonly apiKey: string;
  private authorizationKey: string;

  constructor(options: ChannelOptions) {
    super();
    this.apiKey = options.key;
    this.authorizationKey = options.authorizationKey;
  }

  private buildUri(path: string) {
  
    const baseUrl = 'https://api.webengage.com/v1/accounts/'+this.apiKey+'/';
    return `${baseUrl}${path}`;
  }

  private async sendRequest(uri: string, data: any) {
    try { 
      this.url = '';
      this.url = uri;
      this.setRequestHeaders({
        'Content-Type': 'application/json',
         'Authorization': `Bearer ${this.authorizationKey}`,
         'Accept': 'application/json, text/plain, */*'
      });
      console.log('Webengage API request:', this.authorizationKey);
      this.setRawBody(JSON.stringify(data));
      const response = await this.post('');

      // if (response.data?.response?.status !== 'queued') {
      //   console.error('Unexpected response:', response.data);
      // }
      console.log('Webengage API response:', response);
      return response.data;
    } catch (error:any) {
      console.error('Webengage API error:', {
        uri,
        data,
        error: error.message,
      });
      // throw error;
    }
  }
  async identify(userId: string, traits: object) {
    console.log('Identify called with userId:', userId, 'and traits:', traits);
    const uri = this.buildUri('users');
    const payload = {
      userId,
      email:userId

    };
    return this.sendRequest(uri, payload);
  }

  async track(userId: string, eventName: string, eventData: object) {
    const uri = this.buildUri('events');
    const payload = {
      userId,
      eventName,
      eventData,
    };
    return this.sendRequest(uri, payload);
  }
}

export const WebengageChannelProviders = [
  {
    provide: 'WEBENGAGE_B2C',
    useFactory: (configService: ConfigService) => {
      const config = configService.get('cdp').webEngage;
      return new Webengage({
        key: config.keyB2C,
        authorizationKey: config.webEngageAuthorizationKey,
      });
    },
    inject: [ConfigService],
  },
  {
    provide: 'WEBENGAGE_B2B',
    useFactory: (configService: ConfigService) => {
      const config = configService.get('cdp').webEngage;
      return new Webengage({
        key: config.keyB2B,
        authorizationKey: config.webEngageAuthorizationKeyB2b,
      });
    },
    inject: [ConfigService],
  },
];
