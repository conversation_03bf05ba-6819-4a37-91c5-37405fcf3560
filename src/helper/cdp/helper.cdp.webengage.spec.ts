import { Webengage } from './helper.cdp.webengage';

describe('Webengage', () => {
  let webengage: Webengage;

  beforeEach(() => {
    webengage = new Webengage({ key: 'test-key', authorizationKey: 'test-auth-key' });
    jest.clearAllMocks();
  });

  describe('buildUri', () => {
    it('should build correct URI based on API key and path', () => {
      const result = (webengage as any).buildUri('users');
      expect(result).toBe('https://api.webengage.com/v1/accounts/test-key/users');
    });
  });
  describe('sendRequest', () => {
    it('should send a request with correct headers and payload', async () => {
      const mockResponse = { data: { status: 'queued' } };
  
      // Mock internal methods of Communication
      const setHeadersMock = jest.spyOn(webengage as any, 'setRequestHeaders').mockImplementation(() => {});
      const setBodyMock = jest.spyOn(webengage as any, 'setRawBody').mockImplementation(() => {});
      const postMock = jest.spyOn(webengage as any, 'post').mockResolvedValue(mockResponse);
  
      const uri = 'https://api.webengage.com/v1/accounts/test-key/users';
      const data = { userId: 'abc123' };
  
      // Act
      const result = await (webengage as any).sendRequest(uri, data);
  
      // Assert
      expect(setHeadersMock).toHaveBeenCalledWith({
        'Content-Type': 'application/json',
        Authorization: 'Bearer test-auth-key',
        Accept: 'application/json, text/plain, */*',
      });
  
      expect(setBodyMock).toHaveBeenCalledWith(JSON.stringify(data));
      expect(postMock).toHaveBeenCalledWith('');
      expect(result).toEqual({ status: 'queued' });
    });
  
    it('should handle and log errors from the request', async () => {
      const error = new Error('Send failed');
  
      // Mock internal methods to simulate failure
      jest.spyOn(webengage as any, 'setRequestHeaders').mockImplementation(() => {});
      jest.spyOn(webengage as any, 'setRawBody').mockImplementation(() => {});
      jest.spyOn(webengage as any, 'post').mockRejectedValue(error);
  
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
  
      const uri = 'https://api.webengage.com/v1/accounts/test-key/events';
      const data = { test: true };
  
      // Act
      const result = await (webengage as any).sendRequest(uri, data);
  
      // Assert
      expect(consoleErrorSpy).toHaveBeenCalledWith('Webengage API error:', {
        uri,
        data,
        error: error.message,
      });
  
      expect(result).toBeUndefined();
  
      // Clean up
      consoleErrorSpy.mockRestore();
    });
  });
  

  describe('identify', () => {
    it('should call sendRequest with user info', async () => {
      const sendSpy = jest.spyOn(webengage as any, 'sendRequest').mockResolvedValue({ status: 'queued' });

      const userId = '<EMAIL>';
      const traits = { name: 'User Name' };

      await webengage.identify(userId, traits);

      expect(sendSpy).toHaveBeenCalledWith(
        'https://api.webengage.com/v1/accounts/test-key/users',
        {
          userId: '<EMAIL>',
          email: '<EMAIL>',
        }
      );
    });
  });

  describe('track', () => {
    it('should call sendRequest with event info', async () => {
      const sendSpy = jest.spyOn(webengage as any, 'sendRequest').mockResolvedValue({ status: 'queued' });

      const userId = '<EMAIL>';
      const eventName = 'Clicked CTA';
      const eventData = { campaign: 'welcome' };

      await webengage.track(userId, eventName, eventData);

      expect(sendSpy).toHaveBeenCalledWith(
        'https://api.webengage.com/v1/accounts/test-key/events',
        {
          userId: '<EMAIL>',
          eventName: 'Clicked CTA',
          eventData: { campaign: 'welcome' },
        }
      );
    });
  });
});
