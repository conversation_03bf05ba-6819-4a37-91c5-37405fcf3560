import { Inject, Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Logger } from '../../logging/logger';
import { Webengage } from './helper.cdp.webengage';

@Injectable()
export class CDPEventListener {
  @Inject('WEBENGAGE_B2C') private readonly webengageB2C: Webengage;
  @Inject('WEBENGAGE_B2B') private readonly webengageB2B: Webengage;

  private getWebengageInstance = (gid: number): Webengage =>
    gid == 2 || gid === undefined ? this.webengageB2C : this.webengageB2B;

  @OnEvent('user.login', { async: true })
  @OnEvent('user.impersonate', { async: true })
  async onLogin(params: any) {
    Logger.log('event******************************************',params);

    const webEngage = this.getWebengageInstance(params.gid);
    webEngage.identify(params.email, {});
    webEngage.track(params.email || '<EMAIL>', params.eventName,params.payload);
  }
  @OnEvent('user.signup', { async: true })
  async onSignup(params: any) {
    const webEngage = this.getWebengageInstance(params.gid);
    webEngage.identify(params.email, {});
    webEngage.track(params.email || '<EMAIL>', params.eventName,params.payload);
  }
  @OnEvent('user.complete_signup_frs', { async: true })
  async onFrsCompleteSignup(params: any) {
    const webEngage = this.getWebengageInstance(params.gid);
    webEngage.identify(params.email, {});
    webEngage.track(params.email, params.eventName,params.payload);
  }
}
