import { Test, TestingModule } from '@nestjs/testing';
import { CDPEventListener } from './helper.cdp.listener';
import { Webengage } from './helper.cdp.webengage';

describe('CDPEventListener', () => {
  let listener: CDPEventListener;
  let webengageB2C: Webengage;
  let webengageB2B: Webengage;

  const mockWebengage = {
    identify: jest.fn(),
    track: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CDPEventListener,
        {
          provide: 'WEBENGAGE_B2C',
          useValue: mockWebengage,
        },
        {
          provide: 'WEBENGAGE_B2B',
          useValue: mockWebengage,
        },
      ],
    }).compile();

    listener = module.get<CDPEventListener>(CDPEventListener);
    webengageB2C = module.get<Webengage>('WEBENGAGE_B2C');
    webengageB2B = module.get<Webengage>('WEBENGAGE_B2B');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(listener).toBeDefined();
  });

  describe('onLogin', () => {
    it('should call webengageB2C for gid 2', async () => {
      const params = { gid: 2, email: '<EMAIL>', eventName: 'login', payload: {} };
      await listener.onLogin(params);
      expect(webengageB2C.identify).toHaveBeenCalledWith(params.email, {});
      expect(webengageB2C.track).toHaveBeenCalledWith(params.email, params.eventName, params.payload);
    });

    it('should call webengageB2B for other gids', async () => {
      const params = { gid: 1, email: '<EMAIL>', eventName: 'login', payload: {} };
      await listener.onLogin(params);
      expect(webengageB2B.identify).toHaveBeenCalledWith(params.email, {});
      expect(webengageB2B.track).toHaveBeenCalledWith(params.email, params.eventName, params.payload);
    });
  });

  describe('onSignup', () => {
    it('should call webengageB2C for undefined gid', async () => {
      const params = { email: '<EMAIL>', eventName: 'signup', payload: {} };
      await listener.onSignup(params);
      expect(webengageB2C.identify).toHaveBeenCalledWith(params.email, {});
      expect(webengageB2C.track).toHaveBeenCalledWith(params.email, params.eventName, params.payload);
    });
  });

  describe('onFrsCompleteSignup', () => {
    it('should call webengageB2C', async () => {
      const params = { gid: 2, email: '<EMAIL>', eventName: 'frs_signup', payload: {} };
      await listener.onFrsCompleteSignup(params);
      expect(webengageB2C.identify).toHaveBeenCalledWith(params.email, {});
      expect(webengageB2C.track).toHaveBeenCalledWith(params.email, params.eventName, params.payload);
    });
  });

  describe('getWebengageInstance', () => {
    it('should return B2C instance for gid 2', () => {
      const instance = (listener as any).getWebengageInstance(2);
      expect(instance).toBe(webengageB2C);
    });

    it('should return B2C instance for undefined gid', () => {
      const instance = (listener as any).getWebengageInstance(undefined);
      expect(instance).toBe(webengageB2C);
    });

    it('should return B2B instance for other gids', () => {
      const instance = (listener as any).getWebengageInstance(1);
      expect(instance).toBe(webengageB2B);
    });
  });
});