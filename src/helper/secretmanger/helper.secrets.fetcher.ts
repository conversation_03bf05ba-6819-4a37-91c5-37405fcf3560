import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
import { Logger } from '../../logging/logger';

export const loadAllSecrets = async () => {
  try {
    const secretsIds = (process.env['SENTINEL_AWS_SECRET_IDS'] || '').split(',');
    if (secretsIds.length == 0) {
      Logger.log('No secrets to process');
      return;
    }
    await getSecretKeys(secretsIds, true);
  } catch (err: any) {
    Logger.error('Failed to load secrets', err);
    throw err; //Throwing coz application might not work with some of the secrets
  }
};
export const getSecretKeys = async (secretsIds: string[], loadUnderEnv = false) => {
  try {
    const clientOptions = {
      region: process.env.AWS_REGION,
    };
    if (secretsIds.length == 0) {
      Logger.debug('No secrets to process');
      return;
    }
    const awsSecretsManagerClient = new SecretsManagerClient(clientOptions);
    Logger.debug(`Retrieving secret keys for [${secretsIds}]...`);
    const commands = secretsIds.map(
      (secretId) =>
        new GetSecretValueCommand({
          SecretId: secretId.trim(),
        }),
    );
    const resp = commands.map((command) => awsSecretsManagerClient.send(command));

    const secrets = await Promise.all(resp);
    const response = secrets.reduce((acc, secret) => {
      if ('SecretString' in secret) {
        const sec = JSON.parse(<string>secret.SecretString);
        return {
          ...acc,
          ...sec,
        };
      } else {
        //TODO: handle binary format
        Logger.warn('BINARY KEYS: unhandled format');
      }
    }, {});
    Logger.info('Keys', response);
    if (loadUnderEnv == true) {
      Object.keys(response).forEach((key) => {
        process.env[key] = response[key];
      });
    }
    return response;
  } catch (err: any) {
    Logger.error('Failed to load secrets', err);
    throw err; //Throwing coz application might not work with some of the secrets
  }
};
