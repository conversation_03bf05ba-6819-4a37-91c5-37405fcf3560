import { SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import { loadAllSecrets, getSecretKeys } from './helper.secrets.fetcher'; // adjust path as needed
import { Logger } from '../../logging/logger';

jest.mock('@aws-sdk/client-secrets-manager');
jest.mock('../../logging/logger');

describe('AWS Secrets Loader', () => {
  const sendMock = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (SecretsManagerClient as jest.Mock).mockImplementation(() => ({
      send: sendMock,
    }));
    process.env = { AWS_REGION: 'us-east-1' };
  });

  describe('loadAllSecrets', () => {
    it('should log and return if no secrets are configured', async () => {
      process.env.SENTINEL_AWS_SECRET_IDS = '';

      await loadAllSecrets();

      expect(Logger.log).toHaveBeenCalledWith('No secrets to process');
      expect(sendMock).not.toHaveBeenCalled();
    });

    it('should call getSecretKeys if secrets are defined', async () => {
      process.env.SENTINEL_AWS_SECRET_IDS = 'secret-1';

      sendMock.mockResolvedValue({
        SecretString: JSON.stringify({ DB_PASSWORD: 'pass' }),
      });

      // const result = await loadAllSecrets();

      expect(Logger.log).not.toHaveBeenCalledWith('No secrets to process');
      expect(sendMock).toHaveBeenCalledTimes(1);
    });

    it('should log and throw error if getSecretKeys fails', async () => {
      process.env.SENTINEL_AWS_SECRET_IDS = 'secret-1';

      sendMock.mockRejectedValue(new Error('SecretsManager error'));

      await expect(loadAllSecrets()).rejects.toThrow('SecretsManager error');
      expect(Logger.error).toHaveBeenCalledWith('Failed to load secrets', expect.any(Error));
    });
  });

  describe('getSecretKeys', () => {
    it('should return empty if no secrets provided', async () => {
      const result = await getSecretKeys([]);
      expect(Logger.debug).toHaveBeenCalledWith('No secrets to process');
      expect(result).toBeUndefined();
    });

    it('should retrieve and merge secrets correctly', async () => {
      const mockSecret1 = { DB_HOST: 'localhost' };
      const mockSecret2 = { DB_USER: 'admin' };

      sendMock
        .mockResolvedValueOnce({ SecretString: JSON.stringify(mockSecret1) })
        .mockResolvedValueOnce({ SecretString: JSON.stringify(mockSecret2) });

      const result = await getSecretKeys(['secret-1', 'secret-2']);

      expect(Logger.debug).toHaveBeenCalledWith('Retrieving secret keys for [secret-1,secret-2]...');
      expect(result).toEqual({ DB_HOST: 'localhost', DB_USER: 'admin' });
    });

    it('should load keys under process.env if loadUnderEnv = true', async () => {
      const secret = { API_KEY: 'secret123' };
      sendMock.mockResolvedValueOnce({ SecretString: JSON.stringify(secret) });

      await getSecretKeys(['secret-api'], true);

      expect(process.env.API_KEY).toBe('secret123');
    });

    it('should handle non-SecretString (binary format) with warning', async () => {
      sendMock.mockResolvedValueOnce({}); // no SecretString

      await getSecretKeys(['secret-binary']);

      expect(Logger.warn).toHaveBeenCalledWith('BINARY KEYS: unhandled format');
    });

    it('should throw and log error if AWS call fails', async () => {
      sendMock.mockRejectedValue(new Error('AWS Error'));

      await expect(getSecretKeys(['bad-secret'])).rejects.toThrow('AWS Error');
      expect(Logger.error).toHaveBeenCalledWith('Failed to load secrets', expect.any(Error));
    });
  });
});
