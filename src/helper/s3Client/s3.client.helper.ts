import { Injectable } from '@nestjs/common';
import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  PutObjectCommandInput,
  GetObjectCommandInput
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Logger } from '../../logging/logger';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class S3UploaderService {
  private client: S3Client;

  constructor(private readonly configService: ConfigService) {   
    const region = this.configService.get<string>('s3Region');
    const isDev = this.configService.get<string>('NODE_ENV') === 'development';

    this.client = new S3Client({
      region,
      ...(isDev && {
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
        sessionToken: process.env.AWS_SESSION_TOKEN || '',
      }
      }),
    });
  }

  async uploadToS3(bucket: string, key: string, buffer, mimetype: string) {
    const params: PutObjectCommandInput = {
      Bucket: bucket,
      Key: key,
      Body: buffer,
      ContentType: mimetype,
    };

    const command = new PutObjectCommand(params);

    try {
      const response = await this.client.send(command);
      return response;
    } catch (error: any) {
      Logger.error('uploadToS3', {
        METHOD: this.constructor.name + '@' + this.uploadToS3.name,
        MESSAGE: error.message,
        REQUEST: { bucket, key, mimetype },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async getObjectFromS3(bucket: string, key: string): Promise<any> {
    Logger.log(`Fetching from S3: Bucket=${bucket}, Key=${key}`);
    const params: GetObjectCommandInput = {
      Bucket: bucket,
      Key: key,
    };

    const command = new GetObjectCommand(params);

    try {
      const url = await getSignedUrl(this.client, command, { expiresIn: 80000 });
      Logger.log(`Generated Signed URL: ${url}`);
      return url;
    } catch (error: any) {
      Logger.error('getObjectFromS3', {
        METHOD: this.constructor.name + '@' + this.getObjectFromS3.name,
        MESSAGE: error.message,
        REQUEST: { bucket, key },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
}