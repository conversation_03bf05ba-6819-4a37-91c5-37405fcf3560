
import { Test, TestingModule } from '@nestjs/testing';
import { S3UploaderService } from './s3.client.helper';
import { ConfigService } from '@nestjs/config';
import { S3Client, PutObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

// Mock the AWS S3 Client and its commands
jest.mock('@aws-sdk/client-s3', () => {
  const originalModule = jest.requireActual('@aws-sdk/client-s3');
  return {
    ...originalModule,
    S3Client: jest.fn(() => ({
      send: jest.fn(),
    })),
    PutObjectCommand: jest.fn(),
    GetObjectCommand: jest.fn(),
  };
});

// Mock the s3-request-presigner
jest.mock('@aws-sdk/s3-request-presigner', () => ({
  getSignedUrl: jest.fn(),
}));

describe('S3UploaderService', () => {
  let service: S3UploaderService;
  let configService: ConfigService;
  let s3Client: S3Client;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        S3UploaderService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<S3UploaderService>(S3UploaderService);
    configService = module.get<ConfigService>(ConfigService);
    s3Client = (service as any).client;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('constructor', () => {
    it('should initialize S3Client without credentials if NODE_ENV is not development', () => {
      jest.clearAllMocks();
      (configService.get as jest.Mock).mockImplementation((key: string) => {
        if (key === 's3Region') return 'us-east-1';
        if (key === 'NODE_ENV') return 'production';
        return null;
      });
      new S3UploaderService(configService);
      expect(S3Client).toHaveBeenCalledWith({ region: 'us-east-1' });
    });

    it('should initialize S3Client with credentials if NODE_ENV is development', () => {
        jest.clearAllMocks();
        process.env.AWS_ACCESS_KEY_ID = 'test-key';
        process.env.AWS_SECRET_ACCESS_KEY = 'test-secret';
        process.env.AWS_SESSION_TOKEN = 'test-token';

        (configService.get as jest.Mock).mockImplementation((key: string) => {
            if (key === 's3Region') return 'us-east-1';
            if (key === 'NODE_ENV') return 'development';
            return null;
        });

        new S3UploaderService(configService);
        expect(S3Client).toHaveBeenCalledWith({
            region: 'us-east-1',
            credentials: {
                accessKeyId: 'test-key',
                secretAccessKey: 'test-secret',
                sessionToken: 'test-token',
            },
        });
    });
  });


  describe('uploadToS3', () => {
    it('should upload a file to S3 and return the response', async () => {
      const bucket = 'test-bucket';
      const key = 'test-key';
      const buffer = Buffer.from('test-data');
      const mimetype = 'text/plain';
      const s3Response = { $metadata: { httpStatusCode: 200 } };

      (s3Client.send as jest.Mock).mockResolvedValue(s3Response);

      const result = await service.uploadToS3(bucket, key, buffer, mimetype);

      expect(result).toEqual(s3Response);
      expect(PutObjectCommand).toHaveBeenCalledWith({
        Bucket: bucket,
        Key: key,
        Body: buffer,
        ContentType: mimetype,
      });
      expect(s3Client.send).toHaveBeenCalled();
    });

    it('should throw an error if S3 upload fails', async () => {
      const error = new Error('S3 upload failed');
      (s3Client.send as jest.Mock).mockRejectedValue(error);

      await expect(
        service.uploadToS3('bucket', 'key', Buffer.from(''), 'type'),
      ).rejects.toThrow(error);
    });
  });

  describe('getObjectFromS3', () => {
    it('should return a signed URL for an S3 object', async () => {
      const bucket = 'test-bucket';
      const key = 'test-key';
      const signedUrl = 'https://s3.signed.url/test-key';

      (getSignedUrl as jest.Mock).mockResolvedValue(signedUrl);

      const result = await service.getObjectFromS3(bucket, key);

      expect(result).toBe(signedUrl);
      expect(GetObjectCommand).toHaveBeenCalledWith({
        Bucket: bucket,
        Key: key,
      });
      expect(getSignedUrl).toHaveBeenCalledWith(s3Client, expect.any(GetObjectCommand), { expiresIn: 80000 });
    });

    it('should throw an error if getting the signed URL fails', async () => {
      const error = new Error('Failed to get signed URL');
      (getSignedUrl as jest.Mock).mockRejectedValue(error);

      await expect(service.getObjectFromS3('bucket', 'key')).rejects.toThrow(error);
    });
  });
});
