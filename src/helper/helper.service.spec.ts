import { HelperService } from './helper.service';
import { INestApplication } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
const createAppMock = () => {
  return {
    get: jest.fn(),
  } as Partial<INestApplication>;
};
const createModuleRefMock = () => {
  return {
    get: jest.fn(),
  } as Partial<ModuleRef>;
};
describe('HelperService', () => {
  let helperService: HelperService;
  let appMock: INestApplication;
  let moduleRefMock: ModuleRef;

  beforeEach(() => {
    moduleRefMock = createModuleRefMock() as ModuleRef;
    appMock = createAppMock() as INestApplication;

    helperService = new HelperService(moduleRefMock);
    helperService.initialize(appMock);
  });

  describe('get: from helper module(optionally app) context', () => {
    it('should retrieve an instance of the provided helper ', async () => {
      const expectedResult = 'SomeHelper';

      jest.spyOn(moduleRefMock, 'get').mockImplementation(() => expectedResult);

      const result = await helperService.getHelper('SomeHelper');

      expect(moduleRefMock.get).toHaveBeenCalledWith('SomeHelper'.toUpperCase());
      expect(result).toBe(expectedResult);
    });

    it('should retrieve an instance of the provided injectable', async () => {
      const expectedResult = 'SomeHelper';

      jest.spyOn(moduleRefMock, 'get').mockImplementation(() => {
        throw new Error('Helper not found');
      });

      jest.spyOn(appMock, 'get').mockImplementation(() => expectedResult);

      const result = await helperService.getHelper('SomeHelper');

      expect(moduleRefMock.get).toHaveBeenCalledWith('SomeHelper'.toUpperCase());
      expect(appMock.get).toHaveBeenCalledWith('SomeHelper');
      expect(result).toBe(expectedResult);
    });

    it('should retrieve an instance of the provided helper - by type', async () => {
      class SomeClass {}
      const expectedResult = new SomeClass();
      jest.spyOn(moduleRefMock, 'get').mockImplementation(() => expectedResult);

      const result = await helperService.getHelper(SomeClass);

      expect(moduleRefMock.get).toHaveBeenCalledWith(SomeClass);

      expect(result).toBe(expectedResult);
    });

    it('should throw an error if the helper or injectable is not found', async () => {
      jest.spyOn(moduleRefMock, 'get').mockImplementation(() => {
        throw new Error('Helper not found');
      });

      jest.spyOn(appMock, 'get').mockImplementation(() => {
        throw new Error('Helper not found');
      });

      await expect(helperService.getHelper('NonExistentHelper')).rejects.toThrowError();
      expect(moduleRefMock.get).toHaveBeenCalledWith('NonExistentHelper'.toUpperCase());
      expect(appMock.get).toHaveBeenCalledWith('NonExistentHelper');
    });

    it('should throw an error if the helper or injectable is not found - for type', async () => {
      class SomeClass {}

      jest.spyOn(moduleRefMock, 'get').mockImplementation(() => {
        throw new Error('Helper not found');
      });

      jest.spyOn(appMock, 'get').mockImplementation(() => {
        throw new Error('Helper not found');
      });

      await expect(helperService.getHelper(SomeClass)).rejects.toThrowError();
      expect(moduleRefMock.get).toHaveBeenCalledWith(SomeClass);
      expect(appMock.get).toHaveBeenCalledWith(SomeClass);
    });
  });
  describe('get: from app Context', () => {
    it('should retrieve an instance of the provided service', async () => {
      const expectedResult = 'SomeService';

      jest.spyOn(appMock, 'get').mockImplementation(() => expectedResult);

      const result = await helperService.get('SomeService');

      expect(appMock.get).toHaveBeenCalledWith('SomeService');
      expect(result).toBe(expectedResult);
    });
    it('should retrieve an instance of the provided service - by type', async () => {
      class SomeClass {}
      const expectedResult = new SomeClass();

      jest.spyOn(appMock, 'get').mockImplementation(() => expectedResult);

      const result = await helperService.get(SomeClass);

      expect(appMock.get).toHaveBeenCalledWith(SomeClass);

      expect(result).toBe(expectedResult);
    });

    it('should throw an error if the service or injectable is not found', async () => {
      jest.spyOn(appMock, 'get').mockImplementation(() => {
        throw new Error('Helper not found');
      });

      await expect(helperService.get('NonExistentHelper')).rejects.toThrowError();
      expect(appMock.get).toHaveBeenCalledWith('NonExistentHelper');
    });
  });
});
