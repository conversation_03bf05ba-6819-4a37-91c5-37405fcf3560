import { Test, TestingModule } from '@nestjs/testing';
import { Lrs<PERSON>elper } from './lrs.helper';
import { ConfigService } from '@nestjs/config';
import { LrsService } from '../common/services/communication/lrs/lrs.service';
import { KafkaService } from '../common/services/communication/kafka/kafka.service';
import { Logger } from '../logging/logger';

// Mock dependencies
jest.mock('../logging/logger');
jest.mock('md5', () => jest.fn((str) => `md5_${str}`));

describe('LrsHelper', () => {
  let lrsHelper: LrsHelper;
  let configService: ConfigService;
  let lrsService: LrsService;
  let kafkaService: KafkaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LrsHelper,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: LrsService,
          useValue: {
            sendLrsData: jest.fn(),
          },
        },
        {
          provide: KafkaService,
          useValue: {
            publish: jest.fn(),
          },
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: {
            createHmac: jest.fn().mockReturnValue('test_hmac_key'),
          },
        },
      ],
    }).compile();

    lrsHelper = module.get<LrsHelper>(LrsHelper);
    configService = module.get<ConfigService>(ConfigService);
    lrsService = module.get<LrsService>(LrsService);
    kafkaService = module.get<KafkaService>(KafkaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
    if (global.window) delete global.window;
    if (global.navigator) delete global.navigator;
  });

  it('should be defined', () => {
    expect(lrsHelper).toBeDefined();
  });

  describe('sendDataToLrs', () => {
    const userData = { id: 'user1', email: '<EMAIL>', name: 'Test User', first_name: 'Test', last_name: 'User', timezone: 'UTC', roles: ['user'] };
    const lrsData = { verb: 'login', objectType: 'test', objectId: '123', dataVals: ['a', 'b'] };

    it('should send data to LRS and Kafka when enabled', async () => {
      (configService.get as jest.Mock).mockImplementation((key: string) => {
        if (key === 'lrsEnabled') return '1';
        return `test_${key}`;
      });

      await lrsHelper.sendDataToLrs(userData, lrsData);

      expect(lrsService.sendLrsData).toHaveBeenCalled();
      expect(kafkaService.publish).toHaveBeenCalled();
    });

    it('should not send to LRS if disabled, but still publish to Kafka', async () => {
        (configService.get as jest.Mock).mockImplementation((key: string) => {
            if (key === 'lrsEnabled') return '0';
            return `test_${key}`;
        });

        await lrsHelper.sendDataToLrs(userData, lrsData);

        expect(lrsService.sendLrsData).not.toHaveBeenCalled();
        expect(kafkaService.publish).toHaveBeenCalled();
    });

    it('should use group data when provided', async () => {
        (configService.get as jest.Mock).mockReturnValue('1');
        const groupData = { gid: 123, groupName: 'Test Group' };
        await lrsHelper.sendDataToLrs(userData, lrsData, groupData);
        expect(lrsService.sendLrsData).toHaveBeenCalledWith(expect.objectContaining({
            applicationId: 123,
            applicationName: 'Test Group',
        }));
    });

    it('should use group gid for appName if groupName is not a string', async () => {
        (configService.get as jest.Mock).mockReturnValue('1');
        const groupData = { gid: 456, groupName: {} }; // not a string
        await lrsHelper.sendDataToLrs(userData, lrsData, groupData);
        expect(lrsService.sendLrsData).toHaveBeenCalledWith(expect.objectContaining({
            applicationId: 456,
            applicationName: 'group_456',
        }));
    });

    it('should handle anonymous user data', async () => {
        (configService.get as jest.Mock).mockImplementation((key: string) => {
            if (key === 'lrsUserSecret') return 'test_lrsUserSecret';
            return '1';
        });
        await lrsHelper.sendDataToLrs({}, lrsData);
        expect(lrsService.sendLrsData).toHaveBeenCalledWith(expect.objectContaining({
            userId: 'anonymous_userid',
            userEmail: 'anonymous_email',
            signature: 'md5_test_lrsUserSecretanonymous_userid'
        }));
    });

    it('should handle lrsData without dataVals', async () => {
        (configService.get as jest.Mock).mockReturnValue('1');
        const noDataVals = { ...lrsData, dataVals: undefined };
        await lrsHelper.sendDataToLrs(userData, noDataVals);
        expect(lrsService.sendLrsData).toHaveBeenCalled();
        const sentData = (lrsService.sendLrsData as jest.Mock).mock.calls[0][0];
        expect(sentData.dataVals).toBeUndefined();
    });

    it('should use window and navigator when available', async () => {
        (configService.get as jest.Mock).mockReturnValue('1');
        global.window = { document: { referrer: 'http://referrer.com' } } as any;
        global.navigator = { userAgent: 'mock-user-agent' } as any;

        await lrsHelper.sendDataToLrs(userData, lrsData);

        expect(lrsService.sendLrsData).toHaveBeenCalledWith(expect.objectContaining({
            referalUrl: 'http://referrer.com',
            userAgent: 'mock-user-agent',
        }));
    });

    it('should handle verbs with no associated topics', async () => {
        (configService.get as jest.Mock).mockReturnValue('1');
        const noTopicLrsData = { ...lrsData, verb: 'logout' };
        await lrsHelper.sendDataToLrs(userData, noTopicLrsData);
        expect(lrsService.sendLrsData).toHaveBeenCalled();
        expect(kafkaService.publish).not.toHaveBeenCalled();
    });

    it('should log and throw an error if something fails', async () => {
        const error = new Error('LRS service failed');
        (configService.get as jest.Mock).mockReturnValue('1');
        (lrsService.sendLrsData as jest.Mock).mockImplementation(() => {
            throw error;
        });

        await expect(lrsHelper.sendDataToLrs(userData, lrsData)).rejects.toThrow(error);
        expect(Logger.error).toHaveBeenCalledWith('Error sending lrs data', error);
    });
  });

  describe('sendDataToKafka', () => {
    const topic = 'test_topic';
    const data = { message: 'hello' };

    it('should publish data to a Kafka topic', async () => {
      await lrsHelper.sendDataToKafka(topic, data);
      expect(kafkaService.publish).toHaveBeenCalledWith(topic, data, 'test_hmac_key');
    });

    it('should not publish if topic is not provided', async () => {
        await lrsHelper.sendDataToKafka('', data);
        expect(kafkaService.publish).not.toHaveBeenCalled();
    });

    it('should log and throw an error if publishing fails', async () => {
        const error = new Error('Kafka publish failed');
        (kafkaService.publish as jest.Mock).mockImplementation(() => {
            throw error;
        });

        await expect(lrsHelper.sendDataToKafka(topic, data)).rejects.toThrow(error);
        expect(Logger.error).toHaveBeenCalledWith('Error sending data to kafka', error);
    });
  });

  describe('_formatLRSData', () => {
    const baseData = {
        applicationId: 'app1',
        applicationName: 'App One',
        userEmail: '<EMAIL>',
        eventTime: Date.now(),
        verb: 'tested',
        objectName: 'Test Object',
        objectType: 'test',
        ip: '127.0.0.1',
        userAgent: 'jest',
        url: '/test',
    };

    it('should format data correctly when all fields are present', () => {
        const data = { ...baseData, objectId: 'obj1' };
        const result = (lrsHelper as any)._formatLRSData(data);
        expect(result).toBeDefined();
        expect(result.objectInfo).toBe('test_obj1');
        expect(result.created).toBeDefined();
    });

    it('should handle missing non-mandatory fields', () => {
        const result = (lrsHelper as any)._formatLRSData(baseData);
        expect(result).toBeDefined();
        expect(result.objectId).toBe('');
        expect(result.objectInfo).toBe('test_');
    });

    it('should throw an error if a mandatory field is missing', () => {
        const incompleteData = { verb: 'login' }; // Missing mandatory fields
        expect(() => (lrsHelper as any)._formatLRSData(incompleteData)).toThrow('Missing field value for applicationId');
    });
  });
});
