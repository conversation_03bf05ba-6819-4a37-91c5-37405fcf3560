import { Inject, Res } from '@nestjs/common';
import { HelperService } from './helper.service';
import { Logger } from '../logging/logger';
import { AuthTokenHelper } from '../auth/helper/auth.tokenhelper';
import { ConfigService } from '@nestjs/config';
import { ResponseCookieType, TribeUserType } from '../common/typeDef/auth.type';
import { UserMgmtCommunityService } from '../user/services/communication/usermgmt.community.service';
import { VIEW_PAGES } from '../auth/config/view.constants';
import { Utility } from './../common/util/utility';

export class CookieHelper {
  @Inject(HelperService) private readonly helperService: HelperService;
  @Inject(ConfigService) private readonly configService: ConfigService;

  async setCookie(
    @Res() res,
    cookieName: string,
    cookieValue: string,
    options: {
      httpOnly?: boolean;
      expires?: Date;
      path?: string;
      domain?: string;
      secure?: boolean;
      sameSite?: string;
    } = {},
  ) {
    const defaultOptions = {
      httpOnly: true,
      expires: new Date(Date.now() - 3600000),
      path: '/',
      domain: this.configService.get('ssoCookieDomain'),
      secure: true,
      // sameSite: 'Strict', //TODO: Need to check with other production applications
    };

    // Conditionally merge defaultOptions with provided options
    //To support other dependent apps. disabled httpOnly and secure option from cookie
    const mergedOptions = {
      // ...(options.httpOnly !== undefined ? { httpOnly: options.httpOnly } : { httpOnly: defaultOptions.httpOnly }),
      ...(options.expires !== undefined ? { maxAge: options.expires } : { maxAge: defaultOptions.expires }),
      ...(options.path !== undefined ? { path: options.path } : { path: defaultOptions.path }),
      ...(options.domain !== undefined ? { domain: options.domain } : { domain: defaultOptions.domain }),
      // ...(options.secure !== undefined ? { secure: options.secure } : { secure: defaultOptions.secure }),
      // ...(options.sameSite !== undefined ? { sameSite: options.sameSite } : { sameSite: defaultOptions.sameSite }),
    };
    res.cookie(cookieName, cookieValue, mergedOptions);
  }

  clearCookieIfExists(@Res() res,@Res() req, cookieName) {
    if (req.cookies[cookieName]) {
      res.clearCookie(cookieName, {
    httpOnly: true,
    domain: this.configService.get('ssoCookieDomain'),
      });
    }
  };

  clearCookie(@Res() res, cookieKey) {
    res.clearCookie(cookieKey, {
      httpOnly: true,
      domain: this.configService.get('ssoCookieDomain'),
    });
  }
  async getCalendarCookie(calendarUrl: string, @Res() res): Promise<string> {
    try {
      const encodedUrl = encodeURIComponent(calendarUrl);
      calendarUrl = this.configService.get('calendarUrl') + encodedUrl;
      await this.setCookie(res, this.configService.get('calendarRedirect'), calendarUrl, {
        expires: this.configService.get('cookieMaxAge'),
      });
      return calendarUrl;
    } catch (error: any) {
      Logger.error('getCalendarCookie', {
        METHOD: this.constructor?.name + '@' + this.getCalendarCookie?.name,
        MESSAGE: error.message,
        REQUEST: { calendarUrl: calendarUrl },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }
  async setCalenderCookie(calendarUrl: string, @Res() res) {
    calendarUrl = calendarUrl ? calendarUrl.trim() : '';
    if (calendarUrl) {
      const maxAge = new Date(Date.now() + 3600000);
      await this.setCookie(res, this.configService.get('calendarRedirect'), calendarUrl, { expires: maxAge });
    }
  }

  async setFermiumCookie(assignmentToken: string, @Res() res) {
    try {
      assignmentToken = assignmentToken ? assignmentToken.trim() : '';
      if (assignmentToken) {
        const maxAge = new Date(Date.now() + 3600000);
        await this.setCookie(res, this.configService.get('freemiumAssignmentToken'), assignmentToken, {
          expires: maxAge,
        });
      }
    } catch (error: any) {
      Logger.error('setFermiumCookie', {
        METHOD: this.constructor.name + '@' + this.setFermiumCookie.name,
        MESSAGE: error.message,
        REQUEST: { assignmentToken },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async setNpsCookie(redirectUrl: string, @Res() res) {
    if (!Utility.isEmpty(redirectUrl) && Utility.validateNpsSessionUrl(redirectUrl)) {
      redirectUrl = decodeURIComponent(redirectUrl);
      const maxAge = new Date(Date.now() + 3600000);
      const authTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
      const npsCookiePayload = { url: redirectUrl };
      const npsCookieValue = await authTokenHelper.createSignedToken(npsCookiePayload, {
        secret: this.configService.get('jwtSecret'),
      });
      await this.setCookie(res, this.configService.get('npsRedirectCookie'), npsCookieValue, {
        expires: maxAge,
      });
    }
  }
  async setCommunityCookie(redirectUrl: string, @Res() res) {
    try {
      if (
        !Utility.isEmpty(redirectUrl) &&
        Utility.validateCommunityUrl({
          url: redirectUrl,
          communityBaseUrl: this.configService.get('communityBaseUrl'),
          tribeCommunityBaseUrl: this.configService.get('tribeCommunityBaseUrl'),
        })
      ) {
        redirectUrl = decodeURIComponent(redirectUrl);
        const maxAge = new Date(Date.now() + 3600000);
        const authTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
        //set community cookie

        const isTribeCommunityUrl = Utility.isTribeCommunityUrl(
          redirectUrl,
          this.configService.get('tribeCommunityBaseUrl'),
        );
        let communityCookiePayload = {};
        if (isTribeCommunityUrl) {
          communityCookiePayload = { url: redirectUrl, isTribeRedirect: true };
        } else {
          communityCookiePayload = { url: redirectUrl };
        }
        const communityCookieValue: string = await authTokenHelper.createSignedToken(communityCookiePayload, {
          secret: this.configService.get('jwtSecret'),
        });
        await this.setCookie(res, this.configService.get('communityCookie'), communityCookieValue, {
          expires: maxAge,
        });
      }
    } catch (error: any) {
      Logger.error('setCommunityCookie', {
        METHOD: this.constructor.name + '@' + this.setCommunityCookie.name,
        MESSAGE: error.message,
        REQUEST: { redirectUrl },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }
  async getRedirectUrlFromCookie(
    redirectUrls: { calendar_url: string; redirect_url: string },
    userTokenDetail: { id: string; email: string; name: string },
  ): Promise<string> {
    try {
      let redirectUrl: string;
      const calendarUrl: string = redirectUrls?.calendar_url || '';
      const redirectURLValue: string = redirectUrls?.redirect_url || '';

      redirectUrl = decodeURIComponent(redirectURLValue.trim());
      const isTribeCommunityUrl = Utility.isTribeCommunityUrl(
        redirectUrl,
        this.configService.get('tribeCommunityBaseUrl'),
      );
      if (isTribeCommunityUrl) {
        const cryptoHelper = await this.helperService.getHelper('CRYPTO_HELPER');
        const userId: string = cryptoHelper.decrypt(userTokenDetail?.id) || '';
        const userName: string = userTokenDetail?.name || '';
        const email: string = cryptoHelper.decrypt(userTokenDetail?.email) || '';
        const userInfoForTribe: TribeUserType = {
          id: userId,
          name: userName,
          email: email,
        };
        const usermgmtCommunityServiceInstance: UserMgmtCommunityService =
          await this.helperService.get<UserMgmtCommunityService>(UserMgmtCommunityService);
        const tribeSSORedirectUrl = await usermgmtCommunityServiceInstance.getTribeSSOLink(userInfoForTribe);
        redirectUrl = tribeSSORedirectUrl;
        return redirectUrl;
      }
      if (calendarUrl) {
        const encodedCalendarUrl: string = encodeURIComponent(calendarUrl);
        redirectUrl = `${this.configService.get('calendarUrl')}${encodedCalendarUrl}`;
        return redirectUrl;
      }
      redirectUrl = VIEW_PAGES.VIEW_ROUTES.PROFILES.PROFILE;
      return redirectUrl;
    } catch (error: any) {
      Logger.error('getRedirectUrlFromCookie', {
        METHOD: this.constructor?.name + '@' + this.getRedirectUrlFromCookie.name,
        MESSAGE: error.message,
        REQUEST: { redirectUrls },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  setBulkCookie(res, data: ResponseCookieType[], options) {
    data.forEach((cookieValue) => {
      this.setCookie(res, this.configService.get(cookieValue.name), cookieValue.value, options);
    });
  }

  clearBulkCookie(res, data: Array<string>) {
    data.forEach((cookieValue) => {
      this.clearCookie(res, cookieValue);
    });
  }
}
