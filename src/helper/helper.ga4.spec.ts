import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { GoogleAnalytics } from './helper.ga4';
import { Logger } from '../logging/logger';

// Mock dependencies
jest.mock('axios');
jest.mock('uuid');
jest.mock('../logging/logger');

describe('GoogleAnalytics', () => {
  let googleAnalytics: GoogleAnalytics;
  let configService: ConfigService;

  const mockConfig = {
    cdp: {
        ga4: {
        ga4APIUrl: 'https://test.com/mp/collect',
        ga4APISecret: 'test-secret',
        ga4MeasurementId: 'G-TEST12345',
        },
    }
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GoogleAnalytics,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue(mockConfig.cdp),
          },
        },
      ],
    }).compile();

    googleAnalytics = module.get<GoogleAnalytics>(GoogleAnalytics);
    configService = module.get<ConfigService>(ConfigService);
    (configService.get as jest.Mock).mockReturnValue(mockConfig.cdp);

  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendEvents', () => {
    const eventData = {
      user_id: 'user123',
      event_name: 'test_event',
      event_params: { param1: 'value1' },
    };

    it('should send events successfully', async () => {
      const responseData = { status: 'success' };
      (axios.post as jest.Mock).mockResolvedValue({ data: responseData });

      const result = await googleAnalytics.sendEvents('client123', eventData);

      expect(axios.post).toHaveBeenCalledWith(
        `${mockConfig.cdp.ga4.ga4APIUrl}?api_secret=${mockConfig.cdp.ga4.ga4APISecret}&measurement_id=${mockConfig.cdp.ga4.ga4MeasurementId}`,
        {
          clientId: 'client123',
          user_id: 'user123',
          sl_freemium_user: false,
          events: [{ event_name: 'test_event', event_params: { param1: 'value1' } }],
        },
        { headers: { 'Content-Type': 'application/json' } },
      );
      expect(Logger.log).toHaveBeenCalledWith('Google Analytics response:', responseData);
      expect(result).toEqual(responseData);
    });

    it('should generate a clientID if not provided', async () => {
      const generatedUUID = 'generated-uuid';
      (uuidv4 as jest.Mock).mockReturnValue(generatedUUID);
      (axios.post as jest.Mock).mockResolvedValue({ data: {} });

      await googleAnalytics.sendEvents('', eventData);

      expect(uuidv4).toHaveBeenCalled();
      expect(axios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({ clientId: generatedUUID }),
        expect.any(Object),
      );
    });

    it('should handle sl_freemium_user correctly', async () => {
      (axios.post as jest.Mock).mockResolvedValue({ data: {} });
      const freemiumEventData = { ...eventData, sl_freemium_user: true };

      await googleAnalytics.sendEvents('client123', freemiumEventData);

      expect(axios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({ sl_freemium_user: true }),
        expect.any(Object),
      );
    });

    it('should throw an error if user_id is not provided', async () => {
      const invalidData = { event_name: 'test_event' };
      await expect(googleAnalytics.sendEvents('client123', invalidData)).rejects.toThrow('Please provide user_id');
    });

    it('should throw and log an error when sending events fails', async () => {
      const error = new Error('Network Error');
      (axios.post as jest.Mock).mockRejectedValue(error);

      await expect(googleAnalytics.sendEvents('client123', eventData)).rejects.toThrow(error);
      expect(Logger.error).toHaveBeenCalledWith('Error sending events:', error);
    });
  });

  describe('generateUniqueID', () => {
    it('should return a UUID', () => {
      const generatedUUID = 'a-unique-id';
      (uuidv4 as jest.Mock).mockReturnValue(generatedUUID);

      const result = googleAnalytics.generateUniqueID();

      expect(uuidv4).toHaveBeenCalled();
      expect(result).toBe(generatedUUID);
    });
  });
});
