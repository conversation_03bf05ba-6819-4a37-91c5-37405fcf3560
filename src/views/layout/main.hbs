<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Learning on Simplilearn</title>
  <link rel="stylesheet" href="/frontend/css/bootstrap.css" />
  <link rel="icon" type="image/x-icon" href="/images/favicon.png">
  <link rel="stylesheet" href="../frontend/css/main.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js/public/assets/styles/choices.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
</head>

<body>
   {{{ injectGlobalVariables }}}
  {{> (route)}}
  <script src="/frontend/js/common.js"></script>
  <script src="/frontend/js/amplitude_webengage.js"></script>
  {{> routeScripts}}
  <h1>{{{ injectGA4 }}}</h1>
  
  <script src="/frontend/js/ga4events.js"></script>
  <script type="text/javascript" src="https://cfs9.simplicdn.net/frontend/js/app/CountryData.js"></script>
  <script id="_webengage_script_tag" type="text/javascript">
    var webengage;
    !function(e,t,n,o,i){
      function s(e,t){e[t[t.length-1]]=function(){a.__queue.push([t.join("."),arguments])}}
      var r,g,a=e[n],c=" ",l="init options track screen onReady".split(c),p="feedback survey notification".split(c),u="options render clear abort".split(c),f="Open Close Submit Complete View Click".split(c),d="identify login logout setAttribute".split(c);
      if(!a||!a.__v){
        for(e[n]=a={__queue:[],__v:"6.0",user:{}},r=0;r<l.length;r++)s(a,[l[r]]);
        for(r=0;r<p.length;r++){
          for(a[p[r]]={},g=0;g<u.length;g++)s(a[p[r]],[p[r],u[g]]);
          for(g=0;g<f.length;g++)s(a[p[r]],[p[r],"on"+f[g]])
        }
        for(r=0;r<d.length;r++)s(a.user,["user",d[r]]);
        setTimeout(function(){
          var e=t.createElement("script"),n=t.getElementById("_webengage_script_tag");
          e.type="text/javascript",e.async=!0,e.src=("https:"==t.location.protocol?"https://ssl.widgets.webengage.com":"http://cdn.widgets.webengage.com")+"/js/webengage-min-v-6.0.js",n.parentNode.insertBefore(e,n)
        })
      }
    }(window,document,"webengage");
  </script>
  <script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>
    {{> auth/termsModal }} 
</body>
</html>