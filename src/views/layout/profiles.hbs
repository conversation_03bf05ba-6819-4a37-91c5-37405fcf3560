<script>
  const profileUpdate = "{{profileStatus.status}}";
  const isPglearner = true;
  const affiliateId = "{{affiliateId}}";
  const groupName = "{{groupName}}";
  const userIp = "{{userIp}}"
</script>
<script type='text/javascript' src='https://service.force.com/embeddedservice/5.0/esw.min.js'></script>
{{> user/header}}


<div class="profile-sec" role="main" aria-label="User Profile Section">
<div class="profile-updated hide">
    <p class="success_msg">Your profile has been successfully updated.</p>
</div>
<div class="container">
        <h2 class="header-wrap my-profile" tabindex="0" aria-label="My Profile">
        My Profile
        {{#if gdprExport}}
        <div class="drop_list export_options dropdown" role="menu">
            <i class="dropdown-icon" id="dropdown-icon" tabindex="0" role="button" aria-haspopup="true"
                aria-expanded="false"></i>
            <ul class="dropdown-menu" id="profile-dropdown" role="menu" aria-hidden="true">
                {{#if (equals showDownloadButton "Y")}}
                    <script type="text/javascript">
                        export_options = 1;
                    </script>
                    <li class="download">
                        <a href="#" class="download-data" role="menuitem">Download profile data</a>
                    </li>
                    {{#unless isB2bStudent}}
                    <li class="deleted">
                        <a href="#" class="delete-data" role="menuitem">Delete account</a>
                    </li>
                    {{/unless}}
                {{/if}}
            </ul>
        </div>
        {{/if}}
    </h2>

    <div class="profile-content">
        <!-- Left Navigation -->
        <div class="left_nav_menu_wrap" role="navigation" aria-label="Profile Navigation">
            {{> user/leftNav}}
        </div>
        <div class="col-md-9 col-sx-9 pr-tab-content">
            {{> routeProfiles}}
            <div class="profile-wraper" id="profile">
                <section class="show-profile" aria-labelledby="profile">
                    <div class="form_main_wrapper bounceInLeft">
                        <div class="panel-body">
                            <div class="tab-content">
                                <div id="basic_tab" class="tab-section {{#if (equals profileStatus.edit_type 'basic')}}active{{/if}}" role="tabpanel"
                                    aria-labelledby="basicTabLabel">
                                    <h3 id="basicTabLabel" class="sr-only">Basic Details</h3>
                                    {{> user/basicDetails}}
                                </div>
                                <div id="contact_tab" class="tab-section {{#if (equals profileStatus.edit_type 'contact')}}active{{/if}}" role="tabpanel"
                                    aria-labelledby="contactTabLabel">
                                    <h3 id="contactTabLabel" class="sr-only">Contact Details</h3>
                                    {{> user/contactDetails}}
                                </div>
                                <div id="professional_tab" class="tab-section {{#if (equals profileStatus.edit_type 'professional')}}active{{/if}}" role="tabpanel"
                                    aria-labelledby="professionalTabLabel">
                                    <h3 id="professionalTabLabel" class="sr-only">Professional Details</h3>
                                    {{> user/professionalDetails}}
                                </div>
                                <div id="academics_tab" class="tab-section {{#if (equals profileStatus.edit_type 'academics')}}active{{/if}}" role="tabpanel"
                                    aria-labelledby="academicsTabLabel">
                                    <h3 id="academicsTabLabel" class="sr-only">Academic Details</h3>
                                    {{> user/academicDetails}}
                                </div>
                                <div id="outcome_tab" class="tab-section {{#if (equals profileStatus.edit_type 'outcome')}}active{{/if}}" role="tabpanel"
                                    aria-labelledby="outcomeTabLabel">
                                    <h3 id="outcomeTabLabel" class="sr-only">Learning Outcome</h3>
                                    {{> user/outcomeDetails}}
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
</div>
</div>
{{> user/helpAndSupport}}

<!-- Include modals at the bottom of the body, outside the profile-sec container -->
{{#if gdprExport}}
    {{#if (equals showDownloadButton "Y")}}
        {{> user/exportProfileModal}}
        {{> user/deleteAccountModal}}
    {{/if}}
{{/if}}

{{> user/footer}}