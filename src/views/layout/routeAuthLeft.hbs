{{#isEqual route.page 'register-email'}}
    {{> auth/registerEmail}}
{{/isEqual}}

{{#isEqual route.page 'register'}}
    {{> auth/register}}
{{/isEqual}}

{{#isEqual route.page 'login'}}
    {{> auth/login}}
{{/isEqual}}
    
{{#isEqual route.page 'forgot-password'}}
    {{> auth/forgotPassword}}
{{/isEqual}}

{{#isEqual route.page 'reset'}}
    {{> auth/resetPassword}}
{{/isEqual}}

{{#isEqual route.page 'social-link'}}
    {{> auth/socialLink}}
{{/isEqual}}

{{#isEqual route.page 'register-complete'}}
    {{> auth/registerComplete}}
{{/isEqual}}

{{#isEqual route.page 'multi-account'}}
    {{> auth/multiAccount}}
{{/isEqual}}

{{#isEqual route.page 'account-setup'}}
    {{> auth/registerEmail}}
{{/isEqual}}