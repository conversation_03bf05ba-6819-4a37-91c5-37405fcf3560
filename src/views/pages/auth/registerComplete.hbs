{{#setMessages 'registerComplete'}}
{{/setMessages}}
<div class="container register-complete-container">
   <div class="row">
      <div class="logo-image">
         <a href="https://accounts.simplilearn.com">
            <img src="https://cfaccounts.simplilearn.com/frontend/images/simplilearn-logo.png" alt="Simplilearn Logo">
         </a>
         <span class="logo-text">Learning Platform</span>
      </div>
      <div class="content-heading">Enter your contact number</div>
      <div class="content-sub-heading register-completed">
         Please provide a valid contact number to keep <br>
            your account safe and secure.
            </div>
      <div class="form-container form_div_account register-complete-wrap">
<div class="form-container form_div_account form_container signup-wrap" role="region" aria-label="Sign Up with Email">
   <div id="error_msg" class="error_msg hide">
      {{#if validationErrors}}
      {{validationErrors.[0]}}
      <script>
         var registerErrors = "{{validationErrors}}"
      </script>
      {{/if}}
   </div>
         <div class="sso-details">
            <div class="sso-item">
               <div class="sso-logo social-logo">
                  <div class="{{type}}-logo logo-item"></div>
               </div>
            </div>
            <div class="sso-icon sso-social-icon"></div>
            <div class="sso-item">
               <div class="sso-logo">
                  <img src="//cfaccounts.simplicdn.net/frontend/images/simplilearn-logo.png" alt="Simplilearn Logo">
               </div>
            </div>
         </div>
         <form name="reg-form" method="post" id="registerComplete" action="{{route.action}}">
            <div id="error_box" class="hide"></div>
            <div class="row form-input">
               <div class="col-xs-6 pr-20">
                  <label>First name<span>*</span></label>
                  <input type="text" aria-label="First Name" id="first_name" name="first_name" placeholder="First name" autocomplete="0"
                     value="{{formData.first_name}}" oninput="allowTextOnly(event)" required class="uname fname">
               </div>
               <div class="col-xs-6 pl-0">
                  <label>Last name<span>*</span></label>
                  <input type="text" aria-label="Last Name" id="last_name" name="last_name" placeholder="Last name" autocomplete="0"
                     value="{{formData.last_name}}" oninput="allowTextOnly(event)" required class="uname lname">
               </div>
            </div>
            <div class="form-input">
               <label>Email<span>*</span></label>
               <input type="email" id="mail" name="email" aria-label="Email Address" placeholder="Email address"
                    value="{{#if email}}{{email}}{{else if dataPassToView.email}}{{dataPassToView.email}}{{else}}{{formData.email}}{{/if}}" 
                    readonly="readonly" autocomplete="0" required class="email">
            </div>
            <div class="support_coutry_code form-input">
               <div class="support_inner">
                  <div class="country">
                     <label>Country<span>*</span></label>
                     <div class="select_wraper for_cun">
                        <select tabindex="0" aria-label="Country Code Drop Down" id="country_code" name="country_code"
                           class="sel_cr">
                        </select>
                        <b class="caret"></b>
                     </div>
                  </div>
                  <input type="hidden" name="country_data" id="country_data">
                  <div class="contact">
                     <label>Contact number<span>*</span></label>
                     <div class="placeholder_wrap code_wrap">
                  <span id="country_code_span" class="country_code_span">
                     {{prependText}}
                  </span>
                        <input type="number" aria-label="Phone Number" id="phone_no" name="phone_no" placeholder="Contact number"
                           maxlength="15" minlength="7" autocomplete="0" required class="phone-no" value="{{#if dataPassToView.phoneNo}}{{dataPassToView.phoneNo}}{{else}}{{formData.phone_no}}{{/if}}" ></input>
                     </div>
                     <span id="phoneError"></span>
                  </div>
               </div>
            </div>
            <div class="password-field form-input">
               
            </div>
            {{!-- <div class="form-input">
               <input aria-label="Terms and Conditions" tabindex="0" style="display:inline-block;" type="checkbox"
                  id="termCondition" name="termCondition" value="Y" />
               <label for="termCondition" class="terms-content"><span>I agree to the <a tabindex="0" class="forgot_password link1" href="javascript:void(0);" onclick="TermsConsent.showModalAndWait(document.getElementById('registerComplete'))">Terms and Conditions.</a></span></label>
            </div> --}}
            <input type="hidden" name="termCondition" id="termCondition" value="Y">
            <input type="hidden" id="termsAndConditionsContent" value="{{termsAndConditions}}">
            <input type="hidden" id="termsAndConditionsStyles" value="{{termsStyles}}">
            
            <div class="submit-container">
               <input tabindex="0" title="Create Account" type="submit" id="btn_register" name="btnlogin" value="Create Account"
                  class="btn btn-default btn-signup disabled" data-event="sl_lms_user_login_clicked" data-eventd=""
                  data-clickedtype="Email">
            </div>
         </form>
         <div class="create_account d-flex justify-content-center">
            <p class="back-register"><a href="register" class="link1">Other Sign Up options</a> </p>
         </div>
      </div>
   </div>
</div>