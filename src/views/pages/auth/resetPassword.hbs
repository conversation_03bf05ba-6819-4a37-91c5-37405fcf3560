<div class="reset-password-container">
    <div class="logo-image">
        <a href="https://accounts.simplilearn.com">
            <img src="https://cfaccounts.simplilearn.com/frontend/images/simplilearn-logo.png" alt="Simplilearn Logo">
        </a>
        <span class="logo-text">Learning Platform</span>
    </div>
    
    {{#if status}}
        {{#unless validationErrors}}
            {{!-- SUCCESS STATE: Password reset successful --}}
            <div class="additional-wrapper">
                <div class="success-icon"></div>
                <div class="content-heading green">Password Reset Successfully</div>
                <div class="content-sub-heading">Please login with your new credentials</div>
                <button class="btn-signup btn btn-default" id="btn-reset-success">Return to Login</button>
            </div>
        {{else}}
            {{!-- ERROR STATE: Password reset failed with validation errors --}}
            <div class="content-heading">
                <div>Reset Password</div>
            </div>
            <div class="content-sub-heading">
                <div>
                    Enter and confirm a new password for the account.<br>
                    You will be logged out from all devices.
                </div>
            </div>
            <div class="form-container form_div_account form_container position-relative">
                <form name="resetForm" class="reset-form" id="resetPassword"
                    action="{{route.action}}/{{params.uid}}/{{params.token}}" method="post">
                    <div id="error_msg" class="error_msg hide">
                        {{validationErrors.[0]}}
                        <script>
                            var loginErrors = "{{validationErrors}}"
                        </script>
                    </div>
                    <div class="password-field form-input">
                        <div class="passowrd-wrap">
                            <input type="password" id="password" aria-label="Password" name="password" placeholder="Password" autocomplete="0" value="{{formData.password}}"
                                required class="password" />
                            <span aria-label="eye icon" tabindex="0" class="eye-icon password-icon info-icon password-eye-slash"
                                id="eye-icon"></span>
                            <span aria-label="eye icon" tabindex="0" class="eye-icon password-icon password-info info-icon info-invalid"
                                id="password-icon">
                                <div class="password-tooltip">
                                    <div class="password-heading">
                                        Password Criteria:
                                    </div>
                                    <div class="password-list">
                                        <ul>
                                            <li class="crit-min"> Password must be at least 8 characters long</li>
                                            <li class="crit-max">Password cannot be longer than 128 characters</li>
                                            <li class="crit-lower">It should contain at least one lowercase letter</li>
                                            <li class="crit-upper">It should contain at least one uppercase letter</li>
                                            <li class="crit-num">It should contain at least one number</li>
                                        </ul>
                                    </div>
                                       <i class="triangle"></i>
                                </div>
                            </span>
                        </div>
                    </div>
                    <div class="password-field form-input">
                        <input type="password" id="confirm-password" class="confirm-password" placeholder="Confirm Password*"
                            name="confirm-password" value="{{formData.confirm_password}}" required />
                        <span class="confirmpassword eye-icon password-icon password-eye-slash" id="reset-password-icon"></span>
                    </div>
                    <div class="submit-container">
                        <input tabindex="0" title="Create Account" type="submit" name="btnlogin" value="Send"
                            class="btn btn-default btn-signup disabled" data-event="sl_lms_user_login_clicked" data-eventd=""
                            data-clickedtype="Email" id="btn_reset" disabled>
                    </div>
                </form>
            </div>
        {{/unless}}
    {{else}}
        {{!-- INITIAL STATE: Show form for password reset --}}
        <div class="content-heading">
            <div>Reset Password</div>
        </div>
        <div class="content-sub-heading">
            <div>
                Enter and confirm a new password for the account.<br>
                You will be logged out from all devices.
            </div>
        </div>
        <div class="form-container form_div_account form_container position-relative">
            <form name="resetForm" class="reset-form" id="resetPassword"
                action="{{route.action}}/{{params.uid}}/{{params.token}}" method="post">
                <div id="error_msg" class="error_msg hide">
                    {{#if validationErrors}}
                    {{validationErrors.[0]}}
                    <script>
                        var loginErrors = "{{validationErrors}}"
                    </script>
                    {{/if}}
                </div>
                <div class="password-field form-input">
                    <div class="passowrd-wrap">
                        <input type="password" id="password" aria-label="Password" name="password" placeholder="Password" autocomplete="0" value="{{formData.password}}"
                            required class="password" />
                        <span aria-label="eye icon" tabindex="0" class="eye-icon password-icon info-icon password-eye-slash"
                            id="eye-icon"></span>
                        <span aria-label="eye icon" tabindex="0" class="eye-icon password-icon password-info info-icon info-invalid"
                            id="password-icon">
                            <div class="password-tooltip">
                                <div class="password-heading">
                                    Password Criteria:
                                </div>
                                <div class="password-list">
                                    <ul>
                                        <li class="crit-min"> Password must be at least 8 characters long</li>
                                        <li class="crit-max">Password cannot be longer than 128 characters</li>
                                        <li class="crit-lower">It should contain at least one lowercase letter</li>
                                        <li class="crit-upper">It should contain at least one uppercase letter</li>
                                        <li class="crit-num">It should contain at least one number</li>
                                    </ul>
                                </div>
                                   <i class="triangle"></i>
                            </div>
                        </span>
                    </div>
                </div>
                <div class="password-field form-input">
                    <input type="password" id="confirm-password" class="confirm-password" placeholder="Confirm Password*"
                        name="confirm-password" value="{{formData.confirm_password}}" required />
                    <span class="confirmpassword eye-icon password-icon password-eye-slash" id="reset-password-icon"></span>
                </div>
                <div class="submit-container">
                    <input tabindex="0" title="Create Account" type="submit" name="btnlogin" value="Send"
                        class="btn btn-default btn-signup disabled" data-event="sl_lms_user_login_clicked" data-eventd=""
                        data-clickedtype="Email" id="btn_reset" disabled>
                </div>
            </form>
        </div>
    {{/if}}
</div>