<div class='container social-link-container' role="main">
  <div class='row'>
    <div class='content-heading' role="heading" aria-level="1">
      {{#isEqual code 2}} Email not found {{/isEqual}}
      {{#isEqual code 0}} Account not found {{/isEqual}}
      {{#isEqual code 3}} Account found {{/isEqual}}
    </div>

    <div class='content-sub-heading' role="region" aria-live="polite">
      {{#isEqual code 2}}
        <div>There is no email address associated with your social account. Please use a different method to proceed.</div>
      {{/isEqual}}

      {{#isEqual code 0}}
        <div>The email address has no Simplilearn account associated with it. Do you want to continue with creating a new account?</div>
      {{/isEqual}}

      {{#isEqual code 3}}
        <div class="social-account-exists">
          It looks like you already have created a Simplilearn account with 
          {{#obfuscateEmail user.email}}{{/obfuscateEmail}}. 
          Would you like to go ahead and link your Social account?
        </div>
      {{/isEqual}}
    </div>

    <div class='form-container form_div_account form_container position-relative' role="form">
      <form name='registrationForm' class='mx-1 mx-md-4 registration-form' id='loginUser' 
            action='{{route.action}}' method='post' aria-labelledby="form-heading">

        <input type="hidden" name="emailAddress" id="emailAddress" value="{{user.email}}" />
        <input type="hidden" name="type" id="type" value="{{user.type}}" />
        <input type="hidden" name="userAuthState" id="userAuthState" value="{{buttonText}}" />

          {{#if validationErrors}}
        <div id='error_msg' class='error_msg' role="alert" aria-live="assertive">
            {{validationErrors.[0]}}
            <script> var loginErrors = "{{validationErrors}}"; </script>
        </div>
          {{/if}}

        <div>
          {{#isEqual code 3}}
            <input type='submit' name='btn_login' value='Link Account' 
                   class='btn btn-default btn-signup' id='btn_login' 
                   aria-label="Link your account with Simplilearn" />
          {{/isEqual}}

          {{#isEqual code 0}}
            <div class="signup_link">
              <a href="/auth/register-complete" class="link1" aria-label="Create a new ac</form>count on Simplilearn">Create New Account</a>
            </div>
          {{/isEqual}}

          {{#isEqual code 2}}
            <div class="signup_link">
              <a href="/user/login" class="link1" aria-label="Return to the login page">Go back to login</a>
            </div>
          {{/isEqual}}

          {{#isEqual code 3}}
            <div class="signup_link">
              <a href="/user/login" class="link1" aria-label="Return to the login page">Go back to login</a>
            </div>
          {{/isEqual}}
        </div>
      </form>
    </div>
  </div>
</div>
