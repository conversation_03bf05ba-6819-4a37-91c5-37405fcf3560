<div class="container multi-account-header">
    <form name="multiAccountForm" class="mx-1 registration-form" id="multiAccount"
                action="{{route.action}}" method="post">
    <div class="row">
        <div class="logo-image">
            <a href="https://accounts.simplilearn.com">
                <img src="https://cfaccounts.simplilearn.com/frontend/images/simplilearn-logo.png" alt="Simplilearn Logo">
            </a>
            <span class="logo-text">Learning Platform</span>
        </div>
        <div class="content-heading">Hey {{name}}!</div>
        <div class="content-sub-heading">
            <div>Your account is associated with multiple instances.<br>Please choose the instance from the list below to continue. </div>
        </div>
        <div class="multi-account">
            <div class="multi-account-container">
                <div class="row justify-content-center">
                    <div class="ml-2 col text-center">
                        <div class="d-flex flex-column">
                            {{#each items}}
                            <label class="input-radio">
                                <input type="radio" name="url" value="{{lmsHost}}" class="multi-radio-input"
                                    groupId="{{groupId}}" groupName="{{groupName}}" id="multi-radio-input" />
                                <div class="multi-radio-box">
                                    <div class="row row-no-padding">
                                        <div class="col-md-4 col-xs-4">
                                            {{#if (groupIdIsTwo groupId)}}
                                            <div class="multi-logo-box">
                                                <img class="multi-logo" src={{getMultiAccountLogo logoUrl}}>
                                            </div>
                                            {{else}}
                                            <div class="multi-logo-box">
                                                <img class="multi-logo" src={{getMultiAccountLogo logoUrl}} />
                                            </div>
                                            {{/if}}
                                        </div>
                                        <div class="col-md-8 col-xs-8 multi-group-details">
                                            <div class="multi-group-name">{{groupName}}</div>
                                            <div class="multi-group-url">{{lmsHost}}</div>
                                        </div>
                                    </div>
                                </div>
                            </label>
                            {{/each}}
                        </div>
                    </div>
                    <input type="submit" name="btn_signup" value="Continue" class="btn btn-default btn-signup"
                        id="btn-multi-account" disabled>
                </div>
            </div>
        </div>
    </div>
    </form>
</div>