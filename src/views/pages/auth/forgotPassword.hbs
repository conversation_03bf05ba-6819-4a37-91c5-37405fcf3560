<div class="forgot-password-container {{#if viewInfo.attemptLimitExceed}} limit-exceeded-container {{/if}}" role="main">
    <div class="logo-image">
        <a href="https://accounts.simplilearn.com">
            <img src="https://cfaccounts.simplilearn.com/frontend/images/simplilearn-logo.png" alt="Simplilearn Logo">
        </a>
        <span class="logo-text">Learning Platform</span>
    </div>
    {{#if viewInfo.attemptLimitExceed}}
    <!-- Show "Forgot your Password?" in Attempt Limit Exceeded Case -->
    <div class="content-heading" role="heading" aria-level="1">Forgot your Password?</div>
    <hr class="limit-sent-line">
    <div id="msg_box" class="content-heading alert alert-danger limit-exceeded-heading" role="alert"
        aria-live="assertive">
        Attempt Limit Exceeded
    </div>
    {{else}}
    {{#if viewInfo.userNotFound}}
    <div class="content-heading no-active-account" role="alert" aria-live="polite">No active account found</div>
    {{else}}
    {{#unless params.isSent}}
    <div class="content-heading" role="heading" aria-level="1">Forgot your Password?</div>
    {{/unless}}
    <div class="content-sub-heading {{#if params.isSent}} sent_reset_link {{/if}}" id="redirect_sub_heading"
        aria-live="polite">
        {{#if params.isSent}}
        We have sent the link to reset your password at<br>
        <b>{{#printMsg params.email}}{{/printMsg}}</b>
        {{else}}
        Enter your email address to receive <br> a password reset link
        {{/if}}
    </div>
    {{/if}}
    {{/if}}
<div id="error_msg" class="error_msg hide">
      {{#if validationErrors}}
      {{validationErrors.[0]}}
      <script>
         var registerErrors = "{{validationErrors}}"
      </script>
      {{/if}}
   </div>
    <div class="form-container form_div_account form_container" role="region" aria-label="Reset Password">
        {{#if viewInfo.attemptLimitExceed}}
        <div class="mail-sent" role="alert" aria-live="assertive">
            We can see that you have tried to reset your password more than <br><b>{{viewInfo.limit}} times.</b><br><br>
        </div>
        <div class="mail-sent">
            Due to security reasons, we request you to wait for <b>{{viewInfo.waitTime}} minutes</b> <br>before trying
            again.
        </div>
        {{else}}
        {{#if params.isSent}}
        <hr class="mail-sent-line">
        <div class="mail-sent" role="alert" aria-live="polite">
            Didn't receive the email yet? <br>Please check your spam folder, or <a href="forgot-password">resend</a> the
            email.
        </div>
        {{else}}
        {{!-- else block for isSent --}}
        <form id="forgotPassword" name="forgot-password-form" novalidate action="{{route.action}}" method="post"
            aria-labelledby="forgot-password-form">
            <div id="error_msg"
                class="hide {{#if viewInfo.userNotFound}} no-active-account-msg {{else}} error_msg {{/if}}" role="alert"
                aria-live="assertive">
                {{#if validationErrors}}
                {{validationErrors.[0]}}
                <script> var loginErrors = "{{validationErrors}}"; </script>
                {{/if}}
            </div>
            <div class="form-input">
                <input type="email" name="email" placeholder="Email address*" autocomplete="0" value="{{formData.email}}" required
                   id="mail" class="email  {{#if viewInfo.userNotFound}} mail-border {{/if}}" />
            </div>
            <div class="submit-container">
                <input tabindex="0" type="submit" name="btn_forgotPassword" value="Send"
                    class="btn btn-default send_email btn-signup" data-event="sl_lms_user_login_clicked"
                    data-eventd="" id="btn_forgot" data-clickedtype="Email">
            </div>
        </form>
        {{/if}}
        {{/if}}
    </div>

    {{#setDTOValue isSent=false mail=""}}
    {{/setDTOValue}}

    {{#if viewInfo.attemptLimitExceed}}
    {{else}}
    {{#unless params.isSent}}
    <div class="create_account" role="region" aria-label="Create Account Now">
        <p class="signup_link back-register"><span>Don't have an account? </span><a href="register" class="link1"
                aria-label="Sign up for a new account">Sign Up</a></p>
    </div>
    {{/unless}}
    {{/if}}
</div>