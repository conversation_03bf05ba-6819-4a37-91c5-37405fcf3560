{{#setMessages 'registerEmail'}}
{{/setMessages}}
<div class="logo-image">
   <a href="https://accounts.simplilearn.com">
      <img src="https://cfaccounts.simplilearn.com/frontend/images/simplilearn-logo.png" alt="Simplilearn Logo">
   </a>
   <span class="logo-text">Learning Platform</span>
</div>
<div class="register-heading register-user-container {{#if dataPassToView.userEmail}}account-setup-heading{{/if}}" id="register-user-container">{{#if dataPassToView.userEmail}}Set
   up
   your account{{else}}Create your account{{/if}}</div>

{{#if dataPassToView.userEmail}}
<div class="content-sub-heading account-setup-subheading">
   Hey! fill in the following information to <br> set up your account.
</div>
{{/if}}

<div class="form-container form_div_account form_container signup-wrap" role="region" aria-label="Sign Up with Email">
   <div id="error_msg" class="hide error_msg">
      {{#if validationErrors}}
      {{validationErrors.[0]}}
      <script>
         var registerErrors = "{{validationErrors}}"
      </script>
      {{/if}}
   </div>
   <form name="reg-form" method="post" novalidate id="registerUser" action="{{route.action}}">
      <div id="error_box" class="hide"></div>
      <div class="row form-input">
         <div class="col-xs-6 pr-20">
            <label>First name<span>*</span></label>
            <input oninput="allowTextOnly(event)" type="text" aria-label="First Name" id="first_name" name="first_name"
               value="{{#if dataPassToView.firstName}}{{dataPassToView.firstName}}{{else}}{{formData.first_name}}{{/if}}"
               placeholder="First name" autocomplete="off" required class="uname fname">
         </div>
         <div class="col-xs-6 pl-0">
            <label>Last name<span>*</span></label>
            <input oninput="allowTextOnly(event)" type="text" aria-label="Last Name" id="last_name" name="last_name"
            value="{{#if dataPassToView.lastName}}{{dataPassToView.lastName}}{{else}}{{formData.last_name}}{{/if}}"
               placeholder="Last name" autocomplete="off" required class="uname lname">
         </div>
      </div>
      <div class="form-input">
         <label>Email<span>*</span></label>
         <input type="email" id="mail" name="email" aria-label="Email Address" placeholder="Email address"
            value="{{#if dataPassToView.userEmail}}{{dataPassToView.userEmail}}{{else}}{{formData.email}}{{/if}}"
            autocomplete="new-password" required class="email">
      </div>
      <div class="support_coutry_code form-input">
         <div class="support_inner">
            <div class="country">
               <label>Country<span>*</span></label>
               <div class="select_wraper for_cun">
                  <select tabindex="0" aria-label="Country Code Drop Down" id="country_code" name="country_code"
                     class="sel_cr">
                  </select>
                  <b class="caret"></b>
               </div>

         <input type="hidden" name="country_data" id="country_data">
         {{#if dataPassToView.validateRedirectUrl}}
         <input type="hidden" name="redirect_url" value='{{dataPassToView.validateRedirectUrl}}' id="country_data">
         {{/if}}
         {{#if dataPassToView.referer}}
         <input type="hidden" name="referer" value='{{dataPassToView.referer}}' id="referer">
         {{/if}}
         {{#if dataPassToView.referrer}}
         <input type="hidden" name="referrer" value='{{dataPassToView.referrer}}' id="referrer">
         {{/if}}
         {{#if dataPassToView.isB2bStudentFrontend}}
         <input type="hidden" name="isB2bStudentFrontend" value='{{dataPassToView.isB2bStudentFrontend}}'
            id="isB2bStudentFrontend">
         {{/if}}
         {{#if dataPassToView.ssoRequestFrontend}}
         <input type="hidden" name="ssoRequestFrontend" value='{{dataPassToView.referer}}' id="ssoRequestFrontend">
         {{/if}}

            </div>
            <div class="contact">
               <label>Contact number<span>*</span></label>
               <div class="placeholder_wrap code_wrap">
                  <span id="country_code_span" class="country_code_span">
                     {{prependText}}
                  </span>
                  <input oninput="allowNumbersOnly(event)" type="tel" aria-label="Phone Number" id="phone_no" name="phone_no"
                     placeholder="Contact number" maxlength="15" minlength="7" autocomplete="on" required 
                     value="{{#if dataPassToView.phone_no}}{{dataPassToView.phone_no}}{{else}}{{formData.phone_no}}{{/if}}" 
                     class="phone-no"></input>
                  <span id="phoneError">Your contact number should be at least 7 characters long</span>
               </div>
            </div>
         </div>
      </div>
      <div class="password-field form-input">
         <label>Password<span>*</span></label>
         <div class="passowrd-wrap">
            <input type="password" aria-label="Password" id="password" name="password" placeholder="Password"
               autocomplete="new-password" required class="password" />
            <span aria-label="eye icon" tabindex="0" 
             class="eye-icon password-icon" id="password-icon">
            </span> 
               <span aria-label="eye icon" tabindex="0" class="eye-icon password-icon password-info info-icon">
               <div class="password-tooltip">
                  <div class="password-heading">
                     Password Criteria:
                  </div>
                  <div class="password-list">
                     <ul>
                        <li class="crit-min"> Password must be at least 8 characters long</li>
                        <li class="crit-max">Password cannot be longer than 128 characters</li>
                        <li class="crit-lower">It should contain at least one lowercase letter</li>
                        <li class="crit-upper">It should contain at least one uppercase letter</li>
                        <li class="crit-num">It should contain at least one number</li>
                     </ul>
                  </div>
                     <i class="triangle"></i>
               </div>
             </span>
         </div>
      </div>
      {{!--
      <div class="form-input">
         <input aria-label="Terms and Conditions" tabindex="0" style="display:inline-block;" type="checkbox"
            id="termCondition" name="termCondition" value="Y" required />
         <label for="termCondition" class="terms-content"><span>I agree to the <a tabindex="0" class="forgot_password link1" href="https://www.simplilearn.com/terms-and-conditions" rel="nofollow" target="_blank">Terms and Conditions.</a></span></label>
      </div>
      --}}
      <input type="hidden" name="termCondition" id="termCondition" value="Y">
      <input type="hidden" id="termsAndConditionsContent" value="{{termsAndConditions}}">
      <input type="hidden" id="termsAndConditionsStyles" value="{{termsStyles}}">
      <div class="submit-container">
         <input tabindex="0" title="Create Account" type="submit" id="btn_register" name="btnlogin"
            value="Create Account" class="btn btn-default btn-signup disabled" data-event="sl_lms_user_login_clicked"
            data-eventd="" data-clickedtype="Email">
      </div>
   </form>
   <div class="create_account d-flex justify-content-center">
      <p class="back-register"><a href="register" class="link1">Other Sign Up options</a> </p>
   </div>
</div>

<script>
  window.pageType = '{{route.page}}';
</script>