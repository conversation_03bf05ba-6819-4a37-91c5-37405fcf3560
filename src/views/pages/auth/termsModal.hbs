<!-- Terms and Conditions Modal -->
<div class="modal" id="termsModal" tabindex="-1" role="dialog" aria-labelledby="termsModalLabel" aria-describedby="termsModalBody" aria-modal="true" aria-hidden="true" data-keyboard="false" data-backdrop="static">
  <div class="modal-dialog custom-dialog-box modal-dialog-centered" role="document">
    <div class="modal-content custom-modal-content">
      <div class="modal-body" id="termsModalBody">
        <button type="button" class="btn-close tac-btn-close" aria-label="Close" onclick="TermsConsent.closeTncModal()">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd"
              d="M0 12C0 18.612 5.388 24 12 24C18.612 24 24 18.612 24 12C24 5.388 18.612 0 12 0C5.388 0 0 5.388 0 12ZM16.032 16.032C15.852 16.212 15.624 16.296 15.396 16.296C15.168 16.296 14.94 16.212 14.76 16.032L12 13.272L9.24 16.032C9.06 16.212 8.832 16.296 8.604 16.296C8.376 16.296 8.148 16.212 7.968 16.032C7.62 15.684 7.62 15.108 7.968 14.76L10.728 12L7.968 9.24C7.62 8.892 7.62 8.316 7.968 7.968C8.316 7.62 8.892 7.62 9.24 7.968L12 10.728L14.76 7.968C15.108 7.62 15.684 7.62 16.032 7.968C16.38 8.316 16.38 8.892 16.032 9.24L13.272 12L16.032 14.76C16.38 15.108 16.38 15.684 16.032 16.032Z"
              fill="#9E9E9E" />
          </svg>
        </button>
        <!-- Container for dynamic content -->
        <div class="body-content" id="bodyContentTC">
          <!-- Content will be dynamically added by JavaScript -->
        </div>
      </div>
    </div>
  </div>
</div>