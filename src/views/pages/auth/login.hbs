<div class="logo-image">
    <a href="https://accounts.simplilearn.com">
        <img src="https://cfaccounts.simplilearn.com/frontend/images/simplilearn-logo.png" alt="Simplilearn Logo">
    </a>
    <span class="logo-text">Learning Platform</span>
</div>
<div class="content-heading" role="heading" aria-level="1">Welcome back!</div>
<div class="content-sub-heading" role="heading" aria-level="2">
    <div>Log in with your email</div>
</div>
<div class="form-container form_div_account form_container">
    <form role="region" aria-label="Login with Email" name="loginForm" class="login-form" id="loginUser"
        action="{{route.action}}" method="post" aria-labelledby="login-heading">
        <div id="error_msg" class="error_msg" aria-live="polite" role="alert">
            {{#if validationErrors}}{{validationErrors.[0]}}
            <script>
                var loginErrors = "{{validationErrors}}"
            </script>
            {{/if}}
        </div>
        <div class="form-input">
            <input type="text" id="mail" name="email" placeholder="Email address*" autocomplete="0" class="email"
                value="">
        </div>
        <div class="password-field form-input">
            <input type="password" name="password" placeholder="Password*" id="password" autocomplete="0" required="">
            <span aria-label="eye icon" tabindex="0"
                class="loginpassword eye-icon password-icon info-icon" id="password-icon">
            </span>
        </div>
        <div class="row m-b-35 remember">
            <div class="col-md-6 col-xs-6">
                <input aria-label="rememberMe" tabindex="0" type="checkbox" id="remember-me" name="rememberMe"
                    value="Y">
                <label for="remember-me" class="rememberMe">Remember Me</label>
            </div>
            <div class="col-md-6 col-xs-6 text-right">
                <label class="forgot-link"><a tabindex="0" href="/user/forgot-password"
                        data-event="sl_lms_user_forgot_password_clicked" data-eventd="">Forgot Password?</a></label>
            </div>
        </div>
        <div class="submit-container">
            <input tabindex="0" title="Login" type="submit" name="btnlogin" value="Login" id="btn_login"
                class="btn btn-default btn-signup" data-event="sl_lms_user_login_clicked" data-eventd="" disabled
                data-clickedtype="Email">
        </div>
        <div id="hidden_div">
            {{#if redirect_url}}
            <input type="hidden" id="redirect_url" name="redirect_url" value="{{redirect_url}}" /> {{else}}
            <input type="hidden" id="redirect_url" name="redirect_url" value="" /> {{/if}} {{#if calendar_url}}
            <input type="hidden" id="calendar_url" name="calendar_url" value="{{calendar_url}}" /> {{else}}
            <input type="hidden" id="calendar_url" name="calendar_url" value="" /> {{/if}} {{#if domainGid}}
            <input type="hidden" id="domainGid" name="domainGid" value="{{domainGid}}" /> {{else}}
            <input type="hidden" id="domainGid" name="domainGid" value="" /> {{/if}} {{#if isB2BAndB2C}}
            <input type="hidden" id="isB2BAndB2C" name="isB2BAndB2C" value="{{isB2BAndB2C}}" /> {{else}}
            <input type="hidden" id="isB2BAndB2C" name="isB2BAndB2C" value="" /> {{/if}} {{#if domainUrl}}
            <input type="hidden" id="domainUrl" name="domainUrl" value="{{domainUrl}}" /> {{else}}
            <input type="hidden" id="domainUrl" name="domainUrl" value="" /> {{/if}}
        </div>
    </form>
</div>
<div class="or-div"></div>
<div class="social-login" aria-label="Social Login Options">
    <button class="login-button" id="google-login" aria-label="Login with Google">
        <div class="social-icon google-icon"></div> <span>Continue with Google</span>
    </button>
    <button class="login-button" id="linkedin-login" aria-label="Login with LinkedIn">
        <div class="social-icon linkedin-icon"></div> <span>Continue with LinkedIn</span>
    </button>
    <button class="login-button hidden" id="facebook-login" aria-label="Login with Facebook">
        <div class="social-icon facebook-icon"></div> <span>Continue with Facebook
    </button></span>
    <button class="login-button hidden" id="SignInWithApple-login" aria-label="Login with Apple">
        <div class="social-icon apple-icon"></div> <span>Continue with Apple</span>
    </button>
    <a href="#" class="more-options" id="view-more" aria-label="View more login options">View more login options</a>

    <p class="signup_link"><span class="signup-text">Don't have an account? </span><a href="register" class="link1"
            aria-label="Sign Up">Sign Up</a></p>
</div>