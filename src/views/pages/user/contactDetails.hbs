<script>
  const userData = {{{ safeJson userData }}};
</script>
<div class="tab_inner contact_tab tab-pane active" id="contact_tab">
    <form name="form_contact_info" id="form_contact_info" class="edit_form track_changes" method="post">
        <div class="form_details">
            <div class="form_info">
                <h3 id="contact_details_label">Contact details</h3>
            </div>

            <div class="form-wrap">
                <div class="frm_abel">
                    <label for="user_email">Email<i>*</i></label>
                </div>
                <div class="right-col">
                    <input type="text" value={{userData.email}} id="user_email" aria-describedby="email_desc" disabled/>
                    <p id="email_desc" class="p_error error hide">This field cannot be edited.</p>
                </div>
            </div>

            <div class="form-wrap">
                <div class="frm_abel">
                    <label for="phone_no">Contact number<i>*</i></label>
                </div>
                <div class="right-col">
                    <div class="support_coutry_code">
                        <div class="select_wraper for_cun">
                            <select id="country_code" name="country_code"
                                class="sel_cr monospace_class contact_details input_field_country_code"
                                aria-labelledby="phone_no">
                            </select>
                            <b class="caret contact_country_caret"></b>
                        </div>
                        <div class="placeholder_wrap code_wrap">
                            <span id="country_code_span" class="country_code_span">{{prependText}}</span>
                            <input tabindex="0" type="number" id="phone_no" name="phone_no" value={{userData.phone_no}}
                                class="form-control contact_details input_field_phone_no" maxlength="15"
                                autocomplete="0" required aria-required="true" />
                        </div>
                    </div>
                    <p class="p_error error hide">Required</p>
                </div>
            </div>
            <div class="form-wrap">
                <div class="frm_abel">Country of residence<i>*</i></div>
                <div class="right-col">
                    <select id="country_of_residence"
                        name="country_of_residence" class="contact_details input_field_country_of_residence required" required>
                    </select>
                    <p class="p_error error hide">Required</p>
                </div>
            </div>


            <div class="form-wrap">
                <div class="frm_abel">State<i>*</i></div>
                <div class="right-col">
                    <select class="state contact_details input_field_state" id="state" name="state" style="width:100%" required>
                    </select>
                    <p class="p_error error hide"></p>
                </div>
            </div>
            <div class="form-wrap">
                <div class="frm_abel">City<i>*</i></div>
                <div class="right-col">
                    <input type="text" id="location" name="location" value="{{userData.location}}" class="contact_details input_field_location" required />
                    <p class="p_error error hide"></p>
                </div>
            </div>
            <div class="form-wrap">
                <div class="frm_abel">Timezone<i>*</i></div>
                <div class="right-col">
                    <select id="timezone-dropdown" class="single-select contact_details input_field_timezone"
                        name="timezone" style="width:100%" required>
                    </select>
                    <p class="p_error error hide" id="timezone-error">Required</p>
                </div>
            </div>
            <div class="form-wrap">
                <div class="frm_abel">Correspondence address
                    <span>If a physical certificate is applicable, you will receive yours from the university at this
                        address.</span>
                </div>
                <div class="right-col">
                    <textarea id="correspondence_address" class="correspondence_address" name="correspondence_address"
                        rows="4" cols="50">{{ userData.correspondence_address }}</textarea>
                </div>
                <p class="p_error error hide" id="timezone-error">Required</p>
            </div>

            <div class="form-wrap">
                <div class="frm_abel"></div>
                <div class="right-col">
                    <div class="btn-group">
                        <input type="hidden" name="edit_type" class="edit_type" value="contact">
                        <button class="btn discard contact" type="button" disabled>Discard</button>
                        <button class="btn save" disabled>Save changes</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>