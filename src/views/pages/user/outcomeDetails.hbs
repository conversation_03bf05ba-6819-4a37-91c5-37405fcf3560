<script>
  const selectedOutcomeId = "{{selectedOutcomeId}}";
</script>
<div class="tab_inner outcome_tab tab-pane active" id="outcome_tab">
    <form name="form_outcome_info" id="form_outcome_info" class="edit_form track_changes" method="post">
        <div class="form_details">
            <div class="form_info">
                <h3>Learning outcome</h3>
            </div>

            <div class="form-wrap" role="group" aria-labelledby="learning-outcome-label">
                <div class="frm_abel" id="learning-outcome-label">What is your desired learning outcome?<i>*</i></div>
                <div class="right-col">
                    <select id="objective_taking_course" name="objective_taking_course"
                        class="custom_select1 input_field_objective_taking_course outcome_details"
                        aria-labelledby="learning-outcome-label"
                        aria-required="true"
                        aria-describedby="learning-outcome-error">
                    </select>
                    <p class="p_error error hide" id="learning-outcome-error">This field is required.</p>
                </div>
            </div>

            <div class="form-wrap">
                <div class="frm_abel"></div>
                <div class="right-col">
                    <div class="btn-group">
                        <input type="hidden" name="edit_type" class="edit_type" value="outcome">
                        <button class="btn discard" id="discard-btn" type="button" disabled>Discard</button>
                        <button class="btn save" id="save-btn" disabled>Save changes</button>
                    </div>
                </div>
            </div>

        </div>
    </form>
</div>