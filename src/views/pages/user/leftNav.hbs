<div class="left_nav_menu_wrap" role="navigation" aria-label="Profile Navigation">
    <div class="left_nav_main">
        <div class="profile_info">
            <div class="profile_pic">
                <img class="profile_picture" src="{{fullProfilePicUrl userData.profile_pic.filename}}" width="65" height="65"
                    alt="profile">
            </div>
            <div class="profile_detail">
                <h3 class="profile_user_name">{{userData.display_name}}</h3>
                <p class="profile_user_text">Profile completed 
                    <b class="profile_progress_val" aria-live="polite">{{profileCompletion.overallCompletion}}</b>
                </p>
                <div class="progress" role="progressbar" aria-valuenow="14" aria-valuemin="0" aria-valuemax="100">
                    <div class="bar" id="profile_progress" style="width: {{profileCompletion.overallCompletion}};"></div>
                </div>
            </div>
        </div>
        <ul class="left_nav_list" role="tablist">
            <li class="left_nav_basic basic {{#if (equals profileStatus.edit_type 'basic')}}active{{/if}} {{#if (equals profileCompletion.categoryCompletion.Basic '100%')}}complete{{/if}}" role="tab" aria-selected="true" data-navmenu="basic_tab">
                <a href="#basic_tab" aria-current="page" data-navmenu="basic_tab" tabindex="0"><i aria-hidden="true"></i>Basic details</a>
            </li>
            <li class="contact {{#if (equals profileStatus.edit_type 'contact')}}active{{/if}} {{#if (equals profileCompletion.categoryCompletion.Contact '100%')}}complete{{/if}}" role="tab" aria-selected="false" data-navmenu="contact_tab">
                <a href="#contact_tab" data-navmenu="contact_tab" class=""><i aria-hidden="true"></i>Contact details</a>
            </li>
            <li class="professional {{#if (equals profileStatus.edit_type 'professional')}}active{{/if}} {{#if (equals profileCompletion.categoryCompletion.Professional '100%')}}complete{{/if}}" role="tab" aria-selected="false" data-navmenu="professional_tab">
                <a href="#professional_tab" data-navmenu="professional_tab" class=""><i aria-hidden="true"></i>Professional details</a>
            </li>
            <li class="academics {{#if (equals profileStatus.edit_type 'academics')}}active{{/if}} {{#if (equals profileCompletion.categoryCompletion.Academics '100%')}}complete{{/if}}" role="tab" aria-selected="false" data-navmenu="academics_tab">
                <a href="#academics_tab" data-navmenu="academics_tab" class=""><i aria-hidden="true"></i>Academics</a>
            </li>
            <li class="outcome {{#if (equals profileStatus.edit_type 'outcome')}}active{{/if}} {{#if (equals profileCompletion.categoryCompletion.LearningOutcome '100%')}}complete{{/if}}" role="tab" aria-selected="false" data-navmenu="outcome_tab">
                <a href="#outcome_tab" data-navmenu="outcome_tab" class=""><i aria-hidden="true"></i>Learning outcome</a>
            </li>
        </ul>
    </div>
</div>