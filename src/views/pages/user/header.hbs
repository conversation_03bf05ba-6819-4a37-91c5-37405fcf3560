<div class="body_container">
<div class="header headertab" role="banner" aria-label="Primary Navigation">
<div class="body-container">
<div class="header headertab" role="banner" aria-label="Primary Navigation">
  <div class="container">
    <div class="header_section">
      {{#if (and isB2bStudent atpLogoUrl)}}
    <img tabindex="0" class="atplogo" src="{{atpLogoUrl}}" alt="">

     {{else if isB2BAndB2C}}
    <img tabindex="0" src="{{'https://cfaccounts.simplilearn.com/frontend/images/fsa-logo1.png'}}" alt="FSA Logo">
   {{else}}
      <!-- Logo -->
      <img 
        src="https://www.simplilearn.com/ice9/assets/simpli_user_logo.svgz" 
        alt="Simplilearn Logo" 
        border="0" 
        width="228" 
        height="56" 
        usemap="#image-maps"
      />

      <map name="image-maps">
        <area 
          alt="Simplilearn" 
          title="Simplilearn" 
          href="https://www.simplilearn.com" 
          shape="rect" 
          coords="0,0,126,45" 
          style="outline:none; cursor: pointer;" 
          target="_blank" 
        />
        
        <area 
          alt="Learning Platform" 
          title="Learning Platform" 
          href="https://lms.simplilearn.com" 
          shape="rect" 
          coords="125,0,224,46" 
          style="outline:none; cursor: pointer;" 
        />
      </map>
      {{/if}}
    {{#if (and userData (not hideUserMenuBar))}}
      <div class="top-nav">
        <ul class="nav navbar-nav">
          <!-- Help Button -->
          {{#if (and (equals showHelpSupport "Yes") (equals userType "Paid"))}}
                <li role="navigation" aria-label="Help" class="need-help">
                  <button class="help_and_support" onclick="callChatbot('Profile');">
                    <span class="icon"></span>
                    <p class="help">Help</p>
                  </button>
                </li>
          {{else if (and (equals showHelpSupport "Custom") (equals userType "Paid"))}}
                <li role="navigation" aria-label="Help" class="need-help">
                  <span class="help_and_support_redirect">
                    <a href="{{helpSupportUrl}}" target="_blank" data-event="sl_lms_nav_popup_help_support_clicked">
                      <button class="help_and_support">
                        <span class="icon"></span>
                        <p class="help">Help</p>
                      </button>
                    </a>
                  </span>
                </li>
          {{/if}}


          <!-- Profile Dropdown -->
          <li role="navigation" aria-label="Profile" class="profile dropdown">
            <a href="/user/profile" class="icon-set-after profile-btn down-arrow" 
               id="profileToggle" data-toggle="dropdown" aria-expanded="false">
              <span class="profile-img">
                <img class="profile_picture" src="{{fullProfilePicUrl userData.profile_pic.filename}}" />
              </span>
              <span class="profile-name">{{capitalize userData.first_name}}</span>
            </a>
            <ul class="dropdown-menu profile-drop" id="profileDropdown">
            
              {{#if (equals sectionActive "profile")}}
  <li role="navigation" aria-label="My Profile" class="edit-prof active">
    <a href="#profile" data-toggle="tab" data-event="sl_lms_profile_edit_opened">My Profile</a>
  </li>
{{else}}
  <li role="navigation" aria-label="My Profile" class="edit-prof">
    <a href="/user/profile" data-event="sl_lms_profile_edit_opened">My Profile</a>
  </li>
{{/if}}
             
              {{#unless isEnableSamlAuthentication}}
  {{#if (equals sectionActive "change-password")}}
    <li role="navigation" aria-label="Change Password" class="change-pass active">
      <a href="/user/profile/change-password" data-toggle="tab">Change Password</a>
    </li>
  {{else}}
    <li role="navigation" aria-label="Change Password" class="change-pass">
      <a href="/user/profile/change-password">Change Password</a>
    </li>
  {{/if}}
{{/unless}}
              {{#unless isB2bStudent}}
                  {{#if (equals sectionActive "manage-payments")}}
                      <li role="navigation" aria-label="Invoices" class="invoices active hidden-xs">
                        <a href="#payment" data-toggle="tab" data-event="sl_lms_nav_invoices_clicked">Invoices</a>
                      </li>
                    {{else}}
                      <li role="navigation" aria-label="Invoices" class="invoices hidden-xs">
                        <a href="{{sectionUrl.managePaymentsUrl}}" data-event="sl_lms_nav_invoices_clicked">Invoices</a>
                      </li>
                  {{/if}}
                 {{#if (equals sectionActive "digital-key")}}
                    <li role="navigation" aria-label="Digital Keys" class="digit-key {{fsaClassToAdd}} active hidden-xs">
                      <a href="#digital-key" data-toggle="tab">Digital Key</a>
                    </li>
                  {{else}}
                    <li role="navigation" aria-label="Digital Keys" class="digit-key {{fsaClassToAdd}} hidden-xs">
                      <a href="{{sectionUrl.digitalKeyUrl}}">Digital Key</a>
                    </li>
                  {{/if}}

                <li role="navigation" aria-label="Exam" class="exam-voucher {{fsaClassToAdd}} hidden-xs {{#if (equals sectionActive "exam-voucher")}}active{{/if}}">
                  {{#if (equals sectionActive "exam-voucher")}}
                    <a href="#exam-voucher" data-toggle="tab">Exam vouchers</a>
                  {{else}}
                    <a href="{{sectionUrl.examVoucherUrl}}">Exam vouchers</a>
                  {{/if}}
                </li>
{{!-- Deprecated: Free Trial Settings tab
                {{#if freeTrialExists}}
                    {{#if (equals sectionActive "free_trial")}}
                      <li role="navigation" aria-label="Free Trial Settings" class="free_trial active hidden-xs">
                        <a href="#free_trial" data-toggle="tab" class="">Free Trial Settings</a>
                      </li>
                    {{else}}
                      <li role="navigation" aria-label="Free Trial Settings" class="free_trial hidden-xs">
                        <a href="{{sectionUrl.freeTrialUrl}}">Free Trial Settings</a>
                      </li>
                    {{/if}}
                {{/if}}
             
--}}
 {{/unless}}
  {{#if (equals showHelpSupport "Yes")}}
    {{#if (or (equals sectionActive "manage-tickets") (equals sectionActive "ticket-details"))}}
      <li role="navigation" aria-label="Support requests" class="manage-tickets active hidden-xs">
        <a href="#tickets" data-toggle="tab" class="" onclick="openSupportReq()">Support requests</a>
      </li>
    {{else}}
      <li role="navigation" aria-label="Support requests" class="manage-tickets hidden-xs">
        <a href="{{sectionUrl.tickets}}" onclick="openSupportReq()">Support requests</a>
      </li>
    {{/if}}
  {{else if (equals showHelpSupport "Custom")}}
    <li role="navigation" aria-label="Support requests" class="custom hidden-xs">
      <a href="{{helpSupportUrl}}" onclick="openSupportReq()" target="_blank">Support requests</a>
    </li>
  {{/if}}


              <li id="logout" class="logout"><a href="#" class="logout" data-event="sl_user_logout">Logout</a></li>

              <!-- Refer & Earn Section -->

{{#unless isB2bStudent}}
    {{#if (equals userType "Paid")}}
      <li class="new-refer-li" id="refer_earn_section">
        <div class="new-ref-earn">
          <div class="content-box-new">
            <div class="gif-refer-and-earn"></div>
            <div>
              <p>Earn benefits up to <span id="ref-amount-dynamic">₹10,000</span></p>
              <p>for every friend who enrolls</p>
            </div>
          </div>
          <button class="pr-refer-button copy-referral-link" role="link">
            <span class="referral-icon"></span>
            <span class="referral-icon-last">Copy my referral link</span>
          </button>
        </div>
      </li>
    {{/if}}
  {{/unless}}


<!-- JavaScript variable exposure for eligibility checking -->
<script>
  var loadrefEarn = 
    {{#unless isB2bStudent}}
      {{#unless isB2BAndB2C}}
        {{#if (equals userType "Paid")}}1{{else}}1{{/if}}
      {{else}}0{{/unless}}
    {{else}}0{{/unless}};
</script>
            </ul>
          </li>
        </ul>
      </div>

      {{/if}}
    </div>
  </div>
</div> 
