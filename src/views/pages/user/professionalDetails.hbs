<script>
  const selectedExperienceId = "{{selectedExperienceId}}";
  const workExperience = {{{ safeJson workExperience }}};
</script>
<div class="tab_inner professional_tab tab-pane active" id="professional_tab">
    <form name="form_professional_info" id="form_professional_info" class="edit_form track_changes" method="post">
        <div class="form_details">
            <div class="form_info">
                <h3 id="professional_details_label">Professional details</h3>
            </div>

            <div class="form-wrap" id="js_total_exp">
    <div class="frm_abel">
        <label for="where_are_you_in_career">Total years of experience<i>*</i></label>
    </div>
    <div class="right-col">
        <select id="where_are_you_in_career" name="where_are_you_in_career"
            class="custom_select1 work_details input_field_where_are_you_in_career">
        </select>
        <p class="p_error error hide">Required</p>
    </div>
</div>
            <div class="form-wrap">
                <div class="frm_abel">Work Experience(s)</div>
                <div class="right-col">
                    <a id="add_work_exp" href="javascript:void(0)" class="add_plus" role="button" aria-label="Add work experience">
                        <i>+</i>Add
                    </a>
                </div>
            </div>
            <div class="work-info" id="js_work_exp">
                {{#each workExperience}}
                <div class="form-wrap exp_main_wrap professional" id="exp_main_wrap_w_{{@index}}">
                    <div class="exp-inner-wrap">
                        <div class="frm_abel exp_label">
                            <b>Work Experience <font class="work_exp_box_counter">{{increment @index}}</font></b>
                        </div>
                        <div class="right-col">
                            <a href="javascript:void(0)" class="delete" role="button" 
                               onclick="removeWorkExperience('exp_main_wrap_w_{{@index}}');"
                               aria-label="Delete work experience {{increment @index}}">
                                Delete
                            </a>
                        </div>
                    </div>

                    <div class="exp-inner-wrap">
                        <div class="frm_abel">
                            <label for="company_designation_{{@index}}">Designation<i>*</i></label>
                        </div>
                        <div class="right-col">
                            <input type="text" id="company_designation_{{@index}}" name="company_designation[]"
                                class="company_designation work_details input_field_company_designation"
                                value="{{this.designation}}" required aria-required="true"/>
                            <p class="p_error error hide">Required</p>
                        </div>
                    </div>

                    <div class="exp-inner-wrap">
                        <div class="frm_abel">
                            <label for="company_name_{{@index}}">Company<i>*</i></label>
                        </div>
                        <div class="right-col">
                            <input type="text" id="company_name_{{@index}}" name="company_name[]"
                                class="company_name work_details input_field_company_name"
                                value="{{this.company}}" required aria-required="true"/>
                            <p class="p_error error hide">Required</p>
                        </div>
                    </div>

                    <div class="exp-inner-wrap">
                <div class="frm_abel">
                    <label for="department_{{@index}}">Department<i>*</i></label>
                </div>
                <div class="right-col">
                    <select id="department_{{@index}}" name="job_function[]" class="job_function_prepopulate custom_select1 work_details input_field_job_function choices" required aria-required="true">
                    </select>
                    <p class="p_error error hide">Required</p>
                </div>
            </div>            

                  <div class="exp-inner-wrap">
                <div class="frm_abel">
                    <label for="industry_{{@index}}">Industry<i>*</i></label>
                </div>
                <div class="right-col">
                    <select id="industry_{{@index}}" name="industry[]" 
                            class="select_industry_prepopulate industry work_details input_field_industry" 
                            required aria-required="true">
                        {{#each industryList}}
                        <option value="{{this.id}}" {{isSelected ../this.industry this.id}}>{{this.title}}</option>
                        {{/each}}
                    </select>
                    <p class="p_error error hide">Required</p>
                </div>
            </div>

                    <div class="exp-inner-wrap select_dates">
                        <div class="frm_abel" for="from_{{@index}}">Start Date<i>*</i></div>
                        <div class="right-col">
                            <div class="flex-date">
                                <div class="select_wraper for_cun month">
                                    <select name="exp_from_month[]" id="from_month_{{@index}}" class="exp_from_month work_details input_field_exp_from_month choices" required>
                                    </select>
                                </div>
                                <div class="select_wraper for_cun year">
                                    <select name="exp_from_year[]" id="from_year_{{@index}}" class="exp_from_year work_details input_field_exp_from_year choices" required>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="exp-inner-wrap end_date select_dates">
                        <div class="frm_abel" for="to_{{@index}}" >End Date<i>*</i>
                        <span>
                            <input type="hidden" id="current_role_{{@index}}" name="current_role[]" value="{{this.current_role}}">
                            <input type="checkbox" id="current_company_{{@index}}" class="current_company work_details input_field_current_company" {{#if (equals this.current_role "1")}}checked{{/if}} >
                            <label for="current_company_{{@index}}" class="current_company_label">Current role</label>
                        </span>
                        </div>
                        <div class="right-col">
                            <div class="flex-date">
                                <div class="select_wraper for_cun month">
                                    <input type="hidden" id="exp_to_month_{{@index}}" name="exp_to_month[]">
                                    <select id="to_month_{{@index}}" class="exp_to_month work_details input_field_exp_to_month choices" required>
                                    </select>
                                </div>
                                <div class="select_wraper for_cun year">
                                    <input type="hidden" id="exp_to_year_{{@index}}" name="exp_to_year[]">
                                    <select id="to_year_{{@index}}" class="exp_to_year work_details input_field_exp_to_year choices" required>
                                    </select>
                                </div>
                            </div>
                            <p class="p_error error hide">Required</p>
                            <p class="work_exp_err error hide"></p>
                        </div>
                    </div>
                </div>
                {{/each}}
            </div>

            <div class="form-wrap">
                <div class="frm_abel"></div>
                <div class="right-col">
                    <div class="btn-group">
                        <input type="hidden" name="edit_type" class="edit_type" value="professional">
                        <button class="btn discard" type="button" disabled aria-disabled="true">Discard</button>
                        <button class="btn save" disabled aria-disabled="true">Save changes</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
