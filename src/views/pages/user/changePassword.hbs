<div class="heading-wrap">Change Password</div>
<div class="main_content_wrap" role="main" aria-label="Main Content">
    <div class="col-lg-12 col-xs-12 tab-content left details-tab other_detail_wrap">
        <div class="tab-pane tab-section active" id="change_passwd">
            <div class="form-container form_div_account form_container reset-password change-password-container"
                role="region" aria-label="Reset Password">
                <form id="fmr_change_passwd" id="fmr_change_passwd" name="fmr_change_passwd" method="post"
                    action="{{route.action}}">
                     <div id="msg_box" class="info_msg" role="alert" aria-live="polite" aria-relevant="additions">
                        Please log in with your updated credentials after resetting your password.
                    </div>
                    <div id="error_msg" class="error_msg">
                        {{#if validationErrors}}
                        {{validationErrors.[0]}}
                        <script>
                            var registerErrors = "{{validationErrors}}"
                        </script>
                        {{/if}}
                    </div>
                    {{!-- old password --}}
                    <div class="password-field form-input">
                        <input type="password" id="cur_passwd" class="change-password-input oldpassword form-control"
                            placeholder="Old Password*" name="cur_passwd" required />
                        <span class="oldpassword-see eye-password-icon" data-target="cur_passwd"></span>
                    </div>
                    {{!-- new password --}}
                    <div class="password-field form-input">
                        <input type="password" id="new_passwd" class="change-password-input newpassword form-control"
                            placeholder="New Password*" name="new_passwd" required />
                        <span class="newpassword-see info-icon  eye-password-icon" data-target="new_passwd"></span>
                        <span class="newpassword-info">
                            <div class="password-tooltip">
                                <div class="password-heading">Password Criteria:</div>
                                <div class="password-list">
                                    <ul>
                                        <li class="crit-min">Password must be at least 8
                                            characters long
                                        </li>
                                        <li class="crit-max">Password cannot be longer than 128
                                            characters
                                        </li>
                                        <li class="crit-lower">It should contain at least one
                                            lowercase letter
                                        </li>
                                        <li class="crit-upper">It should contain at least one
                                            uppercase letter
                                        </li>
                                        <li class="crit-num">It should contain at least one
                                            number
                                        </li>

                                    </ul>
                                </div>
                                <i></i>
                            </div>
                        </span>
                    </div>
                    {{!-- confirm password --}}
                    <div class="password-field form-input">
                        <input type="password" id="confirm_passwd" class="change-password-input confirmpassword form-control"
                            placeholder="Confirm Password*" name="confirm_passwd" required />
                        <span class="oldpassword-see eye-password-icon" data-target="confirm_passwd"></span>
                    </div>

                    <div class="submit-container">
                        <input tabindex="0" title="Change Password" type="submit" id="btn_login" name="btn_login"
                            value="Change Password" class="btn btn-default btn-signup" data-eventd=""
                            data-clickedtype="Email" disabled>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>