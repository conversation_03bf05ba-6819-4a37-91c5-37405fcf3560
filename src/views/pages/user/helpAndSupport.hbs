{{!-- <script type='text/javascript' src='https://service.force.com/embeddedservice/5.0/esw.min.js'></script> 
the above has been used in profile-layout --}}

<!-- Simple Custom Backdrop -->
<div id="feedbackModalBackdrop" class="feedback-modal-backdrop hidden"></div>

<div class="modal fade" id="feedbackModal" tabindex="-1" role="dialog" aria-labelledby="feedbackModalLabel" aria-hidden="true" data-keyboard="false" data-backdrop="false">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <a class="close-btn icon-set-before" title="Close Popup" aria-label="Close Popup" href="javascript:void(0);" role="button" onclick="closeThankyouModal()"></a>

      <div class="hns-modal-header">
      </div>
      <div class="modal-body">
      </div>
      <div class="modal-footer">
        {{!-- Footer content will be dynamically inserted --}}
      </div>
      <div id="hns_loader" class="hidden">
        <div class="center_to_div">
          <img class="hns_loader_img" src="https://cfs22.simplilearn.com/paperclip/shanu_dummy.gif"><br>
          <p>One moment...</p>
        </div>
      </div>
    </div>
  </div>
</div>

<div id="did-you-find-template" class="hidden">
  <div id="did-you-find-content-id">
    <h3>Did you find what you are looking for?</h3>
    <div class="image-container">
      <button class="dislike-btn" title="Dislike" aria-label="Dislike">
        <img src="https://cfs22.simplilearn.com/paperclip/dislike_2020-07-17/dislike.png" alt="no">
      </button>
      <button class="like-btn" title="Like" aria-label="Like">
        <img src="https://cfs22.simplilearn.com/paperclip/like_2020-07-17/like.png" alt="yes">
      </button>
    </div>
  </div>
</div>

<div id="thank-you-for-feedback-template" class="hidden full_to_div">
  <div id="thank-you-for-feedback-id" class="center_to_div w-100">
    <h3 class="feedback-que"></h3>
    <p>You can select more than one choice</p>
    <ul id="sub_ques_checkbox">
      <!-- sub question will be insert from js -->
    </ul>
    <button type="button" class="btn btn-primary sub-btn" title="Submit" aria-label="Submit" onclick="submitSupportFeedback('')">Submit</button>
  </div>
</div>

<div id="thank-you-for-feedback-footer-template" class="hidden">
</div>

<div id="thank-you-very-much-template" class="hidden thanks-msg-pad">
  <div id="thank-you-very-much-id">
    <img src="https://cfs22.simplilearn.com/paperclip/feedback_2020-07-17/feedback3x.png" alt="feedback">
    <div class="thank-you-container">
      <h3>Thank you for your feedback, {{user.name}}!</h3>
      <p>Your valuable feedback will help us improve our customer experience.</p>
    </div>
  </div>
</div>

<div id="thank-you-very-much-footer-template" class="hidden">
</div>

<input type="hidden" name="liked" id="liked" value="0">
<input type="hidden" name="selectedOption" id="selectedOption" value="">


{{!-- this is the dummyCLass --}}
<div class="modalContainer sidebarMaximized layout-docked-dummy embeddedServiceSidebar-dummy hidden" id="default-chat-bot">
  <div class="dummyHelp dummyLayout">
    <header class="sidebarHeader dummySideBar">
      <div class="embeddedServiceInvitation" id="snapins_invite" inert="true" aria-live="assertive" role="dialog" aria-atomic="true"></div>
      <button onclick="closeDummyChatBot()" id="close-dummy-chat-bot">X</button>
    </header>
    <div>
      <img id="img-loader" src="https://cfs22.simplilearn.com/paperclip/shanu_dummy.gif">
      <span id="dummy-text" class="icon">
        <p>One moment,</p> <br>
        <p>we're stacking the articles &amp; lining up our rescue teams...</p>
      </span>
      <div id="cookie-error-block" class="error-block">
        <div class="white-bg">
          <div class="error-communication-image">
            <img src="https://cfs22.simplilearn.com/paperclip/cookies-v2.png" alt="cookies-disabled" border="0">
          </div>
          <div class="error-communication-text">
            <h3>Cookies Disabled</h3>
            <p>Your browser has third-party cookies blocked. Please change your browser settings to allow cookies to use Help.</p>
            <div>&nbsp;</div>
          </div>
        </div>
        <div class="blue-bg">
          <h4>Other things to try:</h4>
          <ul>
            <li>Clear your browser's cache and refresh your page.</li>
            <li>Allow third-party cookies in the browser and refresh the page.</li>
          </ul>
        </div>
      </div>
      <div id="generic-error-block" class="error-block">
        <div class="white-bg">
          <div class="error-communication-image">
            <img src="https://cfs22.simplilearn.com/paperclip/oops-error-v2.png" alt="generic-error" border="0">
          </div>
          <div class="error-communication-text">
            <h3>OOPS!</h3>
            <p>Something went wrong. Please try again after some time.</p>
            <div>&nbsp;</div>
          </div>
        </div>
        <div class="blue-bg">
          <h4>Other things to try:</h4>
          <ul>
            <li>Clear your browser's cache and refresh your page.</li>
            <li>Allow third-party cookies in the browser and refresh the page.</li>
            {{#if (equals gId "2")}}
              <li>You can post your concern in the Simplilearn <a href="{{communityUrl}}" target="_blank">support community forum</a></li>
            {{/if}}
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
