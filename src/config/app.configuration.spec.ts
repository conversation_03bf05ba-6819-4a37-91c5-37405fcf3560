import appConfiguration from './app.configuration';

describe('appConfiguration', () => {
  it('should return the default configuration', () => {
    const config = appConfiguration();
    expect(config).toHaveProperty('applicationEnv');
    expect(config).toHaveProperty('appPort');
    expect(config).toHaveProperty('swaggerTitle');
    expect(config).toHaveProperty('swaggerDescription');
    expect(config).toHaveProperty('swaggerVersion');
    expect(config).toHaveProperty('swaggerPath');
    expect(config).toHaveProperty('b2bB2cGroupId');
    expect(config).toHaveProperty('userOptionsAccountSetupComplete');
    expect(config).toHaveProperty('defaultLrsApplicationId');
    expect(config).toHaveProperty('defaultUserGroupId');
    expect(config).toHaveProperty('userOptionsAccountSetupPending');
    expect(config).toHaveProperty('baseUrl');
    expect(config).toHaveProperty('npsFeedbackApiUrl');
    expect(config).toHaveProperty('npsUsername');
    expect(config).toHaveProperty('npsPassword');
    expect(config).toHaveProperty('industry_disable_list');
    expect(config).toHaveProperty('prod_industry_disable_list');
    expect(config).toHaveProperty('fpResetCount');
    expect(config).toHaveProperty('fpResetWaitTime');
    expect(config).toHaveProperty('fpEmailInCacheTime');
    expect(config).toHaveProperty('resetResponseTime');
    expect(config).toHaveProperty('defaultRole');
    expect(config).toHaveProperty('apiAuthHeader');
    expect(config).toHaveProperty('updateSource');
    expect(config).toHaveProperty('planTypes');
    expect(config).toHaveProperty('profileVisibilitySettings');
    expect(config).toHaveProperty('learnerActivationRequest');
    expect(config).toHaveProperty('teamLearnerActivationRequest');
    expect(config).toHaveProperty('freeUserType');
    expect(config).toHaveProperty('paidUserType');
    expect(config).toHaveProperty('skillUpUserReferralCode');
    expect(config).toHaveProperty('calendarRedirect');
    expect(config).toHaveProperty('skillUpOneTapRedirectCookie');
    expect(config).toHaveProperty('skillUpQuestionnaireRedirectCookie');
    expect(config).toHaveProperty('skillUpQuizRedirectCookie');
    expect(config).toHaveProperty('skillUpQuestionnaireDataSubmitted');
    expect(config).toHaveProperty('skillUpOriginRedirectCookie');
    expect(config).toHaveProperty('gdprUrl');
    expect(config).toHaveProperty('frsOneTapRedirectCookie');
    expect(config).toHaveProperty('defaultFreemiumSignupUtm');
    expect(config).toHaveProperty('slUuUtmz');
    expect(config).toHaveProperty('isFrsPage');
    expect(config).toHaveProperty('skillUpReferralBaseUrl');
    expect(config).toHaveProperty('skillUpQuestionnaireUrl');
    expect(config).toHaveProperty('skillUpQuizUrl');
    expect(config).toHaveProperty('countyDataCdnLink');
    expect(config).toHaveProperty('calendarUrl');
    expect(config).toHaveProperty('manageAuthRedirectUrl');
    expect(config).toHaveProperty('sheldonSiteUrl');
    expect(config).toHaveProperty('freemiumAssignmentToken');
    expect(config).toHaveProperty('communityCookie');
    expect(config).toHaveProperty('calendarRedirectCookie');
    expect(config).toHaveProperty('npsRedirectCookie');
    expect(config).toHaveProperty('skillupEnrollmentCampaign');
    expect(config).toHaveProperty('socialLinkToken');
    expect(config).toHaveProperty('socialLinkTokenRegistration');
    expect(config).toHaveProperty('httpRedirectPrefix');
    expect(config).toHaveProperty('groupName');
    expect(config).toHaveProperty('defaultGroupId');
    expect(config).toHaveProperty('simplilearnLogoUrl');
    expect(config).toHaveProperty('passwordLength');
    expect(config).toHaveProperty('maxPasswordLength');
    expect(config).toHaveProperty('facebookPopupDefaultValue');
    expect(config).toHaveProperty('calenderRedirectDefaultValue');
    expect(config).toHaveProperty('npsRedirectDefaultValue');
    expect(config).toHaveProperty('communityRedirectDefaultValue');
    expect(config).toHaveProperty('isB2BAndB2cDefaultValue');
    expect(config).toHaveProperty('lmsPreferenceName');
    expect(config).toHaveProperty('skillUpReplaceString');
    expect(config).toHaveProperty('skillUpReplaceStringWith');
    expect(config).toHaveProperty('cookieMaxAge');
    expect(config).toHaveProperty('linkData');
    expect(config).toHaveProperty('profileCookie');
    expect(config).toHaveProperty('page');
    expect(config).toHaveProperty('defaultCountryCode');
    expect(config).toHaveProperty('ic9SimplexEndPoint');
    expect(config).toHaveProperty('qNameGdprUserExport');
    expect(config).toHaveProperty('qNameGdprUserDelete');
    expect(config).toHaveProperty('s3BucketName');
    expect(config).toHaveProperty('s3BaseUrl');
    expect(config).toHaveProperty('defaultProfilePicUrl');
    expect(config).toHaveProperty('s3Region');
    expect(config).toHaveProperty('gdprDeletePublicEnabled');
    expect(config).toHaveProperty('gdprEnabled');
    expect(config).toHaveProperty('isBlockedPublicDomain');
    expect(config).toHaveProperty('isSimplilearnDomain');
    expect(config).toHaveProperty('profileLmsSettingIdentifier');
    expect(config).toHaveProperty('phoneLmsSettingIdentifier');
    expect(config).toHaveProperty('helpAndSupportIdentifier');
    expect(config).toHaveProperty('myResourcesIdentifier');
    expect(config).toHaveProperty('communityIdentifier');
    expect(config).toHaveProperty('selectedPGPrograms');
    expect(config).toHaveProperty('pgUserCacheTime');
    expect(config).toHaveProperty('enableDrupalSync');
  });
});
