import communicationConfig from './communication.config';

describe('communicationConfig', () => {
  it('should return the default configuration', () => {
    const config = communicationConfig();
    expect(config).toHaveProperty('cloud6Url');
    expect(config).toHaveProperty('enterpriseUrl');
    expect(config).toHaveProperty('deviantsUrl');
    expect(config).toHaveProperty('ic9SiteUrl');
    expect(config).toHaveProperty('ice9ApiUrl');
    expect(config).toHaveProperty('accountSetupUrl');
    expect(config).toHaveProperty('tribeAPIEndpoint');
    expect(config).toHaveProperty('communityBaseUrl');
    expect(config).toHaveProperty('emailCommunicationUrl');
    expect(config).toHaveProperty('communicationUsername');
    expect(config).toHaveProperty('communicationPassword');
    expect(config).toHaveProperty('communityAtpUserGroupId');
    expect(config).toHaveProperty('communityDefaultUserGroupId');
    expect(config).toHaveProperty('freemiumAssignmentBlockRedirectUrl');
    expect(config).toHaveProperty('freemiumAssignmentRedirectUrl');
    expect(config).toHaveProperty('paperclipApiEndpoint');
    expect(config).toHaveProperty('dataChangeLogApiUrl');
    expect(config).toHaveProperty('enableDataChangeLog');
    expect(config).toHaveProperty('tribeCommunityBaseUrl');
    expect(config).toHaveProperty('tribePrivateKey');
    expect(config).toHaveProperty('tribeApiNetworkId');
    expect(config).toHaveProperty('tribeEncSecretKey');
    expect(config).toHaveProperty('tribeEncSecretIv');
    expect(config).toHaveProperty('tribeAPIEndpointWithoutCredentials');
    expect(config).toHaveProperty('skillUpSimpliLogo');
    expect(config).toHaveProperty('credentialsForTribe');
    expect(config).toHaveProperty('kafkaUrl');
    expect(config).toHaveProperty('lrsApiUrl');
    expect(config).toHaveProperty('topicPrefix');
    expect(config).toHaveProperty('defaultAppId');
    expect(config).toHaveProperty('defaultAppName');
    expect(config).toHaveProperty('lrsUserSecret');
    expect(config).toHaveProperty('lrsTtl');
    expect(config).toHaveProperty('lrsEnabled');
    expect(config).toHaveProperty('allowedTopics');
    expect(config).toHaveProperty('couch_salt');
    expect(config).toHaveProperty('sync_cookie');
    expect(config).toHaveProperty('profileTopic');
    expect(config).toHaveProperty('futurex_title');
    expect(config).toHaveProperty('b2b_vendor_title');
    expect(config).toHaveProperty('b2b_without_sso_template');
    expect(config).toHaveProperty('account_setup_template');
    expect(config).toHaveProperty('b2b_with_sso_course');
    expect(config).toHaveProperty('existing_b2b_without_sso_course');
    expect(config).toHaveProperty('existing_learner_template');
    expect(config).toHaveProperty('digitalKeyUrl');
    expect(config).toHaveProperty('examVoucherUrl');
    expect(config).toHaveProperty('managePaymentsUrl');
    expect(config).toHaveProperty('freeTrialUrl');
    expect(config).toHaveProperty('tickets');
  });
});
