import firebaseConfig from './firebase.config';

describe('firebaseConfig', () => {
  it('should return the default configuration', () => {
    const config = firebaseConfig();
    expect(config).toHaveProperty('type');
    expect(config).toHaveProperty('project_id');
    expect(config).toHaveProperty('private_key_id');
    expect(config).toHaveProperty('private_key');
    expect(config).toHaveProperty('client_email');
    expect(config).toHaveProperty('client_id');
    expect(config).toHaveProperty('auth_uri');
    expect(config).toHaveProperty('token_uri');
    expect(config).toHaveProperty('auth_provider_x509_cert_url');
    expect(config).toHaveProperty('client_x509_cert_url');
    expect(config).toHaveProperty('universe_domain');
    expect(config).toHaveProperty('FIREBASE_ADMIN_CREDENTIALS');
  });
});
