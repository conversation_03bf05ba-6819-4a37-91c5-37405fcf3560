import config from './config';

describe('config', () => {
  it('should return the combined configuration', () => {
    const combinedConfig = config();
    expect(combinedConfig).toHaveProperty('applicationEnv');
    expect(combinedConfig).toHaveProperty('jwtSecret');
    expect(combinedConfig).toHaveProperty('cloud6Url');
    expect(combinedConfig).toHaveProperty('mysqlDatabaseHost');
    expect(combinedConfig).toHaveProperty('defaultdb');
    expect(combinedConfig).toHaveProperty('cacheEndpointUrl');
    expect(combinedConfig).toHaveProperty('cdp');
    expect(combinedConfig).toHaveProperty('linkedinClientID');
    expect(combinedConfig).toHaveProperty('type');
  });
});
