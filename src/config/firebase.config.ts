export default () => ({
  type: 'service_account',
  project_id: 'simplilearnrd',
  private_key_id: 'f59298494ac0d77dcaa87d4b936405324d731089',
  private_key:
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  client_email: '<EMAIL>',
  client_id: '116308586299149859087',
  auth_uri: 'https://accounts.google.com/o/oauth2/auth',
  token_uri: 'https://oauth2.googleapis.com/token',
  auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
  client_x509_cert_url:
    'https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-rhhy9%40simplilearnrd.iam.gserviceaccount.com',
  universe_domain: 'googleapis.com',
  FIREBASE_ADMIN_CREDENTIALS: {
    databaseURL: 'https://simplilearnrd.firebaseio.com',
  },
});
