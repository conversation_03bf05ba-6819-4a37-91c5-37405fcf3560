import mongoConfiguration from './mongo.configuration';

describe('mongoConfiguration', () => {
  it('should return the default configuration', () => {
    const config = mongoConfiguration();
    expect(config).toHaveProperty('defaultdb');
    expect(config).toHaveProperty('url');
    expect(config).toHaveProperty('maxPoolSize');
    expect(config).toHaveProperty('maxTimeout');
    expect(config).toHaveProperty('kmsSettings');
  });
});
