import mysqlConfiguration from './mysql.configuration';

describe('mysqlConfiguration', () => {
  it('should return the default configuration', () => {
    const config = mysqlConfiguration();
    expect(config).toHaveProperty('mysqlDatabaseHost');
    expect(config).toHaveProperty('mysqlDatabaseUserName');
    expect(config).toHaveProperty('mysqlDatabasePassword');
    expect(config).toHaveProperty('mysqlDatabaseName');
    expect(config).toHaveProperty('mysqlConnection');
    expect(config).toHaveProperty('mysqlDatabasePort');
    expect(config).toHaveProperty('connectionTimeout');
  });
});
