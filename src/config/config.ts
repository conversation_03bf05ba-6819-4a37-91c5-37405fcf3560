import authConfig from './auth.configuration';
import communicationConfig from './communication.config';
import mysqlConfig from './mysql.configuration';
import mongoConfig from './mongo.configuration';
import cachingConfiguration from './caching.configuration';
import appConfiguration from './app.configuration';
import cdpConfiguration from './cdp.configuration';
import passportConfiguration from './passport.configuration';
import firebaseConfig from './firebase.config';

const config = () => {
  let envConfig = {};

  envConfig = {
    ...authConfig(),
    ...communicationConfig(),
    ...mongoConfig(),
    ...mysqlConfig(),
    ...cachingConfiguration(),
    ...appConfiguration(),
    ...cdpConfiguration(),
    ...passportConfiguration(),
    ...firebaseConfig(),
  };
  return envConfig;
};

export default config;
