import authConfiguration from './auth.configuration';

describe('authConfiguration', () => {
  it('should return the default configuration', () => {
    const config = authConfiguration();
    expect(config).toHaveProperty('jwtSecret');
    expect(config).toHaveProperty('mobileExpiresIn');
    expect(config).toHaveProperty('expiresIn');
    expect(config).toHaveProperty('maxAge');
    expect(config).toHaveProperty('issuer');
    expect(config).toHaveProperty('clientKey');
    expect(config).toHaveProperty('ssoCookie');
    expect(config).toHaveProperty('ssoCookieExpire');
    expect(config).toHaveProperty('ssoVerifiedUserCookie');
    expect(config).toHaveProperty('ssoLeewayTime');
    expect(config).toHaveProperty('hashAlgo');
    expect(config).toHaveProperty('shaAlgorithm');
    expect(config).toHaveProperty('ssoCookieDomain');
    expect(config).toHaveProperty('socialAuth');
    expect(config).toHaveProperty('socialAuthLinkedin');
    expect(config).toHaveProperty('socialAuthFacebook');
    expect(config).toHaveProperty('socialAuthGoogle');
    expect(config).toHaveProperty('saml');
    expect(config).toHaveProperty('apiAuthReqTimeLimit');
    expect(config).toHaveProperty('cloud6ApiAuthSecretSalt');
    expect(config).toHaveProperty('drupal_hash_salt');
    expect(config).toHaveProperty('paperclipApiAuthSecretSalt');
    expect(config).toHaveProperty('lmsSiteUrl');
    expect(config).toHaveProperty('hmacAlgo');
    expect(config).toHaveProperty('hmacEncoding');
    expect(config).toHaveProperty('JwtPayloadEncryptionApiKey');
    expect(config).toHaveProperty('branchKey');
    expect(config).toHaveProperty('branchApiName');
    expect(config).toHaveProperty('branchApiUrl');
    expect(config).toHaveProperty('crypt');
    expect(config).toHaveProperty('tribecrypt');
    expect(config).toHaveProperty('clientSecret');
    expect(config).toHaveProperty('slSalesforceRoles');
    expect(config).toHaveProperty('allowedEngagexRoles');
    expect(config).toHaveProperty('setupManagerRequestValidate');
    expect(config).toHaveProperty('setupManagerRequestSave');
    expect(config).toHaveProperty('userOptionsSetPasswordPending');
    expect(config).toHaveProperty('resetPasswordRequestSave');
    expect(config).toHaveProperty('resetPasswordRequestValidate');
    expect(config).toHaveProperty('sessionCache');
    expect(config).toHaveProperty('newUserAuthState');
    expect(config).toHaveProperty('slCookie');
  });
});
