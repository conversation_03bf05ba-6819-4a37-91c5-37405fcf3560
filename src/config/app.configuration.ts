export default () => ({
  applicationEnv: process.env.NODE_ENV,
  appPort: process.env.APPLICATION_PORT,

  swaggerTitle: 'Simplilearn Account Management',
  swaggerDescription: 'Manage Simplilearn application users',
  swaggerVersion: '1.0',
  swaggerPath: 'docs/api',

  b2bB2cGroupId: [3],
  userOptionsAccountSetupComplete: 1,
  defaultLrsApplicationId: 2,
  defaultUserGroupId: 2,
  userOptionsAccountSetupPending: 0,
  baseUrl: process.env.BASE_URL,
  npsFeedbackApiUrl: process.env.NPS_FEEDBACK_API_URL,
  npsUsername: process.env.NPS_USERNAME,
  npsPassword: process.env.NPS_PASSWORD,
  industry_disable_list: String(process.env.COMMON_INDUSTRY_DISABLE_LIST).split(','),
  prod_industry_disable_list: String(process.env.PROD_INDUSTRY_DISABLE_LIST).split(','),
  fpResetCount: 5,
  fpResetWaitTime: 10,
  fpEmailInCacheTime: 10000,
  resetResponseTime: 60 * 60* 24,
  defaultRole: { '2': 'authenticated user' },
  apiAuthHeader: false,
  updateSource: 'linkedin',
  planTypes: {
    reseller: 'forgotPasswordB2B',
    standard: 'forgotPasswordB2BForStandardEnterprise',
    special: 'forgotPasswordB2BForSpecialReseller',
    premium: 'forgotPasswordB2BForStandardEnterprise',
    'premium-reseller': 'forgotPasswordB2B',
  },

  profileVisibilitySettings: {
    0: 'simplilearn',
    1: 'public',
  },

  learnerActivationRequest: 1,
  teamLearnerActivationRequest: 2,
  freeUserType: 'Freemium',
  paidUserType: 'Paid', 
  skillUpUserReferralCode: 'refCode',
  calendarRedirect: 'calendar_url_redirect',
  skillUpOneTapRedirectCookie: 'skillup_home_google_login',
  skillUpQuestionnaireRedirectCookie: 'skillup_homepage_questionnaire',
  skillUpQuizRedirectCookie: 'skillup_quiz_redirect_url',
  skillUpQuestionnaireDataSubmitted: 'skillup_questionnaire_response_submitted',
  skillUpOriginRedirectCookie: 'skillup_redirect_url',
  gdprUrl: '/sso/gdpr/list',
  frsOneTapRedirectCookie: 'frs_google_login_redirect',
  defaultFreemiumSignupUtm: '31204797.1605269003.1.1.utmcsr=(direct)|utmccn=(direct)|utmcmd=(none)',
  slUuUtmz: 'sl_su_utmz',
  isFrsPage: 'isFrsPage',
  skillUpReferralBaseUrl: process.env.SKILLUP_REFERRAL_BASE_URL,
  skillUpQuestionnaireUrl: 'https://www.simplilearn.com/skillup-questionnaire?origin=signup',
  skillUpQuizUrl: 'https://www.simplilearn.com/free-quiz-game-skillup',
  countyDataCdnLink: 'https://cfs9.simplicdn.net/frontend/js/app/CountryData.js',
  calendarUrl: process.env.CALENDER_URL,
  manageAuthRedirectUrl: '/auth/manage-auth-redirect?token=',
  sheldonSiteUrl: process.env.SHELDON_SITE_URL,
  freemiumAssignmentToken: 'FREEMIUM_ASSIGNMENT_TOKEN',
  communityCookie: 'COMMUNITY_REDIRECT',
  calendarRedirectCookie: 'CALENDAR_REDIRECT',
  npsRedirectCookie: 'NPS_REDIRECT',
  skillupEnrollmentCampaign: 'skillup_enrollment_campaign',
  socialLinkToken: '_slt',
  socialLinkTokenRegistration: '_sltr',
  httpRedirectPrefix: 'http://',
  groupName: 'Simplilearn',
  defaultGroupId: '2',
  simplilearnLogoUrl: 'frontend/images/simplilearn-logo.png',
  passwordLength: 8,
  maxPasswordLength: 128,
  facebookPopupDefaultValue: 1,
  calenderRedirectDefaultValue: 0,
  npsRedirectDefaultValue: 0,
  communityRedirectDefaultValue: 0,
  isB2BAndB2cDefaultValue: 0,
  lmsPreferenceName: 'SCORM',
  skillUpReplaceString: 'https://simpli.',
  skillUpReplaceStringWith: 'https://simpli-web.',
  cookieMaxAge: new Date(Date.now() + 10 * 1000),
  linkData: process.env.LINKDATA_COOKIE,
  profileCookie: '_saveProfile',
  page: process.env.PAGE_COOKIE,
  defaultCountryCode: 'IN',
  ic9SimplexEndPoint: process.env.ICE9_SIMPLEX_ENDPOINT,
  qNameGdprUserExport: 'GDPR_USER_EXPORT',
  qNameGdprUserDelete: 'GDPR_USER_DELETE',
  s3BucketName: process.env.S3_BUCKET_NAME,
  s3BaseUrl: process.env.S3_BASE_URL,
  defaultProfilePicUrl: process.env.DEFAULT_PROFILE_PIC_URL,
  s3Region: process.env.AWS_REGION,
  gdprDeletePublicEnabled:'no',
  gdprEnabled: 1,
  isBlockedPublicDomain: String(process.env.IS_BLOCKED_PUBLIC_DOMAIN).split(','),
  isSimplilearnDomain: String(process.env.IS_SIMPLILEARN_DOMAIN).split(','),
  profileLmsSettingIdentifier : 'Profile Details',
  phoneLmsSettingIdentifier : 'Phone Number',
  helpAndSupportIdentifier: 'Help & Support',
  myResourcesIdentifier: 'My Resources',
  communityIdentifier: 'Community', 
  selectedPGPrograms:  '220,216,160,318,319,302,301,298,297,316,296,203,156,232,213,324,244,210,267,250,221,279,251,307,286,288,242,234,291,317,332,327,331',
  pgUserCacheTime: 2629746,
  enableDrupalSync: true,
});
