export default () => ({
  //NOTE: Most of these settings (process.env.*) are populated/overrriten as part of secret manager loading of secretes from AWS secret manager
  cdp: {
    webEngage: {
      host: process.env.WEBENGAGE_HOST_ENDPOINT,
      keyB2C: process.env.WEBENGAGE_KEY_B2C,
      keyB2B: process.env.WEBENGAGE_KEY_B2B,
      webEngageAuthorizationKey: process.env.WEB_ENGAGE_AUTHORIZATION_KEY,
      webEngageAuthorizationKeyB2b: process.env.WEB_ENGAGE_AUTHORIZATION_B2B_KEY,
    },
    ga4: {
      ga4ConfigCode: process.env.GA4_CONFIG_CODE,
      showGa4InConsole: true,
      ga4DebugMode: true,
      ga4SiteSpeedRate: 20,
      ga4APIUrl: process.env.GA4_API_URL,
      ga4APISecret: process.env.GA4_API_SECRET,
      ga4MeasurementId: process.env.GA4_MEASUREMENT_ID,
    },
  },
});
