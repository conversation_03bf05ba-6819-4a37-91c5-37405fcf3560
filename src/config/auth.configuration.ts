export default () => ({
  jwtSecret: process.env.JWTKEY,
  mobileExpiresIn: '365d',
  expiresIn: '2d',
  maxAge: 86400000,
  issuer: process.env.APP_URL,
  clientKey: 'sl_looper',
  ssoCookie: process.env.SSO_COOKIE,
  ssoCookieExpire: 172800,
  ssoVerifiedUserCookie: '_sso_verified_user',
  ssoLeewayTime: 300,
  hashAlgo: 'HS512',
  shaAlgorithm: 'sha512',
  ssoCookieDomain: '.simplilearn.com',

  // SSO related
  socialAuth: {
    type: ['Google', 'Facebook', 'SignInWithApple', 'LinkedIn'],
    allowedSocialLoginPlatforms: process.env.ALLOWED_SOCIAL_LOGIN_PLATFORMS,
  },
  socialAuthLinkedin: process.env.SOCIAL_AUTH_LINKEDIN,
  socialAuthFacebook: process.env.SOCIAL_AUTH_FACEBOOK,
  socialAuthGoogle: process.env.GOOGLE_TOKEN_VERIFY_URL,

  saml: {
    samlAppKeySet: 'idp',
    callbackUrl: process.env.BASE_URL + process.env.SAML_CALLBACK_URL,
    logoutCallbackUrl: process.env.BASE_URL + process.env.SAML_LOGOUT_CALLBACK_URL,
  },


  // API related
  apiAuthReqTimeLimit: 120,
  cloud6ApiAuthSecretSalt: process.env.CLOUD6_SECRET_SALT,
  drupal_hash_salt: process.env.DRUPAL_HASH_SALT,
  paperclipApiAuthSecretSalt: process.env.PAPERCLIP_SECRET_SALT,
  lmsSiteUrl: process.env.LMS_SITE_URL,
  hmacAlgo: 'SHA256',
  hmacEncoding: 'base64',
  JwtPayloadEncryptionApiKey: process.env.JWT_PAYLOAD_ENCRYPTION_API_KEY,

  // Branch config
  branchKey: process.env.BRANCH_KEY,
  branchApiName: process.env.BRANCH_API_NAME,
  branchApiUrl: process.env.BRANCH_API_URL,

  crypt: {
    key: process.env.SECRET_KEY,
    iv: process.env.SECRET_IV,
    algo: 'aes-256-cbc',
  },
  tribecrypt: {
    key: process.env.TRIBE_SECRET_KEY,
    iv: process.env.TRIBE_SECRET_IV,
    algo: 'aes-256-cbc',
  },

  clientSecret: {
    cloud6_zend: { secret: process.env.CLOUD6_ZEND, redirect_uri: '' },
    sl_looper: { secret: process.env.SL_LOOPER_KEY, redirect_uri: '' },
    sl_ice9: { secret: process.env.SL_IC9, redirect_uri: '' },
    sl_xenia: { secret: process.env.SL_XENIA, redirect_uri: '' },
    mimic11: { secret: process.env.MIMIC11, redirect_uri: '' },
    sl_salesforce: { secret: process.env.SL_SALESFORCE, redirect_uri: '' },
    sl_engagex: { secret: process.env.SL_ENGAGEX, redirect_uri: '' },
    client_key_nps: { secret: process.env.CLIENT_KEY_NPS },
  },

  slSalesforceRoles: {
    AD: 'i9simplex_sso_ad',
    BM: 'i9simplex_sso_bm',
    ISM: 'i9simplex_sso_ism',
    Finance: 'i9simplex_sso_finance',
    BusinessOperations: 'i9simplex_sso_business_operations',
    DataComplianceAdmin: 'i9simplex_sso_gdpr',
  },

  allowedEngagexRoles: JSON.parse(process.env.ALLOW_ENAGEX_ROLES),
  setupManagerRequestValidate: 1,
  setupManagerRequestSave: 2,
  userOptionsSetPasswordPending: 1,
  resetPasswordRequestSave: 2,
  resetPasswordRequestValidate: 1,
  sessionCache: 'sc_',
  newUserAuthState: '2',
  slCookie: 'sl_u',
});
