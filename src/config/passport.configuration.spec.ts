import passportConfiguration from './passport.configuration';

describe('passportConfiguration', () => {
  it('should return the default configuration', () => {
    const config = passportConfiguration();
    expect(config).toHaveProperty('linkedinClientID');
    expect(config).toHaveProperty('linkedindClientSecret');
    expect(config).toHaveProperty('linkedinCallback');
    expect(config).toHaveProperty('facebookAppId');
    expect(config).toHaveProperty('facebookAppSecret');
    expect(config).toHaveProperty('facebookCallback');
    expect(config).toHaveProperty('googleClientId');
    expect(config).toHaveProperty('googleClientSecret');
    expect(config).toHaveProperty('googleCallback');
    expect(config).toHaveProperty('appleTeamId');
    expect(config).toHaveProperty('appleClientId');
    expect(config).toHaveProperty('appleKeyId');
    expect(config).toHaveProperty('appleCallback');
  });
});
