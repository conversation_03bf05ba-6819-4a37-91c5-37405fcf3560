import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { Logger } from './logging/logger';

describe('AppController', () => {
  let appController: AppController;
  let appService: AppService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [
        {
          provide: AppService,
          useValue: {
            healthCheck: jest.fn(),
          },
        },
      ],
    }).compile();

    appController = module.get<AppController>(AppController);
    appService = module.get<AppService>(AppService);
    jest.spyOn(Logger, 'info').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('redirectTo', () => {
    it('should redirect to auth/login and log the redirection', () => {
      const result = appController.redirectTo();
      expect(result).toBeUndefined(); // Redirect decorator handles the response
      expect(Logger.info).toHaveBeenCalledWith('Redirecting to login page');
    });
  });

  describe('healthCheck', () => {
    it('should return the health check message from AppService', () => {
      const healthMessage = 'Service is healthy';
      jest.spyOn(appService, 'healthCheck').mockReturnValue(healthMessage);

      expect(appController.healthCheck()).toBe(healthMessage);
      expect(appService.healthCheck).toHaveBeenCalled();
    });
  });
});
