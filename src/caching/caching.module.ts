import { DynamicModule, Module } from '@nestjs/common';

import * as memcacheDriver from 'memcache-plus';
import * as cacheStore from 'cache-manager-memcached-store';
import { CacheModule } from '@nestjs/cache-manager';
import { CachingService } from './caching.service';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  providers: [CachingService],
})
export class CachingModule {
  static registerAsync(local?: boolean): DynamicModule {
    if (local === true) {
      return CacheModule.register();
    }
    return CacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (config: ConfigService) => ({
        store: cacheStore,
        options: {
          hosts: [config.get<string>('cacheEndpointUrl')],
        },
        ttl: config.get<number>('cacheTTL'),
        driver: memcacheDriver,
      }),
      inject: [ConfigService],
      //extraProviders:[CachingService],
      isGlobal: true,
    });
  }
}
