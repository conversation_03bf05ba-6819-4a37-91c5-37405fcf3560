import { Inject, Injectable } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class CachingService {
  constructor(@Inject(CACHE_MANAGER) private readonly cacheManager: Cache) {}
  async get<T>(key: string): Promise<T> {
    return this.cacheManager.get<T>(key);
  }
  async set<T>(key: string, value: T, ttl?: number) {
    return this.cacheManager.set(key, value, ttl);
  }
  
  async del(key: string): Promise<boolean> {
    return this.cacheManager.del(key);
  }

  async invalidateCache<T>(key: string, value: T, ttl?: number) {
    return this.cacheManager.set(key, value, ttl);
  }
}
