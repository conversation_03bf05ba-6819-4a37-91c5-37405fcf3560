import { Test, TestingModule } from '@nestjs/testing';
import { SamlController } from './saml.controller';
import { SamlService } from './services/saml.service';
import { ConfigService } from '@nestjs/config';
import { Logger } from '../logging/logger';

// Mock dependencies
jest.mock('../logging/logger');

describe('SamlController', () => {
  let controller: SamlController;

  const mockSamlService = {
    generateServiceProviderMetadata: jest.fn(),
    acs: jest.fn(),
    mobileSuccess: jest.fn(),
    mobileUserAgent: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SamlController],
      providers: [
        { provide: SamlService, useValue: mockSamlService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    controller = module.get<SamlController>(SamlController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('spInitiatedSso', () => {
    it('should log sp initiated sso', async () => {
      await controller.spInitiatedSso();
      expect(Logger.log).toHaveBeenCalledWith('SP initiated SSO');
    });
  });

  describe('mobileSuccess', () => {
    it('should return success response for mobile', async () => {
      const req = { params: { email: '<EMAIL>' }, get: jest.fn().mockReturnValue('user-agent') };
      const response = {
        data: { some: 'data' },
        mobile: { isIos: true, isAndroid: false },
      };
      mockSamlService.mobileSuccess.mockResolvedValue(response);

      const result = await controller.mobileSuccess(req as any);

      expect(result.isiOS).toBe(true);
      expect(result.isAndroidOS).toBe(false);
      expect(JSON.parse(result.data)).toEqual(response);
    });

    it('should handle null response in mobileSuccess', async () => {
        const req = { params: { email: '<EMAIL>' }, get: jest.fn().mockReturnValue('user-agent') };
        mockSamlService.mobileSuccess.mockResolvedValue(null);
        const result = await controller.mobileSuccess(req as any);
        expect(result.isiOS).toBeUndefined();
        expect(result.isAndroidOS).toBeUndefined();
    });

    it('should throw error if service fails', async () => {
        const req = { params: { email: '<EMAIL>' }, get: jest.fn() };
        mockSamlService.mobileSuccess.mockRejectedValue(new Error('Service Error'));
        await expect(controller.mobileSuccess(req as any)).rejects.toThrow('Service Error');
    });
  });

  describe('mobileError', () => {
    it('should return error response for mobile', async () => {
        const req = { get: jest.fn().mockReturnValue('user-agent') };
        mockSamlService.mobileUserAgent.mockReturnValue({ isIos: false, isAndroid: true });
        const result = await controller.mobileError(req as any);
        expect(result.isAndroidOS).toBe(true);
        expect(result.isiOS).toBe(false);
    });

    it('should handle null mobileAgent in mobileError', async () => {
        const req = { get: jest.fn().mockReturnValue('user-agent') };
        mockSamlService.mobileUserAgent.mockReturnValue(null);
        const result = await controller.mobileError(req as any);
        expect(result.isAndroidOS).toBeUndefined();
        expect(result.isiOS).toBeUndefined();
    });

    it('should throw error if service fails', async () => {
        const req = { get: jest.fn() };
        mockSamlService.mobileUserAgent.mockImplementation(() => {
            throw new Error('Service Error');
        });
        await expect(controller.mobileError(req as any)).rejects.toThrow('Service Error');
    });
  });

  describe('spMetaData', () => {
    it('should handle errors without stack', async () => {
        const req = { params: { titleValue: 'test' } };
        const res = { status: jest.fn().mockReturnThis(), send: jest.fn() };
        mockSamlService.generateServiceProviderMetadata.mockRejectedValue({ message: 'Meta Error' });
        await controller.spMetaData(req as any, res as any);
        expect(res.status).toHaveBeenCalledWith(500);
        expect(res.send).toHaveBeenCalledWith('Error generating metadata');
    });

    it('should handle errors', async () => {
        const req = { params: { titleValue: 'test' } };
        const res = { status: jest.fn().mockReturnThis(), send: jest.fn() };
        mockSamlService.generateServiceProviderMetadata.mockRejectedValue(new Error('Meta Error'));
        await controller.spMetaData(req as any, res as any);
        expect(res.status).toHaveBeenCalledWith(500);
        expect(res.send).toHaveBeenCalledWith('Error generating metadata');
    });
  });

  describe('logout', () => {
    it('should logout and redirect', async () => {
        const req = { user: {}, logout: jest.fn(), params: {} };
        const res = { redirect: jest.fn() };
        await controller.logout(req as any, res as any);
        expect(req.logout).toHaveBeenCalled();
        expect(res.redirect).toHaveBeenCalledWith('/auth/login');
    });

    it('should handle errors without stack in logout', async () => {
        const req = { params: {}, logout: jest.fn(() => { throw { message: 'Logout Error' } }) };
        const res = { status: jest.fn().mockReturnThis(), send: jest.fn() };
        await controller.logout(req as any, res as any);
        expect(res.status).toHaveBeenCalledWith(500);
        expect(res.send).toHaveBeenCalledWith('Error during logout');
    });

    it('should handle errors', async () => {
        const req = { params: {}, logout: jest.fn(() => { throw new Error('Logout Error') }) };
        const res = { status: jest.fn().mockReturnThis(), send: jest.fn() };
        await controller.logout(req as any, res as any);
        expect(res.status).toHaveBeenCalledWith(500);
        expect(res.send).toHaveBeenCalledWith('Error during logout');
    });
  });

  describe('acs', () => {
    it('should handle errors without stack in acs', async () => {
        const req = { body: {} };
        const res = { redirect: jest.fn() };
        mockSamlService.acs.mockImplementation(() => {
            throw { message: 'ACS Error' };
        });
        await controller.acs(req as any, res as any);
        expect(res.redirect).toHaveBeenCalledWith('/saml/error');
    });

    it('should handle errors', async () => {
        const req = { body: {} };
        const res = { redirect: jest.fn() };
        mockSamlService.acs.mockImplementation(() => {
            throw new Error('ACS Error');
        });
        await controller.acs(req as any, res as any);
        expect(res.redirect).toHaveBeenCalledWith('/saml/error');
    });
  });

  describe('proxyAcs', () => {
    it('should handle errors without stack', async () => {
        const req = { body: {} };
        const res = { redirect: jest.fn() };
        mockSamlService.acs.mockRejectedValue({ message: 'Proxy ACS Error' });
        await controller.proxyAcs(req as any, res as any);
        expect(res.redirect).toHaveBeenCalledWith('/saml/error');
    });

    it('should handle errors', async () => {
        const req = { body: {} };
        const res = { redirect: jest.fn() };
        mockSamlService.acs.mockRejectedValue(new Error('Proxy ACS Error'));
        await controller.proxyAcs(req as any, res as any);
        expect(res.redirect).toHaveBeenCalledWith('/saml/error');
    });
  });

  describe('spInitiatedSlsAction', () => {
    it('should logout and redirect', async () => {
        const req = { user: {}, logout: jest.fn(), params: {} };
        const res = { redirect: jest.fn() };
        await controller.spInitiatedSlsAction(req as any, res as any);
        expect(req.logout).toHaveBeenCalled();
        expect(res.redirect).toHaveBeenCalledWith('/auth/login');
    });

    it('should handle errors without stack in spInitiatedSlsAction', async () => {
        const req = { params: {}, logout: jest.fn(() => { throw { message: 'SLS Error' } }) };
        const res = { status: jest.fn().mockReturnThis(), send: jest.fn() };
        await controller.spInitiatedSlsAction(req as any, res as any);
        expect(res.status).toHaveBeenCalledWith(500);
        expect(res.send).toHaveBeenCalledWith('Error during SP initiated SLS');
    });

    it('should handle errors', async () => {
        const req = { params: {}, logout: jest.fn(() => { throw new Error('SLS Error') }) };
        const res = { status: jest.fn().mockReturnThis(), send: jest.fn() };
        await controller.spInitiatedSlsAction(req as any, res as any);
        expect(res.status).toHaveBeenCalledWith(500);
        expect(res.send).toHaveBeenCalledWith('Error during SP initiated SLS');
    });
  });

  describe('error', () => {
    it('should return ic9SiteUrl', async () => {
        const req = { params: {} };
        const res = { status: jest.fn().mockReturnThis(), send: jest.fn() };
        mockConfigService.get.mockReturnValue('http://ic9.com');
        const result = await controller.error(req as any, res as any);
        expect(result.ICE9_SITE_URL).toBe('http://ic9.com');
    });

    it('should handle errors without stack in error', async () => {
        const req = { params: {} };
        const res = { status: jest.fn().mockReturnThis(), send: jest.fn() };
        mockConfigService.get.mockImplementation(() => { throw { message: 'Config Error' } });
        await controller.error(req as any, res as any);
        expect(res.status).toHaveBeenCalledWith(500);
        expect(res.send).toHaveBeenCalledWith('Error rendering error page');
    });

    it('should handle errors', async () => {
        const req = { params: {} };
        const res = { status: jest.fn().mockReturnThis(), send: jest.fn() };
        mockConfigService.get.mockImplementation(() => { throw new Error('Config Error') });
        await controller.error(req as any, res as any);
        expect(res.status).toHaveBeenCalledWith(500);
        expect(res.send).toHaveBeenCalledWith('Error rendering error page');
    });
  });
});
