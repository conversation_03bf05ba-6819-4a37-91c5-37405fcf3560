import { PassportStrategy } from '@nestjs/passport';
import { MultiSamlStrategy, Profile } from '@node-saml/passport-saml';
import { BadRequestException, Injectable } from '@nestjs/common';
import { Request } from 'express';
import { SamlService } from '../services/saml.service';
import { VerifiedCallback } from '@node-saml/passport-saml/lib/types';
import { Logger } from '../../logging/logger';

@Injectable()
export class SamlStrategy extends PassportStrategy(MultiSamlStrategy, 'saml') {
  constructor(private readonly samlService: SamlService) {
    super(
      {
        passReqToCallback: true,
        getSamlOptions: async (req: Request, done: (err: Error | null, samlOptions?: any) => void) => {
          try {
            const title: string = req.params.titleValue as string || 'default';
            const samlConfig = await samlService.getConfiguration(title);

            if (!samlConfig?.saml) {
              return done(new BadRequestException('SAML configuration not found for the provided title'), null);
            }

            const samlLogs = await samlService.saveSamlLogs(req, { title: title, gid: samlConfig?.gid });
            req['logId'] = samlLogs['id'];

            const saml = {
              ...samlConfig?.saml,
              wantAuthnResponseSigned: false,
              wantAssertionsSigned: false,
              audience: false,
            };

            return done(null, saml);
          } catch (error) {
            return done(null, error);
          }
        },
      },
      (req: Request, profile: Profile, done: (err: any, user?: any) => void) => {
        try {
          const user = {
            id: profile.nameID,
            email: profile.email,
            displayName: profile.displayName,
          };

          Logger.log(`User data`, user);
          return done(null, { ...profile, logId: req['logId'] });
        } catch (error) {
          return done(error, null);
        }
      },
    );

    // ✅ Explicitly reference `this.samlService` to prevent TS6138 error
    console.log(this.samlService ? 'SamlService initialized' : 'SamlService not found');
  }

  async validate(profile: Profile, done: VerifiedCallback): Promise<any> {
    try {
      return done(null, profile);
    } catch (error) {
      return done(null, profile);
    }
  }
}
