import { Test, TestingModule } from '@nestjs/testing';
import { SamlStrategy } from './saml.strategy';
import { SamlService } from '../services/saml.service';
import { BadRequestException } from '@nestjs/common';
import { Profile } from '@node-saml/passport-saml';

describe('SamlStrategy', () => {
  let strategy: SamlStrategy;

  const mockSamlService = {
    getConfiguration: jest.fn(),
    saveSamlLogs: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SamlStrategy,
        { provide: SamlService, useValue: mockSamlService },
      ],
    }).compile();

    strategy = module.get<SamlStrategy>(SamlStrategy);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('getSamlOptions', () => {
    it('should return saml options when config is valid', async () => {
      const req: any = { params: { titleValue: 'test' } };
      const done = jest.fn();
      const samlConfig = { saml: { issuer: 'test' }, gid: 1 };

      mockSamlService.getConfiguration.mockResolvedValue(samlConfig);
      mockSamlService.saveSamlLogs.mockResolvedValue({ id: 456 });

      await (strategy as any)._options.getSamlOptions(req, done);

      expect(mockSamlService.getConfiguration).toHaveBeenCalledWith('test');
      expect(mockSamlService.saveSamlLogs).toHaveBeenCalledWith(req, { title: 'test', gid: 1 });
      expect(req['logId']).toBe(456);
      expect(done).toHaveBeenCalledWith(null, expect.objectContaining({
        issuer: 'test',
        wantAuthnResponseSigned: false,
        wantAssertionsSigned: false,
        audience: false,
      }));
    });

    it('should use "default" title if titleValue is missing', async () => {
      const req: any = { params: {} };
      const done = jest.fn();
      const samlConfig = { saml: { issuer: 'default-issuer' }, gid: 2 };

      mockSamlService.getConfiguration.mockResolvedValue(samlConfig);
      mockSamlService.saveSamlLogs.mockResolvedValue({ id: 999 });

      await (strategy as any)._options.getSamlOptions(req, done);

      expect(mockSamlService.getConfiguration).toHaveBeenCalledWith('default');
      expect(req['logId']).toBe(999);
      expect(done).toHaveBeenCalledWith(null, expect.objectContaining({ issuer: 'default-issuer' }));
    });

    it('should return BadRequestException if saml config is missing', async () => {
      const req: any = { params: { titleValue: 'bad' } };
      const done = jest.fn();

      mockSamlService.getConfiguration.mockResolvedValue({ saml: null });

      await (strategy as any)._options.getSamlOptions(req, done);

      expect(done).toHaveBeenCalledWith(expect.any(BadRequestException), null);
    });

    it('should return error if exception is thrown', async () => {
      const req: any = { params: { titleValue: 'error-case' } };
      const done = jest.fn();

      const error = new Error('Unexpected');
      mockSamlService.getConfiguration.mockRejectedValue(error);

      await (strategy as any)._options.getSamlOptions(req, done);

      expect(done).toHaveBeenCalledWith(null, error);
    });
  });

  describe('validate', () => {
    it('should return enriched profile with logId', async () => {
      const profile = {
        nameID: 'user1',
        email: '<EMAIL>',
        displayName: 'User One',
        issuer: 'test-issuer',
        nameIDFormat: 'urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress',
        logId: 'log123',
      } as any; // Cast to bypass strict type checks in test

      const done = jest.fn();

      await strategy.validate(profile, done);

      expect(done).toHaveBeenCalledWith(null, profile);
    });

    it('should handle error inside validate and still call done', async () => {
  const profile = {
    nameID: 'user1',
    email: '<EMAIL>',
    displayName: 'User One',
    issuer: 'test-issuer',
    nameIDFormat: 'urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress',
  } as Profile;

  const throwingDone = jest.fn(() => {
    throw new Error('done failed');
  });

  // Since both try and catch return done(null, profile), it will call again in catch
  const safeDone = jest.fn();

  // Wrap validate in try-catch to suppress test crash from error in "done"
  try {
    await strategy.validate(profile, throwingDone);
  } catch (e) {
    // Call again with safeDone in catch to complete flow
    await strategy.validate(profile, safeDone);
  }

  expect(throwingDone).toHaveBeenCalledWith(null, profile);
  expect(safeDone).toHaveBeenCalledWith(null, profile);
});

  });
});
