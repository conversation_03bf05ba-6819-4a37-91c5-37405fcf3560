import { BadRequestException, Inject, Injectable, Req, Res } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Logger } from '../../logging/logger';
import { SAML } from '@node-saml/passport-saml';
import { GroupSettings } from '../../db/mysql/entity/group-settings.entity';
import { SamlLogs } from '../../db/mysql/entity/saml-logs.entity';
import { HelperService } from '../../helper/helper.service';
import { IUserRepository, UserRepository } from '../../user/repositories/user/user.repository';
import { Utility } from '../../common/util/utility';
import { AuthTokenHelper } from '../../auth/helper/auth.tokenhelper';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';
import { User } from '../../db/mongo/schema/user/user.schema';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
import { CustomFieldLabelMaster } from '../../db/mysql/entity/custom_field_label_master.entity';
import { CustomFieldUserValue } from '../../db/mysql/entity/custom_field_user_value.entity';
import { FirebaseService } from '../../db/firebase/firebase.service';

@Injectable()
export class SamlService {
  @Inject() private readonly helperService: HelperService;
  @InjectRepository(GroupSettings) private groupSetting: Repository<GroupSettings>; 
  @InjectRepository(SamlLogs) private samlLogs: Repository<SamlLogs>;
  @InjectRepository(CustomFieldLabelMaster) private customFieldLabel: Repository<CustomFieldLabelMaster>;
  @InjectRepository(CustomFieldUserValue) private customFieldUserValue: Repository<CustomFieldUserValue>;
  @Inject() private readonly configService: ConfigService;

  #isMobile = false;
  #appIOS = 'SimplilearnAppIOS';
  #appAndroid = 'SimplilearnAppAndroid';
  #errorRedirect = '/saml/service-provider/error';
  async getConfiguration(title: string): Promise<{
    saml: {
      path: string;
      callbackUrl: string;
      entryPoint: string;
      issuer: string;
      logoutCallbackUrl: string;
      logoutUrl: string;
      idpIssuer: string;
      idpCert: string;
    };
    gid: number;
  }> {
    try {
      const group = await this.groupSetting.findOne({ where: { identifier: 'title', value: title } });
      const team_id = 0;
      const samlSetting = await this.getGroupsSettingByTitle(
        group?.gid,
        team_id,
        this.configService.get('saml')?.samlAppKeySet,
      );
      const idpMetaData = JSON.parse(this.getGroupsSettingFilter(samlSetting, 'idp_metadata')?.value);
      const idpMetaDataCertificate = this.getGroupsSettingFilter(samlSetting, 'certificate');
      const acsUrl = `${this.configService.get('saml')?.callbackUrl}${title}`;
      const slsUrl = `${this.configService.get('saml')?.logoutCallbackUrl}${title}`;
      const samlConfig = {
        path: acsUrl, //acs url
        callbackUrl: acsUrl, //acs call back ur
        logoutCallbackUrl: slsUrl, //logout call back url
        entryPoint: idpMetaData?.idp?.sso_url,
        idpIssuer: idpMetaData?.idp?.entity_id, //idp issuer
        issuer: this.configService.get('issuer'), // site url as issuer
        logoutUrl: idpMetaData?.idp?.sso_url, //idp logout url
        idpCert: idpMetaDataCertificate?.value,
      };
      return { saml: samlConfig, gid: group?.gid };
    } catch (e: any) {
      Logger.log('te', e);
    }
  }

  private async getGroupsSettingByTitle(gId: number, team_id: number, samlAppKeySet: string) {
    const whereClause = { gid: gId, status: 1 };
    if (team_id) {
      whereClause['team_id'] = team_id;
    }
    if (samlAppKeySet) {
      whereClause['key_set'] = samlAppKeySet;
    }
    const groupSetting = await this.groupSetting.find({ where: whereClause });

    return groupSetting;
  }

  private getGroupsSettingFilter(groupSetting, identifier = null) {
    return groupSetting.find((item) => item.identifier === identifier) || null;
  }

  async generateServiceProviderMetadata(title) {
    const samlConfig:any = await this.getConfiguration(title);
    // const samlStrategy = new Strategy(samlConfig,((profile,done)=>{}),null);
    // return samlStrategy._saml.generateServiceProviderMetadata(samlConfig['idpCert'],null);
    const saml = new SAML(samlConfig.saml);
    return saml.generateServiceProviderMetadata(samlConfig['idpCert'], null);
  }

  async saveSamlLogs(@Req() req, data: { gid: number; title: string }) {
    try {
      let samlLog = new SamlLogs();
      samlLog = {
        gid: data?.gid,
        email: '',
        title: data?.title,
        attributes: '',
        get_params: JSON.stringify(req?.query) || '',
        post_params: JSON.stringify(req?.body) || '',
        created_on: Math.floor(Date.now() / 1000),
        updated_on: Math.floor(Date.now() / 1000),
        id: 0,
      };

      const response = await this.samlLogs.save(samlLog);
      if (!response) {
        Logger.log('saml logs failed');
      }
      return response;
    } catch (e: any) {
      Logger.log('saml logs failed', e);
    }
  }

  async updateSamlLogs(gid: number, data) {
    try {
      return await this.samlLogs.update(gid, data);
    } catch (e: any) {
      Logger.log('saml logs failed', e);
    }
  }

  async saveSsoAttributes(gid: number, email: string, ssoAttributes: Record<string, any> = {}): Promise<boolean> {
    try {
      if(!ssoAttributes) {
        const userRepository = await this.helperService.get<IUserRepository>(UserRepository)
        let user = await userRepository.getUserByEmail(email);
        ssoAttributes = JSON.parse(user?.sso_attributes || '{}');
      }
      const customLabelDetails = await this.customFieldLabel.find({
        where: { gid, display_name: In(Object.keys(ssoAttributes)) },
      });
      const currentTimestamp = Math.floor(Date.now() / 1000);
      const customLabelValueMap = customLabelDetails.map(item => ({
        custom_field_label_id: item.custom_field_label_id,
        gid,
        value: ssoAttributes[item.display_name],
        email,
        creation_date: currentTimestamp,
        modification_date: currentTimestamp,
      }));
      // Parallelize processing
      const operations = customLabelValueMap.map(async customField => {
        const existingRecord = await this.customFieldUserValue.findOne({
          where: {
            gid: customField.gid,
            custom_field_label_id: customField.custom_field_label_id,
            email: customField.email,
          },
        });
        if (existingRecord) {
          return this.customFieldUserValue.update(
            {
              gid: customField.gid,
              custom_field_label_id: customField.custom_field_label_id,
              email: customField.email,
            },
            {
              value: customField.value,
              modification_date: customField.modification_date,
            }
          );
        } else {
          return this.customFieldUserValue.save(customField);
        }
      });
      const results = await Promise.allSettled(operations);
      const hasFailures = results.some(result => result.status === 'rejected');
      return !hasFailures;
    } catch (error: any) {
      Logger.error('Error saving SSO attributes', {
        TRACE: { METHOD: `${this.constructor.name}@${this.saveSsoAttributes.name}` },
        MESSAGE: 'An error occurred while saving SSO attributes.',
        REQUEST: { gid, email, ssoAttributes },
        ERROR: error,
      });
      return false;
    }
  }

  /**
   * Handles the Assertion Consumer Service (ACS) endpoint for SAML authentication.
   * @param object req
   * @param object res
   * @param string responseType
   * @returns
   */
  async acs(@Req() req, @Res() res, responseType = 'return') {
    try {
      const requestParams = req.params;
      const clientKey = this.configService.get('clientKey');
      const relayState = req?.body?.RelayState;
      let { email } = { email: req.user?.nameID || req?.body?.email };
      let skipGroupMemberCheck;
      // TODO : check attributes work flow
      const ssoAttributes = req?.user?.attributes || req?.body?.attributes || req?.user?.sso_attributes || {};
      if (requestParams.titleValue.toLowerCase() === this.configService.get('futurex_title').toLowerCase()) {
        email = ssoAttributes?.email || (req.user?.nameID || req?.body?.email);
      }
      const [userRepository, userHelper, userMgmtUtilityHelper, authTokenHelper, cookieHelper] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.getHelper('UserHelper'),
        this.helperService.getHelper('UserMgmtUtilityHelper'),
        this.helperService.get<AuthTokenHelper>(AuthTokenHelper),
        this.helperService.getHelper('CookieHelper'),
        this.helperService.getHelper('lrsHelper'),
      ]);
      const group = await this.groupSetting.findOne({ where: { identifier: 'title', value: req?.params.titleValue } });
      const userAgent = req.get('User-Agent');
      const isMobile = this.mobileUserAgent(userAgent)?.isMobile;
      this.#isMobile = isMobile;
      this.#errorRedirect = isMobile ? '/saml/service-provider/mobile/error' : this.#errorRedirect;

      // update logs
      if (req?.logId) {
        this.updateSamlLogs(req?.logId, {
          email: email,
          attributes: JSON.stringify(ssoAttributes),
          get_params: JSON.stringify({ q: req?.url }),
        });
      }
      if (Utility.isEmpty(email)) {
        Logger.error('SAML Request - Learner email missing in request.', {
          METHOD: this.constructor?.name + '@' + this.acs?.name,
          MESSAGE: 'SAML Request - Learner email missing in request.',
          REQUEST: req,
          RESPONSE: null,
          TIMESTAMP: new Date().getTime(),
        });
        throw new BadRequestException('SAML Request - Learner email missing in request.');
      }

      let user: Partial<User> = await userRepository.getUserByEmail(email);
      const samlSettings = await this.getSamlSettings(group);
      const {
        autoRegistrationB2c,
        b2bSelfServePortal,
        isCoursePurchase,
        autoRegistrationB2b,
        loginAsB2c,
        loginAsB2b,
        groupSettingRelayState,
        updateRoleSetting,
        clientId,
        customToken,
        attributes
      } = samlSettings;

      if (!user) {
        // This internally calls userRegistration (userService) so it is synced to Mysql as well  
        user = await this.handleSamlUserRegistration(
          userRepository,
          email,
          clientKey,
          autoRegistrationB2c,
          b2bSelfServePortal,
          isCoursePurchase,
          group?.gid.toString()
        );
        const firebaseInstance = await this.helperService.get<FirebaseService>(FirebaseService);
        const db = await firebaseInstance.getDB(user.email, Number(user.uid));
        const firebaseStatus = await firebaseInstance.createDb(db,user.email,user.uid);
        if(firebaseStatus){
          console.log('Firebase DB created successfully for user:', user.email);
        } else {
          console.log('Firebase DB creation Failed for user:', user.email);
        }
      }

      if (!user?.status) {
        throw new BadRequestException('SAML Request - User account has been blocked.');
      }

      //  Mapping attributes with group setting - send to kafka - TODO : not reuired
      // const kafkaPayload = {
      //   gid: group?.gid,
      //   sso_attributes: ssoAttributes,
      //   auto_register_b2b: 1,
      //   uid: user?.uid,
      //   email: email,
      //   relay_state: relayState,
      // };

      /* Capturing custom user fields - START */
      if (ssoAttributes && typeof ssoAttributes === 'object') {
        let processedSsoAttributes = Object.fromEntries(
          Object.entries(ssoAttributes).map(([key, value]) => [key, value === undefined ? "" : value])
        );
        const userFields = { sso_attributes: JSON.stringify(processedSsoAttributes) }
        const userHelper = await this.helperService.getHelper('UserHelper');
        const [mongoUpdate, mysqlUpdate] = await Promise.all([
          // updating user details in mongo
          userRepository.findOneAndUpdate({ uid : user?.uid }, userFields),
          // updating user details in mysql
          userHelper.updateCloud6SentinelByUidOrMail({ uid : user?.uid, ...userFields})
        ])
        let updateResult = mongoUpdate?.uid && mysqlUpdate
        if (!updateResult) {
          Logger.error('SAML Request - Some error occurred while updating user SSO attributes.', {
            TRACE: { METHOD: `${this.constructor.name}@${this.acs.name}` },
            MESSAGE: 'SAML Request - Some error occurred while updating user SSO attributes.',
            REQUEST: {
              gid: group?.gid,
              email: email,
              title: req?.params.titleValue,
              sso_attributes: ssoAttributes,
            },
          });
        }

        /*  If user is already a member of the group, save custom field values here */
        const membershipInfo = user?.user_groups?.map(Number).includes(Number(group?.gid));
        if(membershipInfo) {
            const saveAttributesStatus = await this.saveSsoAttributes(group?.gid , user?.email , processedSsoAttributes);
            if(!saveAttributesStatus) {
                Logger.error('SAML Request - Error saving SSO attributes.', {
                TRACE: { METHOD: `${this.constructor.name}@${this.acs.name}` },
                REQUEST: { gid: group?.gid, email: user?.email },
                RESPONSE: saveAttributesStatus,
              });
            }

        }
      }
      /* Capturing custom user fields - END */

      /* Mapping profile attributes - START */
        if (!Utility.isEmpty(attributes)) {
            const attributesConfig = JSON.parse(attributes);
            if (
          !Utility.isEmpty(attributesConfig['attribute_fields']) &&
          typeof attributesConfig['attribute_fields'] === 'object' &&
          !Utility.isEmpty(attributesConfig['field_mapping']) &&
          typeof attributesConfig['field_mapping'] === 'object'
            ) {
              const attributeFields = attributesConfig['attribute_fields'];
              const fieldMapping = attributesConfig['field_mapping'];

              if (Object.keys(attributeFields).every((key) => key in ssoAttributes)) {
                for (const [field, mapping] of Object.entries(fieldMapping)) {
                  const fieldKeys = (mapping as string).split(' ').filter(Boolean);
                  const fieldValue = fieldKeys.length ? fieldKeys
                  .map((fieldKey) => ssoAttributes[fieldKey] || '')
                  .filter(Boolean)
                  .join(' ') : '';
                  if (fieldValue) {
                    if(field === 'user_name') {
                      // Update user name in mongo and mysql
                        await Promise.all([
                        userRepository.findOneAndUpdate({ uid: user?.uid }, { display_name: fieldValue }),
                        userHelper.updateCloud6SentinelByUidOrMail({ uid: user?.uid, display_name: fieldValue }),
                        ]);
                    }
                    Logger.log('SAML Request - User profile display name has been updated.', {
                      TRACE: { METHOD: `${this.constructor.name}@${this.acs.name}`},
                      MESSAGE: 'SAML Request - User profile display name has been updated.',
                      REQUEST: { gid: group?.gid, email: email, display_name: fieldValue },
                    });
                  }
                };
              }
            }
        }
      /* Mapping profile attributes - END */

      /* Auto register as B2B - START*/
      if (autoRegistrationB2b === 'Y') {
        const roles = user.roles.map((item) => item.roleName);
        roles.push('looper_affiliate_student');
      // It internally is scyning role with sentinel_users_role
        await this.handleAutoRegistrationRoles(userRepository, user, group?.gid, roles);
      }
      /* Auto register as B2B - ENDS*/
      const enterpriseService = await this.helperService.get<EnterpriseService>(EnterpriseService);
      const enterpriseResponse = await enterpriseService.getGroupDomainByGid({gid : group?.gid});
      let redirectUrl = enterpriseResponse?.data[0]?.lmsSiteUrl;

      /**
       * Update Role
       */
      // Update the role -> sending to kafka

      if (updateRoleSetting === 'Y') {
        skipGroupMemberCheck = true;

        const profileGroup = ssoAttributes?.ProfileGroup;
        const dataComplianceAdmin = ssoAttributes?.DataComplianceAdmin === 'true';
        const businessOperations = ssoAttributes?.BusinessOperations === 'true';

        if (!profileGroup || !clientId) {
          throw new BadRequestException('SAML Request - No roles assigned to user.');
        }

        const sfConfigRoles = this.configService.get('slSalesforceRoles');
        let userClientRoles = [
          ...(profileGroup && profileGroup !== 'Non Sales' ? [sfConfigRoles[profileGroup]] : []),
          ...(dataComplianceAdmin ? [sfConfigRoles['DataComplianceAdmin']] : []),
          ...(businessOperations ? [sfConfigRoles['BusinessOperations']] : []),
        ].filter((role) => role !== undefined);
        const notSfRoles = user?.roles.filter((item) => !Object.values(sfConfigRoles).includes(item.roleName));
        userClientRoles = [...userClientRoles, ...notSfRoles.map((item) => item.roleName)];

        if (dataComplianceAdmin) {
          redirectUrl = `${this.configService.get('ic9SimplexEndPoint')}${this.configService.get('gdprUrl')}`;
        }
        await userHelper.clearCloud6SentinelUserRole(user?.uid);
        // It internally is scyning role with sentinel_users_role
        await this.handleAutoRegistrationRoles(userRepository, user, group?.gid, userClientRoles);

        // kafkaPayload['userRoles'] = userClientRoles;
      }
      // refresing user with updated value after all role updates
      user = await userRepository.getUserByEmail(email);

      // Is this course catalogue URL 
      // Get group domain url and course catalog data
      user = await this.courseCatalog(user, relayState, group?.gid);

      // const existingCourseCatalog = user['course_catalog'] || {};
      // kafkaPayload['course_catalog'] = {
      //   ...(existingCourseCatalog && { course_catalog: existingCourseCatalog?.course_url }),
      // };

      // Redirect URL
      const handleRedirecttUrlResponse = this.handleRedirectUrl(
        loginAsB2c,
        loginAsB2b,
        relayState,
        redirectUrl,
        groupSettingRelayState,
      );

      skipGroupMemberCheck = handleRedirecttUrlResponse.skipGroupMemberCheck;
      // Update the original redirectUrl with the new value from handleRedirecttUrlResponse
      redirectUrl = handleRedirecttUrlResponse.redirectUrl;

      // Redirect URL
      // Login as b2c learner 
      if (!skipGroupMemberCheck) {
        user['lgid'] = group?.gid;
      }

      // Setting cookie value for B2B Self serve portal
      if (b2bSelfServePortal === 'Y') {
        // add the user payload for  cookie or token
        user['gid'] = group?.gid;
        user['lms_url'] = redirectUrl;
        user['is_b2b_sso'] = 'Y';
        if (isCoursePurchase === 'Y') {
          user['checkCoursePurchase'] = 'Y';
        }
      }
        // TODO : check the below workflow
        // Setting cookie value for futurex userid
        if (requestParams.titleValue.toLowerCase() === this.configService.get('futurex_title').toLowerCase()) {
          user['futurex_data'] = ssoAttributes?.uid || null;
        }
        if (this.configService.get('b2b_vendor_title')?.includes(requestParams.title)) {
          user['vendor_data'] = {
            vendor_uid: ssoAttributes?.uid || null
          };
        }

      // Create custom token -> sentinel for cookie

      const clientSecret = this.configService.get('clientSecret');

      // Get the learner data for session
      user = await this.generateCustomToken(email, customToken, clientId, clientSecret, authTokenHelper, user);
      const tokenPayloadData = await authTokenHelper.generateSessionTokens(user, clientKey, {
        algorithm: this.configService.get('hashAlgo'),
        secret: this.configService.get('JWTKEY'),
      });

      // update user timezone
      // It is internally updating to sentinel_users
      userMgmtUtilityHelper.updateUserTimezone({ uid: user?.uid, country: user?.country_code });

      if (!skipGroupMemberCheck && false === user?.user_groups?.map(Number).includes(Number(group?.gid))) {
        //redirecting to error page
        throw new BadRequestException('SAML Request - Learner is not member of requested group.');
      }

      /**
      * Deleting the existing cookies.
      */
      // delete existing  cookie

      const ssoCookie = this.configService.get('ssoCookie');
      if (req?.cookies[ssoCookie]) {
        await cookieHelper.clearCookie(res, ssoCookie);
        await cookieHelper.setCookie(res, ssoCookie, null, { expires: 3600 * -1 });
      }
      const ssoVerifiedUserCookieName = this.configService.get('ssoVerifiedUserCookie');
      if (req?.cookies[ssoVerifiedUserCookieName]) {
        await cookieHelper.clearCookie(res, ssoVerifiedUserCookieName);
        await cookieHelper.setCookie(res, ssoVerifiedUserCookieName, null, { expires: 3600 * -1 });
      }

      // set the cookie for user
      if (responseType === 'return') {
        await cookieHelper.setCookie(res, this.configService.get('ssoCookie'), tokenPayloadData?.idToken);
      }

      //  Update user the login time stamp
      // It is internally updating to sentinel_users as well
      await userHelper.updateUserLoginTime(user);

      //LRS logging via scheduler - START
      const dataValsForLrs = {
        clientId: clientKey,
        redirect_url: redirectUrl,
      };
      const lrsData = {
        verb: 'login',
        objectType: 'saml-service-provider',
        objectId: user?.uid,
        dataVals: dataValsForLrs,
      };
      const lrsInstance = await this.helperService.getHelper('lrsHelper');
      lrsInstance.sendDataToLrs(user, lrsData);
      //LRS logging via scheduler - END

      if (responseType === 'json') {
        const redirect_url_data = this.#isMobile ? `/saml/service-provider/mobile/success/email/${email}` : redirectUrl;
        return {
          status: 'success',
          msg: 'ACS request is success',
          data: {
            redirect_url: redirect_url_data,
            is_mobile: this.#isMobile,
            auth_token: tokenPayloadData?.idToken,
          },
        };
      } else if (responseType === 'return') {
        if (this.#isMobile) {
          return res.redirect(`/saml/service-provider/mobile/success/email/${email}`);
        }
        return res.redirect(redirectUrl);
      }

      /* XAPI request save - START*/
      // if(xapiCall == 'Y' && relayState){
      //     await this.saveXapiData(user?.uid , group?.gid , relayState)
      // }
      /* XAPI request save - END*/
    } catch (error: any) {
      Logger.error('acs action', {
        Trace: error,
        errorRedirect: this.#errorRedirect,
      });

      if (responseType === 'json') {
        return {
          status: 'failed',
          msg: 'SAML Request - Some error occurred.',
          data: { redirect_url: this.#errorRedirect },
        };
      } else if (responseType === 'return') {
        return res.redirect(this.#errorRedirect);
      }
    }
  }

  private async generateCustomToken(
    email: string,
    customToken: string,
    clientId: string,
    clientSecret: string,
    authTokenHelper: AuthTokenHelper,
    user: any,
  ) {
    if (customToken === 'Y') {
      const tokenPayloadData = {
        email: email,
      };
      const secretKey = clientSecret[clientId];
      const tokenResponse = await authTokenHelper.getSignHash(JSON.stringify(tokenPayloadData), secretKey?.secret);
      user['sfSsoUserAuthToken'] = { client_id: clientId, token: tokenResponse };
    }
    return user;
  }
  mobileUserAgent(userAgent: string): { isMobile: boolean; isIos: boolean; isAndroid: boolean } {
    const appAndroidRegex = new RegExp(`\\b${this.#appAndroid}\\b`);
    const appIOSRegex = new RegExp(`\\b${this.#appIOS}\\b`);
    return {
      isMobile: appAndroidRegex.test(userAgent) || appIOSRegex.test(userAgent),
      isIos: appIOSRegex.test(userAgent),
      isAndroid: appAndroidRegex.test(userAgent),
    };
  }

  private async getSamlSettings(group: any) {
    const team_id = 0;
    const samlSetting = await this.getGroupsSettingByTitle(
      group?.gid,
      team_id,
      this.configService.get('saml')?.samlAppKeySet,
    );

    return {
      autoRegistrationB2c: this.getGroupsSettingFilter(samlSetting, 'auto_register_as_b2c')?.value,
      b2bSelfServePortal: this.getGroupsSettingFilter(samlSetting, 'b2b_self_serve_portal')?.value,
      isCoursePurchase: this.getGroupsSettingFilter(samlSetting, 'check_course_purchase')?.value,
      autoRegistrationB2b: this.getGroupsSettingFilter(samlSetting, 'auto_register_as_b2b')?.value,
      loginAsB2c: this.getGroupsSettingFilter(samlSetting, 'login_as_b2c')?.value,
      loginAsB2b: this.getGroupsSettingFilter(samlSetting, 'login_as_b2b')?.value,
      xapiCall: this.getGroupsSettingFilter(samlSetting, 'xapi_call')?.value,
      groupSettingRelayState: this.getGroupsSettingFilter(samlSetting, 'relay_state')?.value,
      updateRoleSetting: this.getGroupsSettingFilter(samlSetting, 'update_role')?.value,
      clientId: this.getGroupsSettingFilter(samlSetting, 'client_id')?.value,
      customToken: this.getGroupsSettingFilter(samlSetting, 'create_custom_token')?.value,
      attributes: this.getGroupsSettingFilter(samlSetting, 'attributes')?.value, 
    };
  }

  private async handleSamlUserRegistration(
    userRepository,
    email: string,
    clientKey: string,
    autoRegistrationB2c: string,
    b2bSelfServePortal: string,
    isCoursePurchase: string,
    gid: string
  ) {
    if (autoRegistrationB2c !== 'Y') {
      throw new BadRequestException('SAML Request - Learner account not found.');
    }
    const userParams = {
      client_id: clientKey,
      user_email: email,
      user_roles: [],
      send_email: 'N',
      sso_request: 'true',
      overwrite: 0,
      auto_provision_request: true,
      gid
    };
    
    if (b2bSelfServePortal === 'Y' && isCoursePurchase === 'N') {
      userParams['b2b_sso_first_login'] = true;
    }

    const userHelper = await this.helperService.getHelper('UserHelper');
    const registerResponse = await userHelper.registerByEmail(userParams);
    if (registerResponse?.type !== 'success') {
      throw new BadRequestException('SAML Request - Failed creating learner account.');
    }
    return await userRepository.findByUID(Number(registerResponse?.user?.id));
  }

  private async handleAutoRegistrationRoles(userRepository: any, user: any, groupId: number, roles: string[] = []) {
    const [userMgmtHelper, userHelper] = await Promise.all([
      this.helperService.getHelper('UserMgmtUtilityHelper'),
      this.helperService.getHelper('UserHelper'),
    ]);
    const [assignRoles, assignRolesMysql] = await Promise.all([
      userMgmtHelper.prepareAssignRoleList(roles, null, true),
      userMgmtHelper.prepareSentinelUserRoleList(roles , user?.uid),
    ]);
    if (assignRoles.length === 0) {
      throw new BadRequestException('SAML Request - Please provide valid user role name.');
    }
    const userGroupsUpdate = { $addToSet: { user_groups: groupId } };
    const rolesUpdate = { $set: { roles: assignRoles } };
    await Promise.all([
      userRepository.findOneAndUpdate({ uid: user?.uid }, { ...userGroupsUpdate, ...rolesUpdate }), 
      userHelper.syncCloud6SentinelUserRole(assignRolesMysql),
    ]);
  }

  private async courseCatalog(user, relayState: string, groupId: number) {
    if (Utility.isThisCourseCatalogUrl(relayState)) {
      user['course_catalog'] = {
        gid: groupId,
        uid: user?.uid,
        email: user?.email,
        course_url: relayState,
      };
    }
    return user;
  }

  private handleRedirectUrl(
    loginAsB2c: string,
    loginAsB2b: string,
    relayState: string,
    redirectUrl: string,
    groupSettingRelayState: string,
  ): { skipGroupMemberCheck: boolean; redirectUrl: string } {
    let skipGroupMemberCheck = false;

    if (loginAsB2c === 'Y') {
      skipGroupMemberCheck = true;
      redirectUrl = relayState || this.configService.get('ic9SiteUrl');
    } else if (loginAsB2b === 'Y') {
      skipGroupMemberCheck = true;
      redirectUrl = relayState || redirectUrl;
    } else if (groupSettingRelayState) {
      skipGroupMemberCheck = true;
      redirectUrl = groupSettingRelayState;
    }
    return { skipGroupMemberCheck, redirectUrl };
  }

  async mobileSuccess(email: string, userAgent: string) {
    try {
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user = await userRepository.getUserByEmail(email);

      const [paperclipService, enterpriseService] = await Promise.all([
        this.helperService.get<PaperclipService>(PaperclipService),
        this.helperService.get<EnterpriseService>(EnterpriseService),
      ]);

      const tokenResponse = await paperclipService.generateUserTokenData({
        userId: user?.uid,
        userEmail: user?.email,
        userName: user?.name,
      });

      const responseData = {
        userId: user?.uid,
        enrolledCourse: 1,
        countryId: 34,
        countryName: 'United States',
        userName: user?.name,
        displayName: user?.display_name,
        msg: '',
      };

      const response = await enterpriseService.getAffiliatesByUid({ uid: user?.uid });

      if (response?.data?.status === 'success' && response.data) {
        const domain = response.data?.data.find(
          (domain) =>
            domain.affiliateId && parseInt(domain.affiliateId) !== this.configService.get('simplilearnGroupIdForApp'),
        );

        if (domain) {
          responseData['affiliateLogoUrl'] = domain.affiliateLogoUrl;
          responseData['isAffiliateUser'] = 1;
          responseData['affiliateCoursePageMessage'] =
            'If you are interested in this course, please contact your manager.';
          responseData['affiliateId'] = parseInt(domain.affiliateId);
        }
      }
      const mobile = this.mobileUserAgent(userAgent);
      if (tokenResponse.status === 'success') {
        responseData['serverAccessKey'] = tokenResponse.token;
        return { data: responseData, status: 200, statusCode: 200, mobile };
      } else {
        return { error: { msg: 'Token Error' }, code: 400, status: -1, mobile };
      }
    } catch (error: any) {
      // Handle the error appropriately
      Logger.error('mobileSuccess', {
        Trace: error,
      });
    }
  }
}
