import { Test, TestingModule } from '@nestjs/testing';
import { SamlService } from './saml.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import { GroupSettings } from '../../db/mysql/entity/group-settings.entity';
import { SamlLogs } from '../../db/mysql/entity/saml-logs.entity';
import { CustomFieldLabelMaster } from '../../db/mysql/entity/custom_field_label_master.entity';
import { CustomFieldUserValue } from '../../db/mysql/entity/custom_field_user_value.entity';

import { Logger } from '../../logging/logger';
import { SAML } from '@node-saml/passport-saml';
import { UserRepository } from '../../user/repositories/user/user.repository';
import { AuthTokenHelper } from '../../auth/helper/auth.tokenhelper';
import { FirebaseService } from '../../db/firebase/firebase.service';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';

// Mock dependencies
jest.mock('@node-saml/passport-saml');
jest.mock('../../logging/logger');

describe('SamlService', () => {
  let service: SamlService;
  let helperService: HelperService;

  const mockGroupSettingRepository = {
    findOne: jest.fn(),
    find: jest.fn(),
  };

  const mockSamlLogsRepository = {
    save: jest.fn(),
    update: jest.fn(),
  };

  const mockCustomFieldLabelRepository = {
    find: jest.fn(),
  };

  const mockCustomFieldUserValueRepository = {
    findOne: jest.fn(),
    update: jest.fn(),
    save: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config = {
        saml: {
          samlAppKeySet: 'saml_app_key',
          callbackUrl: 'http://localhost:3000/saml/callback/',
          logoutCallbackUrl: 'http://localhost:3000/saml/logout/',
        },
        issuer: 'http://localhost:3000',
        clientKey: 'testClientKey',
        futurex_title: 'futurex',
        b2b_vendor_title: ['vendor1'],
        clientSecret: { testClientId: { secret: 'testSecret' } },
        ssoCookie: 'sso_cookie',
        ssoVerifiedUserCookie: 'sso_verified_user_cookie',
        hashAlgo: 'sha256',
        JWTKEY: 'jwtkey',
        ic9SimplexEndPoint: 'http://simplex.com',
        gdprUrl: '/gdpr',
        slSalesforceRoles: {
            'ProfileGroup1': 'Role1',
            'DataComplianceAdmin': 'AdminRole',
            'BusinessOperations': 'BizOpRole'
        },
        ic9SiteUrl: 'http://ic9.com',
        simplilearnGroupIdForApp: 123
      };
      return config[key] || key;
    }),
  };

  const mockUserRepository = {
    getUserByEmail: jest.fn(),
    findOneAndUpdate: jest.fn(),
    findByUID: jest.fn(),
  };

  const mockUserHelper = {
    registerByEmail: jest.fn(),
    updateCloud6SentinelByUidOrMail: jest.fn(),
    clearCloud6SentinelUserRole: jest.fn(),
    syncCloud6SentinelUserRole: jest.fn(),
    updateUserLoginTime: jest.fn(),
  };

  const mockUserMgmtUtilityHelper = {
    updateUserTimezone: jest.fn(),
    prepareAssignRoleList: jest.fn(),
    prepareSentinelUserRoleList: jest.fn(),
  };

  const mockAuthTokenHelper = {
    generateSessionTokens: jest.fn(),
    getSignHash: jest.fn(),
  };

  const mockCookieHelper = {
    clearCookie: jest.fn(),
    setCookie: jest.fn(),
  };

  const mockLrsHelper = {
    sendDataToLrs: jest.fn(),
  };
  
  const mockFirebaseService = {
    getDB: jest.fn(),
    createDb: jest.fn(),
  };

  const mockEnterpriseService = {
      getGroupDomainByGid: jest.fn(),
      getAffiliatesByUid: jest.fn(),
  };

  const mockPaperclipService = {
      generateUserTokenData: jest.fn(),
  };

  const mockHelperService = {
    get: jest.fn().mockImplementation((name) => {
      if (name === UserRepository) return Promise.resolve(mockUserRepository);
      if (name === AuthTokenHelper) return Promise.resolve(mockAuthTokenHelper);
      if (name === FirebaseService) return Promise.resolve(mockFirebaseService);
      if (name === EnterpriseService) return Promise.resolve(mockEnterpriseService);
      if (name === PaperclipService) return Promise.resolve(mockPaperclipService);
      return Promise.resolve({});
    }),
    getHelper: jest.fn().mockImplementation((name) => {
        if (name === 'UserHelper') return Promise.resolve(mockUserHelper);
        if (name === 'UserMgmtUtilityHelper') return Promise.resolve(mockUserMgmtUtilityHelper);
        if (name === 'CookieHelper') return Promise.resolve(mockCookieHelper);
        if (name === 'lrsHelper') return Promise.resolve(mockLrsHelper);
        return Promise.resolve({});
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SamlService,
        { provide: HelperService, useValue: mockHelperService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: getRepositoryToken(GroupSettings), useValue: mockGroupSettingRepository },
        { provide: getRepositoryToken(SamlLogs), useValue: mockSamlLogsRepository },
        { provide: getRepositoryToken(CustomFieldLabelMaster), useValue: mockCustomFieldLabelRepository },
        { provide: getRepositoryToken(CustomFieldUserValue), useValue: mockCustomFieldUserValueRepository },
      ],
    }).compile();

    service = module.get<SamlService>(SamlService);
    helperService = module.get<HelperService>(HelperService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getConfiguration', () => {
    it('should return SAML configuration and gid for a given title', async () => {
      const title = 'test-title';
      const group = { gid: 1, identifier: 'title', value: title };
      const samlSettings = [
        { identifier: 'idp_metadata', value: JSON.stringify({ idp: { sso_url: 'sso_url', entity_id: 'entity_id' } }) },
        { identifier: 'certificate', value: 'cert_value' },
      ];
      mockGroupSettingRepository.findOne.mockResolvedValue(group);
      mockGroupSettingRepository.find.mockResolvedValue(samlSettings);

      const result = await service.getConfiguration(title);

      expect(result).toBeDefined();
      expect(result.gid).toBe(group.gid);
      expect(result.saml.entryPoint).toBe('sso_url');
      expect(mockGroupSettingRepository.findOne).toHaveBeenCalledWith({ where: { identifier: 'title', value: title } });
      expect(mockGroupSettingRepository.find).toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
        mockGroupSettingRepository.findOne.mockRejectedValue(new Error('DB error'));
        const result = await service.getConfiguration('test');
        expect(result).toBeUndefined();
        expect(Logger.log).toHaveBeenCalledWith('te', expect.any(Error));
    });
  });

  describe('generateServiceProviderMetadata', () => {
    it('should generate service provider metadata', async () => {
      const title = 'test-title';
      const config = {
        saml: { path: 'path', callbackUrl: 'callback', entryPoint: 'entry', issuer: 'issuer', logoutCallbackUrl: 'logout_callback', logoutUrl: 'logout_url', idpIssuer: 'idp_issuer', idpCert: 'cert' },
        gid: 1,
        idpCert: 'cert'
      };
      jest.spyOn(service, 'getConfiguration').mockResolvedValue(config as any);
      const mockSamlInstance = {
        generateServiceProviderMetadata: jest.fn().mockReturnValue('metadata'),
      };
      (SAML as jest.Mock).mockImplementation(() => mockSamlInstance);

      const metadata = await service.generateServiceProviderMetadata(title);

      expect(metadata).toBe('metadata');
      expect(service.getConfiguration).toHaveBeenCalledWith(title);
      expect(SAML).toHaveBeenCalledWith(config.saml);
      expect(mockSamlInstance.generateServiceProviderMetadata).toHaveBeenCalledWith('cert', null);
    });
  });

  describe('saveSamlLogs', () => {
    it('should save SAML logs', async () => {
      const req = { query: { test: 'query' }, body: { test: 'body' } };
      const data = { gid: 1, title: 'test-title' };
      const savedLog = { id: 1, ...data };
      mockSamlLogsRepository.save.mockResolvedValue(savedLog);

      const result = await service.saveSamlLogs(req as any, data);

      expect(result).toEqual(savedLog);
      expect(mockSamlLogsRepository.save).toHaveBeenCalled();
    });
    
    it('should log if saving fails', async () => {
        const req = { query: { test: 'query' }, body: { test: 'body' } };
        const data = { gid: 1, title: 'test-title' };
        mockSamlLogsRepository.save.mockResolvedValue(null);
        await service.saveSamlLogs(req as any, data);
        expect(Logger.log).toHaveBeenCalledWith('saml logs failed');
    });

    it('should handle exceptions during save', async () => {
        const req = { query: { test: 'query' }, body: { test: 'body' } };
        const data = { gid: 1, title: 'test-title' };
        mockSamlLogsRepository.save.mockRejectedValue(new Error('DB error'));
        await service.saveSamlLogs(req as any, data);
        expect(Logger.log).toHaveBeenCalledWith('saml logs failed', expect.any(Error));
    });
  });

  describe('updateSamlLogs', () => {
    it('should update SAML logs', async () => {
      const gid = 1;
      const data = { email: '<EMAIL>' };
      mockSamlLogsRepository.update.mockResolvedValue({ affected: 1 } as any);

      const result = await service.updateSamlLogs(gid, data);

      expect(result).toBeDefined();
      expect(mockSamlLogsRepository.update).toHaveBeenCalledWith(gid, data);
    });
    
    it('should handle exceptions during update', async () => {
        const gid = 1;
        const data = { email: '<EMAIL>' };
        mockSamlLogsRepository.update.mockRejectedValue(new Error('DB error'));
        await service.updateSamlLogs(gid, data);
        expect(Logger.log).toHaveBeenCalledWith('saml logs failed', expect.any(Error));
    });
  });

  describe('saveSsoAttributes', () => {
    it('should save new SSO attributes', async () => {
        const gid = 1;
        const email = '<EMAIL>';
        const ssoAttributes = { 'Field1': 'Value1' };
        mockCustomFieldLabelRepository.find.mockResolvedValue([{ custom_field_label_id: 101, display_name: 'Field1' }] as any);
        mockCustomFieldUserValueRepository.findOne.mockResolvedValue(null); // No existing record
        mockCustomFieldUserValueRepository.save.mockResolvedValue({} as any);

        const result = await service.saveSsoAttributes(gid, email, ssoAttributes);

        expect(result).toBe(true);
        expect(mockCustomFieldUserValueRepository.save).toHaveBeenCalled();
        expect(mockCustomFieldUserValueRepository.update).not.toHaveBeenCalled();
    });

    it('should update existing SSO attributes', async () => {
        const gid = 1;
        const email = '<EMAIL>';
        const ssoAttributes = { 'Field1': 'NewValue1' };
        mockCustomFieldLabelRepository.find.mockResolvedValue([{ custom_field_label_id: 101, display_name: 'Field1' }] as any);
        mockCustomFieldUserValueRepository.findOne.mockResolvedValue({ id: 1 }); // Existing record
        mockCustomFieldUserValueRepository.update.mockResolvedValue({} as any);

        const result = await service.saveSsoAttributes(gid, email, ssoAttributes);

        expect(result).toBe(true);
        expect(mockCustomFieldUserValueRepository.update).toHaveBeenCalled();
        expect(mockCustomFieldUserValueRepository.save).not.toHaveBeenCalled();
    });
    
    it('should fetch ssoAttributes from user if not provided', async () => {
        const gid = 1;
        const email = '<EMAIL>';
        const ssoAttributes = { 'Field1': 'Value1' };
        (helperService.get as jest.Mock).mockResolvedValue(mockUserRepository);
        mockUserRepository.getUserByEmail.mockResolvedValue({ sso_attributes: JSON.stringify(ssoAttributes) });
        mockCustomFieldLabelRepository.find.mockResolvedValue([{ custom_field_label_id: 101, display_name: 'Field1' }] as any);
        mockCustomFieldUserValueRepository.findOne.mockResolvedValue(null);
        mockCustomFieldUserValueRepository.save.mockResolvedValue({} as any);

        const result = await service.saveSsoAttributes(gid, email, null);

        expect(result).toBe(true);
        expect(mockUserRepository.getUserByEmail).toHaveBeenCalledWith(email);
    });

    it('should return false if any operation fails', async () => {
        const gid = 1;
        const email = '<EMAIL>';
        const ssoAttributes = { 'Field1': 'Value1' };
        mockCustomFieldLabelRepository.find.mockResolvedValue([{ custom_field_label_id: 101, display_name: 'Field1' }] as any);
        mockCustomFieldUserValueRepository.findOne.mockResolvedValue(null);
        mockCustomFieldUserValueRepository.save.mockRejectedValue(new Error('DB error'));

        const result = await service.saveSsoAttributes(gid, email, ssoAttributes);

        expect(result).toBe(false);
    });
    
    it('should handle general errors and return false', async () => {
        mockCustomFieldLabelRepository.find.mockRejectedValue(new Error('DB error'));
        const result = await service.saveSsoAttributes(1, '<EMAIL>', {});
        expect(result).toBe(false);
        expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('acs', () => {
    let req, res;

    beforeEach(() => {
        req = {
            params: { titleValue: 'test-title' },
            body: { RelayState: 'relay-state' },
            user: { nameID: '<EMAIL>', attributes: { uid: 'user123' } },
            get: jest.fn().mockReturnValue('user-agent'),
            logId: 123,
            cookies: {},
        };
        res = {
            redirect: jest.fn(),
        };
        
        (helperService.get as jest.Mock).mockImplementation((name) => {
            if (name === UserRepository) return Promise.resolve(mockUserRepository);
            if (name === AuthTokenHelper) return Promise.resolve(mockAuthTokenHelper);
            if (name === FirebaseService) return Promise.resolve(mockFirebaseService);
            if (name === EnterpriseService) return Promise.resolve(mockEnterpriseService);
            if (name === PaperclipService) return Promise.resolve(mockPaperclipService);
            return Promise.resolve({});
        });
        (helperService.getHelper as jest.Mock).mockImplementation(async (name) => {
            if (name === 'UserHelper') return mockUserHelper;
            if (name === 'UserMgmtUtilityHelper') return mockUserMgmtUtilityHelper;
            if (name === 'CookieHelper') return mockCookieHelper;
            if (name === 'lrsHelper') return mockLrsHelper;
            return {};
        });

        mockGroupSettingRepository.findOne.mockResolvedValue({ gid: 1, identifier: 'title', value: 'test-title' });
        mockGroupSettingRepository.find.mockResolvedValue([]);
        mockUserRepository.getUserByEmail.mockResolvedValue({
            uid: '123',
            email: '<EMAIL>',
            status: 1,
            user_groups: [1],
            roles: [],
        });
        mockEnterpriseService.getGroupDomainByGid.mockResolvedValue({ data: [{ lmsSiteUrl: 'http://lms.com' }] });
        mockAuthTokenHelper.generateSessionTokens.mockResolvedValue({ idToken: 'id-token' });
    });

    it('should handle missing saml property in generateServiceProviderMetadata', async () => {
        const title = 'test-title';
        const config = {
            gid: 1,
            idpCert: 'cert'
        };
        jest.spyOn(service, 'getConfiguration').mockResolvedValue(config as any);
        const mockSamlInstance = {
            generateServiceProviderMetadata: jest.fn().mockReturnValue('metadata'),
        };
        (SAML as jest.Mock).mockImplementation(() => mockSamlInstance);

        const metadata = await service.generateServiceProviderMetadata(title);

        expect(metadata).toBe('metadata');
    });

    it('should handle exceptions in mobileSuccess', async () => {
        const email = '<EMAIL>';
        const userAgent = 'SimplilearnAppAndroid';
        mockUserRepository.getUserByEmail.mockRejectedValue(new Error('DB error'));

        const result = await service.mobileSuccess(email, userAgent);

        expect(result).toBeUndefined();
    });

    it('should handle missing affiliate data property', async () => {
        const email = '<EMAIL>';
        const userAgent = 'SimplilearnAppAndroid';
        const user = { uid: '123', email: '<EMAIL>', name: 'Test User', display_name: 'Test D. User' };
        mockUserRepository.getUserByEmail.mockResolvedValue(user);
        mockPaperclipService.generateUserTokenData.mockResolvedValue({ status: 'success', token: 'access-token' });
        mockEnterpriseService.getAffiliatesByUid.mockResolvedValue({} as any);

        const result = await service.mobileSuccess(email, userAgent);

        expect(result.status).toBe(200);
        expect((result.data as any).isAffiliateUser).toBeUndefined();
    });

    it('should handle empty affiliate data', async () => {
        const email = '<EMAIL>';
        const userAgent = 'SimplilearnAppAndroid';
        const user = { uid: '123', email: '<EMAIL>', name: 'Test User', display_name: 'Test D. User' };
        mockUserRepository.getUserByEmail.mockResolvedValue(user);
        mockPaperclipService.generateUserTokenData.mockResolvedValue({ status: 'success', token: 'access-token' });
        mockEnterpriseService.getAffiliatesByUid.mockResolvedValue({ data: { status: 'success', data: [] } });

        const result = await service.mobileSuccess(email, userAgent);

        expect(result.status).toBe(200);
        expect((result.data as any).isAffiliateUser).toBeUndefined();
    });

    it('should handle non-mobile user agent', async () => {
        const email = '<EMAIL>';
        const userAgent = 'SomeUserAgent';
        const user = { uid: '123', email: '<EMAIL>', name: 'Test User', display_name: 'Test D. User' };
        mockUserRepository.getUserByEmail.mockResolvedValue(user);
        mockPaperclipService.generateUserTokenData.mockResolvedValue({ status: 'success', token: 'access-token' });
        mockEnterpriseService.getAffiliatesByUid.mockResolvedValue({ data: { status: 'success', data: [] } });

        const result = await service.mobileSuccess(email, userAgent);

        expect(result.mobile.isMobile).toBe(false);
    });

    it('should handle affiliate domain with same group id', async () => {
        const email = '<EMAIL>';
        const userAgent = 'SimplilearnAppAndroid';
        const user = { uid: '123', email: '<EMAIL>', name: 'Test User', display_name: 'Test D. User' };
        mockUserRepository.getUserByEmail.mockResolvedValue(user);
        mockPaperclipService.generateUserTokenData.mockResolvedValue({ status: 'success', token: 'access-token' });
        mockEnterpriseService.getAffiliatesByUid.mockResolvedValue({ data: { status: 'success', data: [{ affiliateId: '123', affiliateLogoUrl: 'logo.png' }] } });

        const result = await service.mobileSuccess(email, userAgent);

        expect(result.status).toBe(200);
        expect((result.data as any).isAffiliateUser).toBeUndefined();
    });

    it('should handle token generation failure in mobileSuccess', async () => {
        const email = '<EMAIL>';
        const userAgent = 'SimplilearnAppAndroid';
        const user = { uid: '123', email: '<EMAIL>', name: 'Test User', display_name: 'Test D. User' };
        mockUserRepository.getUserByEmail.mockResolvedValue(user);
        mockPaperclipService.generateUserTokenData.mockResolvedValue({ status: 'error' });
        mockEnterpriseService.getAffiliatesByUid.mockResolvedValue({ data: { status: 'success', data: [] } });

        const result = await service.mobileSuccess(email, userAgent);

        expect(result.status).toBe(-1);
    });

    it('should handle affiliate domain', async () => {
        const email = '<EMAIL>';
        const userAgent = 'SimplilearnAppAndroid';
        const user = { uid: '123', email: '<EMAIL>', name: 'Test User', display_name: 'Test D. User' };
        mockUserRepository.getUserByEmail.mockResolvedValue(user);
        mockPaperclipService.generateUserTokenData.mockResolvedValue({ status: 'success', token: 'access-token' });
        mockEnterpriseService.getAffiliatesByUid.mockResolvedValue({ data: { status: 'success', data: [{ affiliateId: '456', affiliateLogoUrl: 'logo.png' }] } });

        const result = await service.mobileSuccess(email, userAgent);

        expect(result.status).toBe(200);
        expect((result.data as any).isAffiliateUser).toBe(1);
    });

    it('should handle enterprise service failure', async () => {
        const email = '<EMAIL>';
        const userAgent = 'SimplilearnAppAndroid';
        const user = { uid: '123', email: '<EMAIL>', name: 'Test User', display_name: 'Test D. User' };
        mockUserRepository.getUserByEmail.mockResolvedValue(user);
        mockPaperclipService.generateUserTokenData.mockResolvedValue({ status: 'success', token: 'access-token' });
        mockEnterpriseService.getAffiliatesByUid.mockResolvedValue({ data: { status: 'failed' } });

        const result = await service.mobileSuccess(email, userAgent);

        expect(result.status).toBe(200);
        expect((result.data as any).isAffiliateUser).toBeUndefined();
    });

    it('should handle mobile redirect', async () => {
        req.get.mockReturnValue('SimplilearnAppIOS'); // Mobile user agent
        await service.acs(req, res, 'return');
        expect(res.redirect).toHaveBeenCalledWith('/saml/service-provider/mobile/success/email/<EMAIL>');
    });

    it('should handle customToken', async () => {
        mockGroupSettingRepository.find.mockResolvedValue([
            { identifier: 'create_custom_token', value: 'Y' },
            { identifier: 'client_id', value: 'testClientId' }
        ]);
        await service.acs(req, res, 'return');
        expect(mockAuthTokenHelper.getSignHash).toHaveBeenCalled();
    });

    it('should handle dataComplianceAdmin redirect', async () => {
        req.user.attributes = {
            ProfileGroup: 'ProfileGroup1',
            DataComplianceAdmin: 'true',
            BusinessOperations: 'false'
        };
        mockGroupSettingRepository.find.mockResolvedValue([
            { identifier: 'update_role', value: 'Y' },
            { identifier: 'client_id', value: 'testClientId' }
        ]);
        mockUserMgmtUtilityHelper.prepareAssignRoleList.mockResolvedValue([{ roleName: 'Role1' }, { roleName: 'AdminRole' }]);
        mockUserMgmtUtilityHelper.prepareSentinelUserRoleList.mockResolvedValue([{}]);
        
        await service.acs(req, res, 'return');

        expect(res.redirect).toHaveBeenCalledWith('http://simplex.com/gdpr');
    });

    it('should handle b2bSelfServePortal and isCoursePurchase', async () => {
        mockUserRepository.getUserByEmail.mockResolvedValueOnce(null); // User not found
        mockUserRepository.getUserByEmail.mockResolvedValueOnce({ uid: '123', email: '<EMAIL>', status: 1, user_groups: [1], roles: [] }); // User found
        mockGroupSettingRepository.find.mockResolvedValue([
            { identifier: 'auto_register_as_b2c', value: 'Y' },
            { identifier: 'b2b_self_serve_portal', value: 'Y' },
            { identifier: 'check_course_purchase', value: 'Y' },
        ]);
        mockUserHelper.registerByEmail.mockResolvedValue({ type: 'success', user: { id: '123' } });
        mockUserRepository.findByUID.mockResolvedValue({ uid: '123', email: '<EMAIL>', status: 1, user_groups: [1], roles: [] });
        mockFirebaseService.getDB.mockResolvedValue({} as any);
        mockFirebaseService.createDb.mockResolvedValue(true);

        await service.acs(req, res, 'return');
        
        expect(mockUserHelper.registerByEmail).toHaveBeenCalled();
        expect(res.redirect).toHaveBeenCalledWith('http://lms.com');
    });

    it('should handle groupSettingRelayState redirect', async () => {
        mockGroupSettingRepository.find.mockResolvedValue([
            { identifier: 'relay_state', value: 'group-relay-state' },
        ]);
        await service.acs(req, res, 'return');
        expect(res.redirect).toHaveBeenCalledWith('group-relay-state');
    });

    it('should handle loginAsB2b redirect', async () => {
        mockGroupSettingRepository.find.mockResolvedValue([
            { identifier: 'login_as_b2b', value: 'Y' },
        ]);
        await service.acs(req, res, 'return');
        expect(res.redirect).toHaveBeenCalledWith('relay-state');
    });

    it('should handle loginAsB2c redirect', async () => {
        mockGroupSettingRepository.find.mockResolvedValue([
            { identifier: 'login_as_b2c', value: 'Y' },
        ]);
        await service.acs(req, res, 'return');
        expect(res.redirect).toHaveBeenCalledWith('relay-state');
    });

    it('should handle a standard successful login', async () => {
        mockGroupSettingRepository.find.mockResolvedValue([
            { identifier: 'auto_register_as_b2c', value: 'N' },
            { identifier: 'b2b_self_serve_portal', value: 'N' },
            { identifier: 'check_course_purchase', value: 'N' },
            { identifier: 'auto_register_as_b2b', value: 'N' },
            { identifier: 'login_as_b2c', value: 'N' },
            { identifier: 'login_as_b2b', value: 'N' },
            { identifier: 'update_role', value: 'N' },
            { identifier: 'attributes', value: '{}' },
        ]);
        await service.acs(req, res, 'return');
        expect(res.redirect).toHaveBeenCalledWith('http://lms.com');
        expect(mockCookieHelper.setCookie).toHaveBeenCalled();
        expect(mockUserHelper.updateUserLoginTime).toHaveBeenCalled();
        expect(mockLrsHelper.sendDataToLrs).toHaveBeenCalled();
    });
    
    it('should throw error if email is missing', async () => {
        req.user.nameID = null;
        req.body.email = null;
        await service.acs(req, res, 'return');
        expect(res.redirect).toHaveBeenCalledWith('/saml/service-provider/error');
        expect(Logger.error).toHaveBeenCalledWith(expect.stringContaining('Learner email missing'), expect.any(Object));
    });
    
    it('should handle user auto-registration (B2C)', async () => {
        mockUserRepository.getUserByEmail.mockResolvedValueOnce(null); // First call, user not found
        mockUserRepository.getUserByEmail.mockResolvedValueOnce({ uid: '123', email: '<EMAIL>', status: 1, user_groups: [1], roles: [] }); // Second call, user found
        mockGroupSettingRepository.find.mockResolvedValue([
            { identifier: 'auto_register_as_b2c', value: 'Y' },
            { identifier: 'b2b_self_serve_portal', value: 'N' },
            { identifier: 'check_course_purchase', value: 'N' },
            { identifier: 'auto_register_as_b2b', value: 'N' },
            { identifier: 'login_as_b2c', value: 'N' },
            { identifier: 'login_as_b2b', value: 'N' },
            { identifier: 'update_role', value: 'N' },
            { identifier: 'attributes', value: '{}' },
        ]);
        mockUserHelper.registerByEmail.mockResolvedValue({ type: 'success', user: { id: '123' } });
        mockUserRepository.findByUID.mockResolvedValue({ uid: '123', email: '<EMAIL>', status: 1, user_groups: [1], roles: [] });
        mockFirebaseService.getDB.mockResolvedValue({} as any);
        mockFirebaseService.createDb.mockResolvedValue(true);

        await service.acs(req, res, 'return');
        
        expect(mockUserHelper.registerByEmail).toHaveBeenCalled();
        expect(res.redirect).toHaveBeenCalledWith('http://lms.com');
    });
    
    it('should throw error if user not found and auto-registration is off', async () => {
        mockUserRepository.getUserByEmail.mockResolvedValue(null); // User not found
        await service.acs(req, res, 'return');
        expect(res.redirect).toHaveBeenCalledWith('/saml/service-provider/error');
    });
    
    it('should throw error if user account is blocked', async () => {
        mockUserRepository.getUserByEmail.mockResolvedValue({ status: 0 });
        await service.acs(req, res, 'return');
        expect(res.redirect).toHaveBeenCalledWith('/saml/service-provider/error');
    });
    
    it('should handle attribute mapping', async () => {
        const attributes = JSON.stringify({
            attribute_fields: { 'fullName': '' },
            field_mapping: { 'user_name': 'fullName' }
        });
        req.user.attributes = { 'fullName': 'Test User' };
        mockGroupSettingRepository.find.mockResolvedValue([
            { identifier: 'attributes', value: attributes }
        ]);
        mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: '123' });
        mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);

        await service.acs(req, res, 'return');

        expect(mockUserRepository.findOneAndUpdate).toHaveBeenCalledWith({ uid: '123' }, { display_name: 'Test User' });
        expect(mockUserHelper.updateCloud6SentinelByUidOrMail).toHaveBeenCalledWith({ uid: '123', display_name: 'Test User' });
    });
    
    it('should handle B2B auto-registration', async () => {
        mockGroupSettingRepository.find.mockResolvedValue([
            { identifier: 'auto_register_as_b2b', value: 'Y' }
        ]);
        mockUserMgmtUtilityHelper.prepareAssignRoleList.mockResolvedValue([{ roleName: 'looper_affiliate_student' }]);
        mockUserMgmtUtilityHelper.prepareSentinelUserRoleList.mockResolvedValue([{}]);
        mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: '123' });
        mockUserHelper.syncCloud6SentinelUserRole.mockResolvedValue(true);

        await service.acs(req, res, 'return');

        expect(mockUserMgmtUtilityHelper.prepareAssignRoleList).toHaveBeenCalled();
        expect(mockUserRepository.findOneAndUpdate).toHaveBeenCalled();
    });
    
    it('should handle role updates', async () => {
        req.user.attributes = {
            ProfileGroup: 'ProfileGroup1',
            DataComplianceAdmin: 'true',
            BusinessOperations: 'false'
        };
        mockGroupSettingRepository.find.mockResolvedValue([
            { identifier: 'update_role', value: 'Y' },
            { identifier: 'client_id', value: 'testClientId' }
        ]);
        mockUserMgmtUtilityHelper.prepareAssignRoleList.mockResolvedValue([{ roleName: 'Role1' }, { roleName: 'AdminRole' }]);
        mockUserMgmtUtilityHelper.prepareSentinelUserRoleList.mockResolvedValue([{}]);
        
        await service.acs(req, res, 'return');

        expect(mockUserHelper.clearCloud6SentinelUserRole).toHaveBeenCalledWith('123');
        expect(mockUserMgmtUtilityHelper.prepareAssignRoleList).toHaveBeenCalled();
        expect(res.redirect).toHaveBeenCalledWith('http://simplex.com/gdpr');
    });
    
    it('should handle mobile requests', async () => {
        req.get.mockReturnValue('SimplilearnAppIOS'); // Mobile user agent
        const result = await service.acs(req, res, 'json');
        expect(result.data.is_mobile).toBe(true);
        expect(result.data.redirect_url).toContain('/saml/service-provider/mobile/success/email/');
    });
    
    it('should throw error if user is not a member of the group', async () => {
        mockUserRepository.getUserByEmail.mockResolvedValue({
            uid: '123',
            email: '<EMAIL>',
            status: 1,
            user_groups: [2], // Not a member of group 1
            roles: [],
        });
        await service.acs(req, res, 'return');
        expect(res.redirect).toHaveBeenCalledWith('/saml/service-provider/error');
    });
    
    it('should handle JSON response type', async () => {
        const result = await service.acs(req, res, 'json');
        expect(result.status).toBe('success');
        expect(result.data.redirect_url).toBe('http://lms.com');
        expect(res.redirect).not.toHaveBeenCalled();
    });
    
    it('should handle errors and redirect for return response type', async () => {
        mockUserRepository.getUserByEmail.mockRejectedValue(new Error('DB Error'));
        await service.acs(req, res, 'return');
        expect(res.redirect).toHaveBeenCalledWith('/saml/service-provider/error');
    });

    it('should handle errors and return json for json response type', async () => {
        mockUserRepository.getUserByEmail.mockRejectedValue(new Error('DB Error'));
        const result = await service.acs(req, res, 'json');
        expect(result.status).toBe('failed');
        expect(result.data.redirect_url).toBe('/saml/service-provider/error');
    });
  });

  describe('mobileSuccess', () => {
    it('should return success data for a mobile user', async () => {
        const email = '<EMAIL>';
        const userAgent = 'SimplilearnAppAndroid';
        const user = { uid: '123', email: '<EMAIL>', name: 'Test User', display_name: 'Test D. User' };
        mockUserRepository.getUserByEmail.mockResolvedValue(user);
        mockPaperclipService.generateUserTokenData.mockResolvedValue({ status: 'success', token: 'access-token' });
        mockEnterpriseService.getAffiliatesByUid.mockResolvedValue({ data: { status: 'success', data: [] } });

        const result = await service.mobileSuccess(email, userAgent);

        expect(result.status).toBe(200);
        expect((result.data as any).serverAccessKey).toBe('access-token');
        expect(result.mobile.isAndroid).toBe(true);
    });
    
    it('should handle affiliate user data', async () => {
        const email = '<EMAIL>';
        const userAgent = 'SimplilearnAppIOS';
        const user = { uid: '123', email: '<EMAIL>', name: 'Test User', display_name: 'Test D. User' };
        mockUserRepository.getUserByEmail.mockResolvedValue(user);
        mockPaperclipService.generateUserTokenData.mockResolvedValue({ status: 'success', token: 'access-token' });
        mockEnterpriseService.getAffiliatesByUid.mockResolvedValue({
            data: {
                status: 'success',
                data: [{ affiliateId: '456', affiliateLogoUrl: 'logo.png' }]
            }
        });

        const result = await service.mobileSuccess(email, userAgent);

        expect((result.data as any).isAffiliateUser).toBe(1);
        expect((result.data as any).affiliateId).toBe(456);
        expect(result.mobile.isIos).toBe(true);
    });

    it('should handle token generation failure', async () => {
        const email = '<EMAIL>';
        const userAgent = 'SomeUserAgent';
        mockUserRepository.getUserByEmail.mockResolvedValue({ uid: '123' });
        mockPaperclipService.generateUserTokenData.mockResolvedValue({ status: 'failed' });
        mockEnterpriseService.getAffiliatesByUid.mockResolvedValue({ data: { status: 'success', data: [] } });

        const result = await service.mobileSuccess(email, userAgent);

        expect(result.status).toBe(-1);
        expect(result.error.msg).toBe('Token Error');
    });
    
    it('should handle exceptions', async () => {
        mockUserRepository.getUserByEmail.mockRejectedValue(new Error('DB Error'));
        await service.mobileSuccess('<EMAIL>', 'agent');
        expect(Logger.error).toHaveBeenCalledWith('mobileSuccess', { Trace: expect.any(Error) });
    });
  });
  
  describe('mobileUserAgent', () => {
    it('should detect Android user agent', () => {
        const userAgent = 'Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 SimplilearnAppAndroid';
        const result = service.mobileUserAgent(userAgent);
        expect(result.isMobile).toBe(true);
        expect(result.isAndroid).toBe(true);
        expect(result.isIos).toBe(false);
    });

    it('should detect iOS user agent', () => {
        const userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.1 Mobile/15E148 Safari/604.1 SimplilearnAppIOS';
        const result = service.mobileUserAgent(userAgent);
        expect(result.isMobile).toBe(true);
        expect(result.isAndroid).toBe(false);
        expect(result.isIos).toBe(true);
    });

    it('should not detect mobile for other user agents', () => {
        const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36';
        const result = service.mobileUserAgent(userAgent);
        expect(result.isMobile).toBe(false);
        expect(result.isAndroid).toBe(false);
        expect(result.isIos).toBe(false);
    });
  });
});
