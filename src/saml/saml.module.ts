import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { SamlController } from './saml.controller';
import { SamlStrategy } from './gaurds/saml.strategy';
import { SamlService } from './services/saml.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SamlLogs } from '../db/mysql/entity/saml-logs.entity';
import { GroupSettings } from '../db/mysql/entity/group-settings.entity';
import { CustomFieldLabelMaster } from '../db/mysql/entity/custom_field_label_master.entity';
import { CustomFieldUserValue } from '../db/mysql/entity/custom_field_user_value.entity';

/**
 * The SamlModule is responsible for handling SAML authentication-related functionality.
 * It integrates with TypeORM for database operations and Passport for authentication strategies.
 */
@Module({
  // Importing necessary modules
  imports: [
    // Registers the TypeORM entities for database interaction
    TypeOrmModule.forFeature([GroupSettings, SamlLogs, CustomFieldLabelMaster , CustomFieldUserValue]),

    // Integrates Passport for authentication
    PassportModule,
  ],

  // Declaring the controllers that handle incoming HTTP requests
  controllers: [SamlController],

  // Declaring the providers (services and strategies) used within this module
  providers: [
    SamlService, // Service containing business logic for SAML authentication
    SamlStrategy, // Custom Passport strategy for SAML authentication
  ],

  // Exporting providers if they need to be used in other modules
  exports: [],
})
export class SamlModule {}
