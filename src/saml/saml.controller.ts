import { Controller, Get, Inject, Post, Render, Req, Res, UseFilters, UseGuards } from '@nestjs/common';
import { SamlService } from './services/saml.service';
import { AuthGuard } from '@nestjs/passport';
import { VIEW_PAGES } from '../auth/config/view.constants';
import { ConfigService } from '@nestjs/config';
import { APILog, Logger } from '../logging/logger';
import { HttpExceptionFilter } from '../common/filters/http.exception.filter';
import { ApiExcludeController } from '@nestjs/swagger';

/**
 * Controller for handling SAML-related endpoints.
 * This controller manages SAML authentication, metadata generation, logout, and error handling.
 */
@ApiExcludeController()
@Controller('saml/service-provider')
export class SamlController {
  // Injecting the SamlService to handle SAML-specific logic
  @Inject(SamlService)
  private readonly samlService: SamlService;

  // Injecting ConfigService to access application configuration
  @Inject(ConfigService) configService: ConfigService;

  /**
   * Handles Service Provider (SP) initiated Single Sign-On (SSO).
   * This endpoint is protected by the SAML AuthGuard.
   */
  @Get('/sp-initiated-sso/title/:titleValue')
  @UseGuards(AuthGuard('saml'))
  async spInitiatedSso() {
    Logger.log('SP initiated SSO');
  }

  /**
   * Generates and returns the Service Provider metadata in XML format.
   * This metadata is used for SAML configuration.
   */
  @Get('/metadata/title/:titleValue')
  async spMetaData(@Req() req, @Res() res) {
    try {
      const title = req.params.titleValue;
      const configData = await this.samlService.generateServiceProviderMetadata(title);
      res.type('application/xml').send(configData);
    } catch (error) {
      APILog.error('spMetaData', {
        METHOD: `${this.constructor.name}@${this.spMetaData.name}`,
        MESSAGE: (error as Error)?.message,
        REQUEST: req.params,
        RESPONSE: (error instanceof Error ? error.stack : 'No stack available'),
        TIMESTAMP: new Date().getTime(),
      });
      res.status(500).send('Error generating metadata');
    }
  }

  /**
   * Handles Single Logout Service (SLS) requests.
   * Logs out the user and redirects to the login page.
   */
  @Get('/sls/title/:titleValue')
  @UseGuards(AuthGuard('saml'))
  async logout(@Req() req, @Res() res) {
    try {
      Logger.log('SLS Action');
      Logger.log('SLS', req.user);
      req.logout();
      res.redirect('/auth/login');
    } catch (error) {
      APILog.error('logout', {
        METHOD: `${this.constructor.name}@${this.logout.name}`,
        MESSAGE: (error as Error).message,
        REQUEST: req.params,
        RESPONSE: (error instanceof Error ? error.stack : 'No stack available'),
        TIMESTAMP: new Date().getTime(),
      });
      res.status(500).send('Error during logout');
    }
  }

  /**
   * Handles Assertion Consumer Service (ACS) requests.
   * Processes SAML assertions and handles user authentication.
   * Consumed by Salesforce and SSO
   */
  @Post('/acs/title/:titleValue')
  @UseGuards(AuthGuard('saml'))
  async acs(@Req() req, @Res() res) {
    try {
      console.log('ACS Action');
      this.samlService.acs(req, res);
    } catch (error) {
      APILog.error('acs', {
        METHOD: `${this.constructor.name}@${this.acs.name}`,
        MESSAGE: (error as Error).message,
        REQUEST: req.body,
        RESPONSE: (error instanceof Error ? error.stack : 'No stack available'),
        TIMESTAMP: new Date().getTime(),
      });
      res.redirect('/saml/error');
    }
  }

  /**
   * Handles Assertion Consumer Service (ACS) request from a cloud6 as a proxy.
   * This endpoint is used for proxy SAML assertions.
   */
  @Post('/proxy-acs/title/:titleValue')
  @UseGuards(AuthGuard('saml'))
  async proxyAcs(@Req() req, @Res({passthrough : true}) res) {
    try {
      console.log('Proxy ACS Action');
      const response = await this.samlService.acs(req, res, 'json');
      return response;
    } catch (error) {
      APILog.error('proxy-acs', {
        METHOD: `${this.constructor.name}@${this.proxyAcs.name}`,
        MESSAGE: (error as Error).message,
        REQUEST: req.body,
        RESPONSE: error instanceof Error ? error.stack : 'No stack available',
        TIMESTAMP: new Date().getTime(),
      });
      res.redirect('/saml/error');
    }
  }

  /**
   * Handles SP initiated Single Logout Service (SLS).
   * Logs out the user and redirects to the login page.
   */
  @Get('/sp-initiated-sls/title/:titleValue')
  @UseGuards(AuthGuard('saml'))
  async spInitiatedSlsAction(@Req() req, @Res() res) {
    try {
      Logger.log('sp initiated sls', req.user);
      req.logout();
      res.redirect('/auth/login');
    } catch (error) {
      APILog.error('spInitiatedSlsAction', {
        METHOD: `${this.constructor.name}@${this.spInitiatedSlsAction.name}`,
        MESSAGE: (error as Error).message,
        REQUEST: req.params,
        RESPONSE: (error instanceof Error ? error.stack : 'No stack available'),
        TIMESTAMP: new Date().getTime(),
      });
      res.status(500).send('Error during SP initiated SLS');
    }
  }

  /**
   * Renders the error page.
   * Provides a user-friendly error page for SAML-related issues.
   */
  @Get('/error')
  @Render(VIEW_PAGES.ERROR)
  async error(@Req() req, @Res() res) {
    try {
      Logger.log('render.................................');
      return { ICE9_SITE_URL: this.configService.get('ic9SiteUrl') };
    } catch (error) {
      APILog.error('error', {
        METHOD: `${this.constructor.name}@${this.error.name}`,
        MESSAGE: (error as Error).message,
        REQUEST: req.params,
        RESPONSE: (error instanceof Error ? error.stack : 'No stack available'),
        TIMESTAMP: new Date().getTime(),
      });
      res.status(500).send('Error rendering error page');
    }
  }

  /**
   * Handles mobile success page rendering.
   * Displays a success message for mobile users after successful SAML authentication.
   */
  @Get('/mobile/success/email/:email')
  @Render(VIEW_PAGES.MOBILE_SUCCESS)
  @UseFilters(HttpExceptionFilter)
  async mobileSuccess(@Req() req) {
    try {
      const email = req.params.email;
      const userAgent = req.get('User-Agent');
      const response = await this.samlService.mobileSuccess(email, userAgent);
      const returnResponse = {
        data: JSON.stringify(response),
        msg: 'success',
        isiOS: response?.mobile?.isIos,
        isAndroidOS: response?.mobile?.isAndroid,
      };
      Logger.log('mobile success', returnResponse);
      return returnResponse;
    } catch (error) {
      APILog.error('mobileSuccess', {
        METHOD: `${this.constructor.name}@${this.mobileSuccess.name}`,
        MESSAGE: (error as Error).message,
        REQUEST: req.params,
        RESPONSE: (error instanceof Error ? error.stack : 'No stack available'),
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  /**
   * Handles mobile error page rendering.
   * Displays an error message for mobile users when an issue occurs.
   */
  @Get('/mobile/error')
  @Render(VIEW_PAGES.MOBILE_ERROR)
  async mobileError(@Req() req) {
    try {
      const userAgent = req.get('User-Agent');
      const responseData = {
        error: {
          msg: 'Error occurred',
        },
        status: -1,
        code: 400,
      };
      const mobileAgent = this.samlService.mobileUserAgent(userAgent);
      const returnResponse = {
        data: JSON.stringify(responseData),
        msg: 'error',
        isiOS: mobileAgent?.isIos,
        isAndroidOS: mobileAgent?.isAndroid,
      };
      Logger.log('mobile error', returnResponse);
      return returnResponse;
    } catch (error) {
      APILog.error('mobileError', {
        METHOD: `${this.constructor.name}@${this.mobileError.name}`,
        MESSAGE: (error as Error).message,
        REQUEST: req.params,
        RESPONSE: (error instanceof Error ? error.stack : 'No stack available'),
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
}
