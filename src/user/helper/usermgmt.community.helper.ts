import { BadRequestException, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserSignups } from '../../db/mysql/entity/user-signups.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from '../../logging/logger';
import { CommunityResponse } from '../../common/typeDef/auth.type';
import { HelperService } from '../../helper/helper.service';

export class UsermgmtCommunityHelper {
  @Inject(ConfigService) private configService: ConfigService;
  @Inject() private helperService: HelperService;
  @InjectRepository(UserSignups)
  private userSignupRepository: Repository<UserSignups>;

  //save user in user in the UserSignup model
  async saveUserSignup(signupDetail: { email: string; uid: number; utm_source: string }): Promise<boolean> {
    try {
      // Step 1: Calculate Current Timestamp
      const currentSeconds: number = Math.floor(Date.now() / 1000);

      // Step 2: Create Signup Data
      const signupData = {
        email: signupDetail?.email,
        user_id: signupDetail?.uid,
        utm_source: signupDetail.utm_source,
        user_type: this.configService.get<string>('freeUserType'),
        platform: 'web',
        status: 1,
        created_on: currentSeconds,
      };

      // Step 3: Save User Data
      const userSignup = await this.userSignupRepository.save(signupData);
      return userSignup ? true : false;
    } catch (error: any) {
      // Step 4: Handle Errors
      Logger.error('saveUser', {
        METHOD: `${this.constructor?.name}@${this.saveUserSignup.name}`,
        MESSAGE: error.message,
        REQUEST: { signupDetail },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException('SomethingWentWrong');
    }
  }

  async updateUserSignupUtm(data: { email: string; utm_source: string }): Promise<{ status: string; msg: string }> {
    const result = {
      status: 'failed',
      msg: 'Some error occurred while updating User Signup Logs',
    };
    if (!data || !data.email || !data.utm_source) {
      return result;
    }
    const currentMilliseconds: any = Date.now();
    const currentSeconds = Math.floor(currentMilliseconds / 1000);
    const updateParams = {
      utm_source: data.utm_source,
      updated_on: currentSeconds,
    };
    try {
      const res = await this.userSignupRepository.update({ email: data.email }, updateParams);
      if (res.affected > 0) {
        result.status = 'success';
        result.msg = 'User utm data updated successfully';
      }
    } catch (error: any) {
      Logger.log('Error in updateUserSignupUtm :::::: ', error.message);
      throw error;
    }
    return result;
  }

  async getCommunityRedirectUrl(
    cookieData,
    multiAccountUrl: string,
    userInfo: { first_name: string; last_name: string; uid: string; email: string },
  ): Promise<CommunityResponse> {
    const response: CommunityResponse = { url: multiAccountUrl, setCookie: false };
    try {
      // const communityRedirectCookie = cookieData[this.configService.get('communityCookie')] || '';

      // if (!communityRedirectCookie) {
      //   return response; // No community redirect cookie, return original URL
      // }
      // const authTokenHelper: AuthTokenHelper = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
      // const communityTokenDecode = await authTokenHelper.decodeJWTToken(communityRedirectCookie);
      // response.url = communityTokenDecode['url'];
      // if (!communityTokenDecode || !communityTokenDecode['url']) {
      //   return response; // Invalid token, return original URL
      // }
      // const isTribeCommunityUrl = !!communityTokenDecode['isTribeRedirect'];
      // if (isTribeCommunityUrl) {
      //   const displayName = userInfo?.first_name + ' ' + userInfo?.last_name;
      //   const userInfoForTribe: TribeUserType = {
      //     id: userInfo?.uid,
      //     name: displayName || '',
      //     email: userInfo?.email,
      //   };
      //   const userMgmtCommunityService = await this.helperService.get<UserMgmtCommunityService>(
      //     UserMgmtCommunityService,
      //   );
      //   const tribeSSORedirectUrl = await userMgmtCommunityService.getTribeSSOLink(userInfoForTribe);
      //   multiAccountUrl = tribeSSORedirectUrl || multiAccountUrl;
      // }
      const authHelper = await this.helperService.getHelper('AuthHelper');
      const tokenRedirectUrl = await authHelper.generateRedirectLinkToManageRedirect(
        userInfo,
        'login',
        'email',
        multiAccountUrl,
        cookieData[this.configService.get('calendarRedirectCookie')],
      );
      response.url = tokenRedirectUrl || multiAccountUrl;
      response.setCookie = false;
      response.cookieValue = { name: 'communityCookie', value: '' };
      return response;
    } catch (error: any) {
      Logger.error('getCommunityRedirectUrl', {
        METHOD: this.constructor.name + '@' + this.getCommunityRedirectUrl.name,
        MESSAGE: error.message,
        REQUEST: { userInfo: userInfo, multiAccountUrl: multiAccountUrl },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      return response;
    }
  }
}
