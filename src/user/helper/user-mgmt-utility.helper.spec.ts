import { Test, TestingModule } from '@nestjs/testing';
import { UserMgmtUtilityHelper } from './user-mgmt-utility.helper';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';
import { RoleRepository } from '../repositories/role/role.repository';
import { User } from '../../db/mongo/schema/user/user.schema';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CachingService } from '../../caching/caching.service';
import { SentinelUser } from '../../db/mysql/entity/sentinel-users.entity';
import { UserRepository } from '../repositories/user/user.repository';
import { Ic9Service } from '../../common/services/communication/ice9/ice9.service';
import { Types } from 'mongoose';
import { Logger } from '../../logging/logger';
// import { VIEW_PAGES } from '../../auth/config/view.constants';

// Mock dependencies
jest.mock('../../logging/logger');

describe('UserMgmtUtilityHelper', () => {
  let userMgmtUtilityHelper: UserMgmtUtilityHelper;
  let helperService: HelperService;
  let configService: ConfigService;
  let cachingService: CachingService;

  const mockRoleRepository = {
    findAll: jest.fn(),
  };

  const mockUserRepository = {
    findOneAndUpdate: jest.fn(),
    findOne: jest.fn(),
  };

  const mockCloud6UserRepositoryUpdate = {
    update: jest.fn(),
  };

  const mockIc9Service = {
    getCountryJson: jest.fn(),
  };

  const mockAuthHelper = {
    getUserPassResetUrl: jest.fn(),
  };

  const mockCookieHelper = {
    clearCookie: jest.fn(),
  };

  const mockUserHelper = {
    updateCloud6SentinelByUidOrMail: jest.fn(),
    syncUserDataWithMySQLDrupal: jest.fn(),
  }

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserMgmtUtilityHelper,
        { provide: HelperService, useValue: { get: jest.fn(), getHelper: jest.fn() } },
        { provide: ConfigService, useValue: { get: jest.fn() } },
        { provide: getRepositoryToken(SentinelUser), useValue: mockCloud6UserRepositoryUpdate },
        { provide: CachingService, useValue: { get: jest.fn(), set: jest.fn(), invalidateCache: jest.fn() } },
      ],
    }).compile();

    userMgmtUtilityHelper = module.get<UserMgmtUtilityHelper>(UserMgmtUtilityHelper);
    helperService = module.get<HelperService>(HelperService);
    configService = module.get<ConfigService>(ConfigService);
    cachingService = module.get<CachingService>(CachingService);

    (helperService.get as jest.Mock).mockImplementation(async (service: any) => {
      if (service === RoleRepository) return mockRoleRepository;
      if (service === UserRepository) return mockUserRepository;
      if (service === Ic9Service) return mockIc9Service;
      if (service === CachingService) return cachingService;
      return null;
    });

    (helperService.getHelper as jest.Mock).mockImplementation(async (helper: string) => {
      if (helper === 'AuthHelper') return mockAuthHelper;
      if (helper === 'CookieHelper') return mockCookieHelper;
      if (helper === 'UserHelper') return mockUserHelper;
      return null;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(userMgmtUtilityHelper).toBeDefined();
  });

  describe('prepareAssignRoleList', () => {
    it('should prepare the role list with default role', async () => {
      mockRoleRepository.findAll.mockResolvedValue([{ _id: 'role1' }, { _id: 'role2' }]);
      const roleList = ['role1', 'role2'];
      const result = await userMgmtUtilityHelper.prepareAssignRoleList(roleList);
      expect(result).toEqual(expect.arrayContaining(['role1', 'role2']));
    });

    it('should return an empty array on error', async () => {
        mockRoleRepository.findAll.mockRejectedValue(new Error('DB error'));
        const result = await userMgmtUtilityHelper.prepareAssignRoleList(['role1']);
        expect(result).toEqual([]);
        expect(Logger.log).toHaveBeenCalled();
      });
  });

  describe('prepareSentinelUserRoleList', () => {
    it('should prepare a list of roles for sentinel', async () => {
      const roles = [{ rid: 1 }, { rid: 2 }];
      mockRoleRepository.findAll.mockResolvedValue(roles);
      const result = await userMgmtUtilityHelper.prepareSentinelUserRoleList([new Types.ObjectId(), 'role2'], '123');
      expect(result).toEqual([{ rid: 1, uid: '123' }, { rid: 2, uid: '123' }]);
    });

    it('should return an empty array on error', async () => {
        mockRoleRepository.findAll.mockRejectedValue(new Error('DB error'));
        const result = await userMgmtUtilityHelper.prepareSentinelUserRoleList(['role1'], '123');
        expect(result).toEqual([]);
        expect(Logger.log).toHaveBeenCalled();
      });
  });

  describe('liveClassRedirectUrl', () => {
    it('should generate a redirect URL with calendar URL', async () => {
      const result = await userMgmtUtilityHelper.liveClassRedirectUrl('http://a.com', 1, 'http://b.com');
      expect(result).toContain('calendar_url');
    });

    it('should return the original redirect URL if no calendar URL is provided', async () => {
        const result = await userMgmtUtilityHelper.liveClassRedirectUrl('http://a.com', 1, '');
        expect(result).toBe('http://a.com');
      });

    it('should handle errors gracefully', async () => {
        const result = await userMgmtUtilityHelper.liveClassRedirectUrl(null, 1, 'http://b.com');
        expect(result).toBeUndefined();
        expect(Logger.error).toHaveBeenCalled();
      });
  });

  describe('getTimezoneFromCountryCode', () => {
    it('should return timezone for a valid country code', async () => {
      mockIc9Service.getCountryJson.mockResolvedValue({ data: 'var countryDataIe = [{"code":"US","timeZone":"America/New_York"}]' });
      const result = await userMgmtUtilityHelper.getTimezoneFromCountryCode('US', '');
      expect(result).toBe('America/New_York');
    });

    it('should return default timezone for an invalid country code', async () => {
        mockIc9Service.getCountryJson.mockResolvedValue({ data: 'var countryDataIe = []' });
        const result = await userMgmtUtilityHelper.getTimezoneFromCountryCode('XX', '');
        expect(result).toBe('America/Chicago');
      });

    it('should handle errors gracefully', async () => {
        mockIc9Service.getCountryJson.mockRejectedValue(new Error('API error'));
        const result = await userMgmtUtilityHelper.getTimezoneFromCountryCode('US', '');
        expect(result).toBeUndefined();
        expect(Logger.error).toHaveBeenCalled();
      });
  });

  describe('updateUserTimezone', () => {
    it('should update user timezone successfully', async () => {
      const requestedData = { uid: '1004171', country: 'US' };
      jest.spyOn(userMgmtUtilityHelper, 'getTimezoneFromCountryCode').mockResolvedValue('America/New_York');
      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: '1004171', timezone: 'America/New_York' });
      const result = await userMgmtUtilityHelper.updateUserTimezone(requestedData);
      expect(result.timezone).toBe('America/New_York');
    });

    it('should return undefined if user update fails', async () => {
        const requestedData = { uid: '1004171', country: 'US' };
        jest.spyOn(userMgmtUtilityHelper, 'getTimezoneFromCountryCode').mockResolvedValue('America/New_York');
        mockUserRepository.findOneAndUpdate.mockResolvedValue({});
        const result = await userMgmtUtilityHelper.updateUserTimezone(requestedData);
        expect(result).toBeUndefined();
      });
  });

  describe('getUserInfoForAPI', () => {
    it('should return formatted user info', async () => {
      const userObj = { uid: '1', email: '<EMAIL>', display_name: 'Test', roles: [{ rid: 1, roleName: 'test' }] };
      mockAuthHelper.getUserPassResetUrl.mockResolvedValue({ url: 'http://reset.url' });
      const result = await userMgmtUtilityHelper.getUserInfoForAPI(userObj);
      expect(result['reset_url']).toBe('http://reset.url');
    });
  });

  describe('getProfileCompletionStats', () => {
    it('should return correct completion percentage', () => {
      const userObj: Partial<User> = { name: 'test', email: '<EMAIL>' };
      const result = userMgmtUtilityHelper.getProfileCompletionStats(userObj);
      expect(result).toBe(20);
    });

    it('should return 0 for null user object', () => {
        const result = userMgmtUtilityHelper.getProfileCompletionStats(null);
        expect(result).toBe(0);
      });
  });

  describe('checkAndUpdateAttempt', () => {
    it('should return success on first attempt', async () => {
      (cachingService.get as jest.Mock).mockResolvedValue(0);
      const result = await userMgmtUtilityHelper.checkAndUpdateAttempt('key', 'timeout', 5, 10, 60);
      expect(result.type).toBe('success');
    });

    it('should return notice on exceeding attempts', async () => {
      (cachingService.get as jest.Mock).mockImplementation((key) => {
        if (key === 'key') return 5;
        if (key === 'timeout') return 0;
      });
      const result = await userMgmtUtilityHelper.checkAndUpdateAttempt('key', 'timeout', 5, 10, 60);
      expect(result.type).toBe('notice');
    });

    it('should return success if timeout has expired', async () => {
        (cachingService.get as jest.Mock).mockImplementation((key) => {
          if (key === 'timeout') return Date.now() - 700000;
          return 5;
        });
        const result = await userMgmtUtilityHelper.checkAndUpdateAttempt('key', 'timeout', 5, 10, 60);
        expect(result.type).toBe('success');
      });

    it('should throw an error on cache service failure', async () => {
        (cachingService.get as jest.Mock).mockRejectedValue(new Error('Cache error'));
        await expect(userMgmtUtilityHelper.checkAndUpdateAttempt('key', 'timeout', 5, 10, 60)).rejects.toThrow('An error occurred while checking and updating attempt limits.');
      });
  });

  describe('getTitleGenderList', () => {
    it('should return a list of titles and genders', () => {
      const result = userMgmtUtilityHelper.getTitleGenderList();
      expect(result.title).toBeDefined();
      expect(result.gender).toBeDefined();
    });
  });

  describe('showDeleteButton', () => {
    it('should return Y for eligible users', () => {
      (configService.get as jest.Mock).mockReturnValue('yes');
      const result = userMgmtUtilityHelper.showDeleteButton(null, 'test.com');
      expect(result).toBe('Y');
    });

    it('should return N for non-eligible users', () => {
        (configService.get as jest.Mock).mockReturnValue('no');
        const result = userMgmtUtilityHelper.showDeleteButton(1, 'simplilearn.com');
        expect(result).toBe('N');
      });

    it('should handle errors gracefully', () => {
        (configService.get as jest.Mock).mockImplementation(() => {
          throw new Error('Config error');
        });
        const result = userMgmtUtilityHelper.showDeleteButton(1, 'test.com');
        expect(result).toBe('N');
      });
  });

  describe('showDownloadButton', () => {
    it('should return Y if GDPR is enabled', () => {
      (configService.get as jest.Mock).mockReturnValue(1);
      const result = userMgmtUtilityHelper.showDownloadButton('test.com');
      expect(result).toBe('Y');
    });

    it('should return Y for allowed domains if GDPR is disabled', () => {
        (configService.get as jest.Mock).mockImplementation((key) => {
          if (key === 'gdprEnabled') return 0;
          if (key === 'isSimplilearnDomain') return ['simplilearn.com'];
        });
        const result = userMgmtUtilityHelper.showDownloadButton('simplilearn.com');
        expect(result).toBe('Y');
      });

    it('should handle errors gracefully', () => {
        (configService.get as jest.Mock).mockImplementation(() => {
          throw new Error('Config error');
        });
        const result = userMgmtUtilityHelper.showDownloadButton('test.com');
        expect(result).toBe('N');
      });
  });

  describe('clearCacheAndCookies', () => {
    it('should clear cache and cookies', async () => {
      const res = { clearCookie: jest.fn() };
      await userMgmtUtilityHelper.clearCacheAndCookies(res, 'test-key');
      expect(cachingService.invalidateCache).toHaveBeenCalled();
      expect(mockCookieHelper.clearCookie).toHaveBeenCalled();
    });
  });

  describe('isB2bStudent', () => {
    it('should return true for a B2B student', async () => {
      const user = {
        roles: [{ roleName: 'looper_affiliate_student' }],
        user_groups: [123],
      };
      mockUserRepository.findOne.mockResolvedValue(user);
      (configService.get as jest.Mock).mockReturnValue(999);
      const result = await userMgmtUtilityHelper.isB2bStudent(1, 123);
      expect(result).toBe(true);
    });

    it('should return false for a non-B2B student', async () => {
        mockUserRepository.findOne.mockResolvedValue({ roles: [], user_groups: [] });
        const result = await userMgmtUtilityHelper.isB2bStudent(1, 123);
        expect(result).toBe(false);
      });

    it('should return false if user is not found', async () => {
        mockUserRepository.findOne.mockResolvedValue(null);
        const result = await userMgmtUtilityHelper.isB2bStudent(1, 123);
        expect(result).toBe(false);
      });

    it('should handle errors gracefully', async () => {
        mockUserRepository.findOne.mockRejectedValue(new Error('DB error'));
        const result = await userMgmtUtilityHelper.isB2bStudent(1, 123);
        expect(result).toBe(false);
      });
  });
});