import { Test, TestingModule } from '@nestjs/testing';
import { UsermgmtCommunityHelper } from './usermgmt.community.helper';
import { ConfigService } from '@nestjs/config';
import { UserSignups } from '../../db/mysql/entity/user-signups.entity';
import { BadRequestException } from '@nestjs/common';
import { HelperService } from '../../helper/helper.service';
import { AuthTokenHelper } from '../../auth/helper/auth.tokenhelper';
import { getRepositoryToken } from '@nestjs/typeorm';
describe('UsermgmtCommunityHelper', () => {
  let usermgmtCommunityHelper: UsermgmtCommunityHelper;

  const configServiceMock = {
    get: jest.fn(),
  };

  const userSignupRepositoryMock = {
    save: jest.fn(),
    update: jest.fn(),
  };

  const helperServiceMock = {
    getHelper: jest.fn(),
  };

  const authTokenHelperMock = {
    decodeJWTToken: jest.fn(),
  };


  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsermgmtCommunityHelper,
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: HelperService,
          useValue: helperServiceMock,
        },
        {
          provide: AuthTokenHelper,
          useValue: authTokenHelperMock,
        },
        {
          provide: getRepositoryToken(UserSignups),
          useValue: userSignupRepositoryMock,
        },
      ],
    }).compile();

    usermgmtCommunityHelper = module.get<UsermgmtCommunityHelper>(UsermgmtCommunityHelper);
  });

  it('should be defined', () => {
    expect(usermgmtCommunityHelper).toBeDefined();
  });

  describe('saveUserSignup', () => {
    it('should save user signup data', async () => {
      const signupDetail = {
        email: '<EMAIL>',
        uid: 1,
        utm_source: 'example',
      };
      configServiceMock.get.mockReturnValue('freeUserType');
      userSignupRepositoryMock.save.mockResolvedValue({});
      const result = await usermgmtCommunityHelper.saveUserSignup(signupDetail);
      expect(result).toBe(true);
    });

    it('should handle errors and throw BadRequestException', async () => {
      const signupDetail = {
        email: '<EMAIL>',
        uid: 1,
        utm_source: 'example',
      };
      configServiceMock.get.mockReturnValue('freeUserType');
      userSignupRepositoryMock.save.mockRejectedValue(new Error('Test Error'));
      try {
        await usermgmtCommunityHelper.saveUserSignup(signupDetail);
      } catch (error: any) {
        expect(error).toBeInstanceOf(BadRequestException);
        expect(error.message).toBe('SomethingWentWrong');
      }
    });
  });

  describe('updateUserSignupUtm', () => {
    it('should update user signup utm data', async () => {
      const data = {
        email: '<EMAIL>',
        utm_source: 'new-source',
        updated_on: new Date(),
      };
      userSignupRepositoryMock.update.mockResolvedValue({ affected: 1 });
      const result = await usermgmtCommunityHelper.updateUserSignupUtm(data);
      expect(result.status).toBe('success');
      expect(result.msg).toBe('User utm data updated successfully');
    });

    it('should not update user signup utm data if required data is missing', async () => {
      const data = {
        email: '',
        utm_source: '',
        updated_on: '',
      };
      const result = await usermgmtCommunityHelper.updateUserSignupUtm(data);
      expect(result.status).toBe('failed');
      expect(result.msg).toBe('Some error occurred while updating User Signup Logs');
    });

    it('should handle errors and throw an exception', async () => {
      const data = {
        email: '<EMAIL>',
        utm_source: 'new-source',
        updated_on: new Date(),
      };
      userSignupRepositoryMock.update.mockRejectedValue(new Error('Test Error'));
      try {
        await usermgmtCommunityHelper.updateUserSignupUtm(data);
      } catch (error: any) {
        expect(error).toBeInstanceOf(Error);
        expect(error.message).toBe('Test Error');
      }
    });
  });


  describe('getCommunityRedirectUrl', () => {
    const userInfo = {
      first_name: 'John',
      last_name: 'Doe',
      uid: '123',
      email: '<EMAIL>',
    };
    const multiAccountUrl = 'https://example.com';
    const mockAuthHelper = {
      generateRedirectLinkToManageRedirect: jest.fn(),
    };
    
    beforeEach(async () => {
      helperServiceMock.getHelper.mockImplementation(async (helperName) => {
        if (helperName === 'AuthHelper') return mockAuthHelper;
        return null;
      });
    
      configServiceMock.get.mockImplementation((key) => {
        if (key === 'calendarRedirectCookie') return 'calendar_redirect';
        return null;
      });
    
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          UsermgmtCommunityHelper,
          {
            provide: ConfigService,
            useValue: configServiceMock,
          },
          {
            provide: HelperService,
            useValue: helperServiceMock,
          },
          {
            provide: getRepositoryToken(UserSignups),
            useValue: userSignupRepositoryMock,
          },
          {
            provide: AuthTokenHelper,
            useValue: authTokenHelperMock,
          },
        ],
      }).compile();
    
      usermgmtCommunityHelper = module.get<UsermgmtCommunityHelper>(UsermgmtCommunityHelper);
    });
    
  
    it('should return multiAccountUrl when generateRedirectLinkToManageRedirect returns undefined', async () => {
      const cookieData = { calendar_redirect: 'abc' };
      mockAuthHelper.generateRedirectLinkToManageRedirect.mockResolvedValue(undefined);
  
      const result = await usermgmtCommunityHelper.getCommunityRedirectUrl(cookieData, multiAccountUrl, userInfo);
  
      expect(mockAuthHelper.generateRedirectLinkToManageRedirect).toHaveBeenCalledWith(
        userInfo,
        'login',
        'email',
        multiAccountUrl,
        'abc'
      );
      expect(result).toEqual({
        url: multiAccountUrl,
        setCookie: false,
        cookieValue: { name: 'communityCookie', value: '' },
      });
    });
  
    it('should return redirect URL when generateRedirectLinkToManageRedirect returns a value', async () => {
      const redirectUrl = 'https://redirect.com';
      const cookieData = { calendar_redirect: 'xyz' };
      mockAuthHelper.generateRedirectLinkToManageRedirect.mockResolvedValue(redirectUrl);
  
      const result = await usermgmtCommunityHelper.getCommunityRedirectUrl(cookieData, multiAccountUrl, userInfo);
  
      expect(result).toEqual({
        url: redirectUrl,
        setCookie: false,
        cookieValue: { name: 'communityCookie', value: '' },
      });
    });

  
    it('should handle missing calendarRedirectCookie gracefully', async () => {
      configServiceMock.get.mockImplementation((key) => {
        if (key === 'calendarRedirectCookie') return undefined;
        return null;
      });
  
      const cookieData = {};
      mockAuthHelper.generateRedirectLinkToManageRedirect.mockResolvedValue(undefined);
  
      const result = await usermgmtCommunityHelper.getCommunityRedirectUrl(cookieData, multiAccountUrl, userInfo);
  
      expect(result).toEqual({
        url: multiAccountUrl,
        setCookie: false,
        cookieValue: { name: 'communityCookie', value: '' },
      });
    });
      
    it('should return default response if generateRedirectLinkToManageRedirect throws an error', async () => {
      const cookieData = { calendar_redirect: 'invalid' };
      mockAuthHelper.generateRedirectLinkToManageRedirect.mockRejectedValue(new Error('test failure'));
  
      const result = await usermgmtCommunityHelper.getCommunityRedirectUrl(cookieData, multiAccountUrl, userInfo);
      expect(mockAuthHelper.generateRedirectLinkToManageRedirect).toHaveBeenCalledWith(
        userInfo,
        'login',
        'email',
        multiAccountUrl,
        'invalid'
      );
      expect(result).toEqual({
        url: multiAccountUrl,
        setCookie: false,
        cookieValue: { name: 'communityCookie', value: '' },
      });
    });
  });
  
});
