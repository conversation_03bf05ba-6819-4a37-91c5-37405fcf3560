import { Test, TestingModule } from '@nestjs/testing';
import { ProfileHelper } from './profile.helper';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';
import { Category, Taxonomies } from '../../db/mongo/schema/taxonomies/taxonomies.schema';
import { Types } from 'mongoose';
import { UpdateUserPayloadDto } from '../../internal/dto/update-user-profile-payload.dto';
import { TaxonomyRepository } from '../repositories/taxonomy/taxonomy.repository';

// Mock dependencies
jest.mock('../../logging/logger');

describe('ProfileHelper', () => {
  let profileHelper: ProfileHelper;
  let helperService: HelperService;
  let configService: ConfigService;

  const mockTaxonomyRepository = {
    findOne: jest.fn(),
  };

  const mockUserRepository = {
    findOneAndUpdate: jest.fn(),
    findByUID: jest.fn(),
    getUserByEmail: jest.fn(),
  };

  const mockCloud6SentinelUserProfessionalData = {
    save: jest.fn(),
    delete: jest.fn(),
  };

  const mockCloud6SentinelUserAcademicData = {
    save: jest.fn(),
    delete: jest.fn(),
  };

  const mockS3UploaderService = {
    uploadToS3: jest.fn(),
  };

  const mockChangeLogService = {
    changeLogRequest: jest.fn(),
  };

  const mockUserHelper = {
    updateCloud6SentinelByUidOrMail: jest.fn(),
    getCloud6SentinelUserByUidOrMail: jest.fn(),
  };

  const mockKafkaService = {
    publish: jest.fn(),
  };

  const mockFirebaseService = {
    getDB: jest.fn(),
    fetchReferEarnUrl: jest.fn(),
    saveReferEarnUrl: jest.fn(),
  };

  const mockCookieHelper = {
    setCookie: jest.fn(),
  };

  const mockHelperService = {
    get: jest.fn().mockImplementation((repo) => {
     if (repo === TaxonomyRepository) return mockTaxonomyRepository;
     return null;
   }),
   getHelper: jest.fn().mockImplementation(async (helper: any) => {
    if (helper === 'UserRepository') return mockUserRepository;
    return null;
  }),
  
   };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProfileHelper,
        { provide: 'ITaxonomyRepository', useValue: mockTaxonomyRepository },
        { provide: HelperService, useValue: { get: jest.fn(), getHelper: jest.fn() } },
        { provide: ConfigService, useValue: { get: jest.fn() } },
        { provide: 'UserRepository', useValue: mockUserRepository },
        { provide: 'SentinelUserProfessionalDataRepository', useValue: mockCloud6SentinelUserProfessionalData },
        { provide: 'SentinelUserAcademicDataRepository', useValue: mockCloud6SentinelUserAcademicData },
        { provide: 'S3UploaderService', useValue: mockS3UploaderService },
        { provide: 'ChangeLogService', useValue: mockChangeLogService },
        { provide: 'UserHelper', useValue: mockUserHelper },
        { provide: 'KafkaService', useValue: mockKafkaService },
        { provide: 'FirebaseService', useValue: mockFirebaseService },
        { provide: 'CookieHelper', useValue: mockCookieHelper },
        {
          provide: HelperService, // ✅ Register this missing dependency
          useValue: mockHelperService,
        },
      ],
    }).compile();

    profileHelper = module.get<ProfileHelper>(ProfileHelper);
    helperService = module.get<HelperService>(HelperService);
    configService = module.get<ConfigService>(ConfigService);

    // Mock implementations
    (helperService.get as jest.Mock).mockImplementation(async (service: string) => {
      if (service === 'TaxonomyRepository') return mockTaxonomyRepository;
      if (service === 'UserRepository') return mockUserRepository;
      if (service === 'S3UploaderService') return mockS3UploaderService;
      if (service === 'ChangeLogService') return mockChangeLogService;
      if (service === 'KafkaService') return mockKafkaService;
      if (service === 'FirebaseService') return mockFirebaseService;
      return null;
    });

    (helperService.getHelper as jest.Mock).mockImplementation(async (helper: string) => {
      if (helper === 'UserHelper') return mockUserHelper;
      if (helper === 'CookieHelper') return mockCookieHelper;
      return null;
    });

    (configService.get as jest.Mock).mockImplementation((key: string) => {
      if (key === 's3BucketName') return 'test-bucket';
      return null;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(profileHelper).toBeDefined();
  });

  describe('prepareProfileData', () => {
    it('should prepare profile data correctly', async () => {
      const inputData = { designation: 'dev', company: 'test' };
      jest.spyOn(profileHelper, 'prepareAndSaveTaxonomyData').mockResolvedValueOnce({});
      const result = await profileHelper.prepareProfileData(inputData);
      expect(result.work_experience[0].designation).toBe('dev');
      expect(result.academics).toBeDefined();
    });
  });

describe('ProfileHelper › prepareAndSaveTaxonomyData', () => {
  let profileHelper: ProfileHelper;
  const mockTaxonomyRepository = {
    findOne: jest.fn(),
  };
  const mockHelperService = {
   get: jest.fn().mockImplementation((repo) => {
    if (repo === TaxonomyRepository) return mockTaxonomyRepository;
    return {};
  }),
    getHelper: jest.fn(),
  };
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProfileHelper,
        { provide: 'ITaxonomyRepository', useValue: mockTaxonomyRepository },
        { provide: HelperService, useValue: { get: jest.fn(), getHelper: jest.fn() } },
        { provide: ConfigService, useValue: { get: jest.fn() } },
        { provide: 'UserRepository', useValue: mockUserRepository },
        { provide: 'SentinelUserProfessionalDataRepository', useValue: mockCloud6SentinelUserProfessionalData },
        { provide: 'SentinelUserAcademicDataRepository', useValue: mockCloud6SentinelUserAcademicData },
        { provide: 'S3UploaderService', useValue: mockS3UploaderService },
        { provide: 'ChangeLogService', useValue: mockChangeLogService },
        { provide: 'UserHelper', useValue: mockUserHelper },
        { provide: 'KafkaService', useValue: mockKafkaService },
        { provide: 'FirebaseService', useValue: mockFirebaseService },
        { provide: 'CookieHelper', useValue: mockCookieHelper },
        {
          provide: HelperService, // ✅ Register this missing dependency
          useValue: mockHelperService,
        },
      ],
    }).compile();
    profileHelper = module.get<ProfileHelper>(ProfileHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch and return taxonomy IDs for all fields', async () => {
    const id1 = new Types.ObjectId();
    const id2 = new Types.ObjectId();
    const id3 = new Types.ObjectId();
    const id4 = new Types.ObjectId();
    const mockTaxonomy = (id: Types.ObjectId, category: Category): Taxonomies => ({
      _id: id,
      category, // ✅ Enum instead of plain string
      name: `${category}_name`,
      tid: 1,
    });
    // Mock getOneTaxonomy by mocking taxonomyRepository.findOne used inside it
    jest.spyOn(profileHelper, 'getOneTaxonomy')
    .mockResolvedValueOnce(mockTaxonomy(id1, Category.INDUSTRY)) // industry
    .mockResolvedValueOnce(mockTaxonomy(id2, Category.QUALIFICATION)) // qualification
    .mockResolvedValueOnce(mockTaxonomy(id3, Category.OBJECTIVE_OF_TAKING_COURSE)) // objective
    .mockResolvedValueOnce(mockTaxonomy(id4, Category.JOB_FUNCTION)); // job_function

    const input = {
      industry: 'tech',
      qualification: 'bsc',
      objective: 'career',
      job_function: 'engineering',
    };

    const result = await profileHelper.prepareAndSaveTaxonomyData(input);

    expect(profileHelper.getOneTaxonomy).toHaveBeenCalledTimes(4);
    expect(result[Category.INDUSTRY]).toEqual(id1);
    expect(result[Category.QUALIFICATION]).toEqual(id2);
    expect(result[Category.OBJECTIVE_OF_TAKING_COURSE]).toEqual(id3);
    expect(result[Category.JOB_FUNCTION]).toEqual(id4);
  });

  it('should return empty object when input is empty', async () => {
    const result = await profileHelper.prepareAndSaveTaxonomyData({});
    expect(result).toEqual({});
  });

  it('should handle errors and return message', async () => {
    jest.spyOn(profileHelper, 'getOneTaxonomy').mockImplementation(() => {
      throw new Error('Simulated failure');
    });

    const input = { industry: 'tech' };
    const result = await profileHelper.prepareAndSaveTaxonomyData(input);

    expect(result).toEqual({ msg: 'Error occurred while updating profile. Please try again.' });
  });
});



  describe('saveDynamicProfileData', () => {
    it('should save data to mongo and mysql', async () => {
      const updateObj = { mysql: { work_experience: [{}] } };
      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: '123' });
      mockCloud6SentinelUserProfessionalData.save.mockResolvedValue([{}]);
      const result = await profileHelper.saveDynamicProfileData('123', mockCloud6SentinelUserProfessionalData, updateObj, 'work_experience');
      expect(result).toBe(true);
    });
  });

  describe('saveDynamicProfileData', () => {
    it('should save data to mongo and mysql correctly', async () => {
      const uid = '123';
      const updateObj = {
        mysql: {
          work_experience: [{ company: 'ABC Corp' }],
          other_field: [{ key: 'value' }],
        },
      };
  
      const fieldName = 'work_experience';
  
      // Mock resolved values
      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid });
      mockCloud6SentinelUserProfessionalData.delete = jest.fn().mockResolvedValue({});
      mockCloud6SentinelUserProfessionalData.save.mockResolvedValue([{ id: 1 }]);
      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue({});
  
      const result = await profileHelper.saveDynamicProfileData(
        uid,
        mockCloud6SentinelUserProfessionalData,
        updateObj,
        fieldName,
      );
    
      expect(result).toBe(true);
      expect(mockUserRepository.findOneAndUpdate).toHaveBeenCalledWith(
        { uid },
        expect.objectContaining({
          updated_on: expect.any(Number),
        }),
      );
  
      expect(mockCloud6SentinelUserProfessionalData.delete).toHaveBeenCalledWith({ uid });
  
      expect(mockUserHelper.updateCloud6SentinelByUidOrMail).toHaveBeenCalledWith(
        expect.objectContaining({
          uid: '123',
          other_field: [{ key: 'value' }],
          updated_on: expect.any(Number),
        }),
      );
  
      expect(mockCloud6SentinelUserProfessionalData.save).toHaveBeenCalledWith([
        { company: 'ABC Corp', uid: '123' },
      ]);
    });
  });
  
  

describe('getOneTaxonomy', () => {
  it('should return taxonomy by ObjectId', async () => {
    const id = new Types.ObjectId();
    const mockTaxonomy = {
      _id: id,
      name: 'tech',
      category: Category.INDUSTRY,
      tid: 101,
    };

    mockTaxonomyRepository.findOne.mockResolvedValue(mockTaxonomy);

    const result = await profileHelper.getOneTaxonomy(id.toString());

    expect(result).toBeDefined();
    expect(result?.name).toBe('tech');
    expect(mockTaxonomyRepository.findOne).toHaveBeenCalledWith({ _id: id });
  });

  it('should return taxonomy by tid', async () => {
    const mockTaxonomy = {
      _id: new Types.ObjectId(),
      name: 'tech', 
      category: Category.INDUSTRY,
      tid: 123,
    };

    mockTaxonomyRepository.findOne.mockResolvedValue(mockTaxonomy);

    const result = await profileHelper.getOneTaxonomy('123');

    expect(result).toBeDefined();
    expect(result?.name).toBe('tech');
    expect(mockTaxonomyRepository.findOne).toHaveBeenCalledWith({ tid: 123 });
  });
});

  

  describe('userProfileData', () => {
    it('should return user data by UID', async () => {
      mockUserRepository.findByUID.mockResolvedValue({ uid: 1, name: 'test' });
      const result = await profileHelper.userProfileData({ uid: 1 });
      expect(result.name).toBe('test');
    });
  });

  describe('saveStaticProfileData', () => {
    it('should save static data to mongo and mysql', async () => {
      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: '123', email: '<EMAIL>' });
      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
      mockUserHelper.getCloud6SentinelUserByUidOrMail.mockResolvedValue({});
      const result = await profileHelper.saveStaticProfileData('123', {});
      expect(result).toBe(true);
    });
  });

  describe('saveStaticProfileData', () => {
    it('should update both MongoDB and MySQL and log changes', async () => {
      const uid = 'user123';
      const updateObj: any = {
        email: '<EMAIL>',
        mysql: {
          display_name: 'New Name',
        },
      };

      const mockMongoUser = {
        uid: 'user123',
        email: '<EMAIL>',
      };

      mockUserHelper.getCloud6SentinelUserByUidOrMail.mockResolvedValue(mockMongoUser);
      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 'user123', email: '<EMAIL>' });
      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);

      const result = await profileHelper.saveStaticProfileData(uid, { ...updateObj });

      expect(result).toBe(true);
      expect(mockUserHelper.getCloud6SentinelUserByUidOrMail).toHaveBeenCalledWith({ uid });
      expect(mockUserRepository.findOneAndUpdate).toHaveBeenCalledWith(
        { uid },
        expect.objectContaining({
          updated_on: expect.any(Number),
          email: '<EMAIL>',
        })
      );
      expect(mockUserHelper.updateCloud6SentinelByUidOrMail).toHaveBeenCalledWith(
        expect.objectContaining({
          uid: 'user123',
          updated_on: expect.any(Number),
          display_name: 'New Name',
        })
      );
      expect(mockChangeLogService.changeLogRequest).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'user123',
          userEmail: '<EMAIL>',
          oldValue: mockMongoUser,
          newValue: expect.objectContaining({ email: '<EMAIL>' }),
        })
      );
    });
  });

  describe('uploadProfilePic', () => {
    it('should upload a file to s3 and return metadata', async () => {
      const file = { originalname: 'test.jpg', buffer: Buffer.from(''), mimetype: 'image/jpeg', size: 100 };
      const result = await profileHelper.uploadProfilePic(file, '123');
      expect(mockS3UploaderService.uploadToS3).toHaveBeenCalled();
      expect(result.filename).toContain('test.jpg');
    });

    it('should return null if file is empty', async () => {
      const result = await profileHelper.uploadProfilePic('', '123');
      expect(result).toBeNull();
    });
  });

  describe('saveContactProfileData', () => {
    it('should parse and save contact data', async () => {
      const payload = { country_of_residence: '{"name":"India"}' };
      jest.spyOn(profileHelper, 'saveStaticProfileData').mockResolvedValueOnce(true);
      const result = await profileHelper.saveContactProfileData('123', payload as UpdateUserPayloadDto);
      expect(result).toBe(true);
    });
  });

  describe('saveBasicProfileData', () => {
    it('should save basic profile data with picture', async () => {
      const payload = { dob: '01-01-2000', profile_pic: { originalname: 'test.jpg', buffer: Buffer.from(''), mimetype: 'image/jpeg', size: 100 } };
      jest.spyOn(profileHelper, 'uploadProfilePic').mockResolvedValueOnce({ filename: 'test.jpg', filemime: 'image/jpeg', filesize: '100', status: 'active', timestamp: new Date().toISOString() });
      jest.spyOn(profileHelper, 'saveStaticProfileData').mockResolvedValueOnce(true);
      const result = await profileHelper.saveBasicProfileData('123', payload as unknown as UpdateUserPayloadDto);
      expect(result).toBe(true);
    });
  });

  describe('saveProfessionalProfileData', () => {
    it('should save professional data', async () => {
      const data = {
        company_designation: ['dev'],
        company_name: ['test'],
        job_function: ['jf'],
        industry: ['ind'],
        exp_from_month: ['Jan'],
        exp_from_year: ['2020'],
        exp_to_month: ['Dec'],
        exp_to_year: ['2022'],
        current_role: ['0'],
        where_are_you_in_career: 'mid',
      };
      jest.spyOn(profileHelper, 'getOneTaxonomy').mockResolvedValue({ _id: new Types.ObjectId(), name: 'test' } as Taxonomies);
      jest.spyOn(profileHelper, 'saveDynamicProfileData').mockResolvedValueOnce(true);
      const result = await profileHelper.saveProfessionalProfileData(data as any, '123');
      expect(result).toBe(true);
    });
  });

  describe('saveAcademicProfileData', () => {
    it('should save academic data', async () => {
      const data = {
        field_qualification: ['bsc'],
        institute_name: ['uni'],
        field_specialization: ['cs'],
        course_from_month: ['Jan'],
        course_from_year: ['2020'],
        course_to_month: ['Dec'],
        course_to_year: ['2022'],
        highest_level_of_education: 'high',
      };
      jest.spyOn(profileHelper, 'getOneTaxonomy').mockResolvedValue({ _id: new Types.ObjectId(), name: 'test' } as Taxonomies);
      jest.spyOn(profileHelper, 'saveDynamicProfileData').mockResolvedValueOnce(true);
      const result = await profileHelper.saveAcademicProfileData(data as any, '123');
      expect(result).toBe(true);
    });
  });

  describe('saveOutcomeDetailsData', () => {
    it('should save outcome data', async () => {
      const data = { objective_taking_course: 'learn' };
      jest.spyOn(profileHelper, 'getOneTaxonomy').mockResolvedValue({ _id: new Types.ObjectId(), tid: 1 } as Taxonomies);
      jest.spyOn(profileHelper, 'saveStaticProfileData').mockResolvedValueOnce(true);
      const result = await profileHelper.saveOutcomeDetailsData('123', data);
      expect(result).toBe(true);
    });
  });

  describe('saveProfileData', () => {
    it.each([
      'professional',
      'academics',
      'contact',
      'basic',
      'outcome',
      'default',
    ])('should call the correct save method for edit_type: %s', async (editType) => {
      const data = { edit_type: editType };
      const user = { uid: '123' };
      const spy = jest.spyOn(profileHelper, `save${editType.charAt(0).toUpperCase() + editType.slice(1)}ProfileData` as any);
      if (editType === 'default') {
        const result = await profileHelper.saveProfileData(data, user);
        expect(result).toBe(true);
      } else {
        await profileHelper.saveProfileData(data, user);
        expect(spy).toHaveBeenCalled();
      }
    });
  });

  describe('fetchProfileData', () => {
    it('should fetch and process profile data', async () => {
      mockUserRepository.findByUID.mockResolvedValue({ work_experience: [{}], academics: [{}] });
      jest.spyOn(profileHelper, 'getOneTaxonomy').mockResolvedValue({ _id: new Types.ObjectId() } as Taxonomies);
      jest.spyOn(profileHelper, 'getProfileCompletionStats').mockResolvedValue({ categoryCompletion: {}, overallCompletion: '80%' });
      const result = await profileHelper.fetchProfileData('123');
      expect(result.userData).toBeDefined();
    });
  });

  describe('getIndustryDisableList', () => {
    it('should return the correct disable list based on NODE_ENV', () => {
      process.env.NODE_ENV = 'production';
      (configService.get as jest.Mock).mockReturnValue(['prod-disable']);
      let result = profileHelper.getIndustryDisableList();
      expect(result).toEqual(['prod-disable']);

      process.env.NODE_ENV = 'development';
      (configService.get as jest.Mock).mockReturnValue(['dev-disable']);
      result = profileHelper.getIndustryDisableList();
      expect(result).toEqual(['dev-disable']);
    });
  });

  describe('getProfileCompletionStats', () => {
    it('should calculate profile completion stats correctly', async () => {
      const profileData = { first_name: 'test', email: '<EMAIL>' };
      const result = await profileHelper.getProfileCompletionStats(profileData);
      expect(result.overallCompletion).toBeDefined();
    });
  });

  describe('handleOobtaxonomyData', () => {
    it('should handle OOB taxonomy data and update records', async () => {
      const updateDetails = { industry: 'tech', uid: '123' };
      jest.spyOn(profileHelper, 'getOneTaxonomy').mockResolvedValue({ name: 'Technology' } as Taxonomies);
      const result = await profileHelper.handleOobtaxonomyData(updateDetails);
      expect(mockCloud6SentinelUserProfessionalData.save).toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });

  describe('updateUserProfileAcademicData', () => {
    it('should update academic data', async () => {
      const updateDetails = { qualification: 'bsc', uid: '123' };
      jest.spyOn(profileHelper, 'getOneTaxonomy').mockResolvedValue({ name: 'BSc' } as Taxonomies);
      const result = await profileHelper.updateUserProfileAcademicData(updateDetails);
      expect(mockCloud6SentinelUserAcademicData.save).toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });

  describe('deleteImage', () => {
    it('should delete image and return success', async () => {
      mockUserRepository.getUserByEmail.mockResolvedValue({ profile_pic: 'pic' });
      mockUserRepository.findOneAndUpdate.mockResolvedValue(true);
      jest.spyOn(profileHelper, 'getProfileCompletionStats').mockResolvedValue({ categoryCompletion: {}, overallCompletion: '80%' });
      const result = await profileHelper.deleteImage('<EMAIL>');
      expect(result.type).toBe('success');
    });
  });

  describe('syncTaxonomyDataWithMySQLDrupal', () => {
    it('should call cloud6 service to sync data', async () => {
      const mockCloud6Service = { syncTaxonomyUserDataWithMySQL: jest.fn().mockResolvedValue(true) };
      (helperService.get as jest.Mock).mockResolvedValue(mockCloud6Service);
      const result = await profileHelper.syncTaxonomyDataWithMySQLDrupal({}, ['professional']);
      expect(mockCloud6Service.syncTaxonomyUserDataWithMySQL).toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });

  describe('getReferEarnUrl', () => {
    it('should return existing url from firebase', async () => {
      mockFirebaseService.getDB.mockResolvedValue('user-db');
      mockFirebaseService.fetchReferEarnUrl.mockResolvedValue('http://refer.url');
      const result = await profileHelper.getReferEarnUrl({ uid: '123', email: '<EMAIL>' }, {} as any, {});
      expect(result.url).toBe('http://refer.url');
    });
  });

  describe('syncReferEarnUrl', () => {
    it('should publish message to kafka and set cookie', async () => {
      const postData = { name: 'test', email: '<EMAIL>', userKey: 'key' };
      const res = {};
      const cryptoHelper = { createHmac: jest.fn().mockResolvedValue('hmac') };
      (helperService.getHelper as jest.Mock).mockResolvedValue(cryptoHelper);
      mockKafkaService.publish.mockResolvedValue({ offsets: [{ offset: '1' }] });
      const result = await profileHelper.syncReferEarnUrl(res, postData, '123');
      expect(mockKafkaService.publish).toHaveBeenCalled();
      expect(mockCookieHelper.setCookie).toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });
});
