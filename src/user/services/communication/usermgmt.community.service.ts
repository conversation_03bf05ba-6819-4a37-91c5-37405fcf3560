import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Logger } from '../../../logging/logger';
import { isEmail } from 'class-validator';
import { Communication } from '../../../common/services/communication/communication';
import { CryptoHelper } from '../../../helper/helper.crypto';
import { HelperService } from '../../../helper/helper.service';
import { AuthTokenHelper } from '../../../auth/helper/auth.tokenhelper';
import { TribeUserType, FailedCommunityUserType, CommunityUserSignup } from '../../../common/typeDef/auth.type';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FailedCommunityUser } from '../../../db/mysql/entity/failed-community-user.entity';
import fs from 'fs';
import axios from 'axios';
@Injectable()
export class UserMgmtCommunityService extends Communication {
  @Inject() private readonly configService: ConfigService;
  @Inject('TRIBE_CRYPTO_HELPER') private readonly cryptoHelper: CryptoHelper;
  @Inject(HelperService) private readonly helperService: HelperService;
  @InjectRepository(FailedCommunityUser)
  private failedCommunityUserRepository: Repository<FailedCommunityUser>;
  protected resetUri() {
    this.url = this.configService.get('tribeAPIEndpoint');
  }

  async getTribeSSOLink(user: TribeUserType, redirectLink = '/'): Promise<string> {
    try {
      const token: any = await this.createTribeToken(user);
      const encodedToken = await encodeURIComponent(token);
      return (
        this.configService.get('tribeCommunityBaseUrl') +
        `/api/auth/sso?jwt=${encodedToken}&redirect_uri=${redirectLink}`
      );
    } catch (error: any) {
      Logger.error('getTribeSSOLink', {
        METHOD: this.constructor?.name + '@' + this.getTribeSSOLink.name,
        MESSAGE: error.message,
        REQUEST: { user },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async createTribeToken(user: TribeUserType): Promise<string | false> {
    try {
      if (!user?.id || !user?.email || !user?.name) {
        Logger.error('createTribeToken', {
          METHOD: this.constructor?.name + '@' + this.createTribeToken.name,
          MESSAGE: 'Invalid request to create Tribe token',
          REQUEST: { user },
          TIMESTAMP: new Date().getTime(),
        });
      }
      const userEmail = user?.email;
      const query =
        `{"query":"query Members($after: String, $before: String, $filterBy: [MemberListFilterByInput!], $limit: Int!, $offset: Int, $orderBy: String, $query: String, $reverse: Boolean, $roleIds: [ID!], $status: [MemberStatusInput!]) { \\n  members( \\n    after: $after \\n    before: $before \\n    filterBy: $filterBy \\n    limit: $limit \\n    offset: $offset \\n    orderBy: $orderBy \\n    query: $query \\n    reverse: $reverse \\n    roleIds: $roleIds \\n    status: $status \\n  ) { \\n    totalCount \\n    edges { \\n      node { \\n        displayName \\n        name \\n        id \\n        attributes { \\n          locale \\n        } \\n        profilePictureId \\n        bannerId \\n        status \\n        username \\n        email \\n        emailStatus \\n        newEmail \\n        tagline \\n        lastSeenAt \\n        createdAt \\n        updatedAt \\n        relativeUrl \\n        url \\n        externalId \\n        roleId \\n        role { \\n          id \\n          name \\n          type \\n          description \\n          visible \\n        } \\n        banner { \\n          ... on Image { \\n            __typename \\n            id \\n            url \\n            width \\n            height \\n            dominantColorHex \\n            dpi \\n            cropHeight \\n            cropWidth \\n            cropX \\n            cropY \\n            cropZoom \\n            urls { \\n              __typename \\n              full \\n              large \\n              medium \\n              small \\n              thumb \\n            } \\n          } \\n          ... on Emoji { \\n            __typename \\n            id \\n            text \\n          } \\n          ... on File { \\n            id \\n            name \\n            url \\n          } \\n        } \\n        profilePicture { \\n          ... on Image { \\n            __typename \\n            id \\n            url \\n            width \\n            height \\n            dominantColorHex \\n            dpi \\n            cropHeight \\n            cropWidth \\n            cropX \\n            cropY \\n            cropZoom \\n            urls { \\n              __typename \\n              full \\n              large \\n              medium \\n              small \\n              thumb \\n            } \\n          } \\n          ... on Emoji { \\n            __typename \\n            id \\n            text \\n          } \\n          ... on File { \\n            id \\n            name \\n            url \\n          } \\n        } \\n        badges { \\n          backgroundColor \\n          badgeId \\n          imageId \\n          longDescription \\n          text \\n          shortDescription \\n          textColor \\n          type \\n          badge { \\n            active \\n            backgroundColor \\n            daysUntilExpired \\n            id \\n            imageId \\n            longDescription \\n            name \\n            shortDescription \\n            textColor \\n            text \\n            type \\n            settings { \\n              key \\n              value \\n            } \\n            image { \\n              ... on Image { \\n                __typename \\n                id \\n                url \\n                width \\n                height \\n                dominantColorHex \\n                dpi \\n                cropHeight \\n                cropWidth \\n                cropX \\n                cropY \\n                cropZoom \\n                urls { \\n                  __typename \\n                  full \\n                  large \\n                  medium \\n                  small \\n                  thumb \\n                } \\n              } \\n              ... on Emoji { \\n                __typename \\n                id \\n                text \\n              } \\n              ... on File { \\n                id \\n                name \\n                url \\n              } \\n            } \\n          } \\n        } \\n      } \\n    } \\n    pageInfo { \\n      hasNextPage \\n      endCursor \\n    } \\n  } \\n } \\n","variables":{"limit":1,"query":"` +
        userEmail +
        `"},"operationName":"Members"}`;
      const token = await this.generateToken();
      if (token) {
        const userInfo: any = await this.queryTribe(query, token);
        const result = await this.getNewOrEncryptedUser(userInfo);
        const userData = await this.getUserDataForTokenCreation(user, result, userEmail);
        const authHelperInstance = await this.helperService.get<AuthTokenHelper>(AuthTokenHelper);
        const fToken = await authHelperInstance.createSignedToken(userData, {
          secret: this.configService.get('tribePrivateKey'),
        });
        return fToken;
      }
    } catch (err: any) {
      Logger.error('createTribeToken', {
        METHOD: this.constructor?.name + '@' + this.createTribeToken.name,
        MESSAGE: err?.message,
        REQUEST: { user },
        RESPONSE: err?.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }
  public async generateToken() {
    try {
      this.resetUri();
      const generateTokenQuery =
        '{"query":"query {\\n  limitedToken(\\n    context:NETWORK, \\n    networkId: \\"' +
        this.configService.get('tribeApiNetworkId') +
        '\\", \\n    entityId: \\"' +
        this.configService.get('tribeApiNetworkId') +
        '\\"\\n  ) {\\n    accessToken\\n  }\\n}\\n\\n\\n","variables":{}}';
      this.setFormData('');
      this.setRawBody(generateTokenQuery);
      this.setRequestHeader('Content-Type', 'application/json');
      const result = await this.post('');
      const response = result;
      Logger.log('generateToken response :', response);
      if (response.data?.limitedToken?.accessToken) {
        return response.data.limitedToken.accessToken;
      } else {
        return '';
      }
    } catch (error: any) {
      Logger.error('generateToken', {
        METHOD: this.constructor?.name + '@' + this.generateToken.name,
        MESSAGE: error.message,
        REQUEST: {},
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  public async queryTribe(query: string, token: any): Promise<any> {
    try {
      this.resetUri();
      this.url = this.configService.get('tribeAPIEndpointWithoutCredentials');
      const headers = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        Credentials: this.configService.get('credentialsForTribe'),
      };
      const response = await axios.post(this.url, query, { headers });
      if (response?.data) {
        return response.data;
      } else {
        return [];
      }
    } catch (error: any) {
      Logger.error('queryTribe', {
        METHOD: this.constructor?.name + '@' + this.queryTribe.name,
        MESSAGE: error.message,
        REQUEST: { query: query, token: token },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async getSkillupReferralInviteLink(
    source: string,
    medium: string,
    campaign: string,
    refCode: string,
    skillupUrl: string,
  ): Promise<any> {
    try {
      const data = {
        $desktop_url: skillupUrl + refCode,
        $android_url: skillupUrl + refCode,
        $ios_url: skillupUrl + refCode,
        $web_url: skillupUrl + refCode,
        $og_title: 'Free Online Courses on SkillUp by Simplilearn',
        $og_description: 'Join me on SkillUp and get access to 100+ free courses with completion certificates. Learn the most in-demand digital skills for free and get ahead in your career',
        $og_image_url: this.configService.get('skillUpSimpliLogo'),
        $og_type: 'article',
        $twitter_card: 'summary_large_image',
        $web_only: true,
      };

      const params = {
        branch_key: this.configService.get('branchKey'),
        data: JSON.stringify(data),
      };

      if (campaign) {
        params['campaign'] = campaign;
      }
      if (source) {
        params['channel'] = source;
      }
      if (medium) {
        params['feature'] = medium;
      }
      this.resetUri();
      this.url = '';
      const url = this.configService.get('branchApiUrl') + this.configService.get('branchApiName');
      this.setRequestHeader('Content-Type', 'application/json');
      this.setFormData(params);
      const result = await this.post(url);
      const response = result;
      let urlData = [];
      if (response?.data) {
        urlData = response.data;
        urlData['url'] = urlData['url'].replace(
          this.configService.get('skillUpReplaceString'),
          this.configService.get('skillUpReplaceStringWith'),
          urlData['url'],
        );
        return urlData;
      } else if (response?.url) {
        urlData['url'] = response.url.replace(
          this.configService.get('skillUpReplaceString'),
          this.configService.get('skillUpReplaceStringWith'),
          urlData['url'],
        );
        return urlData;
      }
    } catch (error: any) {
      Logger.error('getSkillupReferralInviteLink', {
        METHOD: this.constructor?.name + '@' + this.getSkillupReferralInviteLink.name,
        MESSAGE: error.message,
        REQUEST: {},
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async getNewOrEncryptedUser(userInfo) {
    try {
      let externalId;
      let temail = '';
      let isNewOrEncryptedUser = false;
      if (userInfo?.data?.members?.totalCount !== undefined) {
        if (userInfo.data.members.totalCount > 0) {
          const edgeInfo = userInfo?.data?.members?.edges[0];
          externalId = edgeInfo?.node?.externalId;
          temail = edgeInfo?.node?.email;

          if (typeof externalId === 'number' || externalId === null) {
            isNewOrEncryptedUser = false;
          } else {
            const decryptEmail = this.cryptoHelper.decrypt(externalId);
            if (isEmail(decryptEmail) && temail === `${externalId}@simplilearn.com`) {
              isNewOrEncryptedUser = true;
            }
          }
        } else {
          isNewOrEncryptedUser = true;
        }
      } else {
        isNewOrEncryptedUser = true;
      }
      return { isNewOrEncryptedUser, externalId, temail };
    } catch (error: any) {
      Logger.error('getNewOrEncryptedUser', {
        METHOD: this.constructor?.name + '@' + this.getNewOrEncryptedUser.name,
        MESSAGE: error.message,
        REQUEST: {},
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async getUserDataForTokenCreation(
    user: any,
    result: { isNewOrEncryptedUser: boolean; externalId: any; temail: any },
    userEmail: string,
  ): Promise<object> {
    try {
      let userData: object;
      const isNewOrEncryptedUser = result?.isNewOrEncryptedUser;
      const externalId = result?.externalId;
      const temail = result?.temail;
      if (isNewOrEncryptedUser) {
        const encryptedEmail = this.cryptoHelper.encrypt(userEmail);
        const maxLength = 60;
        const email = `${encryptedEmail.slice(0, maxLength - '@simplilearn.com'.length)}@simplilearn.com`;
        userData = {
          sub: this.cryptoHelper.encrypt(userEmail),
          email: email,
          name: user?.name,
        };
      } else {
        userData = {
          sub: externalId || this.cryptoHelper.encrypt(userEmail),
          email: temail,
          name: user?.name,
        };
      }
      return userData;
    } catch (error: any) {
      Logger.error('getUserDataForTokenCreation', {
        METHOD: this.constructor?.name + '@' + this.getUserDataForTokenCreation.name,
        MESSAGE: error.message,
        REQUEST: {},
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  //  write the comment for this function
  async signUpNewUser(param: CommunityUserSignup): Promise<{
    status: boolean;
    data: any;
  }> {
    const userType = '';
    const assignRoles: string[] = param?.roles.map((data) => data['roleName']);
    const userGroupId: string =
      (!userType && userType.toUpperCase() === 'ATP') || assignRoles.includes('looper_affiliate_student')
        ? this.configService.get('communityAtpUserGroupId')
        : this.configService.get('communityDefaultUserGroupId');
    try {
      const requestParams = {
        email: param?.email,
        userName: param?.name,
        user_group_id: userGroupId,
      };

      this.setQueryParams(requestParams);

      this.url = this.configService.get<string>('communityBaseUrl');

      const response = await this.post('comm_api.php');

      const result = response?.data;
      if (result && result?.status !== 'success' && param?.logFailedUser) {
        await this.insertFailedCommunityUsers(requestParams);
        return { status: false, data: result };
      }

      return { status: true, data: result };
    } catch (error: any) {
      const requestParams = {
        email: param?.email,
        userName: param?.name,
        user_group_id: userGroupId,
      };
      await this.insertFailedCommunityUsers(requestParams);
      Logger.error('signUpNewUser', {
        METHOD: this.constructor?.name + '@' + this.signUpNewUser.name,
        MESSAGE: error.message,
        REQUEST: { requestParams },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { status: false, data: {} };
    }
  }

  async insertFailedCommunityUsers(params: { email: string; userName: string; user_group_id: string }): Promise<{
    status: boolean;
    msg: string;
  }> {
    try {
      const currentTime: number = Date.now();
      const email: string = params?.email;
      const inputParams: FailedCommunityUserType = {
        email,
        user_data: JSON.stringify(params),
        is_processed: 0,
        status: 0,
        creation_time: currentTime,
        modified_time: currentTime,
      };

      const response: any = await this.failedCommunityUserRepository.insert(inputParams);
      // Step 4: Check `varResult`
      if (!response) {
        const filePath = 'logs/failed_community_user_list.log';
        fs.appendFileSync(filePath, Object.values(params).join(',') + '\n');
      }

      return { status: !!response, msg: 'success' };
    } catch (error: any) {
      Logger.error('insertFailedCommunityUser', {
        METHOD: `${this.constructor.name}@${this.insertFailedCommunityUsers.name}`,
        MESSAGE: error.message,
        REQUEST: params,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { status: false, msg: 'failed' };
    }
  }
}
