import { Test, TestingModule } from '@nestjs/testing';
import { UserMgmtCommunityService } from './usermgmt.community.service';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../../helper/helper.service';
import { FailedCommunityUser } from '../../../db/mysql/entity/failed-community-user.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import axios from 'axios';
// import fs from 'fs';
import { Logger } from '../../../logging/logger';
import { CommunityUserSignup } from '../../../common/typeDef/auth.type';

// Mock external dependencies
jest.mock('axios');
jest.mock('fs');
jest.mock('../../../logging/logger');

const mockedAxios = axios as jest.Mocked<typeof axios>;
// const mockedFs = fs as jest.Mocked<typeof fs>;

describe('UserMgmtCommunityService', () => {
  let userMgmtCommunityService: UserMgmtCommunityService;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config = {
        tribeAPIEndpoint: 'http://tribe.api',
        tribeCommunityBaseUrl: 'http://community.tribe',
        tribePrivateKey: 'a-secret-key',
        tribeApiNetworkId: 'network-id',
        tribeAPIEndpointWithoutCredentials: 'http://tribe.api.nocreds',
        credentialsForTribe: 'include',
        branchKey: 'branch-key',
        branchApiUrl: 'http://branch.api',
        branchApiName: '/v1/url',
        skillUpSimpliLogo: 'http://logo.url',
        skillUpReplaceString: 'skillup.page.link',
        skillUpReplaceStringWith: 'skillup.simplilearn.com',
        communityAtpUserGroupId: 'atp-group-id',
        communityDefaultUserGroupId: 'default-group-id',
        communityBaseUrl: 'http://community.base',
      };
      return config[key];
    }),
  };

  const mockCryptoHelper = {
    encrypt: jest.fn().mockImplementation((text) => `encrypted-${text}`),
    decrypt: jest.fn().mockImplementation((text) => text.replace('encrypted-', '')),
  };

  const mockAuthTokenHelper = {
    createSignedToken: jest.fn().mockResolvedValue('signed-jwt-token'),
  };

  const mockHelperService = {
    get: jest.fn().mockResolvedValue(mockAuthTokenHelper),
  };

  const mockFailedCommunityUserRepository = {
    insert: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserMgmtCommunityService,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: 'TRIBE_CRYPTO_HELPER', useValue: mockCryptoHelper },
        { provide: HelperService, useValue: mockHelperService },
        {
          provide: getRepositoryToken(FailedCommunityUser),
          useValue: mockFailedCommunityUserRepository,
        },
      ],
    }).compile();

    userMgmtCommunityService = module.get<UserMgmtCommunityService>(UserMgmtCommunityService);
    
    // Spy on methods of the service itself to mock parent class behavior or internal calls
    jest.spyOn(userMgmtCommunityService, 'createTribeToken');
    jest.spyOn(userMgmtCommunityService, 'generateToken');
    jest.spyOn(userMgmtCommunityService, 'queryTribe');
    
    // Mock parent Communication methods
    jest.spyOn(Object.getPrototypeOf(userMgmtCommunityService), 'post').mockResolvedValue({ data: {} });
    jest.spyOn(Object.getPrototypeOf(userMgmtCommunityService), 'setQueryParams').mockReturnThis();
    jest.spyOn(Object.getPrototypeOf(userMgmtCommunityService), 'setFormData').mockReturnThis();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(userMgmtCommunityService).toBeDefined();
  });

  describe('getTribeSSOLink', () => {
    it('should return a valid SSO link on success', async () => {
      const user = { id: '1', email: '<EMAIL>', name: 'Test User' };
      (userMgmtCommunityService.createTribeToken as jest.Mock).mockResolvedValueOnce('test-token');
      const link = await userMgmtCommunityService.getTribeSSOLink(user, '/dashboard');
      const encodedToken = encodeURIComponent('test-token');
      expect(link).toBe(`http://community.tribe/api/auth/sso?jwt=${encodedToken}&redirect_uri=/dashboard`);
    });

    it('should log an error and return undefined if createTribeToken fails', async () => {
      const user = { id: '1', email: '<EMAIL>', name: 'Test User' };
      (userMgmtCommunityService.createTribeToken as jest.Mock).mockRejectedValueOnce(new Error('Token creation failed'));
      const link = await userMgmtCommunityService.getTribeSSOLink(user);
      expect(Logger.error).toHaveBeenCalledWith('getTribeSSOLink', expect.any(Object));
      expect(link).toBeUndefined();
    });
  });

  describe('createTribeToken', () => {
    const user = { id: '1', email: '<EMAIL>', name: 'Test User' };

    it('should log an error if user details are invalid', async () => {
      await userMgmtCommunityService.createTribeToken({ id: null, email: null, name: null });
      expect(Logger.error).toHaveBeenCalledWith('createTribeToken', expect.objectContaining({ MESSAGE: 'Invalid request to create Tribe token' }));
    });

    it('should return a signed token for a new user', async () => {
        (userMgmtCommunityService.generateToken as jest.Mock).mockResolvedValueOnce('api-token');
        (userMgmtCommunityService.queryTribe as jest.Mock).mockResolvedValueOnce({ data: { members: { totalCount: 0 } } });
        const token = await userMgmtCommunityService.createTribeToken(user);
        expect(mockCryptoHelper.encrypt).toHaveBeenCalledWith(user.email);
        expect(mockAuthTokenHelper.createSignedToken).toHaveBeenCalled();
        expect(token).toBe('signed-jwt-token');
    });

    it('should return a signed token for an existing, non-encrypted user', async () => {
        (userMgmtCommunityService.generateToken as jest.Mock).mockResolvedValueOnce('api-token');
        const tribeResponse = {
            data: {
                members: {
                    totalCount: 1,
                    edges: [{ node: { externalId: 123, email: '<EMAIL>' } }],
                },
            },
        };
        (userMgmtCommunityService.queryTribe as jest.Mock).mockResolvedValueOnce(tribeResponse);
        const token = await userMgmtCommunityService.createTribeToken(user);
        expect(mockAuthTokenHelper.createSignedToken).toHaveBeenCalledWith(
            { sub: 123, email: '<EMAIL>', name: user.name },
            expect.any(Object)
        );
        expect(token).toBe('signed-jwt-token');
    });

    it('should log an error and return undefined on failure', async () => {
        (userMgmtCommunityService.generateToken as jest.Mock).mockRejectedValueOnce(new Error('API error'));
        const token = await userMgmtCommunityService.createTribeToken(user);
        expect(Logger.error).toHaveBeenCalledWith('createTribeToken', expect.any(Object));
        expect(token).toBeUndefined();
    });
  });

  describe('generateToken', () => {
    it('should return an access token on success', async () => {
        const postSpy = jest.spyOn(Object.getPrototypeOf(userMgmtCommunityService), 'post').mockResolvedValueOnce({
            data: { limitedToken: { accessToken: 'tribe-access-token' } },
        });
        const token = await userMgmtCommunityService.generateToken();
        expect(token).toBe('tribe-access-token');
        expect(postSpy).toHaveBeenCalled();
    });

    it('should return an empty string if accessToken is not in response', async () => {
        jest.spyOn(Object.getPrototypeOf(userMgmtCommunityService), 'post').mockResolvedValueOnce({ data: {} });
        const token = await userMgmtCommunityService.generateToken();
        expect(token).toBe('');
    });

    it('should log an error and return undefined on failure', async () => {
        jest.spyOn(Object.getPrototypeOf(userMgmtCommunityService), 'post').mockRejectedValueOnce(new Error('API error'));
        const token = await userMgmtCommunityService.generateToken();
        expect(Logger.error).toHaveBeenCalledWith('generateToken', expect.any(Object));
        expect(token).toBeUndefined();
    });
  });

  describe('queryTribe', () => {
    it('should return data on successful API call', async () => {
        mockedAxios.post.mockResolvedValueOnce({ data: { success: true } });
        const result = await userMgmtCommunityService.queryTribe('query', 'token');
        expect(result).toEqual({ success: true });
        expect(mockedAxios.post).toHaveBeenCalled();
    });

    it('should return an empty array if response has no data', async () => {
        mockedAxios.post.mockResolvedValueOnce({});
        const result = await userMgmtCommunityService.queryTribe('query', 'token');
        expect(result).toEqual([]);
    });

    it('should log an error and return undefined on failure', async () => {
        mockedAxios.post.mockRejectedValueOnce(new Error('Axios error'));
        const result = await userMgmtCommunityService.queryTribe('query', 'token');
        expect(Logger.error).toHaveBeenCalledWith('queryTribe', expect.any(Object));
        expect(result).toBeUndefined();
    });
  });

  describe('getNewOrEncryptedUser', () => {
    it('should identify a new user when totalCount is 0', async () => {
        const userInfo = { data: { members: { totalCount: 0 } } };
        const result = await userMgmtCommunityService.getNewOrEncryptedUser(userInfo);
        expect(result.isNewOrEncryptedUser).toBe(true);
    });

    it('should identify an existing user with numeric externalId', async () => {
        const userInfo = { data: { members: { totalCount: 1, edges: [{ node: { externalId: 123 } }] } } };
        const result = await userMgmtCommunityService.getNewOrEncryptedUser(userInfo);
        expect(result.isNewOrEncryptedUser).toBe(false);
    });

    it('should identify an encrypted user', async () => {
        const encryptedEmail = '<EMAIL>';
        const userInfo = {
            data: {
                members: {
                    totalCount: 1,
                    edges: [{ node: { externalId: encryptedEmail, email: `${encryptedEmail}@simplilearn.com` } }],
                },
            },
        };
        mockCryptoHelper.decrypt.mockReturnValueOnce('<EMAIL>');
        const result = await userMgmtCommunityService.getNewOrEncryptedUser(userInfo);
        expect(result.isNewOrEncryptedUser).toBe(true);
    });
  });

  describe('getUserDataForTokenCreation', () => {
    const user = { name: 'Test User' };
    const userEmail = '<EMAIL>';

    it('should create data for a new user', async () => {
        const result = { isNewOrEncryptedUser: true, externalId: null, temail: null };
        const userData = await userMgmtCommunityService.getUserDataForTokenCreation(user, result, userEmail);
        expect(userData).toHaveProperty('sub', '<EMAIL>');
        expect(userData).toHaveProperty('email');
    });

    it('should create data for an existing user', async () => {
        const result = { isNewOrEncryptedUser: false, externalId: '123', temail: '<EMAIL>' };
        const userData = await userMgmtCommunityService.getUserDataForTokenCreation(user, result, userEmail);
        expect(userData).toEqual({ sub: '123', email: '<EMAIL>', name: 'Test User' });
    });
  });

  describe('signUpNewUser', () => {
    const param: CommunityUserSignup = { email: '<EMAIL>', name: 'New User', roles: [], logFailedUser: true, password: 'password', userGroupId: 'default-group-id', user_agent: 'jest', ip_address: '127.0.0.1' };

    it('should sign up a user and return success', async () => {
        const postSpy = jest.spyOn(Object.getPrototypeOf(userMgmtCommunityService), 'post').mockResolvedValueOnce({ data: { status: 'success' } });
        const result = await userMgmtCommunityService.signUpNewUser(param);
        expect(result).toEqual({ status: true, data: { status: 'success' } });
        expect(postSpy).toHaveBeenCalled();
        expect(mockFailedCommunityUserRepository.insert).not.toHaveBeenCalled();
    });

    it('should handle signup failure and log the user', async () => {
        jest.spyOn(Object.getPrototypeOf(userMgmtCommunityService), 'post').mockResolvedValueOnce({ data: { status: 'failure' } });
        const result = await userMgmtCommunityService.signUpNewUser(param);
        expect(result.status).toBe(false);
        expect(mockFailedCommunityUserRepository.insert).toHaveBeenCalled();
    });

    it('should use default user group id', async () => {
        const setQuerySpy = jest.spyOn(Object.getPrototypeOf(userMgmtCommunityService), 'setQueryParams');
        await userMgmtCommunityService.signUpNewUser(param);
        expect(setQuerySpy).toHaveBeenCalledWith(expect.objectContaining({
            user_group_id: 'default-group-id'
        }));
    });

    it('should handle ATP user group id', async () => {
        const atpParam: CommunityUserSignup = { ...param, roles: [] };
        const setQuerySpy = jest.spyOn(Object.getPrototypeOf(userMgmtCommunityService), 'setQueryParams');
        await userMgmtCommunityService.signUpNewUser(atpParam);
        expect(setQuerySpy).toHaveBeenCalledWith(expect.objectContaining({
            user_group_id: 'default-group-id'
        }));
    });

    it('should handle API error and log the user', async () => {
        jest.spyOn(Object.getPrototypeOf(userMgmtCommunityService), 'post').mockRejectedValueOnce(new Error('API Error'));
        const result = await userMgmtCommunityService.signUpNewUser(param);
        expect(result.status).toBe(false);
        expect(mockFailedCommunityUserRepository.insert).toHaveBeenCalled();
        expect(Logger.error).toHaveBeenCalledWith('signUpNewUser', expect.any(Object));
    });
  });

  describe('insertFailedCommunityUsers', () => {
    const params = { email: '<EMAIL>', userName: 'Fail User', user_group_id: 'default-group-id' };

    it('should handle repo error', async () => {
        mockFailedCommunityUserRepository.insert.mockRejectedValueOnce(new Error('DB Error'));
        const result = await userMgmtCommunityService.insertFailedCommunityUsers(params);
        expect(result).toEqual({ status: false, msg: 'failed' });
        expect(Logger.error).toHaveBeenCalledWith('insertFailedCommunityUser', expect.any(Object));
    });
  });

  describe('getSkillupReferralInviteLink', () => {
    it('should call post with correct params and return modified url', async () => {
        const postSpy = jest.spyOn(Object.getPrototypeOf(userMgmtCommunityService), 'post').mockResolvedValueOnce({
            data: { url: 'http://skillup.page.link/abc' }
        });
        const setFormSpy = jest.spyOn(Object.getPrototypeOf(userMgmtCommunityService), 'setFormData');
        const result = await userMgmtCommunityService.getSkillupReferralInviteLink('source', 'medium', 'campaign', 'ref123', 'http://skillup.url/');
        
        expect(setFormSpy).toHaveBeenCalled();
        const formData = setFormSpy.mock.calls[0][0] as { branch_key: string, campaign: string, channel: string, feature: string };
        expect(formData.branch_key).toBe('branch-key');
        expect(formData.campaign).toBe('campaign');
        expect(formData.channel).toBe('source');
        expect(formData.feature).toBe('medium');
                
        expect(postSpy).toHaveBeenCalled();
        expect(result.url).toBe('http://skillup.simplilearn.com/abc');
    });
  });
});