import { Test, TestingModule } from '@nestjs/testing';
import { UserService } from './user.service';
import { IUserRepository, UserRepository } from '../repositories/user/user.repository';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import { ChangePasswordDto } from '../../internal/dto/change-password.dto';
import { BadRequestException } from '@nestjs/common';
import { SetLearnerPasswordDto } from '../../internal/dto/set-learner-password.dto';
import { IRoleRepository, RoleRepository } from '../repositories/role/role.repository';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';
import { ChangeLogService } from '../../common/services/communication/changelog/changelog.service';
import { DeviantsService } from '../../common/services/communication/deviants/deviants.service';
import { AuthTokenHelper } from '../../auth/helper/auth.tokenhelper';
import { CachingService } from '../../caching/caching.service';
import { Utility } from '../../common/util/utility';
import { APILog } from '../../logging/logger';
import * as drupalHash from 'drupal-hash';

// Mock modules
jest.mock('../../logging/logger', () => ({
  Logger: {
    error: jest.fn(),
    log: jest.fn(),
    info: jest.fn(),
  },
  APILog: {
    log: jest.fn(),
  },
}));
jest.mock('drupal-hash', () => ({
  checkPassword: jest.fn(),
  hashPassword: jest.fn(),
}));
jest.mock('../../common/util/utility', () => ({
  Utility: {
    isEmpty: jest.fn(),
    emailDomainExtractor: jest.fn(),
  },
}));

describe('UserService', () => {
  let userService: UserService;
  let userRepositoryMock: jest.Mocked<IUserRepository>;
  let roleRepositoryMock: jest.Mocked<IRoleRepository>;
  let configServiceMock: jest.Mocked<ConfigService>;
  let helperServiceMock: jest.Mocked<HelperService>;
  let paperclipServiceMock: jest.Mocked<PaperclipService>;
  let changeLogServiceMock: jest.Mocked<ChangeLogService>;
  let deviantsServiceMock: jest.Mocked<DeviantsService>;
  let authTokenHelperMock: jest.Mocked<AuthTokenHelper>;
  let cachingServiceMock: jest.Mocked<CachingService>;

  // Mock helpers
  const userHelperMock = {
    prepareAssignRoleList: jest.fn(),
    createCloud6SentinelUser: jest.fn(),
    prepareSentinelUserRoleList: jest.fn(),
    syncCloud6SentinelUserRole: jest.fn(),
    updateUserLoginTime: jest.fn(),
    createUserDataWithMySQLDrupal: jest.fn(),
    changePasswordAttemptLimit: jest.fn(),
    syncUserDataWithMySQLDrupal: jest.fn(),
    updateCloud6SentinelByUidOrMail: jest.fn(),
    isUserMemberOfGroup: jest.fn(),
    prepareRoleListForDrupalSync: jest.fn(),
  };
  const userMgmtHelperMock = {
    prepareAssignRoleList: jest.fn(),
    prepareSentinelUserRoleList: jest.fn(),
  };
  const gdprRequestHelperMock = {
    addRequest: jest.fn(),
  };
  const lrsHelperMock = {
    sendDataToLrs: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        { provide: UserRepository, useValue: {
            findByUID: jest.fn(),
            findOneAndUpdate: jest.fn(),
            getUserByEmail: jest.fn(),
            saveUser: jest.fn(),
            deleteOneById: jest.fn(),
        } },
        { provide: ConfigService, useValue: { get: jest.fn() } },
        {
          provide: HelperService,
          useValue: { get: jest.fn(), getHelper: jest.fn() },
        },
        { provide: RoleRepository, useValue: { findAll: jest.fn() } },
        { provide: PaperclipService, useValue: { invalidateTokenForAppUser: jest.fn(), expireAppUsertoken: jest.fn() } },
        { provide: ChangeLogService, useValue: { changeLogRequest: jest.fn() } },
        { provide: DeviantsService, useValue: { pushMessageToQueue: jest.fn() } },
        { provide: AuthTokenHelper, useValue: { generateSessionTokens: jest.fn() } },
        { provide: CachingService, useValue: { invalidateCache: jest.fn() } },
      ],
    }).compile();

    userService = module.get<UserService>(UserService);
    userRepositoryMock = module.get(UserRepository);
    configServiceMock = module.get(ConfigService);
    helperServiceMock = module.get(HelperService);
    roleRepositoryMock = module.get(RoleRepository);
    paperclipServiceMock = module.get(PaperclipService);
    changeLogServiceMock = module.get(ChangeLogService);
    deviantsServiceMock = module.get(DeviantsService);
    authTokenHelperMock = module.get(AuthTokenHelper);
    cachingServiceMock = module.get(CachingService);


    // Setup mock implementations for helpers
    (helperServiceMock.getHelper as jest.Mock).mockImplementation(async (helperName: string) => {
      if (helperName === 'UserHelper') return userHelperMock;
      if (helperName === 'UserMgmtUtilityHelper') return userMgmtHelperMock;
      if (helperName === 'GdprRequestHelper') return gdprRequestHelperMock;
      if (helperName === 'lrsHelper') return lrsHelperMock;
      return {};
    });
     // Setup mock implementations for services from helper
     (helperServiceMock.get as jest.Mock).mockImplementation(async (service: any) => {
        if (service === UserRepository) return userRepositoryMock;
        if (service === CachingService) return cachingServiceMock;
        if (service === PaperclipService) return paperclipServiceMock;
        if (service === ChangeLogService) return changeLogServiceMock;
        if (service === DeviantsService) return deviantsServiceMock;
        if (service === AuthTokenHelper) return authTokenHelperMock;
        if (service === RoleRepository) return roleRepositoryMock;
        return {};
      });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(userService).toBeDefined();
  });

  describe('userRegistration', () => {
    const signUpDto = {
      email: '<EMAIL>',
      password: 'password123',
      name: 'Test User',
    };

    it('should throw BadRequestException if user already exists', async () => {
      userRepositoryMock.getUserByEmail.mockResolvedValue(signUpDto as any);
      await expect(userService.userRegistration(signUpDto)).rejects.toThrow(
        new BadRequestException('UserAlreadyExist'),
      );
    });

    it('should register a user and sync with Drupal if enabled', async () => {
      userRepositoryMock.getUserByEmail.mockResolvedValue(null);
      userRepositoryMock.saveUser.mockResolvedValue({ _id: 'mongoId', ...signUpDto } as any);
      (drupalHash.hashPassword as jest.Mock).mockReturnValue('hashedPassword');
      configServiceMock.get.mockReturnValue(true); // enableDrupalSync
      userMgmtHelperMock.prepareAssignRoleList.mockResolvedValue([]);
      jest.spyOn(userService, 'syncCloud6SentinelUser').mockResolvedValue({ uid: '123' });
      userMgmtHelperMock.prepareSentinelUserRoleList.mockResolvedValue([]);
      userRepositoryMock.findOneAndUpdate.mockResolvedValue({ ...signUpDto, uid: 123 } as any);


      const result = await userService.userRegistration(signUpDto);

      expect(userRepositoryMock.saveUser).toHaveBeenCalledWith({
        ...signUpDto,
        roles: [],
        password: 'hashedPassword',
      });
      expect(userService.syncCloud6SentinelUser).toHaveBeenCalled();
      expect(userHelperMock.createUserDataWithMySQLDrupal).toHaveBeenCalled();
      expect(result).toHaveProperty('uid', 123);
    });

    it('should throw BadRequestException if syncCloud6SentinelUser fails', async () => {
        userRepositoryMock.getUserByEmail.mockResolvedValue(null);
        userRepositoryMock.saveUser.mockResolvedValue({ _id: 'mongoId', ...signUpDto } as any);
        (drupalHash.hashPassword as jest.Mock).mockReturnValue('hashedPassword');
        userMgmtHelperMock.prepareAssignRoleList.mockResolvedValue([]);
        jest.spyOn(userService, 'syncCloud6SentinelUser').mockResolvedValue(null); // Simulate failure
  
        await expect(userService.userRegistration(signUpDto)).rejects.toThrow(
          new BadRequestException(),
        );
        expect(userRepositoryMock.deleteOneById).toHaveBeenCalledWith('mongoId');
      });
  });

  describe('changePassword', () => {
    const changePasswordDto: ChangePasswordDto = {
      cur_passwd: 'oldPassword',
      user_id: 123,
      gid: 1123,
      new_passwd: 'newPassword',
      confirm_passwd: 'newPassword',
    };
    const userMock = {
      password: 'hashedOldPassword',
      email: '<EMAIL>',
      status: 1,
      uid: 123,
      user_groups: [1123],
    };

    beforeEach(() => {
        userRepositoryMock.findByUID.mockResolvedValue(userMock as any);
        (drupalHash.checkPassword as jest.Mock).mockReturnValue(true);
        userHelperMock.changePasswordAttemptLimit.mockResolvedValue({ status: 'ok' });
        userRepositoryMock.findOneAndUpdate.mockResolvedValue(true as any);
        (drupalHash.hashPassword as jest.Mock).mockReturnValue('hashedNewPassword');
        userHelperMock.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
        (changeLogServiceMock.changeLogRequest as jest.Mock).mockResolvedValue({} as any);
    });

    it('should change password successfully', async () => {
        const result = await userService.changePassword(changePasswordDto);
        expect(result).toBe(true);
    });

    it('should throw error for incorrect current password', async () => {
        (drupalHash.checkPassword as jest.Mock).mockReturnValue(false);
        await expect(userService.changePassword(changePasswordDto)).rejects.toThrow(BadRequestException);
    });

    it('should throw error if user not found or inactive', async () => {
        userRepositoryMock.findByUID.mockResolvedValue(null);
        await expect(userService.changePassword(changePasswordDto)).rejects.toThrow(BadRequestException);

        userRepositoryMock.findByUID.mockResolvedValue({ ...userMock, status: 0 } as any);
        await expect(userService.changePassword(changePasswordDto)).rejects.toThrow(BadRequestException);
    });
    
    it('should throw error if user is not a group member', async () => {
        (Utility.isEmpty as jest.Mock).mockReturnValue(false);
        configServiceMock.get.mockReturnValue(9999); // different defaultGroupId
        userRepositoryMock.findByUID.mockResolvedValue({ ...userMock, user_groups: [1] } as any);
        await expect(userService.changePassword(changePasswordDto)).rejects.toThrow(BadRequestException);
    });

    it('should throw error if attempt limit is exceeded', async () => {
        userHelperMock.changePasswordAttemptLimit.mockResolvedValue({ status: 'limit_exceeded' });
        await expect(userService.changePassword(changePasswordDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('setLearnerPassword', () => {
    const setLearnerPasswordDto: SetLearnerPasswordDto = {
        email: '<EMAIL>',
        user_pwd: 'newPassword123',
        gids: ['2381'],
        rids: ['2345'],
        access: 1743576498,
        login: 1743576498,
        user_options: 0,
        client_id: 'testClient',
      };
      const userMock = {
        email: '<EMAIL>',
        uid: 12345,
        status: true,
        roles: [],
        display_name: 'Test User',
      };

      beforeEach(() => {
        userRepositoryMock.getUserByEmail.mockResolvedValue(userMock as any);
        userHelperMock.isUserMemberOfGroup.mockResolvedValue(true);
        (drupalHash.hashPassword as jest.Mock).mockReturnValue('hashedPassword');
        roleRepositoryMock.findAll.mockResolvedValue([{ rid: '2345' }] as any);
        userRepositoryMock.findOneAndUpdate.mockResolvedValue(true as any);
        (authTokenHelperMock.generateSessionTokens as jest.Mock).mockResolvedValue({ idToken: 'mockToken', userData: {} });
      });

      it('should set learner password successfully', async () => {
        const result = await userService.setLearnerPassword(setLearnerPasswordDto);
        expect(result).toBe(true);
        expect(lrsHelperMock.sendDataToLrs).toHaveBeenCalled();
      });

      it('should throw error if user not found', async () => {
        userRepositoryMock.getUserByEmail.mockResolvedValue(null);
        await expect(userService.setLearnerPassword(setLearnerPasswordDto)).rejects.toThrow(BadRequestException);
      });
  });

  describe('syncCloud6SentinelUser', () => {
    const signupDetail = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };

      it('should sync user and return uid', async () => {
        (drupalHash.hashPassword as jest.Mock).mockReturnValue('hashedPassword');
        userHelperMock.createCloud6SentinelUser.mockResolvedValue({ uid: 123 });
        const result = await userService.syncCloud6SentinelUser(signupDetail);
        expect(result).toEqual({ uid: '123' });
      });

      it('should throw if user creation fails', async () => {
        userHelperMock.createCloud6SentinelUser.mockResolvedValue(null);
        await expect(userService.syncCloud6SentinelUser(signupDetail)).rejects.toThrow(
            new BadRequestException('UserNotCreatedException'),
        );
      });
  });

  describe('confirmDeleteAccount', () => {
    const userId = 123;
    const userMock = { uid: userId, email: '<EMAIL>', status: 1 };

    beforeEach(() => {
        userRepositoryMock.findByUID.mockResolvedValue(userMock as any);
    });

    it('should fail if userId is null', async () => {
        const result = await userService.confirmDeleteAccount(null);
        expect(result).toEqual({ type: 'failed', msg: 'Invalid request' });
    });

    it('should fail if user is not found or inactive', async () => {
        userRepositoryMock.findByUID.mockResolvedValue(null);
        let result = await userService.confirmDeleteAccount(userId);
        expect(result).toEqual({ type: 'failed', msg: 'Invalid request' });

        userRepositoryMock.findByUID.mockResolvedValue({ ...userMock, status: 0 } as any);
        result = await userService.confirmDeleteAccount(userId);
        expect(result).toEqual({ type: 'failed', msg: 'Invalid request' });
    });

    it('should delete account successfully', async () => {
        userRepositoryMock.findOneAndUpdate.mockResolvedValue(true as any);
        userHelperMock.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);

        const result = await userService.confirmDeleteAccount(userId);

        expect(deviantsServiceMock.pushMessageToQueue).toHaveBeenCalled();
        expect(result).toEqual({ type: 'success', msg: 'User account has been blocked successfully.' });
    });

    it('should fail if deactivating user fails', async () => {
        userRepositoryMock.findOneAndUpdate.mockResolvedValue(false as any);
        const result = await userService.confirmDeleteAccount(userId);
        expect(result).toEqual({ type: 'failed', msg: 'Some error occured. Please try again after sometime.' });
        expect(APILog.log).toHaveBeenCalled();
    });
  });
});