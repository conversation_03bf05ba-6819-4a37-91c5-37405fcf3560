import { Module } from '@nestjs/common';
import { UserService } from './services/user.service';
import { UserController } from './controllers/user/user.controller';
import { ProfileController } from './controllers/profile/profile.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from '../db/mongo/schema/user/user.schema';
import { UserToken, UserTokenSchema } from '../db/mongo/schema/user-token/user.token.schema';
import { Role, RoleSchema } from '../db/mongo/schema/roles/role.schema';
import { UserMgmtCommunityService } from '../user/services/communication/usermgmt.community.service';
import { FailedCommunityUser } from '../db/mysql/entity/failed-community-user.entity';
import { UserRepository } from './repositories/user/user.repository';
import { RoleRepository } from './repositories/role/role.repository';
import { TaxonomyRepository } from './repositories/taxonomy/taxonomy.repository';
import { Taxonomies, TaxonomySchema } from '../db/mongo/schema/taxonomies/taxonomies.schema';

@Module({
  imports: [
    TypeOrmModule.forFeature([FailedCommunityUser]),

    MongooseModule.forFeature([{ name: Role.name, schema: RoleSchema, collection: 'roles' }], 'default'),
    MongooseModule.forFeature([{ name: Role.name, schema: RoleSchema, collection: 'roles' }], 'secure'),
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema, collection: 'users' }], 'secure'),
    MongooseModule.forFeature(
      [
        {
          name: UserToken.name,
          schema: UserTokenSchema,
          collection: 'usertokens',
        },
      ],
      'secure',
    ),
    MongooseModule.forFeature([{ name: Taxonomies.name, schema: TaxonomySchema, collection: 'taxonomies' }], 'default'),
  ],
  controllers: [UserController, ProfileController],
  providers: [UserRepository, RoleRepository, UserService, JwtService, UserMgmtCommunityService, TaxonomyRepository],
  exports: [UserService],
})
export class UserModule {}
