import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Category, Taxonomies } from '../../../db/mongo/schema/taxonomies/taxonomies.schema';

export interface ITaxonomyRepository {
  find(params: any): Promise<Taxonomies[]>;
  findOne(params: any): Promise<Taxonomies>;
  findByCategory(category: Category): Promise<Taxonomies | null>;
  createTaxonomy(taxonomy: Taxonomies): Promise<Taxonomies>;
  updateTaxonomy(category: Category, updates: Partial<Taxonomies>): Promise<Taxonomies>;
  aggregate(aggregateQuery);
}
@Injectable()
export class TaxonomyRepository implements ITaxonomyRepository {
  @InjectModel(Taxonomies.name, 'default') private taxonomyModel: Model<Taxonomies>;

  async find(params: any): Promise<Taxonomies[]> {
    return await this.taxonomyModel.find(params).exec();
  }

  async findOne(params: any): Promise<Taxonomies> {
    return await this.taxonomyModel.findOne(params).exec();
  }

  async findByCategory(category: Category): Promise<Taxonomies | null> {
    return await this.taxonomyModel.findOne({ category }).exec();
  }

  async createTaxonomy(taxonomy: Taxonomies): Promise<Taxonomies> {
    const createdTaxonomy = new this.taxonomyModel(taxonomy);
    return await createdTaxonomy.save();
  }

  async updateTaxonomy(category: Category, updates: Partial<Taxonomies>): Promise<Taxonomies> {
    const updatedTaxonomy = await this.taxonomyModel.findOneAndUpdate({ category }, { $set: updates }, { new: true });

    if (!updatedTaxonomy) {
      throw new NotFoundException(`Taxonomy with category ${category} not found`);
    }

    return updatedTaxonomy;
  }

  async aggregate(aggregateQuery) {
    return await this.taxonomyModel.aggregate(aggregateQuery).exec();
  }
}
