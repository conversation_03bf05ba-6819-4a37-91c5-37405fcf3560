
import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { TaxonomyRepository } from './taxonomy.repository';
import { Taxonomies } from '../../../db/mongo/schema/taxonomies/taxonomies.schema';
import { NotFoundException } from '@nestjs/common';
import { Category } from '../../../db/mongo/schema/taxonomies/taxonomies.schema';
import { Types, Model } from 'mongoose';

describe('TaxonomyRepository', () => {
  let repository: TaxonomyRepository;

  const mockTaxonomy = {
    category: 'TEST_CATEGORY',
    name: 'Test Taxonomy',
    description: 'Test Description'
  };

  const mockModel = {
    find: jest.fn(),
    findOne: jest.fn(),
    findOneAndUpdate: jest.fn(),
    aggregate: jest.fn(),
    save: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TaxonomyRepository,
        {
          provide: getModelToken(Taxonomies.name, 'default'),
          useValue: mockModel
        },
      ],
    }).compile();

    repository = module.get<TaxonomyRepository>(TaxonomyRepository);
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  describe('find', () => {
    it('should return array of taxonomies', async () => {
      const params = { category: 'TEST' };
      mockModel.find.mockReturnValue({ exec: jest.fn().mockResolvedValue([mockTaxonomy]) });
      
      const result = await repository.find(params);
      
      expect(result).toEqual([mockTaxonomy]);
      expect(mockModel.find).toHaveBeenCalledWith(params);
    });
  });

  describe('findOne', () => {
    it('should return a single taxonomy', async () => {
      const params = { category: 'TEST' };
      mockModel.findOne.mockReturnValue({ exec: jest.fn().mockResolvedValue(mockTaxonomy) });
      
      const result = await repository.findOne(params);
      
      expect(result).toEqual(mockTaxonomy);
      expect(mockModel.findOne).toHaveBeenCalledWith(params);
    });
  });

  describe('aggregate', () => {
    it('should perform aggregation', async () => {
      const aggregateQuery = [{ $match: { category: 'TEST' } }];
      mockModel.aggregate.mockReturnValue({ exec: jest.fn().mockResolvedValue([mockTaxonomy]) });
      
      const result = await repository.aggregate(aggregateQuery);
      
      expect(result).toEqual([mockTaxonomy]);
      expect(mockModel.aggregate).toHaveBeenCalledWith(aggregateQuery);
    });
  });


describe('findByCategory', () => {
  it('should return taxonomy when it exists for a valid category', async () => {
    const category = 'TEST_CATEGORY' as Category;
    mockModel.findOne.mockReturnValue({
      exec: jest.fn().mockResolvedValue(mockTaxonomy),
    });

    const result = await repository.findByCategory(category);

    expect(mockModel.findOne).toHaveBeenCalledWith({ category });
    expect(result).toEqual(mockTaxonomy);
  });

  it('should return null if no taxonomy exists for the given category', async () => {
    const category = 'TEST_CATEGORY' as Category;
    mockModel.findOne.mockReturnValue({
      exec: jest.fn().mockResolvedValue(null),
    });

    const result = await repository.findByCategory(category);

    expect(mockModel.findOne).toHaveBeenCalledWith({ category });
    expect(result).toBeNull();
  });
});

describe('createTaxonomy', () => {
  const category = 'TEST_CATEGORY' as Category;
  const mockTaxonomy: Taxonomies = {
    _id: new Types.ObjectId(),
    tid: 101,
    category,
    name: 'Test Taxonomy',
  };
  it('should create and return a new taxonomy', async () => {
    const saveMock = jest.fn().mockResolvedValue(mockTaxonomy);

    const taxonomyConstructorMock = jest.fn().mockImplementation(() => ({
      save: saveMock,
    }));

    // Override the taxonomyModel with mocked constructor + original model methods
    const fakeModel = Object.assign(taxonomyConstructorMock, mockModel);
    repository['taxonomyModel'] = fakeModel as unknown as Model<Taxonomies>;

    const result = await repository.createTaxonomy(mockTaxonomy);

    expect(taxonomyConstructorMock).toHaveBeenCalledWith(mockTaxonomy);
    expect(saveMock).toHaveBeenCalled();
    expect(result).toEqual(mockTaxonomy);
  });
});


describe('updateTaxonomy', () => {
  
  it('should update and return the taxonomy if found', async () => {
    const updates = { description: 'Updated Description' } as Partial<Taxonomies>;
    const testCategory = 'TEST_CATEGORY' as Category;
    const updatedTaxonomy: Taxonomies = {
      ...mockTaxonomy,
      ...updates,
      _id: new Types.ObjectId(),
    }as Taxonomies;;
  
    mockModel.findOneAndUpdate.mockResolvedValue(updatedTaxonomy);
  
    const result = await repository.updateTaxonomy(testCategory, updates);
  
    expect(mockModel.findOneAndUpdate).toHaveBeenCalledWith(
      { category: testCategory },
      { $set: updates },
      { new: true }
    );
    expect(result).toEqual(updatedTaxonomy);
  });
  

  it('should throw NotFoundException if taxonomy is not found', async () => {
    mockModel.findOneAndUpdate.mockResolvedValue(null);
    const testCategory = 'TEST_CATEGORY' as Category;
    const updates = { description: 'Updated' } as Partial<Taxonomies>;
    await expect(
      repository.updateTaxonomy(testCategory, updates)
    ).rejects.toThrowError(new NotFoundException(`Taxonomy with category ${testCategory} not found`));

    expect(mockModel.findOneAndUpdate).toHaveBeenCalledWith(
      { category: testCategory },
      { $set: { description: 'Updated' } },
      { new: true }
    );
  });
});

});
