import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Role } from '../../../db/mongo/schema/roles/role.schema';

export interface IRoleRepository {
  findAll(query: object): Promise<Role[]>;
  findOne(query: object): Promise<Role>;
  getRoles(roleCriteria: { pageSize: number; skipCount: number;  filter?: Record<string, any>; }): Promise<Role>;
  saveRoles(query: object): Promise<Role>;
  updateRole(query: object, update: Partial<Role>): Promise<Role>;
}

@Injectable()
export class RoleRepository implements IRoleRepository {
  @InjectModel(Role.name, 'default') private roleModel: Model<Role>;

  async findAll(query: object): Promise<Role[]> {
    return await this.roleModel.find(query).lean();
  }
  async findOne(query: object): Promise<Role> {
    return await this.roleModel.findOne(query).lean();
  }

  async getRoles(roleCriteria: { pageSize: number; skipCount: number,  filter?: Record<string, any>; }): Promise<any> {
    return await this.roleModel.find(roleCriteria?.filter).skip(roleCriteria?.skipCount).limit(roleCriteria?.pageSize).lean();
  }

 async saveRoles(query: object): Promise<Role> {
      return await this.roleModel.create(query);
    }

  async updateRole(query: object, update: Partial<Role>): Promise<Role> {
      return await this.roleModel.findOneAndUpdate(query, update, { new: true }).lean();
    }
}
