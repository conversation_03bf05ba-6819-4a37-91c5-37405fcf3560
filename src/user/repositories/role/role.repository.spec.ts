import { Test, TestingModule } from '@nestjs/testing';
import { RoleRepository, IRoleRepository } from './role.repository';
import { Model, Query } from 'mongoose';
import { Role } from '../../../db/mongo/schema/roles/role.schema';
import { getModelToken } from '@nestjs/mongoose';

describe('RoleRepository', () => {
  let roleRepository: IRoleRepository;
  let roleModel: Model<Role>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RoleRepository,
        {
          provide: getModelToken(Role.name, 'default'), // Replace with the actual token used in your application
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    roleRepository = module.get<RoleRepository>(RoleRepository);
    roleModel = module.get<Model<Role>>(getModelToken(Role.name, 'default')); // Replace with the actual token used in your application
  });

  describe('findAll', () => {
    it('should return an array of roles', async () => {
      const roles: Role[] = [
        {
          rid: 59,
          roleName: 'looper_student',
        },
      ];

      // Create a mock DocumentQuery-like object for find
      const mockQuery: Query<Role[], Role> = {
        lean: jest.fn().mockResolvedValue(roles),
      } as unknown as Query<Role[], Role>;

      jest.spyOn(roleModel, 'find').mockReturnValue(mockQuery);

      const query = {}; // Define your query object

      const result = await roleRepository.findAll(query);

      expect(result).toEqual(roles);
    });
  });

  describe('findOne', () => {
    it('should return a role by name', async () => {
      const roleName = 'TestRole';
      const role: Role = {
        rid: 59,
        roleName: 'looper_student',
      }; // create a mock role object

      // Create a mock DocumentQuery-like object for findOne
      const mockQuery: Query<Role, Role> = {
        lean: jest.fn().mockResolvedValue(role),
      } as unknown as Query<Role, Role>;

      jest.spyOn(roleModel, 'findOne').mockReturnValue(mockQuery);

      const result = await roleRepository.findOne({ roleName });

      expect(result).toEqual(role);
    });

    it('should return null if the role is not found', async () => {
      const roleName = 'NonExistentRole';

      // Create a mock DocumentQuery-like object for findOne that returns null
      const mockQuery: Query<Role, Role> = {
        lean: jest.fn().mockResolvedValue(null),
      } as unknown as Query<Role, Role>;

      jest.spyOn(roleModel, 'findOne').mockReturnValue(mockQuery);

      const result = await roleRepository.findOne({ roleName });

      expect(result).toBeNull();
    });
  });

  describe('getRoles', () => {
    it('should return roles with pagination and filter', async () => {
      const mockRoles: Role[] = [{ rid: 59, roleName: 'admin' }];
      const mockFind = {
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        lean: jest.fn().mockResolvedValue(mockRoles),
      };

      (roleModel.find as jest.Mock).mockReturnValue(mockFind);

      const result = await roleRepository.getRoles({
        pageSize: 10,
        skipCount: 0,
        filter: { roleName: 'admin' },
      });

      expect(roleModel.find).toHaveBeenCalledWith({ roleName: 'admin' });
      expect(mockFind.skip).toHaveBeenCalledWith(0);
      expect(mockFind.limit).toHaveBeenCalledWith(10);
      expect(result).toEqual(mockRoles);
    });
  });

  describe('saveRoles', () => {
    const mockRoleModel = {
      find: jest.fn(),
      create: jest.fn(),
      findOne: jest.fn(),
      findOneAndUpdate: jest.fn(),
    };
    
    beforeEach(async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          RoleRepository,
          {
            provide: getModelToken(Role.name, 'default'),
            useValue: mockRoleModel,
          },
        ],
      }).compile();
    
      roleRepository = module.get<RoleRepository>(RoleRepository);
      roleModel = module.get<Model<Role>>(getModelToken(Role.name, 'default'));
    });
    
    it('should create and return a new role', async () => {
      const newRole = { rid: 60, roleName: 'editor' };
      mockRoleModel.create.mockResolvedValue(newRole);
      const result = await roleRepository.saveRoles(newRole);
      expect(mockRoleModel.create).toHaveBeenCalledWith(newRole);
      expect(result).toEqual(newRole);
    });
  });

  describe('updateRole', () => {
    const mockRoleModel = {
      find: jest.fn(),
      create: jest.fn(),
      findOne: jest.fn(),
      findOneAndUpdate: jest.fn(),
    };
    
    beforeEach(async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          RoleRepository,
          {
            provide: getModelToken(Role.name, 'default'),
            useValue: mockRoleModel,
          },
        ],
      }).compile();
    
      roleRepository = module.get<RoleRepository>(RoleRepository);
      roleModel = module.get<Model<Role>>(getModelToken(Role.name, 'default'));
    });

    it('should update and return the updated role', async () => {
      const query = { rid: 60 };
      const update = { roleName: 'updatedRole' };
      const updatedRole = { rid: 60, roleName: 'updatedRole' };

      const mockFindOneAndUpdate = {
        lean: jest.fn().mockResolvedValue(updatedRole),
      };

      (mockRoleModel.findOneAndUpdate as jest.Mock).mockReturnValue(mockFindOneAndUpdate);

      const result = await roleRepository.updateRole(query, update);

      expect(mockRoleModel.findOneAndUpdate).toHaveBeenCalledWith(query, update, { new: true });
      expect(result).toEqual(updatedRole);
    });
  });
});
