
import { Test, TestingModule } from '@nestjs/testing';
import { UserRepository } from './user.repository';
import { getModelToken } from '@nestjs/mongoose';
import { User } from '../../../db/mongo/schema/user/user.schema';
import mongoose from 'mongoose';

describe('UserRepository', () => {
  let repository: UserRepository;
 

  const mockUser = {
    _id: new mongoose.Types.ObjectId(),
    email: '<EMAIL>',
    uid: 123,
    roles: []
  };

  const mockModel = {
    find: jest.fn(),
    findOne: jest.fn(),
    findOneAndUpdate: jest.fn(),
    create: jest.fn(),
    deleteOne: jest.fn(),
    populate: jest.fn(),
    lean: jest.fn(),
    exec: jest.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserRepository,
        {
          provide: getModelToken(User.name, 'secure'),
          useValue: mockModel
        }
      ]
    }).compile();

    repository = module.get<UserRepository>(UserRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findByUID', () => {
    it('should find user by uid', async () => {
      mockModel.findOne.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          lean: jest.fn().mockResolvedValue(mockUser)
        })
      });

      const result = await repository.findByUID(123);
      expect(result).toEqual(mockUser);
      expect(mockModel.findOne).toHaveBeenCalledWith({ uid: 123 });
    });
  });

  describe('upsert', () => {
    it('should upsert user', async () => {
      mockModel.findOneAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockUser)
      });

      const result = await repository.upsert(mockUser as unknown as User);
      expect(result).toEqual(mockUser);
      expect(mockModel.findOneAndUpdate).toHaveBeenCalledWith(
        { email: mockUser.email },
        mockUser,
        { upsert: true }
      );
    });
  });

  describe('saveUser', () => {
    it('should save user', async () => {
      mockModel.create.mockResolvedValue(mockUser);

      const result = await repository.saveUser(mockUser as unknown as User);
      expect(result).toEqual(mockUser);
      expect(mockModel.create).toHaveBeenCalledWith(mockUser);
    });
  });

  describe('getUserByEmail', () => {
    it('should get user by email', async () => {
      mockModel.findOne.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          lean: jest.fn().mockResolvedValue(mockUser)
        })
      });

      const result = await repository.getUserByEmail('<EMAIL>');
      expect(result).toEqual(mockUser);
      expect(mockModel.findOne).toHaveBeenCalledWith({ email: '<EMAIL>' });
    });
  });

  describe('findOneAndUpdate', () => {
    it('should find one and update', async () => {
      const filter = { email: '<EMAIL>' };
      mockModel.findOneAndUpdate.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          lean: jest.fn().mockResolvedValue(mockUser)
        })
      });

      const result = await repository.findOneAndUpdate(filter, mockUser);
      expect(result).toEqual(mockUser);
      expect(mockModel.findOneAndUpdate).toHaveBeenCalledWith(filter, mockUser);
    });
  });

  describe('find', () => {
    it('should find users', async () => {
      const params = { email: '<EMAIL>' };
      mockModel.find.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue([mockUser])
        })
      });

      const result = await repository.find(params);
      expect(result).toEqual([mockUser]);
      expect(mockModel.find).toHaveBeenCalledWith(params);
    });
  });

  describe('findOne', () => {
    it('should find one user', async () => {
      const params = { email: '<EMAIL>' };
      mockModel.findOne.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockUser)
        })
      });

      const result = await repository.findOne(params);
      expect(result).toEqual(mockUser);
      expect(mockModel.findOne).toHaveBeenCalledWith(params);
    });
  });

  describe('deleteOneById', () => {
    it('should delete one user by id', async () => {
      const response = { acknowledged: true, deletedCount: 1 };
      mockModel.deleteOne.mockResolvedValue(response);

      const result = await repository.deleteOneById(mockUser._id.toString());
      expect(result).toEqual(response);
      expect(mockModel.deleteOne).toHaveBeenCalledWith({ 
        _id: new mongoose.Types.ObjectId(mockUser._id.toString()) 
      });
    });
  });
});