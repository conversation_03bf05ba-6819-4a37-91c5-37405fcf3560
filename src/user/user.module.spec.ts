import { Test, TestingModule } from '@nestjs/testing';
import { UserService } from './services/user.service';
import { UserController } from './controllers/user/user.controller';
import { ProfileController } from './controllers/profile/profile.controller';
import { getRepositoryToken } from '@nestjs/typeorm';
import { FailedCommunityUser } from '../db/mysql/entity/failed-community-user.entity';
import { getModelToken } from '@nestjs/mongoose';
import { User } from '../db/mongo/schema/user/user.schema';
import { UserToken } from '../db/mongo/schema/user-token/user.token.schema';
import { Role } from '../db/mongo/schema/roles/role.schema';
import { Taxonomies } from '../db/mongo/schema/taxonomies/taxonomies.schema';
import { JwtService } from '@nestjs/jwt';
import { UserMgmtCommunityService } from './services/communication/usermgmt.community.service';
import { UserRepository } from './repositories/user/user.repository';
import { RoleRepository } from './repositories/role/role.repository';
import { TaxonomyRepository } from './repositories/taxonomy/taxonomy.repository';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../helper/helper.service';

describe('UserModule', () => {
  let module: TestingModule;

  const mockUserMgmtCommunityService = {};
  const mockJwtService = {};
  const mockConfigService = {
    get: jest.fn((key: string) => {
      if (key === 'enableDrupalSync') {
        return true;
      }
      if (key === 'defaultGroupId') {
        return 'someDefaultGroupId';
      }
      if (key === 'clientKey') {
        return 'someClientKey';
      }
      if (key === 'qNameGdprUserDelete') {
        return 'someQueueName';
      }
      return '';
    }),
  };
  const mockHelperService = {
    get: jest.fn(() => {
      return {};
    }),
    getHelper: jest.fn(() => {
      return {};
    }),
  };

  const mockMongooseModel = {
    find: jest.fn().mockReturnThis(),
    findOne: jest.fn().mockReturnThis(),
    findOneAndUpdate: jest.fn().mockReturnThis(),
    create: jest.fn().mockReturnThis(),
    deleteOne: jest.fn().mockReturnThis(),
    populate: jest.fn().mockReturnThis(),
    lean: jest.fn().mockReturnThis(),
    exec: jest.fn().mockResolvedValue(true),
  };

  const mockCryptoHelper = {
    decrypt: jest.fn((data: string) => data),
    encrypt: jest.fn((data: string) => data),
  };

  beforeEach(async () => {
    module = await Test.createTestingModule({
      controllers: [UserController, ProfileController],
      providers: [
        UserService,
        UserRepository,
        RoleRepository,
        TaxonomyRepository,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: UserMgmtCommunityService,
          useValue: mockUserMgmtCommunityService,
        },
        {
          provide: HelperService,
          useValue: mockHelperService,
        },
        {
          provide: getRepositoryToken(FailedCommunityUser),
          useValue: jest.fn(),
        },
        {
          provide: getModelToken(User.name, 'secure'),
          useValue: mockMongooseModel,
        },
        {
          provide: getModelToken(UserToken.name, 'secure'),
          useValue: mockMongooseModel,
        },
        {
          provide: getModelToken(Role.name, 'default'),
          useValue: mockMongooseModel,
        },
        {
          provide: getModelToken(Role.name, 'secure'),
          useValue: mockMongooseModel,
        },
        {
          provide: getModelToken(Taxonomies.name, 'default'),
          useValue: mockMongooseModel,
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: mockCryptoHelper,
        },
      ],
    }).compile();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should resolve UserController', () => {
    const controller = module.get<UserController>(UserController);
    expect(controller).toBeDefined();
  });

  it('should resolve ProfileController', () => {
    const controller = module.get<ProfileController>(ProfileController);
    expect(controller).toBeDefined();
  });

  it('should resolve UserService', () => {
    const service = module.get<UserService>(UserService);
    expect(service).toBeDefined();
  });

  it('should resolve UserRepository', () => {
    const repo = module.get<UserRepository>(UserRepository);
    expect(repo).toBeDefined();
  });

  it('should resolve RoleRepository', () => {
    const repo = module.get<RoleRepository>(RoleRepository);
    expect(repo).toBeDefined();
  });

  it('should resolve TaxonomyRepository', () => {
    const repo = module.get<TaxonomyRepository>(TaxonomyRepository);
    expect(repo).toBeDefined();
  });
});
