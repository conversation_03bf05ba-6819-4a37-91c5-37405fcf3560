import { Test, TestingModule } from '@nestjs/testing';
import { UserController } from './user.controller';
import { Response } from 'express';
import { Logger } from '../../../logging/logger';

// Mock the logger to spy on its methods
jest.mock('../../../logging/logger');

describe('UserController', () => {
  let controller: UserController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
    }).compile();

    controller = module.get<UserController>(UserController);
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('register', () => {
    it('should log "register"' , () => {
      controller.register();
      expect(Logger.log).toHaveBeenCalledWith('register');
    });
  });

  describe('loginRender', () => {
    it('should redirect to /auth/login with all query parameters', async () => {
      const redirectSpy = jest.fn();
      const res = { redirect: redirectSpy } as unknown as Response;
      const queryParam = { redirect_url: 'SOME_VALUE', calendar_url: 'OTHER_VALUE' };

      await controller.loginRender(queryParam, res);

      expect(redirectSpy).toHaveBeenCalledWith('/auth/login?redirect_url=SOME_VALUE&calendar_url=OTHER_VALUE');
    });

    it('should redirect to /auth/login with only redirect_url', async () => {
        const redirectSpy = jest.fn();
        const res = { redirect: redirectSpy } as unknown as Response;
        const queryParam = { redirect_url: 'SOME_VALUE', calendar_url: '' };
  
        await controller.loginRender(queryParam, res);
  
        expect(redirectSpy).toHaveBeenCalledWith('/auth/login?redirect_url=SOME_VALUE&calendar_url=');
      });

    it('should redirect to /auth/login with only calendar_url', async () => {
      const redirectSpy = jest.fn();
      const res = { redirect: redirectSpy } as unknown as Response;
      const queryParam = { calendar_url: 'SOME_VALUE', redirect_url: '' };

      await controller.loginRender(queryParam, res);

      expect(redirectSpy).toHaveBeenCalledWith('/auth/login?calendar_url=SOME_VALUE&redirect_url=');
    });

    it('should not call redirect when no query params are present', async () => {
      const redirectSpy = jest.fn();
      const res = { redirect: redirectSpy } as unknown as Response;
      const queryParam = {};

      await controller.loginRender(queryParam, res);
      expect(redirectSpy).not.toHaveBeenCalled();
    });
  });

  describe('registerEmail', () => {
    it('should return an object with empty token and validationErrors', async () => {
      const result = await controller.registerEmail();
      expect(result).toEqual({ token: '', validationErrors: [] });
    });
  });

  describe('registerComplete', () => {
    it('should log "registerComplete"', () => {
      controller.registerComplete();
      expect(Logger.log).toHaveBeenCalledWith('registerComplete');
    });
  });

  describe('multiAccount', () => {
    it('should redirect to /auth/multi-account with query parameters and log', () => {
      const queryParam = {
        gid: 'group123',
        url: 'example.com',
        calendar_url: 'calendar.example.com',
      };
      const res: Partial<Response> = {
        redirect: jest.fn(),
      };
      controller.multiAccount(queryParam, res as Response);
      expect(Logger.log).toHaveBeenCalledWith('multiAccount');
      expect(res.redirect).toHaveBeenCalledWith(
        '/auth/multi-account?gid=group123&url=example.com&calendar_url=calendar.example.com',
      );
    });
  });

  describe('forgotPassword', () => {
    it('should log "forgotPassword"', () => {
      controller.forgotPassword();
      expect(Logger.log).toHaveBeenCalledWith('forgotPassword');
    });
  });

  describe('reset', () => {
    it('should log "reset" with params', () => {
      const params = { userId: '1', code: 'reset-code', email: '<EMAIL>' };
      controller.reset(params);
      expect(Logger.log).toHaveBeenCalledWith('reset', params);
    });
  });

  describe('resetPassword', () => {
    it('should log "resetPassword" with params', () => {
      const params = { userId: '1', requestTime: '123456', requestToken: 'token' };
      controller.resetPassword(params);
      expect(Logger.log).toHaveBeenCalledWith('resetPassword', params);
    });
  });

  describe('processAppleAuthResponseRegister', () => {
    it('should log "processAppleAuthResponseRegister"', () => {
      controller.processAppleAuthResponseRegister();
      expect(Logger.log).toHaveBeenCalledWith('processAppleAuthResponseRegister');
    });
  });

  describe('processAppleAuthResponseLogin', () => {
    it('should log "processAppleAuthResponseLogin"', () => {
      controller.processAppleAuthResponseLogin();
      expect(Logger.log).toHaveBeenCalledWith('processAppleAuthResponseLogin');
    });
  });

  describe('processSocialAuthResponse', () => {
    it('should log "processSocialAuthResponse"', () => {
      controller.processSocialAuthResponse();
      expect(Logger.log).toHaveBeenCalledWith('processSocialAuthResponse');
    });
  });

  describe('createUserSocialAccountDetails', () => {
    it('should log "createUserSocialAccountDetails"', () => {
      controller.createUserSocialAccountDetails();
      expect(Logger.log).toHaveBeenCalledWith('createUserSocialAccountDetails');
    });
  });

  describe('createUserAccount', () => {
    it('should log "createUserAccount"', () => {
      controller.createUserAccount();
      expect(Logger.log).toHaveBeenCalledWith('createUserAccount');
    });
  });

  describe('socialLink', () => {
    it('should log "socialLink"', () => {
      controller.socialLink();
      expect(Logger.log).toHaveBeenCalledWith('socialLink');
    });
  });

  describe('validateLogin', () => {
    it('should log "validateLogin"', () => {
      controller.validateLogin();
      expect(Logger.log).toHaveBeenCalledWith('validateLogin');
    });
  });

  describe('manageAuthRedirect', () => {
    it('should redirect with the correct token parameter', () => {
      const redirectSpy = jest.fn();
      const res = { redirect: redirectSpy } as unknown as Response;
      const queryParam = { token: 'tokenValue' };
      controller.manageAuthRedirect(queryParam, res);
      expect(redirectSpy).toHaveBeenCalledWith('/auth/manage-auth-redirect?token=tokenValue');
    });
  });

  describe('accountSetUp', () => {
    it('should redirect to the correct URL with encoded query parameters', async () => {
      const queryParam = {
        countryId: 'IN',
        phoneNo: '**********',
        redirect_url: 'http://example.com/dashboard',
        userEmail: '<EMAIL>',
      };

      const res: Partial<Response> = {
        redirect: jest.fn(),
      };

      await controller.accountSetUp(queryParam, res as Response);

      const expectedQueryString = new URLSearchParams(queryParam).toString();
      expect(res.redirect).toHaveBeenCalledWith(`/auth/account-setup?${expectedQueryString}`);
    });
  });

  describe('socialLogin', () => {
    it('should log "socialLogin"', () => {
      controller.socialLogin();
      expect(Logger.log).toHaveBeenCalledWith('socialLogin');
    });
  });
});