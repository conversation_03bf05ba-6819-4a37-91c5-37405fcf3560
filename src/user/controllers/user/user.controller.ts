import { <PERSON>, Get, Param, Redirect, Res, Query } from '@nestjs/common';
import { ApiExcludeController } from '@nestjs/swagger';
import { Response } from 'express';
import { Logger } from '../../../logging/logger';
import { stringify } from 'querystring';

@ApiExcludeController(true)
@Controller('user')
export class UserController {
  @Get('register')
  @Redirect(`/auth/register`)
  register() {
    Logger.log('register');
  }

  @Get('login')
  @Redirect('/auth/login')
  async loginRender(@Query() queryParam, @Res({ passthrough: true }) res: Response) {
    const queryParams = stringify(queryParam);
    if (queryParams) {
      const redirectUrl = '/auth/login';
      res.redirect(`${redirectUrl}?${queryParams}`);
    }
  }

  @Get('register-email')
  @Redirect('/auth/register-email')
  async registerEmail() {
    return { token: '', validationErrors: [] };
  }

  @Get('register-complete')
  @Redirect(`/auth/register-complete`)
  registerComplete() {
    Logger.log('registerComplete');
  }

  @Get('multi-account')
  @Redirect(`/auth/multi-account`)
  multiAccount(@Query() queryParam, @Res({ passthrough: true }) res: Response) {
    Logger.log('multiAccount');
    res.redirect(
      '/auth/multi-account?gid=' +
        queryParam.gid +
        '&url=' +
        queryParam.url +
        '&calendar_url=' +
        queryParam.calendar_url,
    );
  }

  @Get('forgot-password')
  @Redirect(`/auth/forgot-password`)
  forgotPassword() {
    Logger.log('forgotPassword');
  }

  @Get('reset/:userId/:code/:email')
  @Redirect(`/auth/reset/:code/:email`)
  reset(@Param() params: any) {
    Logger.log('reset',params);
  }

  @Get('reset-password/:userId/:requestTime/:requestToken')
  @Redirect(`/auth/reset-password/:userId/:requestTime/:requestToken`)
  resetPassword(@Param() params: any) {
    Logger.log('resetPassword',params);
  }

  @Get('process-apple-auth-response-register')
  @Redirect(`/auth/process-apple-auth-response-register`)
  processAppleAuthResponseRegister() {
    Logger.log('processAppleAuthResponseRegister');
  }

  @Get('process-apple-auth-response-login')
  @Redirect(`/auth/process-apple-auth-response-login`)
  processAppleAuthResponseLogin() {
    Logger.log('processAppleAuthResponseLogin');
  }

  @Get('process-social-auth-response')
  @Redirect(`/auth/process-social-auth-response`)
  processSocialAuthResponse() {
    Logger.log('processSocialAuthResponse');
  }

  @Get('create-user-social-account-details')
  @Redirect(`/auth/create-user-social-account-details`)
  createUserSocialAccountDetails() {
    Logger.log('createUserSocialAccountDetails');
  }

  @Get('create-user-account')
  @Redirect(`/auth/create-user-account`)
  createUserAccount() {
    Logger.log('createUserAccount');
  }

  @Get('social-link')
  @Redirect(`/auth/social-link`)
  socialLink() {
    Logger.log('socialLink');
  }

  @Get('validate-login')
  @Redirect(`/auth/validate-login`)
  validateLogin() {
    Logger.log('validateLogin');
  }

  @Get('manage-auth-redirect')
  manageAuthRedirect(@Query() queryParam, @Res({ passthrough: true }) res: Response) {
    res.redirect('/auth/manage-auth-redirect?token=' + queryParam.token);
  }

  @Get('account-setup')
  @Redirect('/auth/account-setup')
  async accountSetUp(@Query() queryParam, @Res({ passthrough: true }) res: Response) {
    const queryString: string = new URLSearchParams(queryParam).toString();
    const baseURL = '/auth/account-setup';
    const fullURL = `${baseURL}?${queryString}`;
    res.redirect(fullURL);
  }

  @Get('/redirect')
  @Redirect('/auth/redirect')
  socialLogin() {
    Logger.log('socialLogin');
  }
}
