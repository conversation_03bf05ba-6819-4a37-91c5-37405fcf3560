import { Test, TestingModule } from '@nestjs/testing';
import { ProfileController } from './profile.controller';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../../helper/helper.service';
import { JwtService } from '@nestjs/jwt';
import { BadRequestException } from '@nestjs/common';
import { Request, Response } from 'express';
import { ChangePasswordUserDto } from '../../../auth/dtos/change-password-user.dto';
import { ExportProfileDto } from '../../../user-api/dto/export.profile.dto';

// Mock dependencies
jest.mock('../../../logging/logger');

describe('ProfileController', () => {
  let controller: ProfileController;
  let helperService: HelperService;

  // Mock helpers and services
  const mockCookieHelper = {
    clearCookieIfExists: jest.fn(),
    setCookie: jest.fn(),
    setBulkCookie: jest.fn(),
  };
  const mockProfileHelper = {
    fetchProfileData: jest.fn(),
    saveProfileData: jest.fn(),
    deleteImage: jest.fn(),
  };
  const mockUserHelper = {
    ifNotB2COrB2BLearner: jest.fn(),
  };
  const mockUserMgmtUtilityHelper = {
    showDownloadButton: jest.fn(),
    showDeleteButton: jest.fn(),
    clearCacheAndCookies: jest.fn(),
    isB2bStudent: jest.fn(),
  };
  const mockUserApiV1Helper = {
    getViewSettings: jest.fn(),
    loadViewData: jest.fn(),
  };
  const mockLrsHelper = {
    sendDataToKafka: jest.fn(),
  };
  const mockUserService = {
    changePassword: jest.fn(),
    confirmDeleteAccount: jest.fn(),
  };
  const mockIc9Service = {
    getCountryRegionJson: jest.fn(),
  };
  const mockDeviantsService = {
    pushMessageToQueue: jest.fn(),
  };
  const mockUserRepository = {
    getUserByEmail: jest.fn(),
  };
  const mockAuthTokenHelper = {
    decodeJWTToken: jest.fn(),
    generateSessionTokens: jest.fn(),
  };
  const mockCryptoHelper = {
    decrypt: jest.fn(val => val.replace('encrypted_', '')),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProfileController],
      providers: [
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const values = {
                ssoCookie: 'sso_cookie',
                profileCookie: '_profile_status',
                linkData: 'link_data_cookie',
                defaultUserGroupId: '1',
                gdprEnabled: '1',
                profileTopic: 'profile_topic',
                ssoCookieDomain: '.test.com',
                qNameGdprUserExport: 'gdpr_queue',
                profileRequestFailed: 'Request failed',
                managePaymentsUrl: '/payments',
                digitalKeyUrl: '/keys',
                examVoucherUrl: '/vouchers',
                freeTrialUrl: '/free-trial',
                tickets: '/tickets'
              };
              return values[key];
            }),
          },
        },
        {
          provide: HelperService,
          useValue: {
            get: jest.fn().mockImplementation((name: string) => {
                if (name === 'UserService') return mockUserService;
                if (name === 'Ic9Service') return mockIc9Service;
                if (name === 'DeviantsService') return mockDeviantsService;
                if (name === 'UserRepository') return mockUserRepository;
                if (name === 'AuthTokenHelper') return mockAuthTokenHelper;
                return Promise.resolve({});
            }),
            getHelper: jest.fn().mockImplementation((name: string) => {
                if (name === 'CookieHelper') return mockCookieHelper;
                if (name === 'ProfileHelper') return mockProfileHelper;
                if (name === 'UserHelper') return mockUserHelper;
                if (name === 'UserMgmtUtilityHelper') return mockUserMgmtUtilityHelper;
                if (name === 'UserApiV1Helper') return mockUserApiV1Helper;
                if (name === 'lrsHelper') return mockLrsHelper;
                return Promise.resolve({});
            }),
          },
        },
        {
          provide: JwtService,
          useValue: {
            decode: jest.fn().mockReturnValue({ data: { userType: 'test', lgid: '1' } }),
          },
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: mockCryptoHelper,
        },
      ],
    }).compile();

    controller = module.get<ProfileController>(ProfileController);
    helperService = module.get<HelperService>(HelperService);
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getProfile', () => {
   
    it('should fetch profile data and return view model', async () => {
      const req = {
        user: { uid: '123' },
        cookies: {
          ssoCookieKey: 'fake.jwt.token',
        },
        route: { path: '/user/profile' },
        query: { section: 'test' },
        headers: { 'x-client-ip': '127.0.0.1' },
      } as unknown as Request;
    
      const res = {
        clearCookie: jest.fn(),
      } as unknown as Response;
    
      (helperService.getHelper as jest.Mock).mockImplementation((helperName: string) => {
        switch (helperName) {
          case 'CookieHelper':
            return {
              clearCookieIfExists: jest.fn(),
            };
          case 'ProfileHelper':
            return {
              fetchProfileData: jest.fn().mockResolvedValue({
                userData: { email: '<EMAIL>' },
                selectedEducationId: 'edu123',
                selectedExperienceId: 'exp123',
                workExperience: [],
                academicExperience: [],
                selectedOutcomeId: 'outcome123',
                profileCompletion: 80,
              }),
            };
          case 'UserMgmtUtilityHelper':
            return {
              showDownloadButton: jest.fn().mockReturnValue(true),
              isB2bStudent: jest.fn().mockResolvedValue(false),
            };
          case 'UserApiV1Helper':
            return {
              getViewSettings: jest.fn().mockResolvedValue({ viewSettingKey: 'viewValue' }),
              loadViewData: jest.fn().mockResolvedValue({ viewDataKey: 'dataValue' }),
            };
          case 'userHelper':
            return {
              ifNotB2COrB2BLearner: jest.fn().mockResolvedValue(true),
            };
          default:
            return {};
        }
      });
    
      const result = await controller.getProfile(req, res);
    
      expect(result).toBeDefined();
      expect(result.userData.email).toBe('<EMAIL>');
      expect(result.gdprExport).toBe(true);
      expect(result.showDownloadButton).toBe(true);
      expect(result.profileCompletion).toBe(80);
      expect(result.sectionActive).toBe('test');
      expect(result.sectionUrl).toEqual({
        managePaymentsUrl: '/payments',
        digitalKeyUrl: '/keys',
        examVoucherUrl: '/vouchers',
        freeTrialUrl: '/free-trial',
        tickets: '/tickets',
        editProfileUrl: '/free-trial',
      });
      expect(result.fsaClassToAdd).toBe('disabled-li-for-fsa');
    });

    it('should return error view on failure', async () => {
      const req = {
        user: { uid: '123' },
        cookies: { ssoCookieKey: 'fake.jwt.token' },
        route: { path: '/user/profile' },
        query: { section: 'test' },
        headers: { 'x-client-ip': '127.0.0.1' },
      } as unknown as Request;
      const res = { clearCookie: jest.fn() } as unknown as Response;

      (helperService.getHelper as jest.Mock).mockImplementation((helperName: string) => {
        if (helperName === 'ProfileHelper') {
          return {
            fetchProfileData: jest.fn().mockRejectedValue(new Error('Fetch failed')),
          };
        }
        return {
          clearCookieIfExists: jest.fn(),
        };
      });

      const result = await controller.getProfile(req, res);

      expect(result).toEqual({ token: '', validationErrors: [], route: '/user/profile' });
    });
    
  });

  describe('saveProfile', () => {
    it('should save profile data and redirect', async () => {
        const req = { user: { uid: '123', email: '<EMAIL>' } } as unknown as Request;
        const res = { redirect: jest.fn() } as unknown as Response;
        const body = { edit_type: 'basic' };
        const file = {} as Express.Multer.File;

        (helperService.getHelper as jest.Mock).mockResolvedValue(mockProfileHelper);
        (helperService.getHelper as jest.Mock).mockResolvedValue(mockLrsHelper);
        (helperService.getHelper as jest.Mock).mockResolvedValue(mockCookieHelper);
        (helperService.getHelper as jest.Mock).mockResolvedValue(mockUserHelper);
        mockProfileHelper.saveProfileData.mockResolvedValue(true);

        await controller.saveProfile(body, file, req, res);

        expect(mockProfileHelper.saveProfileData).toHaveBeenCalledWith({ ...body, profile_pic: file }, req.user);
        expect(mockLrsHelper.sendDataToKafka).toHaveBeenCalled();
        expect(mockCookieHelper.setCookie).toHaveBeenCalled();
        expect(res.redirect).toHaveBeenCalledWith('/user/profile');
    });

    it('should throw BadRequestException if saveProfileData fails', async () => {
      const req = { user: { uid: '123', email: '<EMAIL>' } } as unknown as Request;
      const res = { redirect: jest.fn() } as unknown as Response;
      const body = { edit_type: 'basic' };
      const file = {} as Express.Multer.File;

      (helperService.getHelper as jest.Mock).mockResolvedValue(mockProfileHelper);
      mockProfileHelper.saveProfileData.mockRejectedValue(new Error('Save failed'));

      await expect(controller.saveProfile(body, file, req, res)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if result is null', async () => {
        const req = { user: { uid: '123', email: '<EMAIL>' } } as unknown as Request;
        const res = { redirect: jest.fn() } as unknown as Response;
        const body = { edit_type: 'basic' };
        const file = {} as Express.Multer.File;

        (helperService.getHelper as jest.Mock).mockResolvedValue(mockProfileHelper);
        mockProfileHelper.saveProfileData.mockResolvedValue(null);

        await expect(controller.saveProfile(body, file, req, res)).rejects.toThrow('Something went wrong, please try again.');
    });

    it('should update session token when profile picture is updated', async () => {
        const req = { user: { uid: '123', email: '<EMAIL>' } } as unknown as Request;
        const res = { redirect: jest.fn() } as unknown as Response;
        const body = { edit_type: 'basic' };
        const file = { originalname: 'avatar.jpg' } as Express.Multer.File;

        (helperService.getHelper as jest.Mock).mockImplementation((name: string) => {
            if (name === 'ProfileHelper') return mockProfileHelper;
            if (name === 'lrsHelper') return mockLrsHelper;
            if (name === 'CookieHelper') return mockCookieHelper;
            if (name === 'UserHelper') return mockUserHelper;
            return Promise.resolve({});
        });
        (helperService.get as jest.Mock).mockImplementation((name: string) => {
            if (name === 'UserRepository') return mockUserRepository;
            if (name === 'AuthTokenHelper') return mockAuthTokenHelper;
            return Promise.resolve({});
        });

        mockProfileHelper.saveProfileData.mockResolvedValue(true);
        mockUserRepository.getUserByEmail.mockResolvedValue({ id: '1' });
        mockAuthTokenHelper.generateSessionTokens.mockResolvedValue({ idToken: 'new_token' });

        await controller.saveProfile(body, file, req, res);

        expect(mockProfileHelper.saveProfileData).toHaveBeenCalledWith({ ...body, profile_pic: file }, req.user);
        expect(mockAuthTokenHelper.generateSessionTokens).toHaveBeenCalled();
        expect(mockCookieHelper.setCookie).toHaveBeenCalledWith(res, 'sso_cookie', 'new_token', expect.any(Object));
        expect(res.redirect).toHaveBeenCalledWith('/user/profile');
    });
  });

  describe('changePassword', () => {
    it('should return view data and clear cookie if it exists', async () => {
        const req = {
            user: { uid: '123' },
            cookies: { '123_changePassword': 'true' },
            route: { path: '/change-password' },
          } as unknown as Request;
          const res = { clearCookie: jest.fn() } as unknown as Response;
    
          const result = await controller.changePassword(req, res);
    
          expect(res.clearCookie).toHaveBeenCalled();
          expect(result.type).toBe(true);
    });
  });

  describe('changePasswordPost', () => {
    it('should return success script on successful password change', async () => {
        const req = { user: { uid: '123' } } as unknown as Request;
        const res = { contentType: jest.fn().mockReturnThis(), send: jest.fn() } as unknown as Response;
        const dto: ChangePasswordUserDto = { cur_passwd: '1', new_passwd: '2', confirm_passwd: '2' };

        (helperService.get as jest.Mock).mockResolvedValue(mockUserService);
        (helperService.getHelper as jest.Mock).mockResolvedValue(mockCookieHelper);
        mockUserService.changePassword.mockResolvedValue(true);

        const result = await controller.changePasswordPost(dto, req, res);

        expect(result).toContain('alert("Your password has been successfully changed. Please log in again with your new password.");');
    });

    it('should throw BadRequestException for UserNotFoundException', async () => {
        const req = { user: { uid: '123' } } as unknown as Request;
        const res = { contentType: jest.fn().mockReturnThis(), send: jest.fn() } as unknown as Response;
        const dto: ChangePasswordUserDto = { cur_passwd: '1', new_passwd: '2', confirm_passwd: '2' };

        (helperService.get as jest.Mock).mockResolvedValue(mockUserService);
        mockUserService.changePassword.mockResolvedValue(new Error('UserNotFoundException'));

        await expect(controller.changePasswordPost(dto, req, res)).rejects.toThrow('User account does not exist for the specified email-id.');
    });

    it('should throw BadRequestException for NotAuthorizedException', async () => {
        const req = { user: { uid: '123' } } as unknown as Request;
        const res = { contentType: jest.fn().mockReturnThis(), send: jest.fn() } as unknown as Response;
        const dto: ChangePasswordUserDto = { cur_passwd: '1', new_passwd: '2', confirm_passwd: '2' };

        (helperService.get as jest.Mock).mockResolvedValue(mockUserService);
        mockUserService.changePassword.mockResolvedValue(new Error('NotAuthorizedException'));

        await expect(controller.changePasswordPost(dto, req, res)).rejects.toThrow('Old password is incorrect.');
    });

    it('should throw BadRequestException for UserNotGroupMember', async () => {
        const req = { user: { uid: '123' } } as unknown as Request;
        const res = { contentType: jest.fn().mockReturnThis(), send: jest.fn() } as unknown as Response;
        const dto: ChangePasswordUserDto = { cur_passwd: '1', new_passwd: '2', confirm_passwd: '2' };
        (helperService.get as jest.Mock).mockResolvedValue(mockUserService);
        mockUserService.changePassword.mockResolvedValue(new Error('UserNotGroupMember'));
        await expect(controller.changePasswordPost(dto, req, res)).rejects.toThrow('User is not a member of the group.');
    });

    it('should throw BadRequestException for AttemptLimitExceed', async () => {
        const req = { user: { uid: '123' } } as unknown as Request;
        const res = { contentType: jest.fn().mockReturnThis(), send: jest.fn() } as unknown as Response;
        const dto: ChangePasswordUserDto = { cur_passwd: '1', new_passwd: '2', confirm_passwd: '2' };
        (helperService.get as jest.Mock).mockResolvedValue(mockUserService);
        mockUserService.changePassword.mockResolvedValue(new Error('AttemptLimitExceed'));
        await expect(controller.changePasswordPost(dto, req, res)).rejects.toThrow('You have made more than 5 attempts, please try again after 10 minutes!');
    });

    it('should throw BadRequestException for processRequestFailed', async () => {
        const req = { user: { uid: '123' } } as unknown as Request;
        const res = { contentType: jest.fn().mockReturnThis(), send: jest.fn() } as unknown as Response;
        const dto: ChangePasswordUserDto = { cur_passwd: '1', new_passwd: '2', confirm_passwd: '2' };
        (helperService.get as jest.Mock).mockResolvedValue(mockUserService);
        mockUserService.changePassword.mockResolvedValue(new Error('processRequestFailed'));
        await expect(controller.changePasswordPost(dto, req, res)).rejects.toThrow('Change password request failed, Due to error in request log.');
    });

    it('should throw BadRequestException for other errors', async () => {
        const req = { user: { uid: '123' } } as unknown as Request;
        const res = { contentType: jest.fn().mockReturnThis(), send: jest.fn() } as unknown as Response;
        const dto: ChangePasswordUserDto = { cur_passwd: '1', new_passwd: '2', confirm_passwd: '2' };

        (helperService.get as jest.Mock).mockResolvedValue(mockUserService);
        mockUserService.changePassword.mockResolvedValue(new Error('SomeOtherError'));

        await expect(controller.changePasswordPost(dto, req, res)).rejects.toThrow('Some error occurred while changing password.');
    });
  });

  describe('getRegionByCountryAction', () => {
    it('should return region data for a valid country ID', async () => {
        (helperService.get as jest.Mock).mockResolvedValue(mockIc9Service);
        mockIc9Service.getCountryRegionJson.mockResolvedValue({ regions: ['test'] });

        const result = await controller.getRegionByCountryAction('US');

        expect(result).toEqual({ regions: ['test'] });
    });

    it('should throw BadRequestException for an empty country ID', async () => {
        await expect(controller.getRegionByCountryAction('')).rejects.toThrow(BadRequestException);
    });
  });

 

  describe('deleteProfilePicture', () => {
    it('should delete picture and return success', async () => {
      const req = {
        user: { uid: '123', email: '<EMAIL>' },
      } as unknown as Request;
      const res = {} as Response;
  
      // Correctly return the right mock based on helper name
      (helperService.getHelper as jest.Mock).mockImplementation((name: string) => {
        if (name === 'ProfileHelper') return mockProfileHelper;
        if (name === 'CookieHelper') return mockCookieHelper;
        return {};
      });
  
      (helperService.get as jest.Mock).mockImplementation((name: string) => {
        if (name === 'UserRepository') return mockUserRepository;
        if (name === 'AuthTokenHelper') return mockAuthTokenHelper;
        return {};
      });
  
      mockProfileHelper.deleteImage.mockResolvedValue({ type: 'success' });
      mockUserRepository.getUserByEmail.mockResolvedValue({ id: '1' });
      mockAuthTokenHelper.generateSessionTokens.mockResolvedValue({ idToken: 'new_token' });
  
      const result = await controller.deleteProfilePicture(req, res);
  
      expect(result.type).toBe('success');
      expect(mockCookieHelper.setCookie).toHaveBeenCalledTimes(2);
    });

    it('should return error if user is not found', async () => {
        const req = { user: null } as unknown as Request;
        const res = {} as Response;

        const result = await controller.deleteProfilePicture(req, res);

        expect(result.type).toBe('error');
        expect(result.msg).toBe('Some error occurred while deleting profile image.');
    });

    it('should return error if deleteImage throws', async () => {
        const req = { user: { uid: '123', email: '<EMAIL>' } } as unknown as Request;
        const res = {} as Response;

        (helperService.getHelper as jest.Mock).mockImplementation((name: string) => {
            if (name === 'ProfileHelper') return mockProfileHelper;
            return {};
        });
        mockProfileHelper.deleteImage.mockRejectedValue(new Error('delete failed'));

        const result = await controller.deleteProfilePicture(req, res);
        expect(result.type).toBe('error');
        expect(result.msg).toBe('Some error occurred while deleting profile image.');
    });
  });
  
  describe('confirmDeleteAccount', () => {
    it('should delete account and redirect on success', async () => {
        const req = { cookies: { sso_cookie: 'encrypted_token' } } as unknown as Request;
        const res = { redirect: jest.fn() } as unknown as Response;

        (helperService.get as jest.Mock).mockResolvedValue(mockUserService);
        (helperService.get as jest.Mock).mockResolvedValue(mockAuthTokenHelper);
        (helperService.getHelper as jest.Mock).mockResolvedValue(mockUserMgmtUtilityHelper);
        (helperService.getHelper as jest.Mock).mockResolvedValue(mockCookieHelper);

        mockAuthTokenHelper.decodeJWTToken.mockResolvedValue({ data: { id: 'encrypted_1', email: '<EMAIL>' } });
        mockUserMgmtUtilityHelper.showDeleteButton.mockResolvedValue('Y');
        mockUserService.confirmDeleteAccount.mockResolvedValue({ type: 'success' });

        await controller.confirmDeleteAccount(res, req);

        expect(res.redirect).toHaveBeenCalledWith('/auth/login');
    });

    it('should redirect if user does not have permission to delete', async () => {
        const req = { cookies: { sso_cookie: 'encrypted_token' } } as unknown as Request;
        const res = { redirect: jest.fn() } as unknown as Response;

        (helperService.get as jest.Mock).mockResolvedValue(mockAuthTokenHelper);
        (helperService.getHelper as jest.Mock).mockResolvedValue(mockUserMgmtUtilityHelper);

        mockAuthTokenHelper.decodeJWTToken.mockResolvedValue({ data: { id: 'encrypted_1', email: '<EMAIL>' } });
        mockUserMgmtUtilityHelper.showDeleteButton.mockResolvedValue('N');

        await controller.confirmDeleteAccount(res, req);

        expect(res.redirect).toHaveBeenCalledWith('/user/profile');
    });

    it('should return result if delete fails', async () => {
        const req = { cookies: { sso_cookie: 'encrypted_token' } } as unknown as Request;
        const res = { redirect: jest.fn() } as unknown as Response;

        (helperService.get as jest.Mock).mockResolvedValue(mockUserService);
        (helperService.get as jest.Mock).mockResolvedValue(mockAuthTokenHelper);
        (helperService.getHelper as jest.Mock).mockResolvedValue(mockUserMgmtUtilityHelper);

        mockAuthTokenHelper.decodeJWTToken.mockResolvedValue({ data: { id: 'encrypted_1', email: '<EMAIL>' } });
        mockUserMgmtUtilityHelper.showDeleteButton.mockResolvedValue('Y');
        mockUserService.confirmDeleteAccount.mockResolvedValue({ type: 'failed' });

        const result = await controller.confirmDeleteAccount(res, req);

        expect(res.redirect).not.toHaveBeenCalled();
        expect(result.type).toBe('failed');
    });

    it('should throw error if service throws', async () => {
        const req = { cookies: { sso_cookie: 'encrypted_token' } } as unknown as Request;
        const res = { redirect: jest.fn() } as unknown as Response;

        (helperService.get as jest.Mock).mockResolvedValue(mockUserService);
        (helperService.get as jest.Mock).mockResolvedValue(mockAuthTokenHelper);
        (helperService.getHelper as jest.Mock).mockResolvedValue(mockUserMgmtUtilityHelper);

        mockAuthTokenHelper.decodeJWTToken.mockRejectedValue(new Error('test error'));

        const result = await controller.confirmDeleteAccount(res, req);
        expect(result).toBeInstanceOf(Error);
    });
  });

  describe('exportProfile', () => {
    it('should push to queue and return success message', async () => {
        const res = { json: jest.fn() } as unknown as Response;
        const dto: ExportProfileDto = { email: '<EMAIL>' };

        (helperService.get as jest.Mock).mockResolvedValue(mockDeviantsService);
        mockDeviantsService.pushMessageToQueue.mockResolvedValue({});

        await controller.exportProfile(dto, res);

        expect(res.json).toHaveBeenCalledWith({ success: true, data: '', message: 'Your request is being processed. You will receive an email on your registered email ID.' });
    });

    it('should throw BadRequestException if service fails', async () => {
        const res = { json: jest.fn() } as unknown as Response;
        const dto: ExportProfileDto = { email: '<EMAIL>' };

        (helperService.get as jest.Mock).mockResolvedValue(mockDeviantsService);
        mockDeviantsService.pushMessageToQueue.mockImplementation(() => {
            throw new Error();
        });

        await expect(controller.exportProfile(dto, res)).rejects.toThrow(BadRequestException);
    });
  });

  describe('getReferEarnUrl', () => {
    const mockProfileHelperForRefer = {
        getReferEarnUrl: jest.fn(),
    };

    it('should return referral URL on success', async () => {
        const req = {
            user: { uid: '123' },
            cookies: { sso_cookie: 'test.jwt' },
        } as unknown as Request;
        const res = {} as Response;

        (helperService.getHelper as jest.Mock).mockImplementation((name: string) => {
            if (name === 'UserMgmtUtilityHelper') return mockUserMgmtUtilityHelper;
            if (name === 'profileHelper') return mockProfileHelperForRefer;
            return {};
        });
        mockUserMgmtUtilityHelper.isB2bStudent.mockResolvedValue(false);
        mockProfileHelperForRefer.getReferEarnUrl.mockResolvedValue({ status: true, url: 'http://test.com/refer' });

        const result = await controller.getReferEarnUrl(req, res);

        expect(result.status).toBe(true);
        expect(result.url).toBe('http://test.com/refer');
        expect(result.isB2bStudent).toBe(false);
    });

    it('should return error message on failure', async () => {
        const req = {
            user: { uid: '123' },
            cookies: { sso_cookie: 'test.jwt' },
        } as unknown as Request;
        const res = {} as Response;

        (helperService.getHelper as jest.Mock).mockImplementation((name: string) => {
            if (name === 'UserMgmtUtilityHelper') return mockUserMgmtUtilityHelper;
            if (name === 'profileHelper') return mockProfileHelperForRefer;
            return {};
        });
        mockUserMgmtUtilityHelper.isB2bStudent.mockResolvedValue(false);
        mockProfileHelperForRefer.getReferEarnUrl.mockRejectedValue(new Error('test error'));

        const result = await controller.getReferEarnUrl(req, res);

        expect(result.status).toBe(false);
        expect(result.message).toBe('Error generating referral URL');
    });
  });
});
