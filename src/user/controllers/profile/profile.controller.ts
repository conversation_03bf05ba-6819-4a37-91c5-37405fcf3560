import {
  <PERSON>,
  <PERSON>,
  Render,
  <PERSON>q,
  <PERSON>s,
  Inject,
  Post,
  UsePipes,
  ValidationPipe,
  UseFilters,
  Body,
  Query,
  BadRequestException,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { UseAuthGuard } from '../../../auth/guards/auth.guards';
import { ApiExcludeController } from '@nestjs/swagger';
import { Logger } from '../../../logging/logger';
import { ConfigService } from '@nestjs/config';
import { VIEW_PAGES } from '../../../auth/config/view.constants';
import { HelperService } from '../../../helper/helper.service';
import { UnauthorizedExceptionFilter, ViewValidationFilter } from '../../../common/filters/view-validation.filter';
import { ChangePasswordUserDto } from '../../../auth/dtos/change-password-user.dto';
import { UserService } from '../../../user/services/user.service';
import { Utility } from './../../../common/util/utility';
import { Ic9Service } from '../../../common/services/communication/ice9/ice9.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtService } from '@nestjs/jwt';
import { Request, Response } from 'express';
import { AuthTokenHelper } from '../../../auth/helper/auth.tokenhelper';
import { IUserRepository, UserRepository } from '../../../user/repositories/user/user.repository';
import { CryptoHelper } from '../../../helper/helper.crypto';
import { DeviantsService } from '../../../common/services/communication/deviants/deviants.service';
import { ExportProfileDto } from '../../../user-api/dto/export.profile.dto';




@ApiExcludeController(true)
@Controller('user/profile')
export class ProfileController {
  @Inject() configService: ConfigService;
  @Inject() helperService: HelperService;
  @Inject(JwtService) private readonly jwtService: JwtService
  @Inject('CRYPTO_HELPER') private readonly crypto: CryptoHelper;
  /**
   * Renders the profiles view.
   * @param req
   * @param res
   * @returns
   */

  @Get('')
  @UseAuthGuard()
  @UseFilters(UnauthorizedExceptionFilter)
  @Render(VIEW_PAGES.MAIN)
  async getProfile(@Req() req, @Res() res) {
    const route = Utility.getRoute(req?.route);
    const userIp = Utility.getIpAddress(req); 
    try {
      // Clear cookie if exists
      const cookieHelper = await this.helperService.getHelper('CookieHelper');
      const profileStatus = {
        edit_type: 'basic',
        status: false,
      };
      // linkData cookie
      cookieHelper.clearCookieIfExists(res, req, this.configService.get('linkData'));
      // profile status cookie
      const profileCookieKey = req.user?.uid + this.configService.get('profileCookie');
      if (req.cookies[profileCookieKey]) {
        const { edit_type = 'basic', status = false } = req.cookies[profileCookieKey];
        profileStatus.edit_type = edit_type;
        profileStatus.status = status;
        cookieHelper.clearCookieIfExists(res, req, profileCookieKey);
      }
     
      const decoded: any = this.jwtService.decode(req.cookies[this.configService.get('ssoCookie')]);
      const userType = decoded.data.userType;
      const usrGId = decoded.data.lgid || this.configService.get('defaultUserGroupId');
      const [profileHelper, userMgmtUtilityHelper, userApiV1Helper, userHelper] = await Promise.all([
        this.helperService.getHelper('ProfileHelper'),
        this.helperService.getHelper('UserMgmtUtilityHelper'),
        this.helperService.getHelper('UserApiV1Helper'),
        this.helperService.getHelper('userHelper')
      ]);
      const {selectedExperienceId, selectedEducationId , workExperience , academicExperience , selectedOutcomeId , profileCompletion ,userData} = await profileHelper.fetchProfileData(req.user.uid);
      const emailDomain = Utility.emailDomainExtractor(userData?.email);
      const gdprInfo = {
        gdprExport : Boolean(Number(this.configService.get<string>('gdprEnabled'))),
        showDownloadButton : userMgmtUtilityHelper.showDownloadButton(emailDomain)
      }
     const isB2bStudent = await userMgmtUtilityHelper.isB2bStudent(req.user.uid, usrGId)
      const sectionActive = req.query.section || ''; 
      const [viewSettings, viewData, ifNotB2COrB2BLearner]=  await Promise.all([
        userApiV1Helper.getViewSettings(userType, usrGId),
        userApiV1Helper.loadViewData(
          usrGId
        ),
        userHelper.ifNotB2COrB2BLearner(usrGId)
      ])
      const sectionUrl = {
        managePaymentsUrl: this.configService.get('managePaymentsUrl'),
        digitalKeyUrl: this.configService.get('digitalKeyUrl'),
        examVoucherUrl: this.configService.get('examVoucherUrl'),
        freeTrialUrl: this.configService.get('freeTrialUrl'),
        tickets: this.configService.get('tickets'),
        editProfileUrl: this.configService.get('freeTrialUrl'),
      };
      let fsaClassToAdd = ''
      if(ifNotB2COrB2BLearner) {
          fsaClassToAdd = 'disabled-li-for-fsa'
      }
     const isB2BAndB2C = ifNotB2COrB2BLearner;
     
      return { 
        token: '',
        validationErrors: [],
        route,
        selectedEducationId,
        selectedExperienceId,
        workExperience,
        academicExperience,
        selectedOutcomeId,
        profileCompletion,
        userData,
        profileStatus,
        isB2bStudent,
        isB2BAndB2C,
        sectionActive,
        sectionUrl,
        fsaClassToAdd,
        freeTrialExists: true,
        hideUserMenuBar: false,
        userType,
        userIp,
        ...viewSettings,
        ...gdprInfo,
        ...viewData
      };
    } catch (error : any) {
      Logger.error('saveProfile', {
        METHOD: this.constructor?.name + '@' + this.saveProfile?.name,
        MESSAGE: error.message,
        REQUEST: route,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { token: '', validationErrors: [], route };
    }
  }

  @Post('')
  @UseAuthGuard()
  @UseInterceptors(FileInterceptor('profile_pic'))
  @UseFilters(new ViewValidationFilter(VIEW_PAGES.MAIN))
  async saveProfile(
    @Body() body,
    @UploadedFile() file: Express.Multer.File, 
    @Req() req, 
    @Res({ passthrough: true }) res
  ) {
    const { edit_type } = body;
    const [profileHelper, lrsInstanse, cookieHelper, userHelper] = await Promise.all([
      this.helperService.getHelper('ProfileHelper'),
      this.helperService.getHelper('lrsHelper'),
      this.helperService.getHelper('CookieHelper'),
      this.helperService.getHelper('UserHelper')
    ]);
    let profilePicUpdate = false;
    let result = null;
    try {
      if (edit_type) {
        if (edit_type === 'basic' && !file) {
          Logger.warn('No profile picture file found for basic profile update.', {
            METHOD: this.constructor?.name + '@' + this.saveProfile?.name,
            REQUEST: { body },
            TIMESTAMP: new Date().getTime(),
          });
        } else {
          body.profile_pic = file;
          profilePicUpdate = true;
        }
        result = await profileHelper.saveProfileData(body , req.user);
      }
      if(result) {
        // drupal profile table sync
        if (this.configService.get('enableDrupalSync')) {
          const updatedUserData = await profileHelper.fetchProfileData(req.user.uid, 1);
          if(["professional", "academics", "outcome"].includes(edit_type)) {
            profileHelper.syncTaxonomyDataWithMySQLDrupal(updatedUserData, [edit_type]);
          } else {
            userHelper.syncUserDataWithMySQLDrupal({uid : req.user.uid, ...body});
          }
        }
        lrsInstanse.sendDataToKafka(this.configService.get('profileTopic'), { uid : req.user.uid , email : req.email});
        await cookieHelper.setCookie(res, req.user?.uid + this.configService.get('profileCookie'), {
          edit_type,
          status: result
        });
        if(profilePicUpdate){
          const [userRepository, tokenService] = await Promise.all([
            this.helperService.get<IUserRepository>(UserRepository),
            this.helperService.get<AuthTokenHelper>(AuthTokenHelper)
          ]);          
          const userData = await userRepository.getUserByEmail(req?.user?.email);
          
          // Updating cookie in sentinel if profile data is been updated in paperclip 
          const userTokenDetail = await tokenService.generateSessionTokens(userData, this.configService.get('clientKey'));
          // updating the token
          await cookieHelper.setCookie(res, this.configService.get('ssoCookie'), userTokenDetail['idToken'], {
            expires: this.configService.get<Date>('maxAge'),
          });
        }
        res.redirect('/user/profile');
      } else {
        throw new BadRequestException('Something went wrong, please try again.');
      } 
    } catch (error : any) {
      Logger.error('saveProfile', {
        METHOD: this.constructor?.name + '@' + this.saveProfile?.name,
        MESSAGE: error.message,
        REQUEST: { body },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException('Something went wrong, please try again.');
    }
  }

  /**
   * Renders change password UI
   * @param req
   * @param res
   * @returns
   */
  @Get('/change-password')
  @UseAuthGuard()
  @Render(VIEW_PAGES.MAIN)
  async changePassword(@Req() req, @Res() res) {
    let successType = false;
    const key = req.user?.uid + '_changePassword';
    if (req.cookies[key]) {
      successType = true;
      res.clearCookie(key, {
        httpOnly: true,
        domain: this.configService.get('ssoCookieDomain'),
      });
    }
    const route = Utility.getRoute(req?.route);
    // Fetch userData and profileCompletion 
    const profileHelper = await this.helperService.getHelper('ProfileHelper');
    const { userData, profileCompletion } = await profileHelper.fetchProfileData(req.user.uid);
    return { token: '', validationErrors: [], route: route, type: successType, userData, profileCompletion, hideUserMenuBar: false };
  }
  @Post('/change-password')
  @UsePipes(new ValidationPipe())
  @UseFilters(new ViewValidationFilter(VIEW_PAGES.MAIN))
  @UseAuthGuard()
  async changePasswordPost(@Body() changePasswordDto: ChangePasswordUserDto, @Req() req: Request, @Res({passthrough: true}) res:Response) {
    try {;
      const [userService, cookieHelper] = await Promise.all([
        this.helperService.get<UserService>(UserService),
        this.helperService.getHelper('CookieHelper'),
      ]);
      const changePasswordInfo = {
        ...changePasswordDto,
        user_id: req.user['uid'],
        gid: this.configService.get('defaultGroupId'),
      };
      const response = await userService.changePassword(changePasswordInfo);
      if (response instanceof Error) {
        if (response?.message === 'UserNotFoundException') {
          throw new BadRequestException('User account does not exist for the specified email-id.');
        } else if (response?.message === 'NotAuthorizedException') {
          throw new BadRequestException('Old password is incorrect.');
        } else if (response?.message === 'UserNotGroupMember') {
          throw new BadRequestException('User is not a member of the group.');
        } else if(response?.message === 'AttemptLimitExceed'){
        throw new BadRequestException('You have made more than 5 attempts, please try again after 10 minutes!');
        } else if (response?.message === 'processRequestFailed'){
          throw new BadRequestException('Change password request failed, Due to error in request log.');
        } else {
          throw new BadRequestException('Some error occurred while changing password.');
        }
      }
      if (response) {
        await cookieHelper.setCookie(res, changePasswordInfo?.user_id + '_changePassword', response);
        const script = `<script>
          alert("Your password has been successfully changed. Please log in again with your new password.");
          location.href = '../../auth/logout';
          </script>`;
        res.contentType('text/html');
        return script;
      }
    } catch (error: any) {
      Logger.error('changePasswordPost', {
        METHOD: this.constructor?.name + '@' + this.changePasswordPost.name,
        MESSAGE: error.message,
        REQUEST: { changePasswordDto: changePasswordDto },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException(error.message);
    }
  }

  @Get('/get-region-by-country')
  async getRegionByCountryAction(@Query('countryId') countryId: string) {
    try {
      if (!countryId) {
        throw new BadRequestException('countryId cannot be empty.');
      }
      const ic9Service =  await this.helperService.get<Ic9Service>(Ic9Service)
      const response = await ic9Service.getCountryRegionJson(countryId);
      return response;
    } catch (error: any) {
      Logger.error('getRegionByCountryAction', {
        METHOD: this.constructor.name + '@' + this.getRegionByCountryAction.name,
        MESSAGE: error.message,
        REQUEST: { countryId },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw new BadRequestException(error.message);
    }
  }
  @UseAuthGuard()
  @Post('/delete-profile-picture')
  async deleteProfilePicture(@Req() req, @Res({ passthrough: true }) res) {
    try {
      if (!req.user) {
        return {
          type: 'error',
          msg: 'Some error occurred while deleting profile image.',
        };
      }
  
      const [profileHelper, cookieHelper, userRepository, tokenService] = await Promise.all([
        this.helperService.getHelper('ProfileHelper'),
        this.helperService.getHelper('CookieHelper'),
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.get<AuthTokenHelper>(AuthTokenHelper)
      ]);
 
      const result = await profileHelper.deleteImage(req.user.email);

      const userData = await userRepository.getUserByEmail(req?.user?.email);

      // Updating cookie in sentinel if profile data is been updated in paperclip 
      const userTokenDetail = await tokenService.generateSessionTokens(userData, this.configService.get('clientKey')); 

      await Promise.all([
        // Always update the cookie
        cookieHelper.setCookie(
          res,
          req.user.uid + this.configService.get('profileCookie'),
          {
            profile_pic: this.configService.get('defaultProfilePicUrl'),
          }
        ),
        // updating user session token
        cookieHelper.setCookie(res, this.configService.get('ssoCookie'), userTokenDetail['idToken'], {
              expires: this.configService.get<Date>('maxAge'),
        })
      ])
      return result;
  
    } catch (error: any) {
      Logger.error('deleteProfilePicture', {
        METHOD: `${this.constructor?.name}@${this.deleteProfilePicture?.name}`,
        MESSAGE: error.message,
        STACK: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
  
      return {
        type: 'error',
        msg: 'Some error occurred while deleting profile image.',
      };
    }
  }
  
  @Get('/confirm-delete-account')
  @UseAuthGuard()
  async confirmDeleteAccount(@Res() res: Response, @Req() req: Request) {
    try {
      const result = {type: 'failed', msg: 'Invalid request'};
     
      const [userService, authTokenHelper, userMgmtUtilityHelper] = await Promise.all([
        this.helperService.get<UserService>(UserService),
        this.helperService.get<AuthTokenHelper>(AuthTokenHelper),
        this.helperService.getHelper('UserMgmtUtilityHelper'),
      ]);

      const decodedData = await authTokenHelper.decodeJWTToken(req?.cookies?.[this.configService.get('ssoCookie')]);
      const emailDomain = Utility.emailDomainExtractor(this.crypto.decrypt(decodedData?.data?.email)) || '';
      const checkPremission = await userMgmtUtilityHelper.showDeleteButton(decodedData?.data?.lgid, emailDomain);

      if (checkPremission === 'N') {
        res.redirect('/user/profile');
        return result;
      }

      const deleteAccount = await userService.confirmDeleteAccount(Number(this.crypto.decrypt(decodedData?.data?.id)));

      if (deleteAccount.type === 'success') {
        const cookieHelper = await this.helperService.getHelper('CookieHelper');
        await cookieHelper.setBulkCookie(res, [this.configService.get('ssoCookie'), this.configService.get('slCookie')], {
          domain: null,
          expires: -1,
          path: '/',
        });
        await userMgmtUtilityHelper.clearCacheAndCookies(res, this.crypto.decrypt(decodedData?.data?.id));
        return res.redirect('/auth/login');
      }

      return deleteAccount;
    } catch (error:any) {
      Logger.error('confirmDeleteAccount', {
        METHOD: `${this.constructor?.name}@${this.confirmDeleteAccount?.name}`,
        MESSAGE: error.message,
        STACK: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return error; 
    }
  }

  /**
 * @description Handles GDPR user profile export requests.
 *              Validates incoming request data, sends the export request to a message queue, 
 *              and returns a success response once queued.
 * 
 * 
 * @param exportProfileDto - DTO containing the user's email address for profile export.
 * @param res - Express response object for sending custom JSON response.
 * 
 * @returns { success: true, data: '', message: 'Your request is being processed...' }
 *          or throws BadRequestException in case of errors.
 */
  @Post('/export-profile')
  @UsePipes(new ValidationPipe())
  @UseFilters(new ViewValidationFilter(VIEW_PAGES.MAIN))
  @UseAuthGuard()
  async exportProfile(@Body() exportProfileDto: ExportProfileDto, @Res() res) {   
     try {
      const email = {
        ...exportProfileDto,
      };
      if (!email) {
        throw new BadRequestException(this.configService.get('profileRequestFailed'));
       }
      
      const deviantsService =  await this.helperService.get<DeviantsService>(DeviantsService)
      const response = deviantsService.pushMessageToQueue({
        Email: exportProfileDto.email, 
        Q: this.configService.get('qNameGdprUserExport'),
      });
      if (response instanceof Error) {
        throw new BadRequestException(this.configService.get('profileRequestFailed'));
      }
      return res.json({ success: true, data: '', message: 'Your request is being processed. You will receive an email on your registered email ID.'});
     } catch (error: any) {
      Logger.error('exportProfile', {
        METHOD: this.constructor.name + '@' + this.exportProfile.name,
         MESSAGE: error.message,
        REQUEST: {  exportProfileDto: exportProfileDto  },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
       });
       throw new BadRequestException(error.message);
      }
    }

/**
 * @description Get referral URL for the current user for the "Refer & Earn" feature.
 *              Validates user eligibility (not a B2B student) and fetches a referral link
 *              from Firebase or triggers its generation if not available.
 *
 * @route GET /get-refer-earn-url
 * @access Authenticated users only (uses @UseAuthGuard)
 *
 * @param req - Express request object, containing user data and cookies
 * @param res - Express response object with passthrough enabled for cookie manipulation
 *
 * @returns {
*   status: boolean,
*   url?: string,
*   message?: string,
*   isB2bStudent: boolean,
*   userType: string
* }
* or a failure response with status `false` and error message
*/
  @Get('get-refer-earn-url')
  @UseAuthGuard()
  async getReferEarnUrl(@Req() req, @Res({ passthrough: true }) res) {
    try {
      // Check if user is eligible for referral program
      const decoded: any = await this.jwtService.decode(req.cookies[this.configService.get('ssoCookie')]);
      const userType = decoded.data.userType;
      const uid = req.user.uid;
      const lgid = decoded.data.lgid
      const [userMgmtUtilityHelper, profileHelper] = await Promise.all([
        this.helperService.getHelper('UserMgmtUtilityHelper'),
        this.helperService.getHelper('profileHelper')])
      const isB2bStudent = await userMgmtUtilityHelper.isB2bStudent(uid, lgid);
      const referEarnResponse = await profileHelper.getReferEarnUrl(req.user, res, req);

      return ({
        status: referEarnResponse.status,
        url: referEarnResponse.url || '',
        message: referEarnResponse.msg || '',
        isB2bStudent,
        userType
      });
     
    } catch (error: any) {
      Logger.error('getReferEarnUrl', {
        METHOD: this.constructor?.name + '@' + this.getReferEarnUrl?.name,
        MESSAGE: error.message,
        REQUEST: { userId: req.user?.uid },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return ({
        status: false,
        message: 'Error generating referral URL'
      });
    }
  }
  
}
