import 'newrelic';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import { SwaggerModule, DocumentBuilder, SwaggerDocumentOptions } from '@nestjs/swagger';
import * as bodyParser from 'body-parser';
import * as hbs from 'hbs';
import * as compression from 'compression';
import * as cookieParser from 'cookie-parser';
import helmet from 'helmet';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HelperModule } from './helper/helper.module';
import { HelperService } from './helper/helper.service';
import { HandlebarsHelper } from './helper/handlebarsHelper';
import { loadAllSecrets } from './helper/secretmanger/helper.secrets.fetcher';
import { Logger } from './logging/logger';

// Initialize Swagger configuration for API documentation
function initSwaggerConfig(app: NestExpressApplication) {
  const configService = app.select(ConfigModule).get(ConfigService);

  // Build Swagger configuration using environment variables
  const config = new DocumentBuilder()
    .setTitle(configService.get('swaggerTitle'))
    .setDescription(configService.get('swaggerDescription'))
    .setVersion(configService.get('swaggerVersion'))
    .build();

  const options: SwaggerDocumentOptions = {
    operationIdFactory: (controllerKey: string, methodKey: string) => { 
      Logger.log(`controllerKey: ${controllerKey}, methodKey: ${methodKey}`);
      return methodKey
    },
  };

  // Create and set up Swagger documentation
  const document = SwaggerModule.createDocument(app, config, options);
  SwaggerModule.setup(configService.get('swaggerPath'), app, document);
}

async function bootstrap() {
  // Load secrets from the secret manager
  await loadAllSecrets();

  // Uncomment the following lines to run the application locally with HTTPS
  const fs = require('fs');
  const keyFile = fs.readFileSync(__dirname + '/../ssl/accounts-dev.simplilearn.com-key.pem');
  const certFile = fs.readFileSync(__dirname + '/../ssl/accounts-dev.simplilearn.com.pem');
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    abortOnError: false,
    httpsOptions: {
      key: keyFile,
      cert: certFile,
    },
  });
  // Create the NestJS application
  // const app = await NestFactory.create<NestExpressApplication>(AppModule, {
  //   abortOnError: false, // Prevent application crash on unhandled errors
  // });

  // Initialize helper service
  const helperService = app.select(HelperModule).get(HelperService);
  helperService.initialize(app);

  const configService = app.select(ConfigModule).get(ConfigService);

  // Security middleware using Helmet
  app.use(
    helmet.hidePoweredBy(), // Hide "X-Powered-By" header
    helmet.hsts({
      maxAge: 86400, // Enforce HTTPS for 60 days
      includeSubDomains: false,
    }),
    helmet.referrerPolicy({
      policy: 'no-referrer', // Prevent referrer information leakage
    }),
  );

  // Serve static assets
  app.useStaticAssets(join(__dirname, '..', 'public'));
  app.useStaticAssets(join(__dirname, '..', 'static'));

  // Configure view engine and directories
  app.setBaseViewsDir(join(__dirname, './', '.', 'views'));
  hbs.registerPartials(join(__dirname, './', '.', 'views/partials'));
  hbs.registerPartials(join(__dirname, './', '.', 'views/layout'));
  hbs.registerPartials(join(__dirname, './', '.', 'views/pages'));
  app.setViewEngine('hbs');

  // Initialize Swagger for API documentation
  initSwaggerConfig(app);

  // Middleware for parsing cookies
  app.use(cookieParser());

  // Middleware for parsing request bodies
  app.use(bodyParser.json());
  app.use(bodyParser.urlencoded({ extended: true }));

  // Enable response compression
  app.use(compression());

  // Register custom Handlebars helpers
  HandlebarsHelper.registerHelpers(hbs, app);

  // Start the application and listen on the configured port
  await app.listen(configService.get<string>('appPort'));
}

// Bootstrap the application
bootstrap();
