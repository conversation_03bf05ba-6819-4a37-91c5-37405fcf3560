import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm';

@Entity({ name: 'user_signups' })
@Index('idx_user_id', ['user_id'])
@Index('idx_created_on', ['created_on'])
@Index('idx_updated_on', ['updated_on'])
export class UserSignups {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'user_id', type: 'int', default: 0 })
  user_id: number;

  @Column({ name: 'email', type: 'varchar', length: 100, nullable: true })
  email: string;

  @Column({ name: 'utm_source', type: 'text', nullable: true })
  utm_source: string;

  @Column({ name: 'user_type', type: 'varchar', length: 100, nullable: true })
  user_type: string;

  @Column({ name: 'platform', type: 'varchar', length: 20, nullable: true })
  platform: string;

  @Column({ name: 'status', type: 'int', default: 0 })
  status: number;

  @Column({ name: 'created_on', type: 'int', default: 0 })
  created_on: number;

  @Column({ name: 'updated_on', type: 'int', default: 0 })
  updated_on: number;
}
