import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm';

@Entity({ name: 'enterprise_membership' })
@Index('gid', ['gid'])
@Index('team_id', ['team_id'])
@Index('uid', ['uid'])
@Index('status', ['status'])
@Index('role', ['role'])
export class EnterpriseMembership {
  @PrimaryGeneratedColumn({ name: 'mid' })
  mid: number;

  @Column({ type: 'int', nullable: true })
  gid: number;

  @Column({ type: 'int', nullable: true })
  team_id: number;

  @Column({ type: 'int', nullable: true })
  uid: number;

  @Column({ type: 'varchar', length: 45, nullable: true })
  role: string;

  @Column({ type: 'varchar', length: 32 })
  status: string;

  @Column({ type: 'varchar', length: 32 })
  blocked_status: string;

  @Column({ type: 'int', nullable: true })
  blocked_on: number;

  @Column({ type: 'int', nullable: true })
  blocked_by: number;

  @Column({ type: 'int', nullable: true })
  joined_on: number;

  @Column({ type: 'int', nullable: true })
  requested_on: number;

  @Column({ type: 'int', nullable: true })
  approved_on: number;

  @Column({ type: 'int', nullable: true })
  approved_by: number;

  @Column({ type: 'int', nullable: true })
  added_on: number;

  @Column({ type: 'int', nullable: true })
  added_by: number;

  @Column({ type: 'int', nullable: true })
  parent_mid: number;

  @Column({ type: 'mediumtext', charset: 'utf8mb4', collation: 'utf8mb4_unicode_ci' })
  heritage: number;

  @Column({ type: 'int', nullable: true })
  invited_on: number;

  @Column({ type: 'int', nullable: true })
  invited_by: number;
}
