import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { SentinelUser } from './sentinel-users.entity';

@Entity({ name: 'sentinel_user_professional_data' })
export class SentinelUserProfessionalData {
  @PrimaryGeneratedColumn({ type: 'int', unsigned: true })
  id: number;

  @Column({ type: 'int', unsigned: true, nullable: false })
  uid: number;

  @ManyToOne(() => SentinelUser, (user) => user.uid, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'uid' })
  user: SentinelUser;

  @Column({ type: 'varchar', length: 250, nullable: true })
  designation: string;

  @Column({ type: 'varchar', length: 250, nullable: true })
  company: string;

  @Column({ type: 'varchar', length: 45, nullable: true })
  job_function: string;

  @Column({ type: 'varchar', length: 45, nullable: true })
  industry: string;

  @Column({ type: 'varchar', length: 45, nullable: true })
  joining_date: string;

  @Column({ type: 'varchar', length: 45, nullable: true })
  relieving_date: string;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    name: 'updated_at',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;
}
