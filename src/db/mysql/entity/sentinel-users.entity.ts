import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm';
@Entity({ name: 'sentinel_users' })
@Index('idx_mail', ['mail'])
@Index('idx_uid', ['uid'])
@Index('idx_name', ['name'])
@Index('idx_first_name', ['first_name'])
@Index('idx_last_name', ['last_name'])
@Index('idx_phone_no', ['phone_no'])
export class SentinelUser {
  @PrimaryGeneratedColumn({ name: 'uid' })
  uid: number;

  @Column({ type: 'varchar', length: 60 })
  name: string;

  @Column({ type: 'varchar', length: 128 })
  pass: string;

  @Column({ type: 'varchar', length: 254 })
  mail: string;

  @Column({ type: 'varchar', length: 400, nullable: true })
  display_name: string;

  @Column({ type: 'varchar', length: 60, nullable: true })
  timezone: string;

  @Column({ type: 'varchar', length: 10, nullable: true })
  country_code: string;

  @Column({ type: 'varchar', length: 110, nullable: true })
  phone_no: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  location: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  gender: string;

  @Column({ type: 'varchar', length: 15, nullable: true })
  profile_visibility: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  linkedin_url: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  blog_url: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  facebook_url: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  website_url: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  twitter_url: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  other_url: string;

  @Column({ type: 'int', nullable: true })
  newsletter: number;

  @Column({ type: 'int', nullable: true })
  accept_agreement: number;

  @Column({ type: 'int', nullable: true })
  dob: number;

  @Column({ type: 'varchar', length: 15, nullable: true })
  training_funded_by: string;

  @Column({ type: 'varchar', length: 15, nullable: true })
  user_career_type: string;

  @Column({ type: 'int', nullable: true })
  user_options: number;

  @Column({ type: 'longtext', nullable: true })
  sso_attributes: string;

  @Column({ type: 'varchar', length: 400, nullable: true })
  first_name: string;

  @Column({ type: 'varchar', length: 400, nullable: true })
  last_name: string;

  @Column({ type: 'int', nullable: true })
  linkedin_status: number;

  @Column({ type: 'int', nullable: true })
  account_setup: number;

  @Column({ type: 'int', nullable: true })
  password_created: number;

  @Column({ type: 'int', nullable: true })
  total_work_experience: number;

  @Column({ type: 'varchar', length: 3, nullable: true })
  user_type: string;

  @Column({ type: 'int', nullable: true })
  updated_on: number;

  @Column({ type: 'varchar', length: 8, nullable: true })
  user_category: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  middle_name: string;

  @Column({ type: 'varchar', length: 5, nullable: true })
  title: string;

  @Column({ type: 'varchar', length: 35, nullable: true })
  state: string;

  @Column({ type: 'longtext', nullable: true })
  correspondence_address: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  country_of_residence: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  profile_pic: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  objective_taking_course: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  where_are_you_in_career: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  highest_level_of_education: string;

  @Column({ type: 'int', nullable: true })
  created: number;

  @Column({ type: 'int', nullable: true })
  access: number;

  @Column({ type: 'int', nullable: true })
  login: number;

  @Column({ type: 'tinyint', nullable: true })
  status: number;
}
