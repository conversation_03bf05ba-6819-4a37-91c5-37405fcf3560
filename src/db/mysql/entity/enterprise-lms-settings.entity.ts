import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

@Entity({ name: 'enterprise_lms_settings' })
@Index('GROUPID', ['group_id'], { unique: false })
export class EnterpriseLmsSetting {
  @PrimaryGeneratedColumn({ type: 'bigint', unsigned: true })
  id: number;

  @Column({ type: 'bigint', unsigned: true, nullable: false })
  group_id: number;

  @Column({ type: 'bigint', unsigned: true, nullable: false })
  preference_id: number;

  @Column({ type: 'varchar', length: 45, nullable: false })
  value: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  custom_value: string | null;

  @Column({ type: 'tinyint', default: 1 })
  status: number;
}
