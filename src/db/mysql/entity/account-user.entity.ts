import { Entity, Column, PrimaryColumn, Unique, Index } from 'typeorm';

@Entity({ name: 'users' })
@Unique(['name'])
export class AccountUser {
  @PrimaryColumn({ name: 'uid', unsigned: true })
  uid: number;

  @Column({ type: 'varchar', length: 60 })
  name: string;

  @Column({ type: 'varchar', length: 128 })
  pass: string;

  @Index()
  @Column({ type: 'varchar', length: 254, nullable: true })
  mail: string;

  @Column({ type: 'varchar', length: 255, default: '' })
  theme: string;

  @Column({ type: 'varchar', length: 255, default: '' })
  signature: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  signature_format: string;

  @Index()
  @Column()
  created: number;

  @Index()
  @Column({ type: 'int', default: 0 })
  access: number;

  @Column({ type: 'int', default: 0 })
  login: number;

  @Column({ type: 'tinyint' })
  status: number;

  @Column({ type: 'varchar', length: 32, nullable: true })
  timezone: string;

  @Column({ type: 'varchar', length: 12 })
  language: string;

  @Index()
  @Column({ type: 'int', default: 0 })
  picture: number;

  @Column({ type: 'varchar', length: 254, nullable: true })
  init: string;

  @Column({ type: 'longblob', nullable: true })
  data: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  displayName: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  userKey: string;
}
