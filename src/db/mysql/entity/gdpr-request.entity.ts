import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity('gdpr_request') // Table name in MySQL
export class GdprRequest {
  @PrimaryGeneratedColumn({ name: 'id', unsigned: true })
  id: number;

  @Column({ type: 'int', unsigned: true, nullable: false })
  uid: number;

  @Column({ type: 'enum', enum: ['edit', 'export', 'delete'], nullable: false })
  action: string;

  @Column({ type: 'enum', enum: ['inqueue', 'done'], nullable: false })
  state: string;

  @Column({ name: 'result', type: 'mediumtext', nullable: true })
  result: string;

  @Column({ name: 'requested_on', type: 'int', nullable: false })
  requestedOn: number;

  @Column({ name: 'modified_on', type: 'int', nullable: false })
  modifiedOn: number;

  //static readonly ACTIONS = ['edit', 'export', 'delete'];
}
