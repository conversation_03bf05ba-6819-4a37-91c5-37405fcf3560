import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity('failed_community_user_list')
//TODO: verify types from model in cloud6
export class FailedCommunityUser {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  email: string;

  @Column({ type: 'text' })
  user_data: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  user_agent: string | null;

  @Column({ type: 'varchar', length: 255 })
  ip_address: string;

  @Column({ type: 'tinyint', default: 0 })
  is_processed: number;

  @Column({ type: 'tinyint', default: 0 })
  status: number;

  @Column({ type: 'bigint' })
  creation_time: number;

  @Column({ type: 'bigint' })
  modified_time: number;
}
