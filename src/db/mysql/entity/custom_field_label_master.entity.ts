import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

@Entity('custom_field_label_master')
@Index('gid_displayname_idx', ['gid', 'display_name'])
export class CustomFieldLabelMaster {
  @PrimaryGeneratedColumn({ type: 'int', unsigned: true })
  custom_field_label_id: number;

  @Column({ type: 'int', unsigned: true, default: 0, comment: 'for public access gid = 0' })
  gid: number;

  @Column({
    type: 'enum',
    enum: ['text', 'select'],
    collation: 'utf8mb4_unicode_ci',
    nullable: false,
  })
  field_type: 'text' | 'select';

  @Column({
    type: 'enum',
    enum: ['text', 'alpha_numeric', 'number', 'single_select'],
    collation: 'utf8mb4_unicode_ci',
    nullable: false,
  })
  field_value_type: 'text' | 'alpha_numeric' | 'number' | 'single_select';

  @Column({
    type: 'enum',
    enum: ['public', 'private'],
    collation: 'utf8mb4_unicode_ci',
    nullable: false,
  })
  access_type: 'public' | 'private';

  @Column({
    type: 'varchar',
    length: 100,
    collation: 'utf8mb4_unicode_ci',
    nullable: false,
    comment: 'Unique within group access',
  })
  label_key: string;

  @Column({
    type: 'varchar',
    length: 100,
    collation: 'utf8mb4_unicode_ci',
    nullable: false,
    comment: 'Unique within group access',
  })
  display_name: string;

  @Column({
    type: 'text',
    collation: 'utf8mb4_unicode_ci',
    nullable: true,
  })
  description: string | null;

  @Column({
    type: 'varchar',
    length: 100,
    collation: 'utf8mb4_unicode_ci',
    nullable: true,
    default: null,
  })
  default_value: string | null;

  @Column({
    type: 'text',
    collation: 'utf8mb4_unicode_ci',
    nullable: true,
    comment: 'placeholder',
  })
  help_text: string | null;

  @Column({
    type: 'enum',
    enum: ['0', '1'],
    collation: 'utf8mb4_unicode_ci',
    nullable: false,
    default: '1',
  })
  status: '0' | '1';

  @Column({ type: 'int', unsigned: true, nullable: false })
  created_by: number;

  @Column({ type: 'int', unsigned: true, nullable: false })
  creation_date: number;

  @Column({ type: 'int', unsigned: true, nullable: false })
  modified_by: number;

  @Column({ type: 'int', unsigned: true, nullable: false })
  modification_date: number;
}
