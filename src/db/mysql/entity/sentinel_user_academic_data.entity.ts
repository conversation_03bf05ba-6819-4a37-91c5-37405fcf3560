import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  JoinColumn,
} from 'typeorm';
import { SentinelUser } from './sentinel-users.entity';

@Entity({ name: 'sentinel_user_academic_data' })
@Index('uid_idx', ['uid']) // Index on uid
export class SentinelUserAcademicData {
  @PrimaryGeneratedColumn({ type: 'int', unsigned: true })
  id: number;

  @Column({ type: 'int', unsigned: true, nullable: false })
  uid: number;

  @ManyToOne(() => SentinelUser, (user) => user.uid, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'uid' })
  user: SentinelUser;

  @Column({ type: 'varchar', length: 255, nullable: true })
  qualification: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: 'School/College' })
  institute: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  specialization: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  course_start_date: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  course_end_date: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updated_at: Date;
}
