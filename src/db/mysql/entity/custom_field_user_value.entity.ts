import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

@Entity('custom_field_user_value')
export class CustomFieldUserValue {
  @PrimaryGeneratedColumn({ type: 'int', unsigned: true })
  id: number;

  @Column({ type: 'int', unsigned: true })
  @Index('labelid_gid_idx')
  custom_field_label_id: number;

  @Column({ type: 'int', unsigned: true })
  @Index('labelid_gid_idx')
  gid: number;

  @Column({ type: 'varchar', length: 255, collation: 'utf8mb4_unicode_ci' })
  email: string;

  @Column({ type: 'mediumtext', nullable: true, collation: 'utf8mb4_unicode_ci' })
  value: string | null;

  @Column({ type: 'int', unsigned: true })
  creation_date: number;

  @Column({ type: 'int', unsigned: true })
  modification_date: number;
}
