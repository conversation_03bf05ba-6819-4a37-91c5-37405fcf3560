// src/group-settings/group-settings.entity.ts

import { Entity, PrimaryColumn, Column, Index, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('group_settings')
@Index('group_team_key_idx', ['gid', 'team_id', 'key_set'])
export class GroupSettings {
  @PrimaryColumn()
  id: number;

  @Column()
  gid: number;

  @Column({ default: 0 })
  team_id: number;

  @Column({ length: 100, collation: 'utf8mb4_unicode_ci' })
  key_set: string;

  @Column({ length: 100, collation: 'utf8mb4_unicode_ci' })
  identifier: string;

  @Column({ type: 'mediumtext', collation: 'utf8mb4_unicode_ci', nullable: true })
  value: string;

  @Column({ default: 1 })
  status: number;

  @Column()
  created_by: number;

  @CreateDateColumn({ type: 'int' })
  created_on: number;

  @Column()
  modified_by: number;

  @UpdateDateColumn({ type: 'int' })
  modified_on: number;
}
