import { Entity, PrimaryGeneratedColumn, Column, Unique } from 'typeorm';

@Entity({ name: 'enterprise_lms_preferences' })
@Unique('display_order1_UNIQUE', ['display_order1'])
export class EnterpriseLmsPreferences {
    @PrimaryGeneratedColumn({ type: 'bigint' })
    id: string;

    @Column({ type: 'varchar', length: 100, collation: 'utf8mb4_unicode_ci', nullable: false })
    preference_name: string;

    @Column({ type: 'varchar', length: 45, collation: 'utf8mb4_unicode_ci', nullable: false })
    preference_type: string;

    @Column({ type: 'varchar', length: 200, collation: 'utf8mb4_unicode_ci', nullable: true })
    options: string | null;

    @Column({ type: 'varchar', length: 45, collation: 'utf8mb4_unicode_ci', nullable: true })
    default: string | null;

    @Column({ type: 'tinyint', default: 1, nullable: true })
    status: number;

    @Column({ type: 'tinyint', nullable: false })
    display_order: number;

    @Column({ type: 'tinyint', nullable: true })
    display_order1: number | null;
}