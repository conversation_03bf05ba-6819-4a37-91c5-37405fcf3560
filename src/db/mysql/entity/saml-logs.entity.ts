import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm';

@Entity({ name: 'saml_logs' })
@Index('idx_gid', ['gid'])
@Index('idx_created_on', ['created_on'])
@Index('idx_updated_on', ['updated_on'])
export class SamlLogs {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'gid', type: 'int', nullable: false })
  gid: number;

  @Column({ name: 'email', type: 'varchar', length: 100, nullable: false })
  email: string;

  @Column({ name: 'title', type: 'varchar', length: 45, nullable: false })
  title: string;

  @Column({ name: 'attributes', type: 'longtext', nullable: false })
  attributes: string;

  @Column({ name: 'get_params', type: 'longtext', nullable: false })
  get_params: string;

  @Column({ name: 'post_params', type: 'longtext', nullable: false })
  post_params: string;

  @Column({ name: 'created_on', type: 'int', nullable: false })
  created_on: number;

  @Column({ name: 'updated_on', type: 'int', nullable: false })
  updated_on: number;
}
