import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('mysqlDatabaseHost'),
        port: configService.get('mysqlDatabasePort'),
        username: configService.get('mysqlDatabaseUserName'),
        password: configService.get('mysqlDatabasePassword'),
        database: configService.get('mysqlDatabaseName'),
        entities: [__dirname + '/../**/*.entity{.ts,.js}'],
        synchronize: false,
        // logging: ['query', 'error'], // logs SQL queries and errors
        // logger: 'advanced-console', // prints to console in readable format
        connectTimeout: configService.get<number>('connectionTimeout'),
        poolSize: configService.get<number>('mysqlConnection'),
      }),
      inject: [ConfigService],
    }),
  ],
})
export class MysqlModule {}
