import { Test, TestingModule } from '@nestjs/testing';
import { SecureMongooseConfig } from './mongo.connection.secure.options';
import { IKMSServiceProvider } from './kms/mongo.kms.provider.service';
import { ConfigService } from '@nestjs/config';

describe('SecureMongooseConfig', () => {
  let service: SecureMongooseConfig;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SecureMongooseConfig,
        {
          provide: IKMSServiceProvider,
          useValue: {
            KeyVaultNamespace: 'test-namespace',
            KmsProvider: Promise.resolve({ local: { key: Buffer.from('test-key') } }),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (key === 'defaultdb') return 'test-db';
              if (key === 'kmsSettings') return { keys: { token: 'dGVzdC10b2tlbg==' } }; // base64 encoded string
              return null;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<SecureMongooseConfig>(SecureMongooseConfig);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getSchemaMap', () => {
    it('should return a valid schema map', () => {
      const schemaMap = service['getSchemaMap']();
      expect(schemaMap).toHaveProperty('test-db.users');
      expect(schemaMap).toHaveProperty('test-db.usertokens');
      expect(schemaMap).toHaveProperty('test-db.passwordhistories');
    });
  });

  describe('createMongooseOptions', () => {
    it('should return valid mongoose options', async () => {
      const options = await service.createMongooseOptions();
      expect(options).toHaveProperty('autoEncryption');
      expect(options.autoEncryption.keyVaultNamespace).toBe('test-namespace');
    });
  });
});
