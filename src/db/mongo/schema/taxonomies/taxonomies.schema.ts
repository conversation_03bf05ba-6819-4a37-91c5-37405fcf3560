  import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export enum Category {
  INDUSTRY = 'industry',
  DESIGNATION = 'designation',
  QUALIFICATION = 'qualification',
  SPECIALIZATION = 'specialization',
  INTERESTS = 'interests',
  INSTITUTES = 'institutes',
  COMPANY = 'company',
  JOB_FUNCTION = 'job_function',
  OBJECTIVE_OF_TAKING_COURSE = 'objective_of_taking_course',
}

export type TaxonomiesDocument = Taxonomies & Document;

@Schema()
export class Taxonomies {
  @Prop({ type: Types.ObjectId })
  _id: Types.ObjectId; 

  @Prop({
    type: String,
    enum: Object.values(Category),
    required: true,
  })
  category: Category;

  @Prop({
    type: String,
    required: true,
  })
  name: string;

  @Prop({
    required: true,
  })
  tid: number;
}

export const TaxonomySchema = SchemaFactory.createForClass(Taxonomies);
