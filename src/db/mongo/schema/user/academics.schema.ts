import { Prop, Schema } from '@nestjs/mongoose';
import { Timestamp } from 'mongodb';
import { Taxonomies } from '../taxonomies/taxonomies.schema';
import { Types } from 'mongoose';
@Schema()
export class Academics {
  @Prop({ type: Types.ObjectId, ref: Taxonomies })
  qualification?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: Taxonomies })
  objective?: Types.ObjectId;

  @Prop()
  specialization?: string;

  @Prop()
  institute?: string;

  @Prop()
  course_start_date?: Timestamp;

  @Prop()
  course_end_date?: Timestamp;
}
