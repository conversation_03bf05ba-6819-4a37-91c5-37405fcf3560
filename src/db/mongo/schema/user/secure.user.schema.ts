export const mongoUserSchema = {
  bsonType: 'object',
  properties: {
    email: {
      encrypt: {
        bsonType: 'string',
      },
    },

    display_name: {
      encrypt: {
        bsonType: 'string',
      },
    },

    name: {
      encrypt: {
        bsonType: 'string',
      },
    },

    first_name: {
      encrypt: {
        bsonType: 'string',
      },
    },

    last_name: {
      encrypt: {
        bsonType: 'string',
      },
    },

    phone_no: {
      encrypt: {
        bsonType: 'string',
      },
    },

    gender: {
      encrypt: {
        bsonType: 'string',
      },
    },

    middle_name: {
      encrypt: {
        bsonType: 'string',
      },
    },

    title: {
      encrypt: {
        bsonType: 'string',
      },
    },

    state: {
      encrypt: {
        bsonType: 'string',
      },
    },

    correspondence_address: {
      encrypt: {
        bsonType: 'string',
      },
    },
  },
};
