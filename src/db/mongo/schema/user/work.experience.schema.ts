import { Prop, Schema } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { Taxonomies } from '../taxonomies/taxonomies.schema';
@Schema()
export class WorkExperience {
  @Prop()
  readonly designation?: string;

  @Prop({})
  readonly company?: string;

  @Prop({ type: Types.ObjectId, ref: Taxonomies })
  readonly job_function?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: Taxonomies })
  readonly industry?: Types.ObjectId;

  @Prop()
  readonly joining_date?: string;

  @Prop()
  readonly relieving_date?: string;
}
