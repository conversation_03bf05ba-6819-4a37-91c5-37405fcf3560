import { Binary, UUID } from 'bson';

export class MongoSchemaFactory {
  static createSchemaMap(...schemaMapEntries: { dbName: string; collectionName: string; mongoSchema: object }[]) {
    const schemaMap: Record<string, object> = {};

    for (const item of schemaMapEntries) {
      schemaMap[`${item.dbName}.${item.collectionName}`] = item.mongoSchema;
    }

    return schemaMap;
  }

  static generateSecureMongoSchema(
    key: string,
    schema: object,
    algorithm = 'AEAD_AES_256_CBC_HMAC_SHA_512-Deterministic',
  ) {
    const keyId =
      typeof key === 'string' && key.length === 36 // UUIDs are 36 chars long
        ? new UUID(key)
        : new Binary(Buffer.from(key, 'base64'), 4);

    return {
      encryptMetadata: {
        keyId: [keyId],
        algorithm,
      },
      ...schema,
    };
  }

  static generateSecureMongoSchemaValidator(key: string, schema: object) {
    return {
      validator: {
        $jsonSchema: this.generateSecureMongoSchema(key, schema),
      },
    };
  }
}
