import { Test, TestingModule } from '@nestjs/testing';
import { SecureMongooseConfig, special_encryptionOptions } from './mongo.connection.secure.options';
import { IKMSServiceProvider } from './kms/mongo.kms.provider.service';
import { ConfigService } from '@nestjs/config';
import { mongoUserTokenSchema } from './schema/user-token/secure.user.token.schema';
import { mongoUserSchema } from './schema/user/secure.user.schema';
import { MongoSchemaFactory } from './mongo.schema.factory';
import { DefaultMongooseConfig } from './mongo.connection.options';
import { mongoPasswordHistorySchema } from './schema/password-history/password.history.secure.schema';

// Create a mock class for the IKMSServiceProvider
class KMSServiceProviderMock {
  KeyVaultNamespace = 'test-kv-namespace';
  KmsProvider = Promise.resolve({
    aws: {
      accessKeyId: 'test-access-key',
      secretAccessKey: 'test-secret-key',
      sessionToken: 'test-session-token',
    },
  });
}

// Create a mock class for the ConfigService
class ConfigServiceMock {
  get = jest.fn((key) => {
    switch (key) {
      case 'defaultdb':
        return 'test_db';
      case 'kmsSettings':
        return {
          provider: 'aws',
          kmsVaultDB: 'test_db',
          keys: {
            user: 'user-key',
            userToken: 'user-token-key',
          },
        };
      case 'defaultdb':
        return 'tedt_db';
      case 'url':
        return 'tedt_db';
      case 'maxPoolSize':
        return 100;
      case 'maxTimeout':
        return 1000;
    }
  });
}

describe('SecureMongooseConfig', () => {
  let secureConfiguration: SecureMongooseConfig;
  let kmsService: IKMSServiceProvider;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SecureMongooseConfig,
        {
          provide: IKMSServiceProvider,
          useClass: KMSServiceProviderMock,
        },
        {
          provide: ConfigService,
          useClass: ConfigServiceMock,
        },
      ],
    }).compile();

    secureConfiguration = module.get<SecureMongooseConfig>(SecureMongooseConfig);
    kmsService = module.get<IKMSServiceProvider>(IKMSServiceProvider);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(secureConfiguration).toBeDefined();
  });
  /*
        describe('getSchemaMap', () => {
            it('should return the correct schema map', () => {
                // Call the method to get the schema map
                const result = secureConfiguration.getSchemaMap();
    
                // Expected schema map
                const expectedSchemaMap = {
                    test_db: {
                        users: MongoSchemaFactory.generateSecureMongoSchema('user-key', mongoUserSchema),
                        usertokens: MongoSchemaFactory.generateSecureMongoSchema('user-token-key', mongoUserTokenSchema),
                    },
                };
    
                expect(result).toEqual(expectedSchemaMap);
            });
        });
    */
  describe('createMongooseOptions', () => {
    it('should create Mongoose options with correct encryption overrides', async () => {
      // Call the method to get the Mongoose options
      const result = await secureConfiguration.createMongooseOptions();

      const defaults1 = await new DefaultMongooseConfig(configService).createMongooseOptions();

      const defaults = {
        uri: configService.get<string>('url'),
        maxPoolSize: configService.get<number>('maxPoolSize'),
        retryAttempts: 9, //using default value. refer https://github.com/nestjs/mongoose/blob/master/lib/common/mongoose.utils.ts
        retryDelay: 3000, //using default value. refer https://github.com/nestjs/mongoose/blob/master/lib/common/mongoose.utils.ts
      };

      // Expected encryption options
      const encryptionOptions = {
        autoEncryption: {
          keyVaultNamespace: 'test-kv-namespace',
          kmsProviders: {
            aws: {
              accessKeyId: 'test-access-key',
              secretAccessKey: 'test-secret-key',
              sessionToken: 'test-session-token',
            },
          },
          schemaMap: {
            'test_db.users': MongoSchemaFactory.generateSecureMongoSchema('user-key', mongoUserSchema),
            'test_db.usertokens': MongoSchemaFactory.generateSecureMongoSchema('user-token-key', mongoUserTokenSchema),
            'test_db.passwordhistories': MongoSchemaFactory.generateSecureMongoSchema(
              'user-token-key',
              mongoPasswordHistorySchema,
            ),
          },
          ...special_encryptionOptions,
        },
      };
      const expectedEncryptionOptions = {
        //...secureConfiguration.defaults,
        // ...secureConfiguration.connectionFactoryOptions,
        //...secureConfiguration.special_encryptionOptions,
        ...defaults,
        ...encryptionOptions,
      };
      console.debug(expectedEncryptionOptions);
      expect(result).toEqual(expectedEncryptionOptions);
    });
  });
});
