import { Test, TestingModule } from '@nestjs/testing';
import { DefaultMongooseConfig } from './mongo.connection.options';
import { ConfigService } from '@nestjs/config';

describe('DefaultMongooseConfig', () => {
  let service: DefaultMongooseConfig;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DefaultMongooseConfig,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              const config = {
                url: 'mongodb://localhost:27017/test',
                maxPoolSize: 10,
                retryAttempts: 9,
                retryDelay: 3000,
              };
              return config[key] || defaultValue;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<DefaultMongooseConfig>(DefaultMongooseConfig);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createMongooseOptions', () => {
    it('should return the default mongoose options', async () => {
      const options = await service.createMongooseOptions();

      expect(options).toEqual({
        uri: 'mongodb://localhost:27017/test',
        maxPoolSize: 10,
        retryAttempts: 9,
        retryDelay: 3000,
      });
    });
  });
});
