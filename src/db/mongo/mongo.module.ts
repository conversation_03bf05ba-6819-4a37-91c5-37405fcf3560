import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { KMSServiceProviderModule } from '../../db/mongo/kms/mongo.kmsmodule';
import { DefaultMongooseConfig } from '../../db/mongo/mongo.connection.options';
import { SecureMongooseConfig } from '../../db/mongo/mongo.connection.secure.options';

/**
 * MongoModule is responsible for configuring and providing
 * MongoDB connections using Mongoose in a NestJS application.
 */
@Module({
  imports: [
    // Default MongoDB connection
    MongooseModule.forRootAsync({
      connectionName: 'default', // Connection name for default database
      useClass: DefaultMongooseConfig, // Configuration class for default connection
    }),
    // Secure MongoDB connection with KMS integration
    MongooseModule.forRootAsync({
      connectionName: 'secure', // Connection name for secure database
      useClass: SecureMongooseConfig, // Configuration class for secure connection
      imports: [KMSServiceProviderModule], // Importing KMS module for secure connection
    }),
  ],
  // Exporting MongooseModule to make it available for other modules
  exports: [MongooseModule],
})
export class MongoModule {}
