import { Injectable, Inject } from '@nestjs/common';
import { MongooseModuleOptions } from '@nestjs/mongoose';
import { mongoUserTokenSchema } from './schema/user-token/secure.user.token.schema';
import { mongoUserSchema } from './schema/user/secure.user.schema';

import { DefaultMongooseConfig } from './mongo.connection.options';
import { MongoSchemaFactory } from './mongo.schema.factory';
import { IKMSServiceProvider } from './kms/mongo.kms.provider.service';
import { Logger } from '../../logging/logger';
import { mongoPasswordHistorySchema } from './schema/password-history/password.history.secure.schema';

// Special encryption options for MongoDB
export const specialEncryptionOptions = {
  /** Allows the user to bypass auto encryption, maintaining implicit decryption */
  bypassAutoEncryption: false,
  options: {
    /** An optional hook to catch logging messages from the underlying encryption engine */
    logger: (level, message: string) => {
      Logger.error(`MongoDB Encryption Error:`, {
        level,
        message,
      });
    },
  },
  extraOptions: {
    /** If true, autoEncryption will not attempt to spawn a mongocryptd before connecting */
    mongocryptdBypassSpawn: false,

    /** The path to the mongocryptd executable on the system */
    mongocryptdSpawnPath: '/usr/bin/mongocryptd',

    /** Command line arguments to use when auto-spawning a mongocryptd */
    mongocryptdSpawnArgs: [],

    /**
     * If specified, never use mongocryptd and instead fail when the MongoDB Crypt
     * shared library could not be loaded.
     *
     * This is always true when `cryptSharedLibPath` is specified.
     *
     * Requires the MongoDB Crypt shared library, available in MongoDB 6.0 or higher.
     */
    cryptSharedLibRequired: false,
  },
};

@Injectable()
export class SecureMongooseConfig extends DefaultMongooseConfig {
  @Inject(IKMSServiceProvider)
  readonly kmsService: IKMSServiceProvider;

  /**
   * Generates the schema map for MongoDB collections with encryption settings.
   */
  protected getSchemaMap(): any {
    const dbName = this.configService.get<string>('defaultdb');
    const kmsKeys = this.configService.get('kmsSettings', {}).keys;

    const schemaDefinitions = [
      {
        dbName: dbName,
        collectionName: 'users',
        mongoSchema: MongoSchemaFactory.generateSecureMongoSchema(kmsKeys.token, mongoUserSchema),
      },
      {
        dbName: dbName,
        collectionName: 'usertokens',
        mongoSchema: MongoSchemaFactory.generateSecureMongoSchema(kmsKeys.token, mongoUserTokenSchema),
      },
      {
        dbName: dbName,
        collectionName: 'passwordhistories',
        mongoSchema: MongoSchemaFactory.generateSecureMongoSchema(kmsKeys.token, mongoPasswordHistorySchema),
      },
    ];

    // Create and return the schema map
    return MongoSchemaFactory.createSchemaMap(...schemaDefinitions);
  }

  /**
   * Creates and returns the Mongoose module options with encryption settings.
   */
  async createMongooseOptions(): Promise<MongooseModuleOptions> {
    const schemaMap = this.getSchemaMap();
    const keyVaultNamespace = this.kmsService.KeyVaultNamespace;
    const kmsProviders: any = await this.kmsService.KmsProvider;

    Logger.log(
      `MongoDB Encryption Config:
       Key Vault Namespace: ${keyVaultNamespace}
       KMS Providers: ${JSON.stringify(kmsProviders, null, 2)}`,
    );

    console.log('MongoDB Encryption Config:');
    console.log('Key Vault Namespace:', keyVaultNamespace);
    console.log('KMS Providers:', JSON.stringify(kmsProviders, null, 2));

    // Combine default options with encryption settings
    const mongooseOptions = {
      ...this.defaults,
      autoEncryption: {
        keyVaultNamespace: keyVaultNamespace,
        kmsProviders: kmsProviders,
        schemaMap: schemaMap,
        ...specialEncryptionOptions,
      },
    };

    console.log(mongooseOptions);
    return mongooseOptions;
  }
}
