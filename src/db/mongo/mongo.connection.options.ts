import { Inject, Injectable } from '@nestjs/common';
import { MongooseOptionsFactory, MongooseModuleOptions } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';

/**
 * DefaultMongooseConfig is a service that provides default configuration options
 * for connecting to a MongoDB database using Mongoose.
 */
@Injectable()
export class DefaultMongooseConfig implements MongooseOptionsFactory {
  /**
   * Default configuration options for Mongoose.
   */
  protected readonly defaults: MongooseModuleOptions;

  /**
   * Constructor to inject the ConfigService for accessing environment variables.
   * @param configService - The ConfigService instance.
   */
  constructor(@Inject(ConfigService) protected readonly configService: ConfigService) {
    this.defaults = {
      // MongoDB connection URI
      uri: this.configService.get<string>('url'),

      // The maximum number of connections in the connection pool
      maxPoolSize: this.configService.get<number>('maxPoolSize', 10), // Default to 10 if not set

      // Retry attempts for reconnection
      retryAttempts: this.configService.get<number>('retryAttempts', 9), // Default to 9 if not set

      // Delay between retry attempts in milliseconds
      retryDelay: this.configService.get<number>('retryDelay', 3000), // Default to 3000ms if not set
    };
  }

  /**
   * Creates and returns the Mongoose configuration options.
   * @returns A promise that resolves to MongooseModuleOptions.
   */
  async createMongooseOptions(): Promise<MongooseModuleOptions> {
    return this.defaults;
  }
}
