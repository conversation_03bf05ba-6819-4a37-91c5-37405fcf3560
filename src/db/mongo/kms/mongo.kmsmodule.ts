import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { IKMSServiceProvider, KMSServiceProviderException } from './mongo.kms.provider.service';
import { LocalKMSService } from './mongo.kms.localservice';
import { AWSKMSService } from './mongo.kms.provider.aws';
import { Logger } from '../../../logging/logger';

@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: IKMSServiceProvider,
      useFactory: async (configService: ConfigService) => {
        try {
          const provider = configService.get('kmsSettings', {}).provider;
          if (provider === 'aws') {
            return new AWSKMSService(configService);
          } else if (provider === 'local') {
            return new LocalKMSService(configService);
          } else {
            throw new KMSServiceProviderException(`Un-Supported KMS provider [${provider}] specified`);
          }
        } catch (err: any) {
          Logger.error('KMS Provider==', err);
          throw err;
        }
      },
      inject: [ConfigService],
    },
  ],
  exports: [IKMSServiceProvider],
})
export class KMSServiceProviderModule {}
