import { Injectable } from '@nestjs/common';
import { IKMSServiceProvider } from './mongo.kms.provider.service';
import { ConfigService } from '@nestjs/config';
import { KMSProviders } from 'mongodb-client-encryption';
import { Logger } from '../../../logging/logger';
@Injectable()
export class AWSKMSService implements IKMSServiceProvider {
  constructor(private readonly configService: ConfigService) {}
  get KeyVaultNamespace(): string {
    const kmsSettings = this.configService.get('kmsSettings', {});
    const kmsVaultDB: string = kmsSettings.kmsVaultDB ?? this.configService.get('defaultdb');

    return `${kmsVaultDB}.__keyVault`;
  }
  get KmsProvider(): Promise<KMSProviders> {
    //await loadMongoKMSKey();
    try {
      return Promise.resolve({
        //The following keys are set as part of secretmanager fetch call.
        // aws: {
        //   accessKeyId: process.env.KMS_AWS_ACCESS_KEY_ID,
        //   secretAccessKey: process.env.KMS_AWS_SECRET_ACCESS_KEY,
        //   sessionToken: process.env.KMS_AWS_SESSION_TOKEN,
        // },
        // for local developement
        aws: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
          sessionToken: process.env.AWS_SESSION_TOKEN,
        },
      });
    } catch (error) {
      Logger.error('KMS Connection Failed:', {
        METHOD: this.constructor.name + '@' + 'KmsProvider',
        MESSAGE: error,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }
}
