import { KMSProviders } from 'mongodb-client-encryption';
import { KMSServiceProviderException, IKMSServiceProvider } from './mongo.kms.provider.service';

describe('KMSServiceProvider', () => {
  describe('KMSServiceProviderException', () => {
    it('should create an instance of KMSServiceProviderException', () => {
      const exception = new KMSServiceProviderException('Test message', 'provider');

      expect(exception).toBeInstanceOf(Error);
      expect(exception).toBeInstanceOf(KMSServiceProviderException);
      expect(exception.message).toBe('Test message');
      expect(exception.provider).toBe('provider');
    });
  });

  describe('IKMSServiceProvider', () => {
    const mockKMSProviders: KMSProviders = {
      aws: {
        accessKeyId: 'access_key',
        secretAccessKey: 'secret_key',
        sessionToken: 'session_token',
      },
    };

    let kmsServiceProvider: IKMSServiceProvider;

    beforeEach(() => {
      kmsServiceProvider = {
        KeyVaultNamespace: 'test-key-vault',
        KmsProvider: Promise.resolve(mockKMSProviders),
      };
    });

    it('should have the correct KeyVaultNamespace', () => {
      expect(kmsServiceProvider.KeyVaultNamespace).toBe('test-key-vault');
    });

    it('should return the KMSProviders instance', async () => {
      const result = await kmsServiceProvider.KmsProvider;
      expect(result).toBe(mockKMSProviders);
    });
  });
});
