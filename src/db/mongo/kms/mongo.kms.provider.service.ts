import { KMSProviders } from 'mongodb-client-encryption';

export interface IKMSServiceProvider {
  readonly KeyVaultNamespace: string;
  KmsProvider: Promise<KMSProviders>;
}
export const IKMSServiceProvider = Symbol('KMSServiceProvider');
export class KMSServiceProviderException extends Error {
  readonly provider: string;
  constructor(message: string, provider = 'unknown') {
    super(message);
    this.provider = provider;
    Object.setPrototypeOf(this, new.target.prototype);
  }
}
