import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { KMSProviders } from 'mongodb-client-encryption';
import { IKMSServiceProvider } from './mongo.kms.provider.service';
import { readFileSync } from 'fs';
import { join } from 'path';

@Injectable()
export class LocalKMSService implements IKMSServiceProvider {
  private readonly masterKey: Buffer;

  constructor(private readonly configService: ConfigService) {
    // Load the master key from the file system
    const path = join(process.cwd(), '/master-key.txt');
    this.masterKey = readFileSync(path);
  }

  // Returns the namespace for the key vault
  get KeyVaultNamespace(): string {
    const kmsSettings = this.configService.get('kmsSettings', {});
    const kmsVaultDB: string = kmsSettings.kmsVaultDB ?? this.configService.get('defaultdb');
    return `${kmsVaultDB}.__keyVault`;
  }

  // Returns the KMS provider configuration for MongoDB encryption
  get KmsProvider(): Promise<KMSProviders> {
    return Promise.resolve({ local: { key: this.masterKey } });
  }
}
