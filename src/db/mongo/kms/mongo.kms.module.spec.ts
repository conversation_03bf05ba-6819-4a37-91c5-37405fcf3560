import { Test, TestingModule, TestingModuleBuilder } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { KMSServiceProviderModule } from './mongo.kmsmodule';
import { IKMSServiceProvider } from './mongo.kms.provider.service';
import { LocalKMSService } from './mongo.kms.localservice';
import { AWSKMSService } from './mongo.kms.provider.aws';
import * as fs from 'fs';
import { KMSProviders } from 'mongodb-client-encryption';

describe('KMSServiceProviderModule', () => {
  let testBuilder: TestingModuleBuilder;
  let moduleRef: TestingModule;

  beforeEach(async () => {
    testBuilder = await Test.createTestingModule({
      imports: [ConfigModule.forRoot(), KMSServiceProviderModule],
    });
  });
  /*
  describe('Un-knownn KMSProvider', () => {
    beforeEach(async () => {
      moduleRef = await testBuilder
      .overrideProvider(ConfigService).useValue({ get: jest.fn().mockReturnValue({ provider: 'dummy' }) })
      .compile();
    });

    it('should throw an error if an unsupported provider is specified', async () => {
      const expectedResult = /Un-Supported KMS provider/;
      expect(moduleRef.get<IKMSServiceProvider>(IKMSServiceProvider)).toThrowError(expectedResult);
    });
  
  });
  */
  describe('Local KMSProvider', () => {
    let kmsProvider;
    let configService: ConfigService;
    beforeEach(async () => {
      moduleRef = await testBuilder
        .overrideProvider(ConfigService)
        .useValue({ get: jest.fn().mockReturnValue({ provider: 'local', kmsVaultDB: 'kmsVaultDB' }) })
        .compile();

      kmsProvider = moduleRef.get<IKMSServiceProvider>(IKMSServiceProvider);
      configService = moduleRef.get<ConfigService>(ConfigService);
    });
    it('should provide an instance of LocalKMSService', async () => {
      const result = await moduleRef.get<IKMSServiceProvider>(IKMSServiceProvider);
      expect(configService.get).toHaveBeenCalledWith('kmsSettings', {});
      expect(result).toBeInstanceOf(LocalKMSService);
    });
    it('should read the master key from the file', async () => {
      // Mock the readFileSync method to return a buffer with the master key
      const readFileSyncMock = jest.spyOn(fs, 'readFileSync').mockReturnValue(Buffer.from('master-key'));

      // Call the constructor of the LocalKMSService
      new LocalKMSService(configService);
      // Ensure that readFileSync is called with the correct path
      expect(readFileSyncMock).toHaveBeenCalledWith(expect.stringContaining('master-key.txt'));
    });

    it('should resolve KmsProvider with the correct master key', async () => {
      const provderOptions = await kmsProvider.KmsProvider;

      expect(provderOptions).toEqual({ local: { key: Buffer.from('master-key') } });
    });
    it('should return a valid KMS vault name', async () => {
      const expectedResult = 'kmsVaultDB.__keyVault';

      const result = kmsProvider.KeyVaultNamespace;

      expect(configService.get).toHaveBeenCalledWith('kmsSettings', {});
      //expect(configService.get).toHaveBeenCalledWith('defaultdb');
      expect(result).toStrictEqual(expectedResult);
    });
    it('should return a valid KMS provider options', async () => {
      const expectedResult = {
        local: {
          key: 'dummyKey',
        },
      };
      jest.spyOn(kmsProvider, 'KmsProvider', 'get').mockReturnValue(expectedResult);

      const result = await kmsProvider.KmsProvider;
      expect(result).toStrictEqual(expectedResult);
    });
  });

  describe('AWS KMSProvider', () => {
    let kmsProvider;
    let configService: ConfigService;
    beforeEach(async () => {
      moduleRef = await testBuilder
        .overrideProvider(ConfigService)
        .useValue({ get: jest.fn().mockReturnValue({ provider: 'aws', kmsVaultDB: 'kmsVaultDB' }) })
        .compile();
      kmsProvider = moduleRef.get<IKMSServiceProvider>(IKMSServiceProvider);
      configService = moduleRef.get<ConfigService>(ConfigService);
    });

    it('should provide an instance of AWSKMSService', async () => {
      const result = await moduleRef.get<IKMSServiceProvider>(IKMSServiceProvider);
      expect(configService.get).toHaveBeenCalledWith('kmsSettings', {});
      expect(result).toBeInstanceOf(AWSKMSService);
    });
    it('should return a valid KMS vault name', async () => {
      const expectedResult = 'kmsVaultDB.__keyVault';

      const result = kmsProvider.KeyVaultNamespace;
      expect(configService.get).toHaveBeenCalledWith('kmsSettings', {});
      //expect(configService.get).toHaveBeenCalledWith('defaultdb');
      expect(result).toStrictEqual(expectedResult);
    });

    it('should return a valid KMS provider options', async () => {
      // Set up the mock environment variables
      process.env.KMS_AWS_ACCESS_KEY_ID = 'test-access-key';
      process.env.KMS_AWS_SECRET_ACCESS_KEY = 'test-secret-key';
      process.env.KMS_AWS_SESSION_TOKEN = 'test-session-token';

      const expectedResult: KMSProviders = {
        aws: {
          accessKeyId: 'test-access-key',
          secretAccessKey: 'test-secret-key',
          sessionToken: 'test-session-token',
        },
      };

      const result = await kmsProvider.KmsProvider;
      expect(result).toStrictEqual(expectedResult);
    });
    /*
    it('should reject the promise with KMSServiceProviderException if there is an error', async () => {

      const error = 'Some error';
      jest.spyOn(kmsProvider, 'KmsProvider','get').mockRejectedValueOnce( new KMSServiceProviderException(error,'aws'));
  
      try {
        await kmsProvider.KmsProvider;
        // The test should not reach this point. It should throw an exception above.
        fail('Expected promise to be rejected.');
      } catch (e) {
        // Check that the error is an instance of KMSServiceProviderException
        expect(e).toBeInstanceOf(KMSServiceProviderException);
        expect(e.provider).toBe('aws'); // The provider name should be 'aws'
        expect(e.message).toBe(error); // The error message should be the same as the error we simulated
      }
    });
    */
  });
});
