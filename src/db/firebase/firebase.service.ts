// src/firebase/firebase.service.ts
import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';
import * as crypto from 'crypto';
import { CachingService } from '../../caching/caching.service';
import { HelperService } from '../../helper/helper.service';
import { Logger } from './../../logging/logger';

@Injectable()
export class FirebaseService implements OnModuleInit {
  private firebaseApp: admin.app.App;

  constructor(private configService: ConfigService,
    private helperService: HelperService
  ) {}
  private readonly _docId = 'refer_earn';
  private readonly _keyId = 'global';
  private readonly _url = 'url';

  private getDocPath(): string {
    return `${this._docId}/${this._keyId}/${this._url}`;
  }
  onModuleInit() {
    const fireBaseCred = this.configService.get('FIREBASE_ADMIN_CREDENTIALS');

    if (!admin.apps.length) {
      this.firebaseApp = admin.initializeApp({
        credential: admin.credential.cert({
          projectId: this.configService.get('project_id'),
          privateKey: this.configService.get('private_key')?.replace(/\\n/g, '\n'),
          clientEmail: this.configService.get('client_email'),
        }),
        databaseURL: fireBaseCred.databaseURL,
      });
    } else {
      this.firebaseApp = admin.app();
    }
  }

  getAdmin(): typeof admin {
    return admin;
  }

  getAuth(): admin.auth.Auth {
    return this.firebaseApp.auth();
  }

  getFirestore(): admin.firestore.Firestore {
    return this.firebaseApp.firestore();
  }

  getStorage(): admin.storage.Storage {
    return this.firebaseApp.storage();
  }

  getDB(email: string, uid: number): string {
    const firebaseKey = this.configService.get<string>('couch_salt');
    return (
      'a' +
      crypto
        .createHash('sha1')
        .update(email + String(firebaseKey) + uid)
        .digest('hex')
    );
  }

  // Read data from Realtime Database
  /**
   * 
   * @param path 
   * @returns 
   *  * @param path 
   * @returns 
   *  const firebaseInstance = await this.helperService.get<FirebaseService>(FirebaseService);
      const userKey = await firebaseInstance.getDB('<EMAIL>',1010463);
      console.log('userKey', userKey);
      const achievementsData = await firebaseInstance.find(`users/${userKey}/elearning_215`);
      console.log('data', achievementsData);
      Method to interact with Realtime Database (find data) 
   */
  async find(path: string) {
    try {
      const snapshot = await this.firebaseApp.database().ref(path).once('value');
      return snapshot.val();
    } catch (error) {
      console.error('Error fetching data from Firebase:', error);
      return null;
    }
  }

  // Method to save data to Firebase Realtime Database
  async save(path: string, data: any) {
    try {
      await this.firebaseApp.database().ref(path).set(data); // set ensures overwrite-safe creation
      console.log('Data saved successfully at:', path);
      return true;
    } catch (error) {
      console.error('Error saving data to Firebase:', error);
      return false;
    }
  }

  // Delete data from Realtime Database
  async delete(path: string) {
    try {
      await this.firebaseApp.database().ref(path).remove();
      console.log('Data deleted successfully from:', path);
      return true;
    } catch (error) {
      console.error('Error deleting data from Firebase:', error);
      return false;
    }
  }

  private cleanUndefined(obj) {
    return Object.fromEntries(Object.entries(obj).filter(([_, value]) => value !== undefined));
  }

  /**
   * 
   * @param db 
   * @param email 
   * @param pass 
   * @returns 
   * const firebaseInstance = await this.helperService.get<FirebaseService>(FirebaseService);
     const db = await firebaseInstance.getDB('<EMAIL>',1010463);
    const users= await firebaseInstance.createDb(db,'<EMAIL>','1010463');
   */
  async createDb(db, email, pass) {
    if (!db || !email || !pass) {
      throw new Error('Required param is missing');
    }

    try {
      const dbPath = `users/${db}`;

      // 1. Check if DB already exists
      const existing = await this.find(dbPath);
      if (existing) {
        console.warn('DB already exists:', db);
        return true;
      }

      // 2. Try to create Firebase Auth user
      let userRecord = null;
      try {
        userRecord = await this.getAuth().createUser({
          uid: db,
          email,
          password: pass.toString(),
        });
      } catch (err) {
        console.warn('User already exists, continuing...');
        userRecord = await this.getAuth()
          .getUser(db)
          .catch(() => null);
      }

      // 3. Prepare DB entry and clean undefined fields
      const dbData = { _id: db };
      if (userRecord) {
        dbData['user_details'] = this.cleanUndefined({
          uid: userRecord?.uid,
          email: userRecord?.email,
          displayName: userRecord?.displayName,
          phoneNumber: userRecord?.phoneNumber,
          emailVerified: userRecord?.emailVerified,
          disabled: userRecord?.disabled,
          metadata: userRecord?.metadata,
          providerData: userRecord?.providerData?.map((provider) => this.cleanUndefined(provider)),
        });
      }

      // 4. Save to Realtime Database
      const saveSuccess = await this.save(dbPath, dbData);
      if (!saveSuccess) {
        throw new Error('Not able to create DB');
      }

      console.log('DB created successfully:', db);
      return true;
    } catch (error) {
      console.error('Firebase Create DB failed', {
        method: 'createDb',
        req: { db, email },
        msg: error || error,
      });
      return false;
    }
  }

  /**
   * Methods created for GDPR use case
   *
   * Get GDPR document path for a user
   */
  getGdprPath(email: string, uid: number): string {
    const userKey = this.getDB(email, uid);
    return `users/${userKey}/gdpr`;
  }

  /**
   * Create a new GDPR document with default values
   */
  createGdprDocument(): any {
    return {
      _id: 'gdpr',
      exportStatus: 0,
      exportAttempt: [],
    };
  }

  /**
   * Add an export attempt to the GDPR document
   */
  addGdprExportAttempt(document: any): any {
    if (!document) {
      document = this.createGdprDocument();
    }

    if (!document.exportAttempt) {
      document.exportAttempt = [];
    }

    document.exportAttempt.push({
      startTime: Math.floor(Date.now() / 1000), // Convert to seconds to match expected format
    });

    return document;
  }

  /**
   * Set the export status in the GDPR document
   */
  setGdprExportStatus(document: any, status: number): any {
    if (!document) {
      document = this.createGdprDocument();
    }
    document.exportStatus = status;
    return document;
  }

/**
 * Retrieves the PG learner type from cache. 
 * If not found, sets it in the cache using the generated user key.
 */
  async getPgLearnerFromCache(userId: number, userEmail: string): Promise<any> {
    try{
    if (!userId || !userEmail) {
      return { status: 'error', msg: 'User id or user email not found.', data: false };
    }
    const userKey = this.getDB(userEmail, userId);
    const cacheKey = `${userKey}_is_pg_learner`;
    const cacheService = await this.helperService.get<CachingService>(CachingService);
    const cachedValue = await cacheService.get(cacheKey);
    if (!cachedValue) {
      const firebasePath = `users/${userKey}`;
     return await this.setPgLearnerInCache(firebasePath, cacheKey); 
    }
    return {
      status: 'success',
      msg: 'Learner type fetched successfully.',
      data: cachedValue,
    };
  }  catch (error: any) {
    Logger.log('getPgLearnerFromCache', {
      METHOD: this.constructor.name + '@' + this.getPgLearnerFromCache.name,
      MESSAGE: error.message,
      REQUEST: { userId, userEmail },
      RESPONSE: error?.stack,
      TIMESTAMP: new Date().getTime(),
    });
    throw error;
}
  }

  /**
 * Retrieves the selected PG learner type from cache. 
 * If not present, sets it using the generated user key.
 */
  async getSelectedPgLearnerFromCache(
    userId: number,
    userEmail: string,
  ): Promise<any> {
    try{
    if (!userId || !userEmail) {
      return {
        status: 'error',
        msg: 'User id or user email not found.',
        data: false,
      };
    }
  
    const userKey = this.getDB(userEmail, userId);
    const selectedCacheKey = `${userKey}_is_selected_pg_learner`;
    const cacheService = await this.helperService.get<CachingService>(CachingService);
    const selectedCacheValue = await cacheService.get(selectedCacheKey);
    if (!selectedCacheValue) {
      const firebasePath = `users/${userKey}`;
      return await this.setPgLearnerInCache(firebasePath, selectedCacheKey, 1); 
    }
   
    return {
      status: 'success',
      msg: 'Learner type fetched successfully.',
      data: selectedCacheValue,
    };
  } catch (error: any) {
        Logger.log('getSelectedPgLearnerFromCache', {
          METHOD: this.constructor.name + '@' + this.getSelectedPgLearnerFromCache.name,
          MESSAGE: error.message,
          REQUEST: { userId, userEmail },
          RESPONSE: error?.stack,
          TIMESTAMP: new Date().getTime(),
        });
        throw error;
  }}
  
  /**
 * Fetches master program documents for a specific user from Firebase.
 * 
 * This method queries the Firebase Realtime Database under the given user path,
 * retrieves keys in the range startKey` to endKey,
 * and filters them to return only valid `master_program_*` entries.
 */
  async getTheMpDocuments(startKey: string, endKey: string, path: string): Promise<any> {

    try {
      const ref = this.firebaseApp.database().ref(path);
      const snapshot = await ref
        .orderByChild('_id')
        .startAt(startKey)
        .endAt(endKey)
        .once('value');
        const allDocs = snapshot.val();

        if (!allDocs) return [];
        const filtered = Object.entries(allDocs)
        .filter(([key]) => /^master_program_\d+$/.test(key))
        .map(([, value]) => value);
        return filtered;
    } catch (error: any) {
      Logger.log('getTheMpDocuments', {
        METHOD: this.constructor.name + '@' + this.getTheMpDocuments.name,
        MESSAGE: error.message,
        RESPONSE: error?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

/**
 * Determines and caches whether a user is a Postgraduate (PG) learner.
 * 
 * This method checks if the user has any master program documents with `programType === 'university'`.
 * If so, it caches a flag indicating the user is a PG learner. Additionally, if the user has selected
 * any PG programs (based on `masterProgramId`), that flag is also cached separately.
 * 
 * The function supports returning either:
 * - `cacheVal` (whether any PG programs exist), or
 * - `isSelectedLearner` (whether user selected a PG program), based on the `flag` parameter.
 */
async setPgLearnerInCache(userKey: string, cacheKey: string, flag = 0): Promise<any> {
  try{
  let cacheVal = false;
  const startKey = 'master_program_0';
  const endKey = 'master_program_\uffff';
   // Get master program documents for this user
  const result = await this.getTheMpDocuments(startKey, endKey, userKey);
  const mpDocs = result ?( Object.values(result) as any[] ): [];
  const pgProgramIds: number[] = [];
  // Step 1: Identify if any programType === 'university'
  for (const mp of mpDocs) {
    if (mp?.programType === 'university') {
      cacheVal = true;
      if (mp.masterProgramId) {
        pgProgramIds.push(mp.masterProgramId);
        console.log(pgProgramIds, 'pg prog id')
      }
      break; 
    }
  }
  const isSelectedLearner = await this.checkSelectedPgProgram(pgProgramIds);
  const cacheService = await this.helperService.get<CachingService>(CachingService);

  // Step 2: Cache if at least one university-type program found
  if (cacheVal === true) {
    await cacheService.set(cacheKey, cacheVal, Number(this.configService.get('pgUserCacheTime')|| 86400));
  }

  // Step 3: Cache selected PG learner flag
  if (isSelectedLearner) {
    const selectedKey = `${userKey}_is_selected_pg_learner`;
    await cacheService.set(selectedKey, isSelectedLearner, Number(this.configService.get('pgUserCacheTime') || 86400));
  }

  return {
    status: 'success',
    msg: 'Learner type fetched successfully.',
    data: flag === 0 ? cacheVal : isSelectedLearner,
  };
  }catch (error: any) {
  Logger.log('setPgLearnerInCache', {
    METHOD: this.constructor.name + '@' + this.setPgLearnerInCache.name,
    MESSAGE: error.message,
    RESPONSE: error?.stack,
    TIMESTAMP: new Date().getTime(),
  });
  throw error;
}}



 async checkSelectedPgProgram(mpIds: number[]): Promise<boolean> {
    const selectedPgPrograms = this.configService.get('selectedPGPrograms')
    const selectedList = selectedPgPrograms.split(',').map(Number);
  
    return mpIds.some(id => selectedList.includes(id));
  }
  
  
   /**
   * Fetches the referral earning URL from Firebase Realtime DB (CouchDB equivalent)
   */
   async fetchReferEarnUrl(dbName: string): Promise<any | boolean> {
    try {
      if (!dbName) return false;
  
      const docPath = this.getDocPath();
      const path = `${dbName}/${docPath}`;
      const data = await this.find(path);
  
      if (!data || data.error) return false;
      return data;
    } catch (error: any) {
      Logger.error('fetchReferEarnUrl', {
        METHOD: this.constructor.name + '@fetchReferEarnUrl',
        MESSAGE: error.message,
        REQUEST: { dbName },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return false;
    }
  }
  
  /**
   * Saves the referral earning URL to Firebase Realtime DB
   */
  async saveReferEarnUrl(dbName: string, url: string): Promise<boolean> {
    try {
      if (!dbName || !url) return false;
  
      const dbExists = await this.find(dbName);
      if (!dbExists) {
        const created = await this.createDb(dbName, '', '');
        if (!created) return false;
      }
  
      const path = `${dbName}/${this._docId}`;
      const docData = {
        [this._keyId]: {
          [this._url]: url,
        },
      };
  
      return await this.save(path, docData);
    } catch (error: any) {
      Logger.error('saveReferEarnUrl', {
        METHOD: this.constructor.name + '@saveReferEarnUrl',
        MESSAGE: error.message,
        REQUEST: { dbName, url },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return false;
    }
  }

}
