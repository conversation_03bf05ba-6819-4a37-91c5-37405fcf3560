import { Test, TestingModule } from '@nestjs/testing';
import { FirebaseService } from './firebase.service';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import * as admin from 'firebase-admin';

// Mock firebase-admin
jest.mock('firebase-admin', () => ({
  initializeApp: jest.fn().mockReturnValue({
    auth: jest.fn(() => ({
      createUser: jest.fn(),
      getUser: jest.fn(),
    })),
    database: jest.fn(() => ({
      ref: jest.fn(() => ({
        once: jest.fn().mockResolvedValue({ val: () => ({ 'master_program_1': { id: 1 } }) }),
        set: jest.fn(),
        remove: jest.fn(),
        orderByChild: jest.fn().mockReturnThis(),
        startAt: jest.fn().mockReturnThis(),
        endAt: jest.fn().mockReturnThis(),
      })),
    })),
    firestore: jest.fn(),
    storage: jest.fn(),
  }),
  credential: {
    cert: jest.fn(),
  },
  apps: [],
  app: jest.fn(() => ({
    auth: jest.fn(() => ({
      createUser: jest.fn(),
      getUser: jest.fn(),
    })),
    database: jest.fn(() => ({
      ref: jest.fn(() => ({
        once: jest.fn().mockResolvedValue({ val: () => ({ 'master_program_1': { id: 1 } }) }),
        set: jest.fn(),
        remove: jest.fn(),
        orderByChild: jest.fn().mockReturnThis(),
        startAt: jest.fn().mockReturnThis(),
        endAt: jest.fn().mockReturnThis(),
      })),
    })),
    firestore: jest.fn(),
    storage: jest.fn(),
  })),
}));

describe('FirebaseService', () => {
  let service: FirebaseService;
  let configService: ConfigService;
  let helperService: HelperService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FirebaseService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (key === 'FIREBASE_ADMIN_CREDENTIALS') {
                return { databaseURL: 'https://test.firebaseio.com' };
              }
              return null;
            }),
          },
        },
        { provide: HelperService, useValue: { get: jest.fn() } },
      ],
    }).compile();

    service = module.get<FirebaseService>(FirebaseService);
    configService = module.get<ConfigService>(ConfigService);
    helperService = module.get<HelperService>(HelperService);
    service.onModuleInit(); // Initialize the firebase app
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('onModuleInit', () => {
    it('should initialize firebase app if not already initialized', () => {
      Object.defineProperty(admin, 'apps', { value: [], configurable: true });
      service.onModuleInit();
      expect(admin.initializeApp).toHaveBeenCalled();
    });

    it('should not initialize firebase app if already initialized', () => {
      Object.defineProperty(admin, 'apps', { value: [{} as any], configurable: true });
      const initializeAppSpy = jest.spyOn(admin, 'initializeApp');
      service.onModuleInit();
      expect(initializeAppSpy).not.toHaveBeenCalled();
    });
  });

  describe('getDB', () => {
    it('should return a sha1 hash', () => {
      jest.spyOn(configService, 'get').mockReturnValue('test-salt');
      const db = service.getDB('<EMAIL>', 123);
      expect(db).toHaveLength(41);
    });
  });

  describe('find', () => {
    it('should return a value from firebase', async () => {
      const result = await service.find('test-path');
      expect(result).toEqual({ master_program_1: { id: 1 } });
    });

    it('should return null if an error is thrown', async () => {
      jest.spyOn(admin.app().database(), 'ref').mockImplementation(() => {
        throw new Error();
      });
      const result = await service.find('test-path');
      expect(result).toBeNull();
    });
  });

  describe('save', () => {
    it('should save a value to firebase', async () => {
      const mockRef = { set: jest.fn().mockResolvedValue(true) };
      jest.spyOn(admin.app().database(), 'ref').mockReturnValue(mockRef as any);
      const result = await service.save('test-path', 'test-value');
      expect(result).toBe(true);
    });

    it('should return false if an error is thrown', async () => {
      jest.spyOn(admin.app().database(), 'ref').mockImplementation(() => {
        throw new Error();
      });
      const result = await service.save('test-path', 'test-value');
      expect(result).toBe(false);
    });
  });

  describe('delete', () => {
    it('should delete a value from firebase', async () => {
      const mockRef = { remove: jest.fn().mockResolvedValue(true) };
      jest.spyOn(admin.app().database(), 'ref').mockReturnValue(mockRef as any);
      const result = await service.delete('test-path');
      expect(result).toBe(true);
    });

    it('should return false if an error is thrown', async () => {
      jest.spyOn(admin.app().database(), 'ref').mockImplementation(() => {
        throw new Error();
      });
      const result = await service.delete('test-path');
      expect(result).toBe(false);
    });
  });

  describe('createDb', () => {
    it('should create a db in firebase', async () => {
      jest.spyOn(service, 'find').mockResolvedValue(null);
      jest.spyOn(service.getAuth(), 'createUser').mockResolvedValue({} as any);
      jest.spyOn(service, 'save').mockResolvedValue(true);
      const result = await service.createDb('test-db', '<EMAIL>', 'password');
      expect(result).toBe(true);
    });

    it('should return true if the db already exists', async () => {
      jest.spyOn(service, 'find').mockResolvedValue(true);
      const result = await service.createDb('test-db', '<EMAIL>', 'password');
      expect(result).toBe(true);
    });

    it('should continue if the user already exists', async () => {
      jest.spyOn(service, 'find').mockResolvedValue(null);
      jest.spyOn(service.getAuth(), 'createUser').mockRejectedValue(new Error());
      jest.spyOn(service.getAuth(), 'getUser').mockResolvedValue({} as any);
      jest.spyOn(service, 'save').mockResolvedValue(true);
      const result = await service.createDb('test-db', '<EMAIL>', 'password');
      expect(result).toBe(true);
    });

    it('should return false if save fails', async () => {
      jest.spyOn(service, 'find').mockResolvedValue(null);
      jest.spyOn(service.getAuth(), 'createUser').mockResolvedValue({} as any);
      jest.spyOn(service, 'save').mockResolvedValue(false);
      const result = await service.createDb('test-db', '<EMAIL>', 'password');
      expect(result).toBe(false);
    });
  });

  describe('getGdprPath', () => {
    it('should return the correct GDPR path', () => {
      jest.spyOn(service, 'getDB').mockReturnValue('test-db');
      const path = service.getGdprPath('<EMAIL>', 123);
      expect(path).toBe('users/test-db/gdpr');
    });
  });

  describe('createGdprDocument', () => {
    it('should create a new GDPR document', () => {
      const doc = service.createGdprDocument();
      expect(doc).toEqual({ _id: 'gdpr', exportStatus: 0, exportAttempt: [] });
    });
  });

  describe('addGdprExportAttempt', () => {
    it('should add an export attempt to a GDPR document', () => {
      const doc = { _id: 'gdpr', exportStatus: 0, exportAttempt: [] };
      const newDoc = service.addGdprExportAttempt(doc);
      expect(newDoc.exportAttempt.length).toBe(1);
    });

    it('should create a new document if one is not provided', () => {
      const newDoc = service.addGdprExportAttempt(null);
      expect(newDoc.exportAttempt.length).toBe(1);
    });
  });

  describe('setGdprExportStatus', () => {
    it('should set the export status of a GDPR document', () => {
      const doc = { _id: 'gdpr', exportStatus: 0, exportAttempt: [] };
      const newDoc = service.setGdprExportStatus(doc, 1);
      expect(newDoc.exportStatus).toBe(1);
    });

    it('should create a new document if one is not provided', () => {
      const newDoc = service.setGdprExportStatus(null, 1);
      expect(newDoc.exportStatus).toBe(1);
    });
  });

  describe('getPgLearnerFromCache', () => {
    it('should return the pg learner from cache', async () => {
      const mockCachingService = { get: jest.fn().mockResolvedValue(true) };
      jest.spyOn(helperService, 'get').mockResolvedValue(mockCachingService as any);
      const result = await service.getPgLearnerFromCache(123, '<EMAIL>');
      expect(result.data).toBe(true);
    });

    it('should return an error if userId or userEmail is not provided', async () => {
      const result = await service.getPgLearnerFromCache(null, null);
      expect(result.status).toBe('error');
    });
  });

  describe('getSelectedPgLearnerFromCache', () => {
    it('should return the selected pg learner from cache', async () => {
      const mockCachingService = { get: jest.fn().mockResolvedValue(true) };
      jest.spyOn(helperService, 'get').mockResolvedValue(mockCachingService as any);
      const result = await service.getSelectedPgLearnerFromCache(123, '<EMAIL>');
      expect(result.data).toBe(true);
    });

    it('should return an error if userId or userEmail is not provided', async () => {
      const result = await service.getSelectedPgLearnerFromCache(null, null);
      expect(result.status).toBe('error');
    });
  });

  describe('getTheMpDocuments', () => {
    it('should return the master program documents', async () => {
      const result = await service.getTheMpDocuments('master_program_0', 'master_program_\uffff', 'test-path');
      expect(result).toEqual([{ id: 1 }]);
    });

    it('should return an empty array if allDocs is null', async () => {
      jest.spyOn(admin.app().database(), 'ref').mockReturnValue({
        orderByChild: jest.fn().mockReturnThis(),
        startAt: jest.fn().mockReturnThis(),
        endAt: jest.fn().mockReturnThis(),
        once: jest.fn().mockResolvedValue({ val: () => null }),
      } as any);
      const result = await service.getTheMpDocuments('master_program_0', 'master_program_\uffff', 'test-path');
      expect(result).toEqual([]);
    });
  });

  describe('setPgLearnerInCache', () => {
    it('should set the pg learner in cache', async () => {
      const mockCachingService = { set: jest.fn().mockResolvedValue(true) };
      jest.spyOn(helperService, 'get').mockResolvedValue(mockCachingService as any);
      jest.spyOn(service, 'getTheMpDocuments').mockResolvedValue([{ programType: 'university', masterProgramId: 1 }]);
      jest.spyOn(service, 'checkSelectedPgProgram').mockResolvedValue(true);
      const result = await service.setPgLearnerInCache('test-key', 'test-cache-key');
      expect(result.data).toBe(true);
    });

    it('should not set the pg learner in cache if cacheVal is false', async () => {
      const mockCachingService = { set: jest.fn() };
      jest.spyOn(helperService, 'get').mockResolvedValue(mockCachingService as any);
      jest.spyOn(service, 'getTheMpDocuments').mockResolvedValue([{ programType: 'not-university', masterProgramId: 1 }]);
      jest.spyOn(service, 'checkSelectedPgProgram').mockResolvedValue(false);
      await service.setPgLearnerInCache('test-key', 'test-cache-key');
      expect(mockCachingService.set).not.toHaveBeenCalled();
    });
  });

  describe('checkSelectedPgProgram', () => {
    it('should return true if the program is selected', async () => {
      jest.spyOn(configService, 'get').mockReturnValue('1,2,3');
      const result = await service.checkSelectedPgProgram([1]);
      expect(result).toBe(true);
    });
  });

  describe('fetchReferEarnUrl', () => {
    it('should return the refer earn url', async () => {
      jest.spyOn(service, 'find').mockResolvedValue({ url: 'test-url' });
      const result = await service.fetchReferEarnUrl('test-db');
      expect(result).toEqual({ url: 'test-url' });
    });

    it('should return false if dbName is not provided', async () => {
      const result = await service.fetchReferEarnUrl(null);
      expect(result).toBe(false);
    });

    it('should return false if data is null or has an error', async () => {
      jest.spyOn(service, 'find').mockResolvedValue(null);
      let result = await service.fetchReferEarnUrl('test-db');
      expect(result).toBe(false);

      jest.spyOn(service, 'find').mockResolvedValue({ error: 'test-error' });
      result = await service.fetchReferEarnUrl('test-db');
      expect(result).toBe(false);
    });
  });

  describe('saveReferEarnUrl', () => {
    it('should save the refer earn url', async () => {
      jest.spyOn(service, 'find').mockResolvedValue(true);
      jest.spyOn(service, 'save').mockResolvedValue(true);
      const result = await service.saveReferEarnUrl('test-db', 'test-url');
      expect(result).toBe(true);
    });

    it('should return false if dbName or url is not provided', async () => {
      let result = await service.saveReferEarnUrl(null, 'test-url');
      expect(result).toBe(false);

      result = await service.saveReferEarnUrl('test-db', null);
      expect(result).toBe(false);
    });

    it('should return false if createDb fails', async () => {
      jest.spyOn(service, 'find').mockResolvedValue(false);
      jest.spyOn(service, 'createDb').mockResolvedValue(false);
      const result = await service.saveReferEarnUrl('test-db', 'test-url');
      expect(result).toBe(false);
    });
  });
});
