import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, IsNotEmpty, IsEmail, IsUrl, IsArray, IsOptional } from 'class-validator';
import { IsCtypeAlpha } from '../../common/validators/ctype-alpha.validator';

export class SignupDto {
  @ApiProperty({ default: '', description: 'time_zone' })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value.trim())
  time_zone?: string;

  @ApiProperty({ default: 2, description: 'affiliateId' })
  affiliateId = 2;

  @ApiProperty({ default: '', description: 'client_id' })
  @IsNotEmpty()
  @Transform(({ value }) => value.trim())
  client_id: string;

  @IsString()
  @IsOptional()
  @Transform(({ value }) => value.trim())
  source?: string;

  @ApiProperty({ default: '', description: 'redirect_url' })
  @IsOptional()
  @IsUrl({}, { message: 'Invalid URL format' })
  @Transform(({ value }) => value.trim())
  redirect_url?: string;

  @ApiProperty({ default: '0', description: 'from_mobile' })
  @IsString()
  from_mobile = '0';

  @ApiProperty({ default: 'IN', description: 'device_type' })
  @IsString()
  @Transform(({ value }) => value.trim())
  device_type: string;

  @ApiProperty({ default: '', description: 'method' })
  @IsString()
  @Transform(({ value }) => value.trim())
  method: string;

  @ApiProperty({ default: '', description: 'user_email' })
  @IsEmail()
  @IsNotEmpty({ message: 'Please provide valid email id.' })
  @Transform(({ value }) => value.trim())
  user_email: string;

  @ApiProperty({ default: '', description: 'first_name' })
  @IsString()
  @Transform(({ value }) => value.trim())
  first_name: string;

  @ApiProperty({ default: '', description: 'last_name' })
  @IsString()
  @Transform(({ value }) => value.trim())
  last_name: string;

  @ApiProperty({ default: '', description: 'auth_token' })
  @IsString()
  @Transform(({ value }) => value.trim())
  auth_token: string;

  @ApiProperty({ default: '', description: 'user_name' })
  @IsString()
  @IsNotEmpty({ message: 'Please provide user name.' })
  @Transform(({ value }) => value.trim())
  user_name: string;

  @ApiProperty({ default: '', description: 'user_pwd' })
  @IsString()
  @Transform(({ value }) => value.trim())
  user_pwd: string;

  @ApiProperty({ default: '', description: 'phone_no' })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value.trim())
  phone_no?: string;

  @ApiProperty({ default: '', description: 'register_mode' })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value.trim())
  register_mode?: string;

  @ApiProperty({ default: 'US', description: 'country_id' })
  @IsString()
  @Transform(({ value }) => value?.trim())
  @IsCtypeAlpha()
  country_id = 'US';

  @ApiProperty({ default: 'US', description: 'country_code' })
  @IsString()
  @Transform(({ value }) => value.trim())
  @IsCtypeAlpha()
  country_code: string;

  @ApiProperty({ default: 0, description: 'city_id' })
  city_id = 0;

  @ApiProperty({ default: 'N', description: 'auto_login' })
  @IsString()
  @Transform(({ value }) => value.trim())
  auto_login = 'N';

  @ApiProperty({ default: [], description: 'user_roles' })
  @IsOptional()
  @IsArray()
  user_roles?: any[];

  @ApiProperty({ default: '', description: 'user_type' })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value.trim())
  user_type?: string;

  @ApiProperty({ default: '', description: 'referral_code' })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value.trim())
  referral_code?: string;

  @ApiProperty({ default: 'Y', description: 'email_agreement' })
  @IsString()
  @Transform(({ value }) => value.trim())
  email_agreement = 'Y';

  @ApiProperty({ default: 1, description: 'status' })
  status = 1;
}
