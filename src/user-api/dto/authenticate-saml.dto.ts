// create-token.dto.ts

import { IsString, IsOptional, IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';

enum DeviceType {
  Mobile = 'mobile',
  Desktop = 'desktop',
}

export class AuthenticateSaml {
  @IsNotEmpty({ message: 'Unauthorized access request.' })
  @IsString({ message: 'Unauthorized access request.' })
  client_id: string;

  @IsOptional()
  readonly redirect_url?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => value.trim())
  readonly from_mobile?: string;

  @IsOptional()
  @Transform(({ value }) => value.trim())
  readonly device_type?: DeviceType;

  @IsString()
  @Transform(({ value }) => value.trim())
  readonly user_login: string;

  @IsOptional()
  readonly additional_info?: object | string;
}
