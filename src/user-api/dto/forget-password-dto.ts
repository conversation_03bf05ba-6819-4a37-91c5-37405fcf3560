import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional, IsEmail } from 'class-validator';

export class ForgetPasswordDto {
  @ApiProperty({ default: 'sl_looper', description: 'Client id' })
  @IsNotEmpty({ message: 'Client id is required' })
  @IsString({ message: 'Client id must be a string' })
  readonly client_id: string;

  @ApiProperty({ default: 'https://lms.simplilearn.com/affiliate/user', description: 'redirect url' })
  @IsString({ message: 'Redirect url must be a string' })
  @IsOptional()
  readonly redirect_url: string;

  @ApiProperty({ default: '1', description: 'from_mobile ' })
  @IsOptional()
  @IsString({ message: 'from mobile must be string' })
  readonly from_mobile: string;

  @ApiProperty({ default: 'app', description: 'device_type ' })
  @IsOptional()
  @IsString({ message: 'device type must be string' })
  readonly device_type: string;

  @ApiProperty({ default: '<EMAIL>', description: 'email ' })
  @IsNotEmpty({ message: 'user email  is required' })
  @IsEmail({}, { message: 'Please provide valid email id.' })
  @IsString({ message: 'user email must be a string' })
  readonly user_email: string;

  @IsOptional()
  @Transform(({ value }) => Number(value))
  linkOnly: number;
}
