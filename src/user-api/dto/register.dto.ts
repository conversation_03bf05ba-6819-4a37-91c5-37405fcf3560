import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, IsNotEmpty, IsEmail, IsOptional } from 'class-validator';
import { IsPasswordValid } from '../../common/validators/password.validator';

export class RegisterDto {
  @ApiProperty({ default: '', description: 'Time zone' })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value.trim())
  time_zone: string;

  @ApiProperty({ default: 2, description: 'affiliateId' })
  affiliateId = 2;

  @ApiProperty({ default: '', description: 'Client id' })
  @IsNotEmpty()
  client_id: string;

  @ApiProperty({ default: '', description: 'source' })
  @IsNotEmpty()
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value.trim())
  source: string;

  @ApiProperty({ default: '', description: 'Redirect url' })
  redirect_url: string;

  @ApiProperty({ default: '0', description: 'From mobile' })
  @IsNotEmpty()
  @IsOptional()
  @Transform((value) => (value.toString() === '0' ? false : true))
  from_mobile;

  @ApiProperty({ default: 'IN', description: 'Device type' })
  @IsNotEmpty()
  @IsOptional()
  device_type: string;

  @ApiProperty({ default: '', description: 'User email' })
  @IsOptional()
  @IsEmail()
  @IsNotEmpty({ message: 'Please provide valid email id.' })
  user_email: string;

  @ApiProperty({ default: '', description: 'User name' })
  @IsString()
  @Transform(({ value }) => value.trim())
  @IsNotEmpty({ message: 'Please provide user name.' })
  user_name: string;

  @ApiProperty({ default: '', description: 'User password' })
  @IsString()
  @Transform(({ value }) => value.trim())
  @IsNotEmpty()
  @IsPasswordValid()
  user_pwd: string;

  @ApiProperty({ default: '', description: 'Phone number' })
  @IsString()
  @Transform(({ value }) => value.trim())
  phone_no: string;

  @ApiProperty({ default: '', description: 'User first name' })
  @IsString()
  @Transform(({ value }) => value.trim())
  @IsOptional()
  user_firstname?: string;

  @ApiProperty({ default: '', description: 'User last name' })
  @IsString()
  @Transform(({ value }) => value.trim())
  @IsOptional()
  user_lastname?: string;
  
  @ApiProperty({ default: '', description: 'Register mode' })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value.trim())
  register_mode: string;

  @IsString()
  @Transform(({ value }) => value?.trim())
  country_id = 'US';

  @IsString()
  @Transform(({ value }) => value?.trim())
  country_code = 'US';

  @ApiProperty({ default: 0, description: 'City id' })
  city_id = 0;

  @ApiProperty({ default: 'N', description: 'Auto login' })
  @IsString()
  @Transform(({ value }) => value.trim())
  auto_login = 'N';

  @ApiProperty({ default: [], description: 'User roles' })
  @IsOptional()
  user_roles: string;

  @ApiProperty({ default: '', description: 'User type' })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value.trim())
  user_type: string;

  @ApiProperty({ default: 'Y', description: 'Email agreement' })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value.trim())
  email_agreement = 'Y';
}
