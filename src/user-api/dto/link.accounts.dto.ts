import { IsEmail } from 'class-validator';
import { Transform } from 'class-transformer';

export class LinkAccountsDto {
  @Transform(({ value }) => value.trim())
  time_zone: string;

  @Transform(({ value }) => value.trim())
  affiliateId = 2;

  @Transform(({ value }) => value.trim())
  client_id: string;

  @Transform(({ value }) => value.trim())
  redirect_url: string;

  @Transform(({ value }) => value.trim())
  from_mobile: string;

  @Transform(({ value }) => value.trim())
  device_type: string;

  @IsEmail({}, { message: 'Please provide valid email id.' })
  @Transform(({ value }) => value.trim())
  user_email: string;

  @Transform(({ value }) => value.trim())
  method: string;

  @Transform(({ value }) => value.trim())
  auth_token: string;

  @Transform(({ value }) => value.trim())
  email_agreement = 'Y';
}
