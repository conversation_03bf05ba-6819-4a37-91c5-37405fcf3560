import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class UserApiLoginDto {
  @ApiProperty({ default: 'Google', description: 'client_id' })
  @IsNotEmpty({ message: 'Unauthorized access request.' })
  @Transform(({ value }) => value.trim())
  readonly client_id: string;

  @ApiProperty({ default: 1, description: 'from_mobile' })
  @Transform(({ value }) => value.trim())
  readonly from_mobile?: string;

  @ApiProperty({ default: 'token', description: 'device_type' })
  @Transform(({ value }) => value.trim())
  device_type?: string;

  @ApiProperty({ description: 'redirect_url' })
  readonly redirect_url?: string;

  @ApiProperty({ default: '', description: 'method' })
  @Transform(({ value }) => value.trim())
  method?: string;

  @ApiProperty({ default: '', description: 'authToken' })
  @Transform(({ value }) => value.trim())
  authToken?: string;

  @ApiProperty({ default: '<EMAIL>' })
  @IsNotEmpty({ message: 'Please provide user email.'})
  @Transform(({ value }) => value.trim())
  email: string;

  @ApiProperty({ default: 'Simpli@1234' })
  @IsOptional()
  @Transform(({ value }) => value.trim())
  password?: string;
}
