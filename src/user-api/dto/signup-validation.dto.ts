import { IsEmail } from 'class-validator';
import { Transform } from 'class-transformer';
import { IsPasswordValid } from '../../common/validators/password.validator';

export class CheckSignupValidationDto {
  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  time_zone: string;

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  affiliateId = 2;

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  client_id: string;

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  redirect_url: string;

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  from_mobile: string;

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  device_type: string;

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  user_name: string;

  @IsEmail({}, { message: 'Please provide valid email id.' })
  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  user_email: string;

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  @IsPasswordValid()
  user_pwd: string;

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  register_mode: string;

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  country_id: number;

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  country_code = 'US';

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  city_id = 0;

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  auto_login = 'N';

  user_roles: number[];

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  user_type: string;

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  email_agreement = 'Y';

  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  mode?: string; 
}
