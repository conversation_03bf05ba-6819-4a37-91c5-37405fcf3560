import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, IsNotEmpty } from 'class-validator';

export class UpdateProfileDto {
  @ApiProperty({ example: '1003679', description: 'User ID' })
  @IsString()
  @IsNotEmpty({ message: 'Parameter Not Found' })
  @Transform(({ value }) => value.trim())
  uid: number;

  @ApiProperty({ example: 'John', description: 'First Name' })
  @Transform(({ value }) => value.trim())
  @IsNotEmpty({ message: 'Parameter Not Found' })
  first_name?: string;

  @ApiProperty({ example: 'Doe', description: 'Last Name' })
  @Transform(({ value }) => value.trim())
  @IsNotEmpty({ message: 'Parameter Not Found' })
  last_name?: string;
}

export class SaveProfileDto {
  @ApiProperty({ example: 'edit', description: 'Edit type of the form' })
  @IsString()
  @IsNotEmpty()
  edit_type: string;

  @ApiProperty({ example: '1003679', description: 'User ID' })
  @IsString()
  @IsNotEmpty()
  userId: number;

  @ApiProperty({ example: '<EMAIL>', description: 'User email' })
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ example: '1', description: 'Is B2B student flag' })
  @IsString()
  @IsNotEmpty()
  isB2bStudent: string;
}
