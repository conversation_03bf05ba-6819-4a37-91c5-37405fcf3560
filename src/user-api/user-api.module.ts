import { Module } from '@nestjs/common';
import { V1Controller } from './v1/v1.controller';
import { UserModule } from '../user/user.module';
import { UserApiService } from './services/user.api.service';
import { CachingService } from '../caching/caching.service';

/**
 * The `UserApiModule` is responsible for handling user-related API operations.
 * It imports the `UserModule` for user-related business logic, defines the
 * `V1Controller` for handling API requests, and provides necessary services
 * like `UserApiService` and `CachingService`.
 */
@Module({
  // Import other modules required by this module
  imports: [UserModule],

  // Define the controllers that handle incoming requests for this module
  controllers: [V1Controller],

  // Specify the providers (services) that are available within this module
  providers: [UserApiService, CachingService],
})
export class UserApiModule {}
