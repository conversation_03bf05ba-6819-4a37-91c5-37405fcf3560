import { Test, TestingModule } from '@nestjs/testing';
import { Response } from 'express';
import { AuthService } from '../../auth/services/auth/auth.service';
import { CachingService } from '../../caching/caching.service';
import { ConfigService } from '@nestjs/config';
import { V1Controller } from './v1.controller';
import { HelperService } from '../../helper/helper.service';
import { UserApiService } from '../services/user.api.service';
import { BadRequestException, HttpStatus, InternalServerErrorException } from '@nestjs/common';
import { CheckSignupValidationDto } from '../dto/signup-validation.dto';
import { UpdateUsernameDto } from '../dto/update-username.dto';
import { AuthenticationRequestDto } from '../../auth/dtos/authentication-request.dto';
import { Utility } from './../../common/util/utility';
import { APILog, Logger } from '../../logging/logger';
import { SignupDto } from '../dto/signup.dto';
import { RegisterDto } from '../dto/register.dto';
import { UserApiLoginDto } from '../dto/v1-login.dto';
import { LinkAccountsDto } from '../dto/link.accounts.dto';
import { AuthenticateSaml } from '../dto/authenticate-saml.dto';
import { UpdateUserTimezoneDto } from '../dto/update-user-tmezone.dto';
import { UpdateProfileDto } from '../dto/update-profile.dto';

jest.mock('../../logging/logger', () => ({
  APILog: {
    error: jest.fn(),
  },
  Logger: {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    verbose: jest.fn(),
  },
}));

describe('V1Controller', () => {
  let v1Controller: V1Controller;

  const authServiceMock = {
    forgetPassword: jest.fn(),
    resetPassword: jest.fn().mockResolvedValue(Promise.resolve({})),
    authenticateUser: jest.fn(),
  };

  const cachingServiceMock = {
    get: jest.fn(),
    del: jest.fn(),
  };

  const configServiceMock = {
    get: jest.fn(),
  };
  const helperServiceMock = {
    get: jest.fn(),
    getHelper: jest.fn(),
  };

  const responseMock = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn(),
  } as unknown as Response;
  class CryptoHelperMock {
  }
  const userApiServiceMock = {
    signup: jest.fn(),
    register: jest.fn(),
    login: jest.fn(),
    resetPassword: jest.fn(),
    checkSignupValidation: jest.fn(),
    getUserByUid: jest.fn(),
    getUserByEmail: jest.fn(),
    updateUserName: jest.fn(),
    validateUserDetails: jest.fn(),
    linkAccounts: jest.fn(),
    authenticate: jest.fn(),
    getLinkAccounts: jest.fn(),
    removeLinkedAccounts: jest.fn(),
    updateLinkedinStatus: jest.fn(),
    authenticateSaml: jest.fn(),
    updateUserTimezone: jest.fn(),
    updateProfile: jest.fn(),
    getUserRoles: jest.fn(),
    assignUserRole: jest.fn(),
    revokeUserRole: jest.fn(),
    getOriginalToken: jest.fn(),
    getTaxonomy: jest.fn(),
    processGoogleOneTapResponse: jest.fn(),
    getTaxonamyName: jest.fn(),
    getTermsListByTaxonomyCategory: jest.fn(),
    validateToken: jest.fn(),
    saveProfileAndFormData: jest.fn(),
    updateAndFetchUserRoles: jest.fn(),
    removeUserRoles: jest.fn(),
    createRole: jest.fn(),
    updateRole: jest.fn(),
  };
  const CryptoProviders = [
    {
      provide: 'CRYPTO_HELPER',
      useClass: CryptoHelperMock, // Use the mock class here
    },
    {
      provide: 'TRIBE_CRYPTO_HELPER',
      useClass: CryptoHelperMock, // Use the mock class here
    },
  ];
  beforeEach(() => {
    jest.spyOn(APILog, 'error').mockImplementation(jest.fn());
  });
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [V1Controller],
      providers: [
        {
          provide: AuthService,
          useValue: authServiceMock,
        },
        {
          provide: CachingService,
          useValue: cachingServiceMock,
        },
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: HelperService, // Provide the real HelperService here if needed
          useValue: helperServiceMock, // Provide the mock object
        },
        {
          provide: UserApiService,
          useValue: userApiServiceMock,
        },
        ...CryptoProviders,
      ],
    }).compile();

    v1Controller = module.get<V1Controller>(V1Controller);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('signup', () => {
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    const mockReq = {
      cookies: {
        session: 'mockSessionToken',
      },
    };

    const mockSignupDto: SignupDto = {
      client_id: 'sl_looper',
      user_email: '<EMAIL>',
      device_type: 'web',
      method: 'standard',
      first_name: 'John',
      last_name: 'Doe',
      auth_token: 'mock_token',
      user_name: 'johndoe',
      user_pwd: 'password123',
      country_code: 'IN',
      country_id: 'IN',
      affiliateId: 2,
      from_mobile: '0',
      city_id: 0,
      auto_login: 'N',
      email_agreement: 'Y',
      status: 1,
      // Optional fields below (can be omitted or set to default/empty)
      phone_no: '',
      redirect_url: '',
      register_mode: '',
      time_zone: '',
      user_roles: [],
      user_type: '',
      referral_code: '',
      source: '',
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return success response on successful signup', async () => {
      const mockResponse = { type: 'success', msg: 'Signup successful' };

      userApiServiceMock.signup.mockResolvedValue(mockResponse);

      await v1Controller.signup(mockSignupDto, mockRes as any, mockReq as any);

      expect(userApiServiceMock.signup).toHaveBeenCalledWith(mockSignupDto, mockReq.cookies);
      expect(mockRes.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse);
    });

    it('should return error response when signup throws error', async () => {
      const error = new Error('Unexpected failure');
      userApiServiceMock.signup.mockRejectedValue(error);

      await v1Controller.signup(mockSignupDto, mockRes as any, mockReq as any);

      expect(APILog.error).toHaveBeenCalledWith('signup?', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'Unexpected failure',
        REQUEST: mockSignupDto,
        RESPONSE: error.stack,
        TIMESTAMP: expect.any(Number),
      }));

      expect(mockRes.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        type: error,
        msg: 'Something went wrong, please try again.',
      });
    });
  });

  describe('register', () => {
    let mockRes, mockReq;

    const mockRegisterDto: RegisterDto = {
      time_zone: '',
      affiliateId: 2,
      client_id: 'sl_looper',
      source: '',
      redirect_url: '',
      from_mobile: false,
      device_type: 'web',
      user_email: '<EMAIL>',
      user_name: 'john_doe',
      user_pwd: 'Password123!',
      phone_no: '1234567890',
      user_firstname: 'John',
      user_lastname: 'Doe',
      register_mode: '',
      country_id: 'US',
      country_code: 'US',
      city_id: 0,
      auto_login: 'N',
      user_roles: '',
      user_type: '',
      email_agreement: 'Y',
    };

    beforeEach(() => {
      mockReq = { cookies: {} };
      mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      jest.spyOn(APILog, 'error').mockImplementation(jest.fn());
      jest.spyOn(Utility, 'returnResponse').mockImplementation(jest.fn());
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should return success response without redirect_url', async () => {
      const registerResponse = {
        status: true,
        msg: 'User created',
        data: { idToken: 'mock-id-token' },
      };

      userApiServiceMock.register.mockResolvedValue(registerResponse);

      await v1Controller.register(mockReq, mockRes as any, mockRegisterDto);

      expect(userApiServiceMock.register).toHaveBeenCalledWith(mockRegisterDto, mockReq.cookies, mockRes);
      expect(Utility.returnResponse).toHaveBeenCalledWith(
        {
          type: 'success',
          msg: 'User created',
          _t: 'mock-id-token',
        },
        'json',
        '',
        mockRes,
      );
    });

    it('should return success response with redirect_url', async () => {
      const redirectDto = { ...mockRegisterDto, redirect_url: 'https://example.com/welcome' };

      const registerResponse = {
        status: true,
        msg: 'User registered with redirect',
        data: { idToken: 'abc123token' },
      };

      userApiServiceMock.register.mockResolvedValue(registerResponse);

      await v1Controller.register(mockReq, mockRes as any, redirectDto);

      expect(userApiServiceMock.register).toHaveBeenCalledWith(redirectDto, mockReq.cookies, mockRes);
      expect(Utility.returnResponse).toHaveBeenCalledWith(
        {
          type: 'success',
          msg: 'User registered with redirect',
          _t: 'abc123token',
        },
        'redirect',
        'https://example.com/welcome',
        mockRes,
      );
    });

    it('should return fallback error message if register fails (no status/idToken)', async () => {
      const registerResponse = {
        status: false,
        msg: 'Missing token',
      };

      userApiServiceMock.register.mockResolvedValue(registerResponse);

      await v1Controller.register(mockReq, mockRes as any, mockRegisterDto);

      expect(Utility.returnResponse).toHaveBeenCalledWith(
        {
          type: 'success',
          msg: 'Error occurred while creating user.',
        },
        'json',
        '',
        mockRes,
      );
    });

    it('should return error response when register throws error', async () => {
      const error = new Error('Unexpected failure');
      userApiServiceMock.register.mockRejectedValue(error);

      await v1Controller.register(mockReq, mockRes as any, mockRegisterDto);

      expect(APILog.error).toHaveBeenCalledWith('register?', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'Unexpected failure',
        REQUEST: mockRegisterDto,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        type: error,
        msg: 'Something went wrong, please try again.',
      });
    });
  });

  describe('login', () => {
    let mockRes;

    const loginDto: UserApiLoginDto = {
      client_id: 'Google',
      from_mobile: '1',
      device_type: 'token',
      email: '<EMAIL>',
      password: 'Simpli@1234',
      method: 'standard',
      authToken: '',
      redirect_url: '', // used to test both with and without redirect
    };

    beforeEach(() => {
      mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      jest.spyOn(Utility, 'returnResponse').mockImplementation(jest.fn());
      jest.spyOn(APILog, 'error').mockImplementation(jest.fn());
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should handle successful login without redirect_url (json response)', async () => {
      const mockLoginResponse = {
        type: 'success',
        msg: 'Login successful',
        token: 'mock-token',
      };

      userApiServiceMock.login.mockResolvedValue(mockLoginResponse);

      await v1Controller.login({ ...loginDto, redirect_url: '' }, mockRes);

      expect(userApiServiceMock.login).toHaveBeenCalledWith(expect.objectContaining(loginDto), mockRes);
      expect(Utility.returnResponse).toHaveBeenCalledWith(
        mockLoginResponse,
        'json',
        '',
        mockRes,
      );
    });

    it('should handle successful login with redirect_url (redirect response)', async () => {
      const redirectLoginDto = { ...loginDto, redirect_url: 'https://example.com/home' };

      const mockLoginResponse = {
        type: 'success',
        msg: 'Login successful',
        token: 'mock-token',
      };

      userApiServiceMock.login.mockResolvedValue(mockLoginResponse);

      await v1Controller.login(redirectLoginDto, mockRes);

      expect(userApiServiceMock.login).toHaveBeenCalledWith(expect.objectContaining(redirectLoginDto), mockRes);
      expect(Utility.returnResponse).toHaveBeenCalledWith(
        mockLoginResponse,
        'redirect',
        'https://example.com/home',
        mockRes,
      );
    });

    it('should return fallback error response if login throws error', async () => {
      const error = new Error('Unexpected login failure');
      userApiServiceMock.login.mockRejectedValue(error);

      await v1Controller.login(loginDto, mockRes);

      expect(APILog.error).toHaveBeenCalledWith('login?', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'Unexpected login failure',
        REQUEST: loginDto,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));

      expect(mockRes.json).toHaveBeenCalledWith({
        type: error,
        msg: 'Something went wrong, please try again.',
      });
    });
  });

  describe('forgotpassword', () => {

    it('should send forgot password link successfully', async () => {
      const forgetPasswordDto = {
        client_id: 'sl_looper',
        redirect_url: 'https://lms.simplilearn.com/affiliate/user',
        from_mobile: '1',
        device_type: 'app',
        user_email: '<EMAIL>',
        linkOnly: 1,
      };

      const param = {
        fm: 1,
        appType: 'app',
        client_id: 'sl_looper',
        linkOnly: 1,
      };

      configServiceMock.get.mockReturnValue('mock-client-secret');

      const validateClientSpy = jest
        .spyOn(Utility, 'validateClientRequest')
        .mockImplementation();

      const mockAuthServiceInstance = {
        forgetPassword: jest.fn().mockResolvedValue(true),
      };

      helperServiceMock.get.mockResolvedValue(mockAuthServiceInstance);

      const result = await v1Controller.forgotPassword(forgetPasswordDto);

      expect(validateClientSpy).toHaveBeenCalledWith('sl_looper', 'mock-client-secret');
      expect(mockAuthServiceInstance.forgetPassword).toHaveBeenCalledWith('<EMAIL>', param);

      // ✅ Correct assertion for directly returned response
      expect(result).toEqual({
        type: 'success',
        msg: 'Reset password link sent on your email.',
      });
    });

    it('should handle error thrown by validateClientRequest', async () => {
      const forgetPasswordDto = {
        client_id: 'sl_looper',
        redirect_url: 'https://lms.simplilearn.com/affiliate/user',
        from_mobile: '1',
        device_type: 'app',
        user_email: '<EMAIL>',
        linkOnly: 1,
      };

      jest
        .spyOn(Utility, 'validateClientRequest')
        .mockImplementation(() => {
          throw new Error('Invalid client');
        });

      await expect(v1Controller.forgotPassword(forgetPasswordDto)).rejects.toThrowError(
        new InternalServerErrorException(),
      );

      expect(responseMock.status).not.toHaveBeenCalled();
      expect(responseMock.json).not.toHaveBeenCalled();
    });
    it('should handle null response from forgetPassword and return undefined', async () => {
      const forgetPasswordDto = {
        client_id: 'sl_looper',
        redirect_url: 'https://lms.simplilearn.com/affiliate/user',
        from_mobile: '1',
        device_type: 'app',
        user_email: '<EMAIL>',
        linkOnly: 1,
      };

      const mockAuthService = {
        forgetPassword: jest.fn().mockResolvedValue(null),
      };

      helperServiceMock.get.mockResolvedValue(mockAuthService);
      jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => true);

      const result = await v1Controller.forgotPassword(forgetPasswordDto);

      expect(result).toBeUndefined(); // ✔️ No error is thrown, just returns undefined
    });


    it('should handle UserNotFoundException', async () => {
      // Arrange
      const forgetPasswordDto = {
        client_id: 'sl_looper',
        redirect_url: 'https://lms.simplilearn.com/affiliate/user',
        from_mobile: '1',
        device_type: 'app',
        user_email: '<EMAIL>',
        linkOnly: 1,
      };

      const errorResponse = new Error('UserNotFoundException'); // Simulate a UserNotFoundException error
      const mockAuthService = {
        forgetPassword: jest.fn().mockResolvedValue(errorResponse),
      };

      // Mock helperService.get to return the mocked AuthService
      helperServiceMock.get.mockResolvedValue(mockAuthService);

      // Mock Utility.validateClientRequest to avoid exception
      jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => true);

      // Act and Assert
      await expect(v1Controller.forgotPassword(forgetPasswordDto)).rejects.toThrowError(
        new BadRequestException('No active account associated with this email address.'),
      );
      expect(responseMock.status).not.toHaveBeenCalled();
      expect(responseMock.json).not.toHaveBeenCalled();
    });

    it('should return success with link when response includes link (linkOnly flow)', async () => {
      const forgetPasswordDto = {
        client_id: 'sl_looper',
        redirect_url: 'https://lms.simplilearn.com/affiliate/user',
        from_mobile: '1',
        device_type: 'app',
        user_email: '<EMAIL>',
        linkOnly: 1,
      };

      const mockResponseWithLink = {
        link: 'https://lms.simplilearn.com/reset-link',
      };

      jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => true);
      helperServiceMock.get.mockResolvedValue({
        forgetPassword: jest.fn().mockResolvedValue(mockResponseWithLink),
      });

      const result = await v1Controller.forgotPassword(forgetPasswordDto);

      expect(result).toEqual({
        type: 'success',
        link: mockResponseWithLink.link,
      });
    });

    it('should handle unknown error message from AuthService and return generic BadRequestException', async () => {
      const forgetPasswordDto = {
        client_id: 'sl_looper',
        redirect_url: 'https://lms.simplilearn.com/affiliate/user',
        from_mobile: '1',
        device_type: 'app',
        user_email: '<EMAIL>',
        linkOnly: 1,
      };

      const errorResponse = new Error('SomeUnexpectedError');

      const mockAuthService = {
        forgetPassword: jest.fn().mockResolvedValue(errorResponse),
      };

      helperServiceMock.get.mockResolvedValue(mockAuthService);
      jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => true);

      await expect(v1Controller.forgotPassword(forgetPasswordDto)).rejects.toThrowError(
        new BadRequestException('Error occurred while requesting for password reset.'),
      );

      expect(responseMock.status).not.toHaveBeenCalled();
      expect(responseMock.json).not.toHaveBeenCalled();
    });


    it('should handle UserDisabled', async () => {
      // Arrange
      const forgetPasswordDto = {
        client_id: 'sl_looper',
        redirect_url: 'https://lms.simplilearn.com/affiliate/user',
        from_mobile: '1',
        device_type: 'app',
        user_email: '<EMAIL>',
        linkOnly: 1,
      };
      const errorResponse = new Error('UserDisabled'); // Simulate a UserDisabled error
      const mockAuthService = {
        forgetPassword: jest.fn().mockResolvedValue(errorResponse), // Simulate user disabled error
      };

      // Mock helperService.get to return the mocked AuthService
      helperServiceMock.get.mockResolvedValue(mockAuthService);
      jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => true);

      // Act and Assert
      await expect(v1Controller.forgotPassword(forgetPasswordDto)).rejects.toThrowError(
        new BadRequestException('Try using a different email address to create an account.'),
      );
      expect(responseMock.status).not.toHaveBeenCalled();
      expect(responseMock.json).not.toHaveBeenCalled();
    });

    it('should handle other errors', async () => {
      // Arrange
      const forgetPasswordDto = {
        client_id: 'sl_looper',
        redirect_url: 'https://lms.simplilearn.com/affiliate/user',
        from_mobile: '1',
        device_type: 'app',
        user_email: '<EMAIL>',
        linkOnly: 1,
      };
      const errorResponse = new Error('OtherError'); // Simulate an error other than UserNotFoundException or UserDisabled

      // Mock the behavior of getHelper to return a mock utility function
      // helperServiceMock.getHelper.mockResolvedValue({
      //   validateClientRequest: jest.fn(),
      //   forgetPassword: jest.fn().mockRejectedValue(errorResponse), // Mock rejection with the error
      // });
      const mockAuthService = {
        forgetPassword: jest.fn().mockRejectedValue(errorResponse),
      };

      // Mock helperService.get to return the mocked AuthService
      helperServiceMock.get.mockResolvedValue(mockAuthService);
      // Act and Assert
      await expect(v1Controller.forgotPassword(forgetPasswordDto)).rejects.toThrowError(

        new InternalServerErrorException()
      );
      expect(responseMock.status).not.toHaveBeenCalled();
      expect(responseMock.json).not.toHaveBeenCalled();
    });
  });

  describe('resetPassword', () => {
    it('should handle successful password reset', async () => {
      const resetPasswordDto = { uid: '123456', pass: 'hello' };

      const successResponse = { success: true, msg: 'Password reset successful' };

      const mockUserApiService = {
        resetPassword: jest.fn().mockResolvedValue(successResponse),
      };

      helperServiceMock.get.mockResolvedValue(mockUserApiService);

      const result = await v1Controller.resetPassword(resetPasswordDto);

      expect(result).toEqual({
        type: 'success',
        msg: 'Password reset successful',
      });

      expect(mockUserApiService.resetPassword).toHaveBeenCalledWith(resetPasswordDto);
      expect(cachingServiceMock.del).toHaveBeenCalledWith('123456');
    });

    it('should rethrow BadRequestException and log the error', async () => {
      const resetPasswordDto = { uid: '123456', pass: 'hello' };

      const apilogSpy = jest.spyOn(APILog, 'error').mockImplementation();

      const badRequestError = new BadRequestException('Invalid input');

      // Simulate helperService.get() throwing BadRequestException
      helperServiceMock.get.mockRejectedValue(badRequestError);

      await expect(v1Controller.resetPassword(resetPasswordDto)).rejects.toThrowError(
        new BadRequestException('Invalid input'),
      );

      expect(apilogSpy).toHaveBeenCalledWith('resetPassword', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'Invalid input',
        REQUEST: resetPasswordDto,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });


    it('should throw InternalServerErrorException on unexpected error and log it', async () => {
      const resetPasswordDto = { uid: '123456', pass: 'hello' };

      const apilogSpy = jest.spyOn(APILog, 'error').mockImplementation();
      helperServiceMock.get.mockRejectedValue(new Error('Unexpected'));

      await expect(v1Controller.resetPassword(resetPasswordDto)).rejects.toThrow(InternalServerErrorException);

      expect(apilogSpy).toHaveBeenCalledWith('resetPassword', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'Unexpected',
        REQUEST: resetPasswordDto,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });

    it('should return error message if resetPassword fails (success: false)', async () => {
      const resetPasswordDto = { uid: '123456', pass: 'hello' };

      const mockUserApiService = {
        resetPassword: jest.fn().mockResolvedValue({ success: false, msg: 'Reset failed' }),
      };

      helperServiceMock.get.mockResolvedValue(mockUserApiService);

      const result = await v1Controller.resetPassword(resetPasswordDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Reset failed',
      });
    });

    it('should return error when uid or pass is missing', async () => {
      const missingUid = { pass: 'newPassword123' };
      const missingPass = { uid: '123456' };

      const result1 = await v1Controller.resetPassword(missingUid as { uid: string; pass: string });
      expect(result1).toEqual({
        type: 'error',
        msg: 'Parameter Not Found',
      });

      const result2 = await v1Controller.resetPassword(missingPass as { uid: string; pass: string });
      expect(result2).toEqual({
        type: 'error',
        msg: 'Parameter Not Found',
      });

      const result3 = await v1Controller.resetPassword({ uid: '', pass: '' });
      expect(result3).toEqual({
        type: 'error',
        msg: 'Parameter Not Found',
      });
    });

    it('should return error when resetPassword fails', async () => {
      const resetPasswordDto = { uid: '123', pass: 'pass123' };

      const mockUserApiService = {
        resetPassword: jest.fn().mockResolvedValue({
          success: false,
          msg: 'User not found',
        }),
      };

      helperServiceMock.get.mockResolvedValue(mockUserApiService);

      const result = await v1Controller.resetPassword(resetPasswordDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'User not found',
      });

      expect(cachingServiceMock.del).not.toHaveBeenCalled();
    });
  });

  describe('checkSignupValidationAction', () => {
    it('should return success message when signup data is valid', async () => {
      const signupData: CheckSignupValidationDto = {
        user_email: '<EMAIL>',
        user_name: 'Uday Singh',
        user_pwd: 'Simpli@123',
        client_id: 'sl_looper',
        device_type: 'ios',
        from_mobile: '1',
        time_zone: '',
        affiliateId: 0,
        redirect_url: '',
        register_mode: '',
        country_id: 0,
        country_code: '',
        city_id: 0,
        auto_login: '',
        user_roles: [],
        user_type: '',
        email_agreement: '',
      };

      userApiServiceMock.checkSignupValidation.mockResolvedValue({
        msg: 'Email available for account creation.',
        type: 'success',
      });

      const result = await v1Controller.checkSignupValidationAction(signupData);

      expect(result).toEqual({
        type: 'success',
        msg: 'Email available for account creation.',
      });
    });

    it('should return an error message when signup data is invalid (non-Error)', async () => {
      const signupData: CheckSignupValidationDto = {
        user_email: '',
        user_name: '',
        user_pwd: '',
        client_id: 'sl_looper1',
        device_type: 'ios',
        from_mobile: '1',
        time_zone: '',
        affiliateId: 0,
        redirect_url: '',
        register_mode: '',
        country_id: 0,
        country_code: '',
        city_id: 0,
        auto_login: '',
        user_roles: [],
        user_type: '',
        email_agreement: '',
      };

      const error = new Error('Email is invalid');

      const apilogSpy = jest.spyOn(APILog, 'error').mockImplementation();

      userApiServiceMock.checkSignupValidation.mockRejectedValue(error);

      const result = await v1Controller.checkSignupValidationAction(signupData);

      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while creating the user.',
      });

      expect(apilogSpy).toHaveBeenCalledWith('checkSignupValidationAction', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'Email is invalid',
        REQUEST: signupData,
        RESPONSE: error.stack,
        TIMESTAMP: expect.any(Number),
      }));
    });


    it('should handle errors (instanceof Error) and return error response', async () => {
      const signupData: CheckSignupValidationDto = {
        user_email: '<EMAIL>',
        user_name: 'Uday Singh',
        user_pwd: 'Simpli@123',
        client_id: 'sl_looper',
        device_type: 'ios',
        from_mobile: '1',
        time_zone: '',
        affiliateId: 0,
        redirect_url: '',
        register_mode: '',
        country_id: 0,
        country_code: '',
        city_id: 0,
        auto_login: '',
        user_roles: [],
        user_type: '',
        email_agreement: '',
      };

      const errorInstance = new Error('Something went wrong');
      const apilogSpy = jest.spyOn(APILog, 'error').mockImplementation();

      userApiServiceMock.checkSignupValidation.mockRejectedValue(errorInstance);

      const result = await v1Controller.checkSignupValidationAction(signupData);

      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while creating the user.',
      });

      expect(apilogSpy).toHaveBeenCalledWith('checkSignupValidationAction', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'Something went wrong',
        REQUEST: signupData,
        RESPONSE: errorInstance.stack,
        TIMESTAMP: expect.any(Number),
      }));
    });
  });

  describe('getUserByUID', () => {
    it('should return user info when the UID is valid', async () => {
      // Arrange
      const uid = '1002312';
      const mockUserInfo = {
        // Define your mock user info data here
      };
      userApiServiceMock.getUserByUid.mockResolvedValue(mockUserInfo);

      // Act
      const result = await v1Controller.getUserByUID(uid);

      // Assert
      expect(result).toEqual(mockUserInfo);
    });

    it('should handle errors and return an empty array', async () => {
      // Arrange
      const uid = '1002312';
      const errorResponse = new Error('An error occurred'); // Simulate an error
      userApiServiceMock.getUserByUid.mockRejectedValue(errorResponse);

      // Act
      const result = await v1Controller.getUserByUID(uid);

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('getUserByEmail', () => {
    it('should return user info when the email is valid', async () => {
      // Arrange
      const email = '<EMAIL>';
      const mockUserInfo = {
        email,
      };
      userApiServiceMock.getUserByEmail.mockResolvedValue(mockUserInfo);

      // Act
      const result = await v1Controller.getUserByEmail(email);

      // Assert
      expect(result).toEqual(mockUserInfo);
    });

    it('should handle errors and return an empty array', async () => {
      // Arrange
      const email = '<EMAIL>';
      const errorResponse = new Error('An error occurred'); // Simulate an error
      userApiServiceMock.getUserByEmail.mockRejectedValue(errorResponse);

      // Act
      const result = await v1Controller.getUserByEmail(email);

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('updateUsername', () => {
    it('should update the username successfully', async () => {
      const mockUpdatedUser = {
        type: 'success',
      };

      const updateRequestDto = {
        uid: 123,
        username: 'new_username',
      } as unknown as UpdateUsernameDto;

      userApiServiceMock.updateUserName.mockResolvedValue(mockUpdatedUser);
      configServiceMock.get.mockReturnValue('mock-client-secret');

      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as unknown as Response;

      await v1Controller.updateUsername(updateRequestDto, responseMock);

      expect(userApiServiceMock.updateUserName).toHaveBeenCalledWith(updateRequestDto);
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith({
        type: 'success',
        msg: 'mock-client-secret',
        data: mockUpdatedUser,
      });
    });

    it('should handle errors and throw an InternalServerErrorException', async () => {
      // Arrange
      const requestBody: UpdateUsernameDto = {
        uid: null,
        name: '',
      };
      const errorResponse = new Error('An error occurred'); // Simulate an error

      userApiServiceMock.updateUserName.mockRejectedValue(errorResponse);

      // Act and Assert
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      await expect(v1Controller.updateUsername(requestBody, responseMock)).rejects.toThrowError(
        new InternalServerErrorException('An error occurred'),
      );

      expect(responseMock.status).not.toHaveBeenCalled();
      expect(responseMock.json).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException when caught error is BadRequestException', async () => {
      const requestBody: UpdateUsernameDto = {
        uid: 123,
        name: 'invalid_user',
      };

      const badRequestError = new BadRequestException('Invalid username');

      userApiServiceMock.updateUserName.mockRejectedValue(badRequestError);

      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      await expect(v1Controller.updateUsername(requestBody, responseMock)).rejects.toThrowError(
        new BadRequestException({
          message: 'Invalid username',
          isData: { data: '' },
        }),
      );

      expect(responseMock.status).not.toHaveBeenCalled();
      expect(responseMock.json).not.toHaveBeenCalled();
    });

  });

  describe('validateUserDetails', () => {
    it('should return success message when UserNotFoundException is thrown', async () => {
      const requestBody = {
        client_id: 'your_client_id',
        method: 'your_method',
        email: '<EMAIL>',
        authToken: 'your_auth_token',
      };

      const error = new Error('UserNotFoundException');
      userApiServiceMock.validateUserDetails.mockRejectedValue(error);

      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      await v1Controller.validateUserDetails(requestBody, responseMock);

      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith({
        type: 'success',
        msg: 'User does not exist',
      });
    });

    it('should return success response when user is valid', async () => {
      const requestBody = {
        client_id: 'your_client_id',
        method: 'your_method',
        email: '<EMAIL>',
        authToken: 'your_auth_token',
      };

      const serviceResponse = {
        type: 'success',
        msg: 'User is valid',
      };

      userApiServiceMock.validateUserDetails.mockResolvedValue(serviceResponse);

      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      await v1Controller.validateUserDetails(requestBody, responseMock);

      expect(userApiServiceMock.validateUserDetails).toHaveBeenCalledWith(requestBody);
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith(serviceResponse);
    });

    it('should throw error or return 400 when requestBody is incomplete (optional)', async () => {
      const invalidRequest = {
        method: 'email',
        email: '<EMAIL>',
        // client_id missing
      };

      userApiServiceMock.validateUserDetails.mockResolvedValue({});

      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      await v1Controller.validateUserDetails(invalidRequest as any, responseMock);

      // Based on behavior — if your service handles this gracefully:
      expect(userApiServiceMock.validateUserDetails).toHaveBeenCalledWith(invalidRequest);
      // Or expect a thrown error or 400 if input validation is implemented
    });

    it('should return 200 and pass through the response from validateUserDetails', async () => {
      const requestBody = {
        client_id: 'client123',
        method: 'email',
        email: '<EMAIL>',
        authToken: 'valid-token',
      };

      const mockServiceResponse = {
        type: 'success',
        msg: 'User is valid',
        data: { uid: 12345 },
      };

      userApiServiceMock.validateUserDetails.mockResolvedValue(mockServiceResponse);

      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      await v1Controller.validateUserDetails(requestBody, responseMock);

      expect(userApiServiceMock.validateUserDetails).toHaveBeenCalledWith(requestBody);
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith(mockServiceResponse);
    });


    it('should handle unknown errors and not respond', async () => {
      const requestBody = {
        client_id: 'your_client_id',
        method: 'your_method',
        email: '<EMAIL>',
        authToken: 'your_auth_token',
      };

      const error = new Error('SomeUnknownError');
      userApiServiceMock.validateUserDetails.mockRejectedValue(error);

      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      const result = await v1Controller.validateUserDetails(requestBody, responseMock);

      // ✅ Assert
      expect(userApiServiceMock.validateUserDetails).toHaveBeenCalledWith(requestBody);
      expect(responseMock.status).not.toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).not.toHaveBeenCalled();

      // Optional: Ensure method just ends silently
      expect(result).toBeUndefined();
    });

  });

  describe('linkAccounts', () => {
    let mockRes;

    const mockLinkAccountsDto: LinkAccountsDto = {
      time_zone: 'Asia/Kolkata',
      affiliateId: 2,
      client_id: 'sl_looper',
      redirect_url: '',
      from_mobile: '1',
      device_type: 'web',
      user_email: '<EMAIL>',
      method: 'standard',
      auth_token: 'mocktoken',
      email_agreement: 'Y',
    };

    beforeEach(() => {
      mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      jest.spyOn(APILog, 'error').mockImplementation(jest.fn());
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should return success response when account linking is successful', async () => {
      const mockResponse = {
        type: 'success',
        msg: 'Accounts linked successfully',
      };

      userApiServiceMock.linkAccounts.mockResolvedValue(mockResponse);

      await v1Controller.linkAccounts(mockLinkAccountsDto, mockRes);

      expect(userApiServiceMock.linkAccounts).toHaveBeenCalledWith(mockLinkAccountsDto);
      expect(mockRes.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse);
    });

    it('should return bad request response when linking fails', async () => {
      const mockResponse = {
        type: 'error',
        msg: 'Linking failed',
      };

      userApiServiceMock.linkAccounts.mockResolvedValue(mockResponse);

      await v1Controller.linkAccounts(mockLinkAccountsDto, mockRes);

      expect(userApiServiceMock.linkAccounts).toHaveBeenCalledWith(mockLinkAccountsDto);
      expect(mockRes.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse);
    });

    it('should handle and log errors properly', async () => {
      const error = new Error('Unexpected failure');
      userApiServiceMock.linkAccounts.mockRejectedValue(error);

      await v1Controller.linkAccounts(mockLinkAccountsDto, mockRes);

      expect(APILog.error).toHaveBeenCalledWith('link-accounts', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'Unexpected failure',
        REQUEST: mockLinkAccountsDto,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });
  });

  describe('authenticate', () => {
    it('should authenticate user successfully', async () => {
      // Arrange
      const authenticationRequest: AuthenticationRequestDto = {
        user_login: '<EMAIL>',
        user_pwd: 'Simpli@12345',
        client_id: 'sl_looper',
        redirect_url: '', // to trigger json flow
        from_mobile: '0',
        device_type: '',
        app_type: '',
      };

      const expectedResult = { type: 'success' };

      // Mock authenticate method
      userApiServiceMock.authenticate.mockResolvedValue(expectedResult);

      // Mock Utility.returnResponse to simulate res.status().json() being called
      jest.spyOn(Utility, 'returnResponse').mockImplementation((response, _responseType, _redirectUrl, res) => {
        res.status(HttpStatus.OK).json(response);
        return response;
      });

      // Mock res object
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      // Act
      const result = await v1Controller.authenticate(authenticationRequest, responseMock);

      // Assert
      expect(userApiServiceMock.authenticate).toHaveBeenCalledWith(authenticationRequest);
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith(expectedResult);
      expect(result).toEqual(expectedResult);
    });

    it('should handle authentication error and throw BadRequestException', async () => {
      // Arrange
      const authenticationRequest: AuthenticationRequestDto = {
        user_login: '<EMAIL>',
        user_pwd: 'Simpli@12345',
        client_id: 'sl_looper',
        redirect_url: '',
        from_mobile: '0',
        device_type: '',
        app_type: ''
      };
      const errorResponse = new Error('AuthenticationError');
      userApiServiceMock.authenticate.mockRejectedValue(errorResponse);
      configServiceMock.get = jest.fn().mockReturnValue('Some error occurred while authenticating user.');
      // Act and Assert
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any; // Mocking Express Response object
      await expect(v1Controller.authenticate(authenticationRequest, responseMock)).rejects.toThrowError(
        new BadRequestException('Some error occurred while authenticating user.'),
      );

      expect(responseMock.status).not.toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).not.toHaveBeenCalled();
    });
    it('should authenticate and set cookie when redirect_url and token are present', async () => {
      const authRequest: AuthenticationRequestDto = {
        user_login: '<EMAIL>',
        user_pwd: 'Test@123',
        client_id: 'sl_looper',
        redirect_url: 'https://example.com', // triggers redirect branch
        from_mobile: '0',
        device_type: '',
        app_type: '',
      };

      const authResponse = {
        _t: 'mock-token',
        type: 'success',
      };

      const mockCookieHelper = {
        setCookie: jest.fn(),
      };

      helperServiceMock.getHelper.mockResolvedValue(mockCookieHelper);
      userApiServiceMock.authenticate.mockResolvedValue(authResponse);
      configServiceMock.get.mockReturnValue('mock-sso-cookie');

      await v1Controller.authenticate(authRequest, responseMock);

      expect(userApiServiceMock.authenticate).toHaveBeenCalledWith(authRequest);
      expect(mockCookieHelper.setCookie).toHaveBeenCalledWith(responseMock, 'mock-sso-cookie', 'mock-token');
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith(authResponse);
    });


  });

  describe('getLinkAccounts', () => {
    it('should fetch linked accounts successfully', async () => {
      // Arrange
      const reqBody = {
        email: '<EMAIL>',
        userId: '1004173',
        from_mobile: '1',
        client_id: 'sl_looper',
        method: 'google',
        redirect_url: '',
      };
      const expectedResult = { type: 'success' /* Provide expected result here */ };
      userApiServiceMock.getLinkAccounts.mockResolvedValue(expectedResult);

      // Act
      const response = await v1Controller.getLinkedAccounts(reqBody, {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockResolvedValue({ status: 200 }),
      });

      // Assert
      expect(response.status).toBe(HttpStatus.OK);
      expect(userApiServiceMock.getLinkAccounts).toHaveBeenCalledWith(reqBody);
      // Add more assertions based on your expected result structure
    });

    it('should handle errors and throw the error', async () => {
      // Arrange
      const reqBody = {
        email: '<EMAIL>',
        userId: '1004173',
        from_mobile: '1',
        client_id: 'sl_looper',
        method: 'google',
        redirect_url: '',
      };
      const errorResponse = new Error('SomeError');
      userApiServiceMock.getLinkAccounts.mockRejectedValue(errorResponse);

      // Act and Assert
      await expect(
        v1Controller.getLinkedAccounts(reqBody, {
          status: jest.fn().mockReturnThis(),
          json: jest.fn(),
        }),
      ).rejects.toThrowError(errorResponse);
      expect(userApiServiceMock.getLinkAccounts).toHaveBeenCalledWith(reqBody);
    });
  });

  describe('removeLinkedAccounts', () => {
    it('should remove linked accounts successfully', async () => {
      // Arrange
      const requestBody = {
        client_id: 'sl_looper',
        userId: '1004173',
        email: '<EMAIL>',
        redirect_url: 'https://example.com',
        method: 'google',
      };

      const expectedResult = { type: 'success', msg: 'Linked accounts removed successfully' };

      userApiServiceMock.removeLinkedAccounts.mockResolvedValue(expectedResult);

      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      // ✅ Act — must call the method before asserting
      await v1Controller.removeLinkedAccounts(requestBody, responseMock);

      // ✅ Assert after method execution
      expect(userApiServiceMock.removeLinkedAccounts).toHaveBeenCalledWith(requestBody);
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith({ status: HttpStatus.OK, ...expectedResult });
    });


    it('should handle errors and throw the error', async () => {
      // Arrange
      const requestBody = {
        client_id: 'sl_looper',
        userId: '1004173',
        email: '<EMAIL>',
        redirect_url: 'https://example.com',
        method: 'google',
      };

      const errorMessage = 'An error occurred';
      userApiServiceMock.removeLinkedAccounts.mockRejectedValue(new Error(errorMessage));

      // Act and Assert
      await expect(async () => await v1Controller.removeLinkedAccounts(requestBody, {} as any)).rejects.toThrowError(
        new Error(errorMessage),
      );

      expect(userApiServiceMock.removeLinkedAccounts).toHaveBeenCalledWith(requestBody);
    });
  });

  describe('updateLinkedin', () => {
    let mockRes;

    const mockRequestBody = {
      redirect_url: 'http://localhost/redirect',
      user_id: 123456,
      source: 'linkedin',
    };

    beforeEach(() => {
      mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      configServiceMock.get.mockReturnValue('linkedin');
      jest.spyOn(APILog, 'error').mockImplementation(jest.fn());
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should update LinkedIn status if source is valid', async () => {
      const mockServiceResponse = { type: 'success', msg: 'LinkedIn updated' };
      userApiServiceMock.updateLinkedinStatus.mockResolvedValue(mockServiceResponse);

      await v1Controller.updateLinkedin(mockRequestBody, mockRes);

      expect(userApiServiceMock.updateLinkedinStatus).toHaveBeenCalledWith(mockRequestBody);
      expect(mockRes.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: HttpStatus.OK,
        ...mockServiceResponse,
      });
    });

    it('should return error if source is invalid', async () => {
      const invalidRequest = {
        ...mockRequestBody,
        source: 'facebook',
      };
      configServiceMock.get.mockReturnValue('linkedin');

      await v1Controller.updateLinkedin(invalidRequest, mockRes);

      expect(userApiServiceMock.updateLinkedinStatus).not.toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockRes.json).toHaveBeenCalledWith({
        type: 'error',
        msg: 'Only LinkedIn status update supported.',
      });
    });

    it('should log and throw error if updateLinkedinStatus fails', async () => {
      const error = new Error('DB failure');
      userApiServiceMock.updateLinkedinStatus.mockRejectedValue(error);

      await expect(v1Controller.updateLinkedin(mockRequestBody, mockRes)).rejects.toThrow(error);

      expect(APILog.error).toHaveBeenCalledWith('update-linkedin', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'DB failure',
        REQUEST: mockRequestBody,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });
  });

  describe('authenticateSaml', () => {
    let mockRes;

    const mockRequestBody: AuthenticateSaml = {
      client_id: 'sl_looper',
      user_login: 'testuser',
      redirect_url: 'http://localhost/redirect',
    };

    beforeEach(() => {
      mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      configServiceMock.get.mockReturnValue('mock-sso-cookie');
      jest.spyOn(APILog, 'error').mockImplementation(jest.fn());
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should authenticate via SAML and set cookie for redirect', async () => {
      const mockCookieHelper = {
        setCookie: jest.fn(),
      };
      const mockResponse = {
        type: 'success',
        msg: 'Authenticated',
        _t: 'mock_token',
      };

      helperServiceMock.getHelper.mockResolvedValue(mockCookieHelper);
      userApiServiceMock.authenticateSaml.mockResolvedValue(mockResponse);

      const returnResponseSpy = jest
        .spyOn(Utility, 'returnResponse')
        .mockImplementation((response, _type, _redirectUrl, res) => {
          return res.status(200).json(response);
        });

      await v1Controller.authenticateSaml(mockRequestBody, mockRes);

      expect(helperServiceMock.getHelper).toHaveBeenCalledWith('CookieHelper');
      expect(userApiServiceMock.authenticateSaml).toHaveBeenCalledWith(mockRequestBody);
      expect(mockCookieHelper.setCookie).toHaveBeenCalledWith(mockRes, 'mock-sso-cookie', 'mock_token');
      expect(returnResponseSpy).toHaveBeenCalledWith(mockResponse, 'redirect', mockRequestBody.redirect_url, mockRes);
    });

    it('should authenticate and return JSON response if no redirect_url', async () => {
      const bodyWithoutRedirect = {
        ...mockRequestBody,
        redirect_url: undefined,
      };

      const mockResponse = {
        type: 'success',
        msg: 'Authenticated',
      };

      const mockCookieHelper = { setCookie: jest.fn() };

      helperServiceMock.getHelper.mockResolvedValue(mockCookieHelper);
      userApiServiceMock.authenticateSaml.mockResolvedValue(mockResponse);

      await v1Controller.authenticateSaml(bodyWithoutRedirect, mockRes);

      expect(userApiServiceMock.authenticateSaml).toHaveBeenCalledWith(bodyWithoutRedirect);
      expect(mockCookieHelper.setCookie).not.toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(200); // ✅ Utility returns response via res
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse); // ✅ Confirms response payload
    });

    it('should log and throw error if authenticateSaml fails', async () => {
      const error = new Error('SAML error');

      helperServiceMock.getHelper.mockRejectedValue(error);
      userApiServiceMock.authenticateSaml.mockRejectedValue(error);

      await expect(
        v1Controller.authenticateSaml(mockRequestBody, mockRes),
      ).rejects.toThrow('SAML error');

      expect(APILog.error).toHaveBeenCalledWith('authenticateSaml', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'SAML error',
        REQUEST: mockRequestBody,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });
  });

  describe('updateUserTimezone', () => {
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    const requestBody: UpdateUserTimezoneDto = {
      uid: '1003679',
      country: 'IN',
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should update user timezone and return response', async () => {
      const mockServiceResponse = { timezone: 'Asia/Kolkata' };

      userApiServiceMock.updateUserTimezone.mockResolvedValue(mockServiceResponse);

      await v1Controller.updateUserTimezone(requestBody, mockRes as any);

      expect(userApiServiceMock.updateUserTimezone).toHaveBeenCalledWith(requestBody);
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({ ...requestBody, timezone: 'Asia/Kolkata' });
    });

    it('should throw BadRequestException if FailedUpdateUserTimezone error is thrown', async () => {
      const error = new Error('FailedUpdateUserTimezone');
      userApiServiceMock.updateUserTimezone.mockRejectedValue(error);

      await expect(v1Controller.updateUserTimezone(requestBody, mockRes as any)).rejects.toThrow(BadRequestException);

      expect(APILog.error).toHaveBeenCalledWith('getTimezoneFromCountryCode', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'FailedUpdateUserTimezone',
        REQUEST: requestBody,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });

    it('should rethrow unexpected errors', async () => {
      const error = new Error('SomeOtherError');
      userApiServiceMock.updateUserTimezone.mockRejectedValue(error);

      await expect(v1Controller.updateUserTimezone(requestBody, mockRes as any)).rejects.toThrow('SomeOtherError');

      expect(APILog.error).toHaveBeenCalledWith('getTimezoneFromCountryCode', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'SomeOtherError',
        REQUEST: requestBody,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });
  });

  describe('updateProfile', () => {
    const mockBody: UpdateProfileDto = {
      uid: 1003679,
      first_name: 'John',
      last_name: 'Doe',
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return updated profile response on success', async () => {
      const mockResponse = {
        type: 'success',
        msg: 'Profile updated successfully',
      };

      userApiServiceMock.updateProfile.mockResolvedValue(mockResponse);

      const result = await v1Controller.updateProfile(mockBody);

      expect(userApiServiceMock.updateProfile).toHaveBeenCalledWith(mockBody);
      expect(result).toEqual(mockResponse);
    });

    it('should handle error and return error message', async () => {
      const mockError = new Error('Something went wrong');
      userApiServiceMock.updateProfile.mockRejectedValue(mockError);

      const result = await v1Controller.updateProfile(mockBody);

      expect(userApiServiceMock.updateProfile).toHaveBeenCalledWith(mockBody);
      expect(APILog.error).toHaveBeenCalledWith('updateProfile', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'Something went wrong',
        REQUEST: mockBody,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while updating profile. Please try again.',
      });
    });
  });

  describe('getRoles', () => {
    it('should fetch user roles successfully', async () => {
      // Arrange
      const reqBody = {
        pageSize: 10,
        page: 1,
      };
      const expectedResult = { type: 'success' /* Provide expected result here */ };
      userApiServiceMock.getUserRoles.mockResolvedValue(expectedResult);

      // Act
      const response = await v1Controller.getRoles(reqBody, {
        status: jest.fn().mockReturnThis(),
        json: jest.fn().mockResolvedValue({ status: 200 }),
      });

      // Assert
      expect(response.status).toBe(HttpStatus.OK);
      expect(userApiServiceMock.getUserRoles).toHaveBeenCalledWith(reqBody);
      // Add more assertions based on your expected result structure
    });
    it('should handle errors and log the error', async () => {
      // Arrange
      const reqQuery = {
        pageSize: 10,
        page: 1,
      };
      const errorMessage = 'some error';
      userApiServiceMock.getUserRoles.mockRejectedValue(new Error(errorMessage));

      // Act
      const response = await v1Controller.getRoles(reqQuery, {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      });

      // Assert
      expect(response).toEqual([]);
      expect(userApiServiceMock.getUserRoles).toHaveBeenCalledWith(reqQuery);

      // Verify APILog.error is called with the correct parameters
    });
  });

  describe('assignUserRole', () => {
    // Success case
    it('should assign user role successfully', async () => {
      // Arrange
      const requestBody = {
        uid: 123,
        rid: '23',
      };

      const expectedResult = { type: 'success', msg: 'User role assigned successfully' };

      userApiServiceMock.assignUserRole.mockResolvedValue(expectedResult);

      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      // ✅ Act - call the controller method
      await v1Controller.assignUserRole(requestBody, responseMock);

      // ✅ Assert - now mock methods would have been called
      expect(userApiServiceMock.assignUserRole).toHaveBeenCalledWith(requestBody);
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith(expectedResult);
    });


    // Error case
    it('should handle error while assigning user role', async () => {
      // Arrange
      const requestBody = {
        uid: 123456,
        rid: '3454',
      };

      const errorMessage = 'An unexpected error occurred';

      userApiServiceMock.assignUserRole.mockRejectedValue(new Error(errorMessage));

      // Act and Assert
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      await v1Controller.assignUserRole(requestBody, responseMock);

      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith({});
      expect(userApiServiceMock.assignUserRole).toHaveBeenCalledWith(requestBody);
    });
  });

  describe('revokeUserRole', () => {
    // Success case
    it('should revoke user role successfully', async () => {
      // Arrange
      const requestBody = {
        uid: 123456,
        rid: '455654',
      };

      const expectedResult = { type: 'success', msg: 'User role revoked successfully' };

      userApiServiceMock.revokeUserRole.mockResolvedValue(expectedResult);

      // Act
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as any;

      // ✅ Call the actual controller method
      await v1Controller.revokeUserRole(requestBody, responseMock);

      // ✅ Then assert
      expect(userApiServiceMock.revokeUserRole).toHaveBeenCalledWith(requestBody);
      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith(expectedResult);
    });


    // Error case
    it('should handle error while revoking user role', async () => {
      // Arrange
      const requestBody = {
        uid: 123456,
        rid: '5345',
      };

      const errorMessage = 'An unexpected error occurred';

      userApiServiceMock.revokeUserRole.mockRejectedValue(new Error(errorMessage));

      // Act and Assert
      const responseMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      await v1Controller.revokeUserRole(requestBody, responseMock);

      expect(responseMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(responseMock.json).toHaveBeenCalledWith({});
      expect(userApiServiceMock.revokeUserRole).toHaveBeenCalledWith(requestBody);
    });
  });

  describe('getOriginalToken', () => {
    const mockResponse = () => {
      const res: any = {};
      res.status = jest.fn().mockReturnValue(res);
      res.json = jest.fn().mockReturnValue(res);
      return res;
    };
    it('should return original token on success', async () => {
      const reqBody = {
        redirect_url: 'https://example.com',
        token: 'validToken',
      };
      const mockResult = {
        type: 'success',
        msg: 'Original Token generated',
        _t: 'encodedToken',
      };

      const res = mockResponse();

      userApiServiceMock.getOriginalToken.mockResolvedValue(mockResult);

      await v1Controller.getOriginalToken(reqBody, res);

      expect(userApiServiceMock.getOriginalToken).toHaveBeenCalledWith(reqBody);
      expect(res.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(res.json).toHaveBeenCalledWith(mockResult);
    });
    it('should catch exceptions and log the error', async () => {
      const reqBody = {
        redirect_url: 'https://example.com',
        token: 'token123',
      };

      const res = mockResponse();
      const error = new Error('Unexpected failure');

      userApiServiceMock.getOriginalToken.mockRejectedValue(error);

      const loggerSpy = jest.spyOn(APILog, 'error').mockImplementation();

      const result = await v1Controller.getOriginalToken(reqBody, res);

      expect(userApiServiceMock.getOriginalToken).toHaveBeenCalledWith(reqBody);
      expect(loggerSpy).toHaveBeenCalledWith('get-original-token', {
        METHOD: expect.any(String),
        MESSAGE: 'Unexpected failure',
        REQUEST: reqBody,
        RESPONSE: error.stack,
        TIMESTAMP: expect.any(Number),
      });

      expect(result).toEqual([]);
    });
  });

  describe('getTaxonomy', () => {
    const queryParams = { type: 'category', id: '123' };

    let mockRes;

    beforeEach(() => {
      mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      jest.clearAllMocks();
    });

    it('should return taxonomy data successfully', async () => {
      const mockResponse = { type: 'success', data: ['item1', 'item2'] };

      userApiServiceMock.getTaxonomy.mockResolvedValue(mockResponse);

      await v1Controller.getTaxonomy(queryParams, mockRes);

      expect(userApiServiceMock.getTaxonomy).toHaveBeenCalledWith(queryParams);
      expect(mockRes.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse);
    });

    it('should handle error and return 400 with error message', async () => {
      const mockError = new Error('Unexpected error');
      userApiServiceMock.getTaxonomy.mockRejectedValue(mockError);

      await v1Controller.getTaxonomy(queryParams, mockRes);

      expect(userApiServiceMock.getTaxonomy).toHaveBeenCalledWith(queryParams);
      expect(APILog.error).toHaveBeenCalledWith('getTaxonomy', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'Unexpected error',
        REQUEST: queryParams,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
      expect(mockRes.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockRes.json).toHaveBeenCalledWith({
        type: 'error',
        msg: 'Failed to fetch taxonomy data.',
      });
    });
  });

  describe('processGoogleOneTapResponse', () => {
    const mockReqData = {
      is_frs_page: '1',
      credential: 'mock-credential',
      calling_api_from: 'web',
    };

    let mockRes;

    beforeEach(() => {
      mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
        redirect: jest.fn(),
      };
      jest.clearAllMocks();
    });

    it('should redirect to token URL if tokenRedirectUrl is present in response', async () => {
      const mockResponse = {
        tokenRedirectUrl: 'https://example.com/token-redirect',
      };

      userApiServiceMock.processGoogleOneTapResponse.mockResolvedValue(mockResponse);

      await v1Controller.processGoogleOneTapResponse(mockReqData, mockRes);

      expect(userApiServiceMock.processGoogleOneTapResponse).toHaveBeenCalledWith(mockReqData, mockRes);
      expect(mockRes.redirect).toHaveBeenCalledWith(mockResponse.tokenRedirectUrl);
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it('should return JSON response if tokenRedirectUrl is not present', async () => {
      const mockResponse = {
        type: 'success',
        msg: 'Logged in',
      };

      userApiServiceMock.processGoogleOneTapResponse.mockResolvedValue(mockResponse);

      await v1Controller.processGoogleOneTapResponse(mockReqData, mockRes);

      expect(userApiServiceMock.processGoogleOneTapResponse).toHaveBeenCalledWith(mockReqData, mockRes);
      expect(mockRes.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockRes.json).toHaveBeenCalledWith(mockResponse);
      expect(mockRes.redirect).not.toHaveBeenCalled();
    });

    it('should log and throw error if processGoogleOneTapResponse fails', async () => {
      const error = new Error('Processing failed');
      userApiServiceMock.processGoogleOneTapResponse.mockRejectedValue(error);

      await expect(
        v1Controller.processGoogleOneTapResponse(mockReqData, mockRes),
      ).rejects.toThrow('Processing failed');

      expect(userApiServiceMock.processGoogleOneTapResponse).toHaveBeenCalledWith(mockReqData, mockRes);
      expect(Logger.error).toHaveBeenCalledWith(
        'Error processing Google One Tap response',
        expect.objectContaining({
          METHOD: expect.any(String),
          MESSAGE: 'Processing failed',
          REQUEST: mockReqData,
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        }),
      ); expect(mockRes.redirect).not.toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });
  });

describe('getTaxonomyName', () => {
  const mockUid = '1001001';
  const mockRequestBody = { uid: mockUid };
  const mockTaxonomyResponse = { name: 'Sample Taxonomy' };

  it('should return taxonomy name when service succeeds', async () => {
    userApiServiceMock.getTaxonamyName.mockResolvedValue(mockTaxonomyResponse);

    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    await v1Controller.getTaxonomyName(mockRequestBody, mockRes as any);

    expect(userApiServiceMock.getTaxonamyName).toHaveBeenCalledWith(mockUid);
    expect(mockRes.status).toHaveBeenCalledWith(HttpStatus.OK);
    expect(mockRes.json).toHaveBeenCalledWith(mockTaxonomyResponse);
  });

  it('should log error if service throws', async () => {
    const error = new Error('Failed to fetch taxonomy name');
    userApiServiceMock.getTaxonamyName.mockRejectedValue(error);

    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    await v1Controller.getTaxonomyName(mockRequestBody, mockRes as any);

    expect(Logger.error).toHaveBeenCalledWith(
      'Error fetching taxonomy name',
      expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: error.message,
        REQUEST: mockRequestBody,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }),
    );

    // Optionally check that res.status/json are NOT called if error is swallowed
    expect(mockRes.status).not.toHaveBeenCalled();
    expect(mockRes.json).not.toHaveBeenCalled();
  });
});

describe('getTaxonomyName', () => {
  it('should return taxonomy name successfully', async () => {
    const mockBody = { uid: '12345' };
    const mockResponse = { taxonomy: 'Engineering' };

    userApiServiceMock.getTaxonamyName.mockResolvedValue(mockResponse);

    const resMock = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    await v1Controller.getTaxonomyName(mockBody, resMock as any);

    expect(userApiServiceMock.getTaxonamyName).toHaveBeenCalledWith('12345');
    expect(resMock.status).toHaveBeenCalledWith(200);
    expect(resMock.json).toHaveBeenCalledWith(mockResponse);
  });

  it('should log error if getTaxonomyName fails', async () => {
    const mockBody = { uid: '12345' };
    const error = new Error('Failed to fetch');
    userApiServiceMock.getTaxonamyName.mockRejectedValue(error);

    const resMock = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    await v1Controller.getTaxonomyName(mockBody, resMock as any);

    expect(Logger.error).toHaveBeenCalledWith('Error fetching taxonomy name', expect.objectContaining({
      METHOD: expect.any(String),
      MESSAGE: error.message,
      REQUEST: mockBody,
      RESPONSE: error.stack,
      TIMESTAMP: expect.any(Number),
    }));
  });
});

describe('getTermListByTaxonomy', () => {
  it('should return term list for valid taxonomy', async () => {
    const mockQuery = { taxonomy: 'industry' };
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    const mockTerms = { '1': 'IT', '2': 'Finance' };
    const mockHelper = {
      getTitleGenderList: jest.fn().mockResolvedValue({
        title: ['Mr', 'Ms'],
        gender: ['Male', 'Female'],
        industry_disable: false,
      }),
    };

    userApiServiceMock.getTermsListByTaxonomyCategory.mockResolvedValue(mockTerms);
    helperServiceMock.getHelper.mockResolvedValue(mockHelper);

    await v1Controller.getTermListByTaxonomy(mockQuery, mockRes as any);

    expect(mockRes.status).toHaveBeenCalledWith(200);
    expect(mockRes.json).toHaveBeenCalledWith(expect.objectContaining({
      type: 'success',
      termlist: expect.any(Object),
    }));
  });

it('should log error and return empty object if taxonomy query param is missing', async () => {
  const query = {}; // No taxonomy param
  const mockRes = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn(),
  };

  jest.spyOn(Logger, 'error').mockImplementation(jest.fn());

  await v1Controller.getTermListByTaxonomy(query, mockRes as any);

  expect(Logger.error).toHaveBeenCalledWith(
    'Error fetching taxonomy terms',
    expect.objectContaining({
      METHOD: expect.any(String),
      MESSAGE: expect.any(String),
      CATEGORY: undefined,
      STACK: expect.any(String),
      TIMESTAMP: expect.any(String),
    }),
  );

  expect(mockRes.json).toHaveBeenCalledWith({});
});

  it('should return empty object and log error if taxonomy missing', async () => {
    const mockQuery = {};
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    await v1Controller.getTermListByTaxonomy(mockQuery, mockRes as any);

    expect(mockRes.json).toHaveBeenCalledWith({});
    expect(Logger.error).toHaveBeenCalled();
  });
});

describe('validateToken', () => {
  it('should validate token and return response', async () => {
    const mockBody = {
      client_id: 'abc',
      token: 'xyz',
      redirect_url: 'http://example.com',
    };
    const mockResponse = { type: 'success' };

    userApiServiceMock.validateToken.mockReturnValue(mockResponse);

    const result = await v1Controller.validateToken(mockBody);
    expect(userApiServiceMock.validateToken).toHaveBeenCalledWith(mockBody);
    expect(result).toEqual(mockResponse);
  });

  it('should log error if validation fails', async () => {
    const error = new Error('Invalid token');
    userApiServiceMock.validateToken.mockImplementation(() => {
      throw error;
    });

    const mockBody = { client_id: 'abc', token: 'xyz' };
    await v1Controller.validateToken(mockBody);

    expect(Logger.error).toHaveBeenCalledWith('validateToken', expect.objectContaining({
      METHOD: expect.any(String),
      MESSAGE: 'Invalid token',
      REQUEST: mockBody,
      RESPONSE: expect.any(String),
      TIMESTAMP: expect.any(String),
    }));
  });
});

describe('saveProfileAndFormData', () => {
  const mockReq = {
    query: {},
    body: {},
    params: {},
  };

  const mockDto = {
    edit_type: 'basic',
    userId: 12345,
    email: '<EMAIL>',
    isB2bStudent: 'true',
  };

  it('should return success response when edit_type is valid', async () => {
    const mockResponse = { type: 'success' };
    userApiServiceMock.saveProfileAndFormData.mockResolvedValue(mockResponse);

    const result = await v1Controller.saveProfileAndFormData(mockDto as any, mockReq as any);
    expect(result).toEqual(mockResponse);
  });

  it('should return error response when edit_type is invalid', async () => {
    const invalidDto = { ...mockDto, edit_type: 'invalid' };
    const result = await v1Controller.saveProfileAndFormData(invalidDto as any, mockReq as any);

    expect(result).toEqual({
      type: 'error',
      msg: 'Invalid Request.',
    });
  });

  it('should log and rethrow error if exception occurs', async () => {
    const error = new Error('DB failure');
    userApiServiceMock.saveProfileAndFormData.mockRejectedValue(error);

    await expect(v1Controller.saveProfileAndFormData(mockDto as any, mockReq as any)).rejects.toThrow(error);
    expect(Logger.error).toHaveBeenCalledWith('saveProfileAndFormData', expect.objectContaining({
      METHOD: expect.any(String),
      MESSAGE: 'DB failure',
      REQUEST: mockDto,
      RESPONSE: error.stack,
      TIMESTAMP: expect.any(String),
    }));
  });
});

describe('updateAndFetchUserRoles', () => {
  it('should update and return user roles successfully', async () => {
    const reqBody = { uid: 1001, roles: ['admin'] };
    const mockResult = ['admin', 'editor'];

    userApiServiceMock.updateAndFetchUserRoles.mockResolvedValue(mockResult);

    const result = await v1Controller.updateAndFetchUserRoles(reqBody);
    expect(result).toEqual({ type: 'success', data: mockResult });
    expect(userApiServiceMock.updateAndFetchUserRoles).toHaveBeenCalledWith(reqBody);
  });

  it('should log and return nothing on failure', async () => {
    const reqBody = { uid: 1001, roles: ['admin'] };
    const error = new Error('DB error');
    userApiServiceMock.updateAndFetchUserRoles.mockRejectedValue(error);

    const result = await v1Controller.updateAndFetchUserRoles(reqBody);
    expect(result).toBeUndefined();
    expect(Logger.error).toHaveBeenCalled();
  });
});

describe('getProfileCompletionStats', () => {
  const mockBody = { userId: 123 };

  it('should return error if userId is not provided', async () => {
    const result = await v1Controller.getProfileCompletionStats({} as any);
    expect(result).toEqual({
      type: 'error',
      msg: 'Invalid Request. User ID is required.',
    });
  });

  it('should return error if user is not found', async () => {
    const mockProfileHelper = { getProfileCompletionStats: jest.fn() };
    const mockUserRepository = { findByUID: jest.fn().mockResolvedValue(null) };

    helperServiceMock.getHelper.mockResolvedValueOnce(mockProfileHelper);
    helperServiceMock.get.mockResolvedValueOnce(mockUserRepository);

    const result = await v1Controller.getProfileCompletionStats(mockBody);
    expect(result).toEqual({
      type: 'error',
      msg: 'User not found.',
    });
    expect(mockUserRepository.findByUID).toHaveBeenCalledWith(mockBody.userId);
  });

  it('should return success with profile completion value', async () => {
    const mockUser = { uid: 123, email: '<EMAIL>' };
    const mockProfileHelper = {
      getProfileCompletionStats: jest.fn().mockResolvedValue({ overallCompletion: 78 }),
    };
    const mockUserRepository = { findByUID: jest.fn().mockResolvedValue(mockUser) };

    helperServiceMock.getHelper.mockResolvedValueOnce(mockProfileHelper);
    helperServiceMock.get.mockResolvedValueOnce(mockUserRepository);

    const result = await v1Controller.getProfileCompletionStats(mockBody);

    expect(result).toEqual({
      type: 'success',
      profileCompletion: 78,
    });
    expect(mockProfileHelper.getProfileCompletionStats).toHaveBeenCalledWith(
      mockUser,
      true,
      'Yes',
      false,
    );
  });

  it('should return generic error if exception occurs', async () => {
    const error = new Error('Unexpected failure');
    helperServiceMock.getHelper.mockRejectedValue(error);

    const result = await v1Controller.getProfileCompletionStats(mockBody);

    expect(result).toEqual({
      type: 'error',
      msg: 'An error occurred while retrieving profile completion stats.',
    });
    expect(Logger.error).toHaveBeenCalledWith('getProfileCompletionStats', expect.any(Object));
  });
});


describe('removeUserRoles', () => {
  it('should remove roles successfully', async () => {
    const reqBody = { uid: '1001', roles: ['admin'] };

    userApiServiceMock.removeUserRoles.mockResolvedValue(true);

    const result = await v1Controller.removeUserRoles(reqBody);
    expect(result).toEqual({ type: 'success', msg: 'User roles removed successfully' });
    expect(userApiServiceMock.removeUserRoles).toHaveBeenCalledWith(reqBody);
  });

  it('should return error message if result is not true', async () => {
    const reqBody = { uid: '1001', roles: ['admin'] };

    userApiServiceMock.removeUserRoles.mockResolvedValue('Some failure');

    const result = await v1Controller.removeUserRoles(reqBody);
    expect(result).toEqual({ type: 'error', msg: 'Some failure' });
  });

  it('should return error object if exception thrown', async () => {
    const reqBody = { uid: '1001', roles: ['admin'] };
    const error = new Error('Unexpected error');
    userApiServiceMock.removeUserRoles.mockRejectedValue(error);

    const result = await v1Controller.removeUserRoles(reqBody);
    expect(result).toEqual({ type: 'error', msg: 'Unexpected error' });
    expect(Logger.error).toHaveBeenCalled();
  });
});

describe('createRole', () => {
  it('should return success when role is created', async () => {
    const reqBody = { rid: 101, roleName: 'trainer' };
    userApiServiceMock.createRole.mockResolvedValue(reqBody);

    const result = await v1Controller.createRole(reqBody);
    expect(result).toEqual({
      type: 'success',
      msg: 'Role created successfully',
      data: reqBody,
    });
  });

  it('should return error when role creation fails (null result)', async () => {
    const reqBody = { rid: 101, roleName: 'trainer' };
    userApiServiceMock.createRole.mockResolvedValue(null);

    const result = await v1Controller.createRole(reqBody);
    expect(result).toEqual({ type: 'error', msg: 'Role creation failed' });
  });

  it('should return error when service throws', async () => {
    const reqBody = { rid: 101, roleName: 'trainer' };
    const error = new Error('Insert failed');
    userApiServiceMock.createRole.mockRejectedValue(error);

    const result = await v1Controller.createRole(reqBody);
    expect(result).toEqual({ type: 'error', msg: 'Insert failed' });
    expect(Logger.error).toHaveBeenCalled();
  });
});

describe('updateRole', () => {
  it('should return success when role is updated', async () => {
    const reqBody = { rid: 102, roleName: 'admin' };
    userApiServiceMock.updateRole.mockResolvedValue(reqBody);

    const result = await v1Controller.updateRole(reqBody);
    expect(result).toEqual({
      type: 'success',
      msg: 'Role updated successfully',
      data: reqBody,
    });
  });

  it('should return error when role update fails (null result)', async () => {
    const reqBody = { rid: 102, roleName: 'admin' };
    userApiServiceMock.updateRole.mockResolvedValue(null);

    const result = await v1Controller.updateRole(reqBody);
    expect(result).toEqual({ type: 'error', msg: 'Role updation failed' });
  });

  it('should return error when service throws', async () => {
    const reqBody = { rid: 102, roleName: 'admin' };
    const error = new Error('Update error');
    userApiServiceMock.updateRole.mockRejectedValue(error);

    const result = await v1Controller.updateRole(reqBody);
    expect(result).toEqual({ type: 'error', msg: 'Update error' });
    expect(Logger.error).toHaveBeenCalled();
  });
});

});
