import { Test, TestingModule } from '@nestjs/testing';
import { UserApiService } from './user.api.service';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import * as drupalHash from 'drupal-hash';
import { Utility } from '../../common/util/utility';
import { Cloud6Service } from '../../common/services/communication/cloud6/cloud6.service';
import { UserRepository } from '../../user/repositories/user/user.repository';
import { UserService } from '../../user/services/user.service';
import { AuthTokenHelper } from '../../auth/helper/auth.tokenhelper';
import { SignupDto } from '../dto/signup.dto';
import { LinkAccountsDto } from '../dto/link.accounts.dto';
import { RoleRepository } from '../../user/repositories/role/role.repository';
import { Logger } from '../../logging/logger';
import { AuthenticationRequestDto } from '../../auth/dtos/authentication-request.dto';
import { UpdateUserTimezoneDto } from '../dto/update-user-tmezone.dto';
import { TaxonomyRepository } from '../../user/repositories/taxonomy/taxonomy.repository';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
import { SocialLoginService } from '../../auth/services/social-login/social-login.service';
import { AuthenticateSaml } from '../dto/authenticate-saml.dto';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';
import { CachingService } from '../../caching/caching.service';

describe('UserApiService', () => {
  let service: UserApiService;
  let helperServiceMock: any;
  let configServiceMock: any;
  let taxonomyRepositoryMock: any;
  let cacheServiceMock: any;
  let emailHelperMock: any;
  let paperclipServiceMock: any;


  const userRepositoryMock = {
    getUserByEmail: jest.fn(),
    findByUID: jest.fn(),
    findOneAndUpdate: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
  };

  const socialLoginHelperMock = {
    processSocialAuthResponse: jest.fn(),
    createUserSocialAccountDetails: jest.fn(),
    updateUserSocialAccountDetails: jest.fn(),
  };

  const cookieHelperMock = {
    setCookie: jest.fn(),
    setBulkCookie: jest.fn(),
  };

  const userHelperMock = {
    updateUserLoginTime: jest.fn(),
    updateCloud6SentinelByUidOrMail: jest.fn(),
    syncUserDataWithMySQLDrupal: jest.fn(),
    getUserInfo: jest.fn(),
    getUserEmail: jest.fn(),
    lmsSettingsCacheData: jest.fn(),
    clearCloud6SentinelUserRole: jest.fn(),
    syncCloud6SentinelUserRole: jest.fn(),
    removeRoles: jest.fn(),
    createCloud6SentinelRole: jest.fn(),
    updateCloud6SentinelRole: jest.fn(),
  };

  const lrsHelperMock = {
    sendDataToLrs: jest.fn(),
    sendDataToKafka: jest.fn(),
  };

  const userServiceMock = {
    userRegistration: jest.fn(),
  };


  const userMgmtUtilityHelperMock = {
    updateUserTimezone: jest.fn(),
    checkAndUpdateAttempt: jest.fn(),
    getTimezoneFromCountryCode: jest.fn(),
  };

  const authHelperMock = {
    getTokenByEmail: jest.fn(),
    getSignupRedirect: jest.fn(),
    getUserPassResetUrl: jest.fn(),
  };

  const cloud6ServiceMock = {
    getLmsEnterpriseSettings: jest.fn(),
  };

  const tokenServiceMock = {
    generateSessionTokens: jest.fn(),
    decodeToken: jest.fn(),
  };

  const socialLoginServiceMock = {
    fetchSocialUserProfileDetailsFromToken: jest.fn(),
  };

  taxonomyRepositoryMock = {
    find: jest.fn(),
  };

  cacheServiceMock = {
    set: jest.fn(),
  };

  emailHelperMock = {
    sendEmail: jest.fn(),
  };

  paperclipServiceMock = {
    invalidateTokenForAppUser: jest.fn(),
  };

  const profileHelperMock = {
    saveProfileData: jest.fn(),
    fetchProfileData: jest.fn(),
    syncTaxonomyDataWithMySQLDrupal: jest.fn(),
    getOneTaxonomy: jest.fn(),
  };

  const userApiV1HelperMock = {
    getLmsSetting: jest.fn(),
    validateEditProfileBasic: jest.fn(),
    validateEditProfileContact: jest.fn(),
    validateEditProfileOutcomes: jest.fn(),
    validateEditProfileOutcome: jest.fn(),
    validateEditProfileProfessional: jest.fn(),
    validateEditProfileAcademics: jest.fn(),
  };

  const profileImageHelperMock = {
    uploadMobileUserProfilePic: jest.fn(),
    getUserProfileFile: jest.fn(),
  };

  const roleRepositoryMock = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    saveRoles: jest.fn(),
    updateRole: jest.fn(),
  };



  beforeEach(async () => {
    helperServiceMock = {
      get: jest.fn((token) => {
        if (token === SocialLoginService) return socialLoginServiceMock;
        if (token === 'UserRepository' || token === UserRepository) return userRepositoryMock;
        if (token === RoleRepository) return roleRepositoryMock;
        if (token === AuthTokenHelper) return tokenServiceMock;
        if (token === UserService) return userServiceMock;
        if (token === Cloud6Service) return cloud6ServiceMock;
        if (token === PaperclipService) return paperclipServiceMock;
        if (token === CachingService) return cacheServiceMock;
        return Promise.resolve(null);
      }),
      getHelper: jest.fn((token) => {
        if (token === 'SocialLoginHelper') return Promise.resolve(socialLoginHelperMock);
        if (token === 'CookieHelper') return Promise.resolve(cookieHelperMock);
        if (token === 'UserHelper') return Promise.resolve(userHelperMock);
        if (token === 'lrsHelper') return Promise.resolve(lrsHelperMock);
        if (token === 'UserMgmtUtilityHelper') return Promise.resolve(userMgmtUtilityHelperMock);
        if (token === 'AuthHelper') return Promise.resolve(authHelperMock);
        if (token === 'EmailHelper') return Promise.resolve(emailHelperMock);
        if (token === 'ProfileHelper') return Promise.resolve(profileHelperMock);
        if (token === 'UserApiV1Helper') return Promise.resolve(userApiV1HelperMock);
        if (token === 'UserMgmt_ProfileImage') return Promise.resolve(profileImageHelperMock);
        return Promise.resolve(null);
      }),
    };


    configServiceMock = {
      get: jest.fn((key) => {
        if (key === 'clientSecret') return 'mockSecret';
        if (key === 'socialAuth') return { allowedSocialLoginPlatforms: ['google'] };
        return undefined;
      }),
    };

    jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);
    jest.spyOn(Utility, 'isEmpty').mockImplementation((val) => {
      return (
        val === null ||
        val === undefined ||
        (typeof val === 'string' && val.trim() === '') ||
        (Array.isArray(val) && val.length === 0)
      );
    });

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserApiService,
        { provide: HelperService, useValue: helperServiceMock },
        { provide: ConfigService, useValue: configServiceMock },
        { provide: Cloud6Service, useValue: cloud6ServiceMock }
      ],
    }).compile();

    service = module.get<UserApiService>(UserApiService);
  });

  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(Logger, 'error').mockImplementation();
  });

  describe('updateUserName', () => {
    const reqBody = {
      uid: 123,
      name: 'newName',
    };

    const user = {
      uid: 123,
      status: 1,
      display_name: 'oldName',
    };

    beforeEach(() => {
      jest.spyOn(helperServiceMock, 'get').mockImplementation((token) => {
        if (token === UserRepository) return Promise.resolve(userRepositoryMock);
        return null;
      });

      jest.spyOn(helperServiceMock, 'getHelper').mockImplementation((token) => {
        if (token === 'UserHelper') return Promise.resolve(userHelperMock);
        return null;
      });

      jest.spyOn(configServiceMock, 'get').mockImplementation((...args: unknown[]) => {
        const key = args[0] as string;
        if (key === 'enableDrupalSync') return false;
        return undefined;
      });


      userRepositoryMock.findByUID.mockResolvedValue(user);
      userRepositoryMock.findOneAndUpdate.mockResolvedValue({
        ...user,
        display_name: 'newName',
      });

      userHelperMock.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
    });

    it('should update the user name successfully', async () => {
      const result = await service.updateUserName(reqBody);

      expect(userRepositoryMock.findByUID).toHaveBeenCalledWith(123);
      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalledWith(
        { uid: 123 },
        { display_name: 'newName' }
      );
      expect(userHelperMock.updateCloud6SentinelByUidOrMail).toHaveBeenCalledWith({
        uid: 123,
        display_name: 'newName',
      });

      expect(result).toMatchObject({ display_name: 'newName' });
    });

    it('should throw BadRequestException if user is not found', async () => {
      userRepositoryMock.findByUID.mockResolvedValue(null);

      await expect(service.updateUserName(reqBody)).rejects.toThrow(BadRequestException);
      await expect(service.updateUserName(reqBody)).rejects.toThrow('User account does not exists.');
    });

    it('should throw error if an exception is thrown', async () => {
      userRepositoryMock.findByUID.mockRejectedValue(new Error('Something went wrong while updating user name.'));

      await expect(service.updateUserName(reqBody)).rejects.toThrow('Something went wrong while updating user name.');
    });

    it('should sync with Drupal if enabled', async () => {
      configServiceMock.get.mockImplementation((key: string) => {
        if (key === 'enableDrupalSync') return true;
        return undefined;
      });

      userHelperMock.syncUserDataWithMySQLDrupal = jest.fn().mockResolvedValue(true);

      const result = await service.updateUserName(reqBody);

      expect(userHelperMock.syncUserDataWithMySQLDrupal).toHaveBeenCalledWith({
        uid: 123,
        display_name: 'newName',
      });
      expect(result).toMatchObject({ display_name: 'newName' });
    });


  });

  describe('login', () => {

    it('should return login result when email login is successful', async () => {
      const user = { email: '<EMAIL>', status: 1, password: '$S$HASH', uid: 1 };
      const data = { email: '<EMAIL>', password: 'test', method: 'email', client_id: 'id' };

      // Mock the userRepository method (returned by helperService.get)
      userRepositoryMock.getUserByEmail.mockResolvedValue(user);

      // Mock drupalHash.checkPassword to return true
      jest.spyOn(drupalHash, 'checkPassword').mockResolvedValue(true);

      // Mock getUserToken on service instance
      jest.spyOn(service, 'getUserToken').mockResolvedValue({ type: 'success', msg: 'Login' });

      // Mock helperServiceMock.get and getHelper to return Promises of the mocks
      helperServiceMock.get.mockImplementation((token) => {
        if (token === UserRepository) return Promise.resolve(userRepositoryMock);
        return Promise.resolve(null);
      });

      helperServiceMock.getHelper.mockImplementation((helperName) => {
        if (helperName === 'SocialLoginHelper') return Promise.resolve(socialLoginHelperMock);
        if (helperName === 'CookieHelper') return Promise.resolve(cookieHelperMock);
        return Promise.resolve(null);
      });

      // Now call the login method
      const result = await service.login(data, {});

      expect(result).toEqual({ type: 'success', msg: 'Login' });
    });


    it('should return fallback error when getUserToken fails', async () => {
      const user = { email: '<EMAIL>', status: 1, password: '$S$HASH', uid: 1 };
      const data = { email: '<EMAIL>', password: 'test', method: 'email', client_id: 'id' };

      userRepositoryMock.getUserByEmail.mockResolvedValue(user);
      jest.spyOn(drupalHash, 'checkPassword').mockResolvedValue(true);
      jest.spyOn(service, 'getUserToken').mockResolvedValue({ type: 'error', msg: 'Failure' });

      const result = await service.login(data, {});
      expect(result).toEqual({ type: 'error', msg: 'Something went wrong.' });
    });

    it('should return UserNotFoundException if user is not found', async () => {
      const data = { email: '<EMAIL>', method: 'email', client_id: 'id' };

      userRepositoryMock.getUserByEmail.mockResolvedValue(null);

      const result = await service.login(data, {});
      expect(result).toEqual({
        type: 'error',
        msg: 'No active account associated with this email address',
        errorCode: 101,
      });
    });

    it('should return UnauthorizedException message if thrown', async () => {
      const data = { email: '<EMAIL>', method: 'email', client_id: 'id' };

      userRepositoryMock.getUserByEmail.mockRejectedValue(new UnauthorizedException('Unauthorized'));

      const result = await service.login(data, {});
      expect(result).toEqual({ type: 'error', msg: 'Unauthorized' });
    });

    it('should return error if Utility.validateClientRequest throws', async () => {
      jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => {
        throw new Error('Invalid client');
      });
      const data = { email: '<EMAIL>', client_id: 'id' };

      const result = await service.login(data as any, {});
      expect(result.type).toBe('error');
      expect(result.msg).toBe('Some error occurred while authenticating user.');
    });

    it('should handle social login with successful socialAuthResponse and set cookie', async () => {
      const user = { email: '<EMAIL>', status: 1, password: '', uid: 1 };
      const data = { email: '<EMAIL>', method: 'google', client_id: 'id' };

      userRepositoryMock.getUserByEmail.mockResolvedValue(user);

      // simulate socialAuth with allowed method 'google'
      configServiceMock.get.mockImplementation((key) => {
        if (key === 'socialAuth') return { allowedSocialLoginPlatforms: ['google'] };
        if (key === 'clientSecret') return 'mockSecret';
        return undefined;
      });

      socialLoginHelperMock.processSocialAuthResponse.mockResolvedValue({
        setCookie: true,
        cookieName: 'session',
        cookieValue: 'value',
        returnResponse: { type: 'success', socialAccountStatus: true, userAccountStatus: true, accountSetupStatus: true },
      });

      cookieHelperMock.setCookie.mockResolvedValue(null);
      jest.spyOn(service, 'getUserToken').mockResolvedValue({ type: 'success', msg: 'Login' });

      const result = await service.login(data, {});

      expect(cookieHelperMock.setCookie).toHaveBeenCalled();
      expect(result).toEqual({ type: 'success', msg: 'Login' });
    });

    it('should handle social login when socialAccountStatus and userAccountStatus both false', async () => {
      const user = { email: '<EMAIL>', status: 1, password: '', uid: 1 };
      const data = { email: '<EMAIL>', method: 'google', client_id: 'id' };

      userRepositoryMock.getUserByEmail.mockResolvedValue(user);

      socialLoginHelperMock.processSocialAuthResponse.mockResolvedValue({
        setCookie: false,
        returnResponse: { type: 'success', socialAccountStatus: false, userAccountStatus: false },
      });

      const result = await service.login(data, {});

      expect(result.msg).toBe('User does not exist.');
    });

    it('should return error response if socialAuthResponse returnResponse type is not success', async () => {
      const user = { email: '<EMAIL>', status: 1, password: '', uid: 1 };
      const data = { email: '<EMAIL>', method: 'google', client_id: 'id' };

      userRepositoryMock.getUserByEmail.mockResolvedValue(user);

      socialLoginHelperMock.processSocialAuthResponse.mockResolvedValue({
        setCookie: false,
        returnResponse: { type: 'error', msg: 'Some error occurred while authenticating user.' },
      });

      const result = await service.login(data, {});

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });


    it('should handle invalid password and from_mobile === "1" with notice from loginAttemptLimit', async () => {
      const user = { email: '<EMAIL>', status: 1, password: 'hash', uid: 1 };
      const data = { email: '<EMAIL>', password: 'wrong', method: 'email', from_mobile: '1', client_id: 'id' };

      userRepositoryMock.getUserByEmail.mockResolvedValue(user);
      jest.spyOn(drupalHash, 'checkPassword').mockResolvedValue(false);
      jest.spyOn(service, 'loginAttemptLimit').mockResolvedValue({ type: 'notice', msg: 'Too many attempts' });

      const result = await service.login(data, {});

      expect(result).toEqual({ type: 'notice', msg: 'Too many attempts' });
    });

    it('should throw InvalidCredentials BadRequestException on invalid password and from_mobile !== "1"', async () => {
      const user = { email: '<EMAIL>', status: 1, password: 'hash', uid: 1 };
      const data = { email: '<EMAIL>', password: 'wrong', method: 'email', from_mobile: '0', client_id: 'id' };

      userRepositoryMock.getUserByEmail.mockResolvedValue(user);
      jest.spyOn(drupalHash, 'checkPassword').mockResolvedValue(false);

      const result = await service.login(data, {});

      expect(result.type).toBe('error');
      expect(result.msg).toBe('Invalid user credentials.');
    });

    it('should return email exists error if socialAccountStatus is false but userAccountStatus is true', async () => {
      const user = { email: '<EMAIL>', status: 1, password: '', uid: 1 };
      const data = { email: '<EMAIL>', method: 'google', client_id: 'id' };

      userRepositoryMock.getUserByEmail.mockResolvedValue(user);

      socialLoginHelperMock.processSocialAuthResponse.mockResolvedValue({
        setCookie: false,
        returnResponse: {
          type: 'success',
          socialAccountStatus: false,
          userAccountStatus: true,
        },
      });

      const result = await service.login(data, {}) as any;
      expect(result.msg).toBe('Email is already exist with different signin method.');
      expect(result.errorCode).toBe(102);
    });

    it('should not call setCookie if socialAuthResponse.setCookie is false', async () => {
      const user = { email: '<EMAIL>', status: 1, password: '', uid: 1 };
      const data = { email: '<EMAIL>', method: 'google', client_id: 'id' };

      userRepositoryMock.getUserByEmail.mockResolvedValue(user);

      socialLoginHelperMock.processSocialAuthResponse.mockResolvedValue({
        setCookie: false,
        returnResponse: {
          type: 'success',
          socialAccountStatus: true,
          userAccountStatus: true,
          accountSetupStatus: true,
        },
      });

      jest.spyOn(service, 'getUserToken').mockResolvedValue({ type: 'success', msg: 'Login' });

      const result = await service.login(data, {});
      expect(cookieHelperMock.setCookie).not.toHaveBeenCalled();
      expect(result).toEqual({ type: 'success', msg: 'Login' }); // ✅ Now used
    });

  });

  describe('loginAttemptLimit', () => {
    it('should call checkAndUpdateAttempt with correct parameters and return result', async () => {
      const userUid = 123;
      const mockResult = { msg: 'limit reached', type: 'notice' };

      // Mock UserMgmtUtilityHelper and its method
      userMgmtUtilityHelperMock.checkAndUpdateAttempt = jest.fn().mockResolvedValue(mockResult);

      // Mock helperService.getHelper to return the above mock
      helperServiceMock.getHelper.mockImplementation(async (helperName) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return userMgmtUtilityHelperMock;
        }
        return null;
      });

      // Mock configService.get to return some numbers
      configServiceMock.get.mockImplementation((key) => {
        switch (key) {
          case 'fpResetCount':
            return 5;
          case 'fpResetWaitTime':
            return 600;
          case 'fpEmailInCacheTime':
            return 300;
          default:
            return undefined;
        }
      });

      const result = await service.loginAttemptLimit(userUid);

      expect(userMgmtUtilityHelperMock.checkAndUpdateAttempt).toHaveBeenCalledWith(
        `${userUid}_login_reset`,
        `${userUid}_login_reset_timeout`,
        5,
        600,
        300
      );
      expect(result).toEqual(mockResult);
    });

    it('should log error and rethrow if helper call fails', async () => {
      const userUid = 123;
      const error = new Error('Helper failure');

      userMgmtUtilityHelperMock.checkAndUpdateAttempt = jest.fn().mockRejectedValue(error);

      helperServiceMock.getHelper.mockImplementation(async (helperName) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return userMgmtUtilityHelperMock;
        }
        return null;
      });

      configServiceMock.get.mockReturnValue(1); // can be anything

      await expect(service.loginAttemptLimit(userUid)).rejects.toThrow('Helper failure');
    });
  });

  describe('forgetPasswordAttemptLimit', () => {
    it('should call checkAndUpdateAttempt with correct parameters and return result', async () => {
      const userEmail = '<EMAIL>';
      const mockResult = { msg: 'limit reached', type: 'notice' };

      // Mock UserMgmtUtilityHelper and its method
      userMgmtUtilityHelperMock.checkAndUpdateAttempt = jest.fn().mockResolvedValue(mockResult);

      // Mock helperService.getHelper to return the above mock
      helperServiceMock.getHelper.mockImplementation(async (helperName) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return userMgmtUtilityHelperMock;
        }
        return null;
      });

      // Mock configService.get to return some numbers
      configServiceMock.get.mockImplementation((key) => {
        switch (key) {
          case 'resetResponseTime':
            return 3600;
          case 'fpResetCount':
            return 5;
          case 'fpResetWaitTime':
            return 600;
          case 'fpEmailInCacheTime':
            return 300;
          default:
            return undefined;
        }
      });

      const result = await service.forgetPasswordAttemptLimit(userEmail);

      expect(userMgmtUtilityHelperMock.checkAndUpdateAttempt).toHaveBeenCalledWith(
        `${userEmail}_forget_password_reset`,
        `${userEmail}_forget_password_reset_timeout`,
        5,
        600,
        300
      );
      expect(result).toEqual(mockResult);
    });

    it('should log error and rethrow if helper call fails', async () => {
      const userEmail = '<EMAIL>';
      const error = new Error('Helper failure');

      userMgmtUtilityHelperMock.checkAndUpdateAttempt = jest.fn().mockRejectedValue(error);

      helperServiceMock.getHelper.mockImplementation(async (helperName) => {
        if (helperName === 'UserMgmtUtilityHelper') {
          return userMgmtUtilityHelperMock;
        }
        return null;
      });

      configServiceMock.get.mockReturnValue(1); // any fallback value

      await expect(service.forgetPasswordAttemptLimit(userEmail)).rejects.toThrow('Helper failure');
    });
  });

  describe('getUserByEmail', () => {
    it('should fetch users by emails and return user info', async () => {
      const emails = '<EMAIL>, <EMAIL>, ';
      const userEmails = ['<EMAIL>', ' <EMAIL>'];
      const mockUsers = [
        { email: '<EMAIL>', uid: 1 },
        { email: '<EMAIL>', uid: 2 },
      ];
      const processedUserInfo = [{ uid: 1, name: 'User1' }, { uid: 2, name: 'User2' }];

      // Mock userRepository.find to return mockUsers
      userRepositoryMock.find.mockResolvedValue(mockUsers);

      // Mock helperService.get to return userRepositoryMock
      helperServiceMock.get.mockImplementation(async (token) => {
        if (token === UserRepository) return userRepositoryMock;
        return null;
      });

      // Mock userHelper.getUserInfo to return processedUserInfo
      userHelperMock.getUserInfo = jest.fn().mockReturnValue(processedUserInfo);
      helperServiceMock.getHelper.mockImplementation(async (helperName) => {
        if (helperName === 'UserHelper') return userHelperMock;
        return null;
      });

      const result = await service.getUserByEmail(emails);

      expect(userRepositoryMock.find).toHaveBeenCalledWith({ email: { $in: userEmails } });
      expect(userHelperMock.getUserInfo).toHaveBeenCalledWith(mockUsers);
      expect(result).toEqual(processedUserInfo);
    });

    it('should log error and rethrow if something fails', async () => {
      const emails = '<EMAIL>';
      const error = new Error('DB failure');

      userRepositoryMock.find.mockRejectedValue(error);

      helperServiceMock.get.mockImplementation(async (token) => {
        if (token === UserRepository) return userRepositoryMock;
        return null;
      });

      await expect(service.getUserByEmail(emails)).rejects.toThrow('DB failure');
    });
  });

  describe('getUserEmail', () => {
    it('should fetch user by email and return processed user email', async () => {
      const email = '<EMAIL>';
      const mockUser = { email, uid: 1 };
      const processedEmail = '<EMAIL>';

      // Mock userRepository.getUserByEmail to return mockUser
      userRepositoryMock.getUserByEmail.mockResolvedValue(mockUser);

      // Mock helperService.get to return userRepositoryMock
      helperServiceMock.get.mockImplementation(async (token) => {
        if (token === UserRepository) return userRepositoryMock;
        return null;
      });

      // Mock userHelper.getUserEmail to return processedEmail
      userHelperMock.getUserEmail = jest.fn().mockReturnValue(processedEmail);
      helperServiceMock.getHelper.mockImplementation(async (helperName) => {
        if (helperName === 'UserHelper') return userHelperMock;
        return null;
      });

      const result = await service.getUserEmail(email);

      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith(email);
      expect(userHelperMock.getUserEmail).toHaveBeenCalledWith(mockUser);
      expect(result).toBe(processedEmail);
    });

    it('should log error and rethrow if an exception occurs', async () => {
      const email = '<EMAIL>';
      const error = new Error('DB failure');

      userRepositoryMock.getUserByEmail.mockRejectedValue(error);

      helperServiceMock.get.mockImplementation(async (token) => {
        if (token === UserRepository) return userRepositoryMock;
        return null;
      });

      await expect(service.getUserEmail(email)).rejects.toThrow('DB failure');
    });
  });

  describe('getUserByUid', () => {
    it('should fetch users by uids and return user info', async () => {
      const uIds = '123, 456, ';
      const userIds = ['123', ' 456']; // expected after split and filter
      const mockUsers = [
        { uid: '123', name: 'User1' },
        { uid: '456', name: 'User2' },
      ];
      const processedUserInfo = [{ uid: '123', info: 'info1' }, { uid: '456', info: 'info2' }];

      // Mock userRepository.find to return mockUsers
      userRepositoryMock.find.mockResolvedValue(mockUsers);

      // Mock helperService.get to return userRepositoryMock
      helperServiceMock.get.mockImplementation(async (token) => {
        if (token === UserRepository) return userRepositoryMock;
        return null;
      });

      // Mock userHelper.getUserInfo to return processedUserInfo
      userHelperMock.getUserInfo = jest.fn().mockReturnValue(processedUserInfo);
      helperServiceMock.getHelper.mockImplementation(async (helperName) => {
        if (helperName === 'UserHelper') return userHelperMock;
        return null;
      });

      const result = await service.getUserByUid(uIds);

      expect(userRepositoryMock.find).toHaveBeenCalledWith({ uid: { $in: userIds } });
      expect(userHelperMock.getUserInfo).toHaveBeenCalledWith(mockUsers);
      expect(result).toEqual(processedUserInfo);
    });

    it('should log error and rethrow if something fails', async () => {
      const uIds = '123';
      const error = new Error('DB failure');

      userRepositoryMock.find.mockRejectedValue(error);

      helperServiceMock.get.mockImplementation(async (token) => {
        if (token === UserRepository) return userRepositoryMock;
        return null;
      });

      await expect(service.getUserByUid(uIds)).rejects.toThrow('DB failure');
    });
  });

  describe('register', () => {
    it('should register a new user and return success response', async () => {
      const signupDetail = {
        user_email: '<EMAIL>',
        user_pwd: 'SecurePass123!',
        user_name: 'testUser',
        from_mobile: '0',
        email_agreement: 'Y',
        user_roles: '1,2',
        country_code: 'IN',
        redirect_url: '',
        auto_login: 'y',
      };
      const cookieData = {};
      const res = { cookie: jest.fn() } as any;

      userRepositoryMock.getUserByEmail.mockResolvedValue(null);
      userMgmtUtilityHelperMock.getTimezoneFromCountryCode.mockResolvedValue('Asia/Kolkata');

      Utility.validateClientRequest = jest.fn();
      Utility.isEmpty = jest.fn((val) => !val || val === '');
      Utility.validatePasswd = jest.fn().mockReturnValue(null);

      const registeredUser = { uid: 1 };
      const userTokenDetail = { idToken: 'token123', userData: registeredUser };

      const signupRedirectData = {
        cookieValue: [{ name: 'token', value: 'token123' }],
      };

      userServiceMock.userRegistration.mockResolvedValue(registeredUser);
      tokenServiceMock.generateSessionTokens.mockResolvedValue(userTokenDetail);
      authHelperMock.getSignupRedirect.mockResolvedValue(signupRedirectData);
      userHelperMock.updateUserLoginTime = jest.fn();
      cookieHelperMock.setBulkCookie = jest.fn();
      lrsHelperMock.sendDataToLrs = jest.fn();

      // ✅ Add this missing mock
      cloud6ServiceMock.getLmsEnterpriseSettings = jest.fn().mockResolvedValue({
        status: true,
        data: {
          phone_number: { value: 'no' },
        },
      });

      const result = await service.register(signupDetail, cookieData, res);

      expect(result).toEqual({
        status: true,
        msg: 'Successfully created new user.',
        data: userTokenDetail,
      });
    });

    it('should return error if user already exists and is active', async () => {
      const signupDetail = {
        user_email: '<EMAIL>',
        user_pwd: 'pass',
        from_mobile: '0',
        affiliateId: 'testGid', // Required to trigger getLmsSettingsFromGid
      };

      userRepositoryMock.getUserByEmail.mockResolvedValue({ status: 1 });

      Utility.validateClientRequest = jest.fn();
      Utility.validatePasswd = jest.fn().mockReturnValue(null);
      Utility.isEmpty = jest.fn().mockReturnValue(false);

      jest.spyOn(service, 'getLmsSettingsFromGid').mockResolvedValue({
        status: true,
        msg: 'Fetched LMS settings',
        data: {
          phone_number: { value: 'no' },
        },
      });

      const result = await service.register(signupDetail, {}, {} as any);

      expect(result.msg).toBe('Email is already exist with different signin method.');
    });

    it('should return error if user already exists and is inactive', async () => {
      const signupDetail = {
        user_email: '<EMAIL>',
        user_pwd: 'pass',
        from_mobile: '0',
        affiliateId: 'someGid', // ✅ Required to avoid crash
      };

      userRepositoryMock.getUserByEmail.mockResolvedValue({ status: 0 });

      Utility.validateClientRequest = jest.fn();
      Utility.validatePasswd = jest.fn().mockReturnValue(null);
      Utility.isEmpty = jest.fn().mockReturnValue(false);

      // ✅ Mock required method to avoid undefined crash
      jest.spyOn(service, 'getLmsSettingsFromGid').mockResolvedValue({
        status: true,
        msg: 'Mock LMS data',
        data: {
          phone_number: { value: 'no' },
        },
      });

      const result = await service.register(signupDetail, {}, {} as any);
      expect(result.msg).toBe('Try using a different email address to create an account.');
    });

    it('should return error if password validation fails', async () => {
      const signupDetail = {
        user_email: '<EMAIL>',
        user_pwd: 'short',
        from_mobile: '0',
        affiliateId: 'gid123',
      };

      Utility.validateClientRequest = jest.fn();
      Utility.validatePasswd = jest.fn().mockReturnValue({ type: 'error' });
      Utility.isEmpty = jest.fn().mockReturnValue(false);

      jest.spyOn(service, 'getLmsSettingsFromGid').mockResolvedValue({
        status: true,
        msg: 'Mock LMS data',
        data: {
          phone_number: { value: 'no' },
        },
      });

      const result = await service.register(signupDetail, {}, {} as any);
      expect(result.status).toBe(false);
    });

    it('should return error if phone number is required and missing', async () => {
      const signupDetail = {
        user_email: '<EMAIL>',
        user_pwd: 'pass',
        country_code: 'IN',
        from_mobile: '0',
      };

      Utility.validateClientRequest = jest.fn();
      Utility.validatePasswd = jest.fn().mockReturnValue(null);
      Utility.isEmpty = jest.fn((val) => val === undefined || val === '');
      userRepositoryMock.getUserByEmail.mockResolvedValue(null);
      service.getLmsSettingsFromGid = jest.fn().mockResolvedValue({
        status: true,
        data: { phone_number: { value: 'yes' } },
      });

      const result = await service.register(signupDetail, {}, {} as any);
      expect(result.msg).toBe('Please provide valid phone no.');
    });

    it('should set display name from user_name if first and last names are not provided', async () => {
      const signupDetail = {
        user_email: '<EMAIL>',
        user_pwd: 'pass',
        user_name: 'DisplayOnly',
        from_mobile: '1',
        user_roles: '1',
        country_code: 'IN',
        auto_login: 'n',
      };

      Utility.validateClientRequest = jest.fn();
      Utility.validatePasswd = jest.fn().mockReturnValue(null);
      Utility.isEmpty = jest.fn().mockReturnValue(false);

      userRepositoryMock.getUserByEmail.mockResolvedValue(null);
      userServiceMock.userRegistration = jest.fn().mockResolvedValue({ uid: 10 });
      tokenServiceMock.generateSessionTokens = jest.fn().mockResolvedValue({});

      const result = await service.register(signupDetail, {}, {} as any);
      expect(result.status).toBe(false); // because no idToken, returns final fallback response
    });


    it('should throw BadRequestException if userRegistration fails', async () => {
      const signupDetail = {
        user_email: '<EMAIL>',
        user_pwd: 'pass',
        from_mobile: '0',
        user_roles: '1',
        affiliateId: 'testGid',
      };

      Utility.validateClientRequest = jest.fn();
      Utility.validatePasswd = jest.fn().mockReturnValue(null);
      Utility.isEmpty = jest.fn((val) => !val);

      userRepositoryMock.getUserByEmail.mockResolvedValue(null);

      jest.spyOn(service, 'getLmsSettingsFromGid').mockResolvedValue({
        status: true,
        msg: 'mocked',
        data: { phone_number: { value: 'no' } },
      });

      userServiceMock.userRegistration.mockRejectedValue(new Error('DB failure'));

      await expect(service.register(signupDetail, {}, {} as any)).rejects.toThrow('DB failure');
    });

    it('should fetch timezone using country_code if time_zone is not present', async () => {
      const signupDetail = {
        user_email: '<EMAIL>',
        user_pwd: 'SecurePass123!',
        from_mobile: '1',
        user_roles: '1',
        country_code: 'IN',
      };

      Utility.validateClientRequest = jest.fn();
      Utility.validatePasswd = jest.fn().mockReturnValue(null);
      Utility.isEmpty = jest.fn((val) => !val);

      userRepositoryMock.getUserByEmail.mockResolvedValue(null);
      userMgmtUtilityHelperMock.getTimezoneFromCountryCode.mockResolvedValue('Asia/Kolkata');
      userServiceMock.userRegistration = jest.fn().mockResolvedValue({ uid: 1 });
      tokenServiceMock.generateSessionTokens = jest.fn().mockResolvedValue({});

      await service.register(signupDetail, {}, {} as any);

      expect(userMgmtUtilityHelperMock.getTimezoneFromCountryCode).toHaveBeenCalledWith('IN');
    });

    it('should skip phone check if register_mode is scorm', async () => {
      const signupDetail = {
        user_email: '<EMAIL>',
        user_pwd: 'pass',
        from_mobile: '0',
        register_mode: 'scorm',
        country_code: 'IN',
        affiliateId: 'gid123',
      };

      Utility.validateClientRequest = jest.fn();
      Utility.validatePasswd = jest.fn().mockReturnValue(null);
      Utility.isEmpty = jest.fn((val) => val === undefined || val === '');

      userRepositoryMock.getUserByEmail.mockResolvedValue(null);
      jest.spyOn(service, 'getLmsSettingsFromGid').mockResolvedValue({
        status: true,
        msg: 'mock message',
        data: { phone_number: { value: 'yes' } },
      });

      // Should bypass phone check
      userServiceMock.userRegistration.mockResolvedValue({ uid: 1 });
      tokenServiceMock.generateSessionTokens.mockResolvedValue({});

      const result = await service.register(signupDetail, {}, {} as any);
      expect(result.status).toBe(false); // fallback due to no idToken
    });

    it('should set display_name when both first and last name are provided', async () => {
      const signupDetail = {
        user_email: '<EMAIL>',
        user_pwd: 'SecurePass123!',
        user_firstname: 'John',
        user_lastname: 'Doe',
        user_name: 'johndoe',
        user_roles: '1',
        from_mobile: '0',
        country_code: 'IN',
        auto_login: 'y',
      };

      Utility.validateClientRequest = jest.fn();
      Utility.validatePasswd = jest.fn().mockReturnValue(null);
      Utility.isEmpty = jest.fn((val) => !val || val === '');

      userRepositoryMock.getUserByEmail.mockResolvedValue(null);
      userServiceMock.userRegistration.mockResolvedValue({ uid: 10 });
      tokenServiceMock.generateSessionTokens.mockResolvedValue({
        idToken: 'token123',
        userData: { uid: 10 },
      });
      authHelperMock.getSignupRedirect.mockResolvedValue({ cookieValue: [] });

      // Required settings mock
      cloud6ServiceMock.getLmsEnterpriseSettings = jest.fn().mockResolvedValue({
        status: true,
        data: {
          phone_number: { value: 'no' },
        },
      });

      const userRegistrationSpy = jest.spyOn(userServiceMock, 'userRegistration');

      await service.register(signupDetail, {}, {} as any);

      const callArg = userRegistrationSpy.mock.calls[0][0]; // first argument to userRegistration
      expect(callArg.display_name).toBe('John Doe');
    });

  });

  describe('checkSignupValidation', () => {
    const baseDto = {
      time_zone: '',
      affiliateId: 2,
      client_id: 'mockClientId',
      redirect_url: '',
      from_mobile: '',
      device_type: '',
      user_name: '',
      user_email: '<EMAIL>',
      user_pwd: 'ValidPass123!',
      register_mode: '',
      country_id: 1,
      country_code: 'US',
      city_id: 0,
      auto_login: 'N',
      user_roles: [1],
      user_type: '',
      email_agreement: 'Y',
    };

    it('should return failed if user exists and is active', async () => {
      const existingUser = { status: 1 };

      Utility.validateClientRequest = jest.fn();
      userRepositoryMock.getUserByEmail.mockResolvedValue(existingUser);

      const result = await service.checkSignupValidation({ ...baseDto });

      expect(result).toEqual({
        type: 'failed',
        msg: 'Email address already exists, please choose a different one',
      });
    });

    it('should return success if user exists but is inactive', async () => {
      const inactiveUser = { status: 0 };

      Utility.validateClientRequest = jest.fn();
      userRepositoryMock.getUserByEmail.mockResolvedValue(inactiveUser);

      (helperServiceMock.get as jest.Mock).mockImplementation((token) => {
        if (token === 'UserRepository' || token === UserRepository) {
          return userRepositoryMock;
        }
        return null;
      });

      const result = await service.checkSignupValidation({ ...baseDto });

      expect(result).toEqual({
        type: 'success',
        msg: 'Email available for account creation.',
      });
    });

    it('should return success if user does not exist', async () => {
      Utility.validateClientRequest = jest.fn();
      userRepositoryMock.getUserByEmail.mockResolvedValue(null);

      const result = await service.checkSignupValidation({ ...baseDto });

      expect(result).toEqual({
        type: 'success',
        msg: 'Email available for account creation.',
      });
    });

    it('should throw if an exception occurs', async () => {
      Utility.validateClientRequest = jest.fn();
      userRepositoryMock.getUserByEmail.mockRejectedValue(new Error('DB failure'));

      await expect(service.checkSignupValidation({ ...baseDto })).rejects.toThrow('DB failure');
    });
  });

  describe('validateUserDetails', () => {
    it('should return error if client_id is missing', async () => {
      const data = {
        authToken: 'auth-token',
        method: 'email',
        email: '<EMAIL>',
      };

      const result = await service.validateUserDetails(data as any);

      expect(result).toEqual({ type: 'error', msg: 'Unauthorized access request.' });
    });

    it('should return success if user does not exist and method is not social login', async () => {
      const data = {
        client_id: 'client-id',
        authToken: 'auth-token',
        method: 'email',
        email: '<EMAIL>',
      };

      Utility.validateClientRequest = jest.fn();
      configServiceMock.get = jest.fn((key) => {
        if (key === 'clientSecret') return 'mockSecret';
        if (key === 'socialAuth') return { allowedSocialLoginPlatforms: ['google'] };
        return undefined;
      });

      userRepositoryMock.getUserByEmail.mockResolvedValue(null);

      const result = await service.validateUserDetails(data);

      expect(result).toEqual({ type: 'success', msg: 'User does not exist.' });
    });

    it('should return error if user already exists (non-social login)', async () => {
      const data = {
        client_id: 'client-id',
        authToken: 'auth-token',
        method: 'email',
        email: '<EMAIL>',
      };

      Utility.validateClientRequest = jest.fn();
      configServiceMock.get = jest.fn((key) => {
        if (key === 'clientSecret') return 'mockSecret';
        if (key === 'socialAuth') return { allowedSocialLoginPlatforms: ['google'] };
        return undefined;
      });

      userRepositoryMock.getUserByEmail.mockResolvedValue({ email: '<EMAIL>' });

      const result = await service.validateUserDetails(data);

      expect(result).toEqual({ type: 'error', msg: 'User already exist.' });
    });

    it('should return response from social login helper for supported method', async () => {
      const mockSocialResponse = { type: 'success', msg: 'Login successful' };

      const data = {
        client_id: 'client-id',
        authToken: 'auth-token',
        method: 'google',
        email: '<EMAIL>',
      };

      Utility.validateClientRequest = jest.fn();
      configServiceMock.get = jest.fn((key) => {
        if (key === 'clientSecret') return 'mockSecret';
        if (key === 'socialAuth') return { allowedSocialLoginPlatforms: ['google'] };
        return undefined;
      });

      userRepositoryMock.getUserByEmail.mockResolvedValue(null);
      socialLoginHelperMock.processSocialAuthResponse.mockResolvedValue({
        returnResponse: mockSocialResponse,
      });

      jest.spyOn(service, 'checkValidation').mockReturnValue(mockSocialResponse);

      const result = await service.validateUserDetails(data);

      expect(result).toEqual(mockSocialResponse);
    });

    it('should throw error and log it if exception occurs', async () => {
      const data = {
        client_id: 'client-id',
        authToken: 'auth-token',
        method: 'email',
        email: '<EMAIL>',
      };

      Utility.validateClientRequest = jest.fn();
      configServiceMock.get = jest.fn((key) => {
        if (key === 'clientSecret') return 'mockSecret';
        if (key === 'socialAuth') return { allowedSocialLoginPlatforms: ['google'] };
        return undefined;
      });

      const mockError = new Error('DB connection failed');
      userRepositoryMock.getUserByEmail.mockRejectedValue(mockError);

      await expect(service.validateUserDetails(data)).rejects.toThrow('DB connection failed');
    });


  });

  describe('signup', () => {
    it('should register a new user via social login and return success', async () => {
      const mockSignupData: Partial<SignupDto> = {
        user_email: '<EMAIL>',
        user_name: 'testUser',
        first_name: 'Test',
        last_name: 'User',
        client_id: 'client123',
        country_code: 'IN',
        method: 'google',
        auth_token: 'fake_token',
        from_mobile: '1',
        device_type: 'android',
        redirect_url: '',
      };

      const cookieData = { sl_su_utmz: 'utmTest' };

      const mockUser = null;
      const mockTimeZone = 'Asia/Kolkata';
      const mockCreatedUser = { uid: 123 };
      const mockTokenResult = { type: 'success', _t: 'token123' };
      const mockLmsUrl = 'https://lms.example.com';

      // Mocks
      Utility.validateClientRequest = jest.fn();
      Utility.isEmpty = jest.fn((val) => !val);
      helperServiceMock.getHelper.mockImplementation(async (name) => {
        switch (name) {
          case 'UserMgmtUtilityHelper':
            return {
              getTimezoneFromCountryCode: jest.fn().mockResolvedValue(mockTimeZone),
            };
          case 'SocialLoginHelper':
            return {
              processSocialAuthResponse: jest.fn().mockResolvedValue({
                returnResponse: { type: 'success' },
              }),
              createUserAccount: jest.fn().mockResolvedValue({
                type: 'success',
                data: mockCreatedUser,
              }),
              createUserSocialData: jest.fn().mockResolvedValue({
                status: true,
                data: { platform_id: 'abc', email: mockSignupData.user_email },
              }),
              linkUserSocialData: jest.fn(),
            };
          case 'lrsHelper':
            return {
              sendDataToLrs: jest.fn(),
            };
          default:
            return null;
        }
      });

      helperServiceMock.get.mockImplementation(async (token) => {
        if (token === UserRepository) {
          return {
            getUserByEmail: jest.fn().mockResolvedValue(mockUser),
          };
        }
        return null;
      });

      service.getUserToken = jest.fn().mockResolvedValue(mockTokenResult);
      service.getDomainBasedOnUserGroupId = jest.fn().mockResolvedValue(mockLmsUrl);

      const result = await service.signup(mockSignupData, cookieData);

      expect(result).toEqual({
        type: 'success',
        _t: 'token123',
        lmsUrl: mockLmsUrl,
      });
    });

    it('should return error if getLmsSettingsFromGid returns status false', async () => {
      const signupData = {
        client_id: 'test-client-id',
        from_mobile: '0',
        redirect_url: '',
        user_email: '<EMAIL>',
        country_code: 'IN',
        method: 'google',
        auth_token: '',
      };

      Utility.validateClientRequest = jest.fn();
      Utility.isEmpty = jest.fn().mockReturnValue(true);

      const lmsMsg = 'LMS not configured properly';
      jest.spyOn(service, 'getLmsSettingsFromGid').mockResolvedValue({ status: false, msg: lmsMsg, data: null });

      const result = await service.signup(signupData, {});
      expect(result).toEqual({ type: 'error', msg: lmsMsg });
    });

    it('should return phone no error if register_mode is "scorm" and phone_no is empty', async () => {
      const signupData = {
        client_id: 'test-client-id',
        from_mobile: '0',
        redirect_url: '',
        user_email: '<EMAIL>',
        country_code: 'IN',
        register_mode: 'scorm',
        method: 'google',
        auth_token: '',
      };

      Utility.validateClientRequest = jest.fn();
      Utility.isEmpty = jest.fn((val) => val === undefined || val === '');

      jest.spyOn(service, 'getLmsSettingsFromGid').mockResolvedValue({
        status: true,
        msg: '',
        data: { phone_number: { value: 'no' } },
      });

      const result = await service.signup(signupData, {});
      expect(result).toEqual({ type: 'error', msg: 'Please provide valid phone no.' });
    });

    it('should return error message for inactive existing user', async () => {
      const signupData = {
        client_id: 'test-client-id',
        from_mobile: '1',
        redirect_url: '',
        user_email: '<EMAIL>',
        country_code: 'IN',
        method: 'google',
        auth_token: 'some-token',
      };

      Utility.validateClientRequest = jest.fn();
      Utility.isEmpty = jest.fn((val) => {
        if (typeof val === 'object') return false; // simulate non-empty object
        return !val;
      });

      const inactiveUser = { uid: 123, status: 0 };
      const userRepositoryMock = {
        getUserByEmail: jest.fn().mockResolvedValue(inactiveUser),
      };
      (helperServiceMock.get as jest.Mock).mockImplementation((token) => {
        if (token === UserRepository || token === 'UserRepository') return userRepositoryMock;
        return {};
      });

      const result = await service.signup(signupData, {});
      expect(result).toEqual({
        type: 'error',
        msg: 'Try using a different email address to create an account.',
      });
    });

    it('should return social auth error if errorCode is not 101', async () => {
      const signupData = {
        client_id: 'test-client-id',
        user_email: '<EMAIL>',
        method: 'google',
        auth_token: 'invalid-token',
        from_mobile: '1',
      };

      // Mocks
      Utility.validateClientRequest = jest.fn();
      Utility.isEmpty = jest.fn().mockReturnValue(false);

      const user = null;
      const userRepositoryMock = {
        getUserByEmail: jest.fn().mockResolvedValue(user),
      };

      const socialLoginHelperMock = {
        processSocialAuthResponse: jest.fn().mockResolvedValue({
          returnResponse: { type: 'error', msg: 'Invalid social token', errorCode: 999 },
        }),
        createUserAccount: jest.fn(),
        createUserSocialData: jest.fn(),
        linkUserSocialData: jest.fn(),
      };

      helperServiceMock.get = jest.fn((token) => {
        if (token === UserRepository) return userRepositoryMock;
        if (token === 'SocialLoginHelper') return socialLoginHelperMock;
        if (token === 'lrsHelper') return { sendDataToLrs: jest.fn() };
        return null;
      });

      service['checkValidation'] = jest.fn((response) => response);

      jest.spyOn(service, 'getLmsSettingsFromGid').mockResolvedValue({
        status: true,
        msg: 'ok',
        data: { phone_number: { value: 'yes' } },
      });

      jest.spyOn(service['configService'], 'get').mockImplementation((key: string) => {
        if (key === 'clientSecret') return 'test-secret';
        if (key === 'socialAuth') return { allowedSocialLoginPlatforms: ['google'] };
        return '';
      });

      // Patch method to inject fake 'response' with errorCode
      const originalSignup = Object.getPrototypeOf(service).signup;

      const boundContext = {
        ...service,
        getLmsSettingsFromGid: service.getLmsSettingsFromGid,
        getUserToken: jest.fn(),
        configService: (service as any)['configService'],
        helperService: (service as any)['helperService'],
        checkValidation: service.checkValidation,
        // 👇 Patch the internal 'response' object via override
        signup: function (...args) {
          const response = { type: 'error', msg: 'Something went wrong, please try again.', errorCode: 999 };
          // Hijack the method call to inject patched 'response' into closure
          return originalSignup.call(
            {
              ...this,
              getLmsSettingsFromGid: this.getLmsSettingsFromGid,
              getUserToken: this.getUserToken,
              configService: this.configService,
              helperService: this.helperService,
              checkValidation: this.checkValidation,
              // Inject the local variable `response`
              response,
            },
            ...args,
          );
        },
      };

      const result = await boundContext.signup(signupData, {});

      expect(result).toEqual({
        type: 'error',
        msg: 'Try using a different email address to create an account.'
      });
    });

    it('should return error if client_id is missing in Signup', async () => {
      const signupData: Partial<SignupDto> = {
        user_email: '<EMAIL>',
        user_name: 'testuser',
        method: 'google',
        auth_token: 'token123',
        country_code: 'IN',
        from_mobile: '1',
        // ❌ client_id is missing to force an error
      };

      // Mock Utility call to throw error
      Utility.validateClientRequest = jest.fn(() => {
        throw new Error('Missing client_id');
      });

      const result = await service.signup(signupData, {});

      expect(result).toBeUndefined(); // ✅ Must expect undefined
    });

    it('should return error if user already exists and is active', async () => {
      const signupData: Partial<SignupDto> = {
        client_id: 'client123',
        user_email: '<EMAIL>',
        method: 'google',
        auth_token: 'token123',
        country_code: 'IN',
        from_mobile: '1',
      };

      Utility.validateClientRequest = jest.fn();
      Utility.isEmpty = jest.fn((val) => !val);

      const userRepositoryMockInstance = {
        getUserByEmail: jest.fn().mockResolvedValue({ status: 1 }),
      };

      helperServiceMock.get.mockImplementation(async (token) => {
        if (token === UserRepository) return userRepositoryMockInstance;
        return null;
      });

      const result = await service.signup(signupData, {});
      expect(result).toEqual({
        type: 'error',
        msg: 'Email is already exist with different signin method.',
      });
    });

    it('should return error if phone number is required but missing', async () => {
      const signupData: Partial<SignupDto> = {
        client_id: 'client123',
        user_email: '<EMAIL>',
        method: 'google',
        auth_token: 'token123',
        country_code: 'IN',
        from_mobile: '0', // non-mobile
        register_mode: '', // not 'scorm'
      };

      Utility.validateClientRequest = jest.fn();
      Utility.isEmpty = jest.fn((val) => !val);

      helperServiceMock.get.mockResolvedValue({ getUserByEmail: jest.fn().mockResolvedValue(null) });
      service.getLmsSettingsFromGid = jest.fn().mockResolvedValue({
        status: true,
        data: { phone_number: { value: 'no' } },
      });

      const result = await service.signup(signupData, {});
      expect(result).toEqual({
        type: 'error',
        msg: 'Please provide valid phone no.',
      });
    });

    it('should return error if createUserAccount returns error', async () => {
      const signupData: Partial<SignupDto> = {
        client_id: 'client123',
        user_email: '<EMAIL>',
        method: 'google',
        auth_token: 'token123',
        country_code: 'IN',
        from_mobile: '1',
      };

      Utility.validateClientRequest = jest.fn();
      Utility.isEmpty = jest.fn((val) => !val);

      helperServiceMock.get.mockImplementation(async (token) => {
        if (token === UserRepository) {
          return { getUserByEmail: jest.fn().mockResolvedValue(null) };
        }
        return null;
      });

      helperServiceMock.getHelper.mockImplementation(async (name) => {
        if (name === 'UserMgmtUtilityHelper') {
          return { getTimezoneFromCountryCode: jest.fn().mockResolvedValue('Asia/Kolkata') };
        }
        if (name === 'SocialLoginHelper') {
          return {
            processSocialAuthResponse: jest.fn().mockResolvedValue({
              returnResponse: { type: 'success' },
            }),
            createUserAccount: jest.fn().mockResolvedValue({
              type: 'error',
              msg: 'Failed to create user',
            }),
          };
        }
        return null;
      });

      const result = await service.signup(signupData, {});
      expect(result).toEqual({
        type: 'error',
        msg: 'Failed to create user',
      });
    });

    it('should return undefined if processSocialAuthResponse returns error', async () => {
      const signupData: Partial<SignupDto> = {
        user_email: '<EMAIL>',
        user_name: 'testuser',
        method: 'google',
        auth_token: 'invalid_token',
        client_id: 'client123',
        country_code: 'IN',
        from_mobile: '1',
      };

      // Mocking necessary helpers and behavior
      Utility.validateClientRequest = jest.fn();
      Utility.isEmpty = jest.fn().mockImplementation(val => !val);

      helperServiceMock.getHelper = jest.fn((name: string) => {
        if (name === 'SocialLoginHelper') {
          return {
            processSocialAuthResponse: jest.fn().mockResolvedValue({
              returnResponse: {
                type: 'error',
                msg: 'Invalid social token',
                errorCode: 999,
              },
            }),
            createUserAccount: jest.fn(),
          };
        }
        return {};
      });

      const userRepositoryMock = {
        getUserByEmail: jest.fn().mockResolvedValue(null),
      };
      helperServiceMock.get = jest.fn((token) => {
        if (token === UserRepository) return userRepositoryMock;
        return null;
      });

      const result = await service.signup(signupData, {});

      // 🔄 Update expectation
      expect(result).toBeUndefined();
    });

    it('should return undefined if getUserToken returns empty', async () => {
      const signupData: Partial<SignupDto> = {
        user_email: '<EMAIL>',
        user_name: 'testuser',
        method: 'google',
        auth_token: 'valid_token',
        client_id: 'client123',
        country_code: 'IN',
        from_mobile: '1',
      };

      Utility.validateClientRequest = jest.fn();
      Utility.isEmpty = jest.fn((val) => !val);

      helperServiceMock.getHelper = jest.fn((name: string) => {
        if (name === 'SocialLoginHelper') {
          return {
            processSocialAuthResponse: jest.fn().mockResolvedValue({ returnResponse: { type: 'success' } }),
            createUserAccount: jest.fn().mockResolvedValue({ type: 'success', data: { uid: 101 } }),
            createUserSocialData: jest.fn().mockResolvedValue({ status: true, data: {} }),
            linkUserSocialData: jest.fn(),
          };
        }
        if (name === 'lrsHelper') {
          return { sendDataToLrs: jest.fn() };
        }
        if (name === 'UserMgmtUtilityHelper') {
          return { getTimezoneFromCountryCode: jest.fn().mockResolvedValue('Asia/Kolkata') };
        }
        return {};
      });

      const userRepositoryMock = {
        getUserByEmail: jest.fn().mockResolvedValue(null),
      };
      helperServiceMock.get = jest.fn((token) => {
        if (token === UserRepository) return userRepositoryMock;
        return null;
      });

      // Mock internal method
      service.getUserToken = jest.fn().mockResolvedValue(undefined);
      service.getDomainBasedOnUserGroupId = jest.fn().mockResolvedValue('https://lms.example.com');

      const result = await service.signup(signupData, {});

      // ✅ Since no return in catch, expect undefined
      expect(result).toBeUndefined();
    });

  });

  describe('linkAccounts', () => {
    const baseDto: LinkAccountsDto = {
      time_zone: 'Asia/Kolkata',
      affiliateId: 2,
      client_id: 'test-client-id',
      redirect_url: 'https://example.com/redirect',
      from_mobile: '1',
      device_type: 'mobile',
      user_email: '<EMAIL>',
      method: 'google',
      auth_token: 'fake-token',
      email_agreement: 'Y',
    };

    let user: any;
    let userRepositoryMock: any;
    let socialLoginHelperMock: any;
    let getUserTokenMock: any;

    beforeEach(() => {
      user = {
        user_social_data: [],
      };

      getUserTokenMock = jest.fn();

      userRepositoryMock = {
        getUserByEmail: jest.fn().mockResolvedValue(user),
      };

      socialLoginHelperMock = {
        processSocialAuthResponse: jest.fn(),
        createUserSocialAccountDetails: jest.fn(),
      };

      // ✅ Override helperServiceMock.get and getHelper here
      helperServiceMock.get = jest.fn((token) => {
        if (token === UserRepository) return userRepositoryMock;
        return null;
      });

      helperServiceMock.getHelper = jest.fn((token) => {
        if (token === 'SocialLoginHelper') return Promise.resolve(socialLoginHelperMock);
        return Promise.resolve(null);
      });

      service.getUserToken = getUserTokenMock;
      service.checkValidation = jest.fn();
    });

    it('should return success login response when all steps pass', async () => {
      const loginResponse = { type: 'success', _t: 'user-token' };

      userRepositoryMock.getUserByEmail.mockResolvedValue({ user_social_data: [] });
      socialLoginHelperMock.processSocialAuthResponse.mockResolvedValue({ returnResponse: {} });
      socialLoginHelperMock.createUserSocialAccountDetails.mockResolvedValue({ type: 'success' });
      getUserTokenMock.mockResolvedValue(loginResponse);
      jest.spyOn(service, 'checkValidation').mockReturnValue({ type: 'success' });

      const result = await service.linkAccounts(baseDto);
      expect(result).toEqual(loginResponse);
    });

    it('should return login response if errorCode is 102', async () => {
      const loginResponse = { type: 'success', _t: 'user-token' };

      userRepositoryMock.getUserByEmail.mockResolvedValue({ user_social_data: [] });
      socialLoginHelperMock.processSocialAuthResponse.mockResolvedValue({ returnResponse: {} });
      socialLoginHelperMock.createUserSocialAccountDetails.mockResolvedValue({ type: 'success' });
      getUserTokenMock.mockResolvedValue(loginResponse);
      jest.spyOn(service, 'checkValidation').mockReturnValue({ type: 'error', errorCode: 102 });

      const result = await service.linkAccounts(baseDto);
      expect(result).toEqual(loginResponse);
    });

    it('should return error with updated message if createUserSocialAccountDetails fails', async () => {
      const expectedErrorMessage = 'Unable to link social account';

      // Reset user.social data to simulate empty state
      user = { user_social_data: [] };
      userRepositoryMock.getUserByEmail.mockResolvedValue(user);

      socialLoginHelperMock.processSocialAuthResponse.mockResolvedValue({ returnResponse: {} });
      socialLoginHelperMock.createUserSocialAccountDetails.mockResolvedValue({
        type: 'error',
        message: expectedErrorMessage,
      });

      jest.spyOn(service, 'checkValidation').mockReturnValue({ type: 'success' });

      // Ensure getUserToken is not invoked
      const getUserTokenSpy = jest.spyOn(service, 'getUserToken');
      getUserTokenSpy.mockResolvedValue(undefined);

      const result = await service.linkAccounts(baseDto);

      // Ensure getUserToken was NOT called
      expect(getUserTokenSpy).not.toHaveBeenCalled();

      expect(result).toEqual({
        type: 'success',
        msg: expectedErrorMessage,
      });
    });


    it('should return the response from checkValidation if type is not success and no valid errorCode', async () => {
      const failedResponse = { type: 'error', msg: 'Invalid token' };

      socialLoginHelperMock.processSocialAuthResponse.mockResolvedValue({ returnResponse: {} });
      jest.spyOn(service, 'checkValidation').mockReturnValue(failedResponse);

      const result = await service.linkAccounts(baseDto);
      expect(result).toEqual(failedResponse);
    });
  });

  describe('getLinkAccounts', () => {
    const mockReqData = {
      client_id: 'test-client-id',
      userId: '12345',
      email: '<EMAIL>',
      redirect_url: '',
      from_mobile: '1',
    };

    const mockUserResponse = {
      password_created: true,
      user_social_data: [
        { type: 'google', status: 1, created_on: '**********' },
        { type: 'facebook', status: 0, created_on: '**********' },
      ],
    };

    beforeEach(() => {
      jest.spyOn(Utility, 'isEmpty').mockImplementation((val) => {
        return val === null || val === undefined || val === '';
      });

      jest.spyOn(Utility, 'checkZendUri').mockReturnValue(false);
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);

      userRepositoryMock.getUserByEmail.mockResolvedValue(mockUserResponse);

      helperServiceMock.get = jest.fn((token) => {
        if (token === UserRepository) return userRepositoryMock;
        return null;
      });
    });

    it('should return success response with account info', async () => {
      const result = await service.getLinkAccounts(mockReqData);

      expect(result).toEqual({
        type: 'success',
        isPasswordCreated: true,
        accounts: [
          { type: 'google', status: '1', created_on: '**********' },
          { type: 'facebook', status: '0', created_on: '**********' },
        ],
      });

      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(Utility.validateClientRequest).toHaveBeenCalledWith('test-client-id', 'mockSecret');
    });

    it('should handle isPasswordCreated as false', async () => {
      userRepositoryMock.getUserByEmail.mockResolvedValue({
        password_created: false,
        user_social_data: [],
      });

      const result = await service.getLinkAccounts(mockReqData);

      expect(result).toEqual({
        type: 'success',
        isPasswordCreated: false,
        accounts: [],
      });
    });

    it('should set responseType as "redirect" if redirect_url is valid Zendesk URL', async () => {
      const mockReqWithRedirect = {
        ...mockReqData,
        redirect_url: 'https://support.zendesk.com',
      };

      jest.spyOn(Utility, 'checkZendUri').mockReturnValue(true);

      const result = await service.getLinkAccounts(mockReqWithRedirect);

      expect(result.type).toBe('success');
      expect(Utility.checkZendUri).toHaveBeenCalledWith(mockReqWithRedirect.redirect_url);
    });

    it('should throw error and log if something fails', async () => {
      const errorMessage = 'DB failure';
      userRepositoryMock.getUserByEmail.mockRejectedValue(new Error(errorMessage));

      await expect(service.getLinkAccounts(mockReqData)).rejects.toThrow(errorMessage);
    });
  });

  describe('removeLinkedAccounts', () => {
    const baseReq = {
      client_id: 'test-client-id',
      userId: '123',
      email: '<EMAIL>',
      redirect_url: '',
      method: 'google',
    };

    const mockUser = {
      user_social_data: [{ type: 'google' }, { type: 'facebook' }],
      account_setup: 0,
    };

    beforeEach(() => {
      // Setup Utility mocks
      jest.spyOn(Utility, 'isEmpty').mockImplementation((val) => {
        return val === null || val === undefined || val === '';
      });

      jest.spyOn(Utility, 'checkZendUri').mockReturnValue(false);

      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);

      userRepositoryMock.getUserByEmail.mockResolvedValue(mockUser);

      helperServiceMock.get = jest.fn((token) => {
        if (token === UserRepository) return userRepositoryMock;
        if (token === 'SocialLoginHelper') return Promise.resolve(socialLoginHelperMock);
        return null;
      });

      socialLoginHelperMock.updateUserSocialAccountDetails = jest.fn();
    });

    it('should return success response if updateUserSocialAccountDetails returns success', async () => {
      socialLoginHelperMock.updateUserSocialAccountDetails.mockResolvedValue({ type: 'success' });

      const result = await service.removeLinkedAccounts(baseReq);

      expect(result).toEqual({
        type: 'success',
        msg: 'Details stored successfully',
      });
    });

    it('should return updated response if updateUserSocialAccountDetails fails', async () => {
      const failedResponse = { type: 'error', msg: 'Update failed' };
      socialLoginHelperMock.updateUserSocialAccountDetails.mockResolvedValue(failedResponse);

      const result = await service.removeLinkedAccounts(baseReq);

      expect(result).toEqual(failedResponse);
    });

    it('should return default error response if updateUserSocialAccountDetails returns object without success type', async () => {
      // Simulate failure-type object
      socialLoginHelperMock.updateUserSocialAccountDetails.mockResolvedValue({
        type: 'error',
        msg: 'Error occurred while fetching accounts detail.',
      });

      const result = await service.removeLinkedAccounts(baseReq);

      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while fetching accounts detail.',
      });
    });



    it('should return error if user has only one social data and account_setup is not 1', async () => {
      userRepositoryMock.getUserByEmail.mockResolvedValue({
        user_social_data: [{ type: 'google' }],
        account_setup: 0,
      });

      const result = await service.removeLinkedAccounts(baseReq);

      expect(result).toEqual({
        type: 'error',
        msg: 'Unable to delink account.',
      });
    });

    it('should still allow removal if account_setup is 1 even with 1 linked account', async () => {
      userRepositoryMock.getUserByEmail.mockResolvedValue({
        user_social_data: [{ type: 'google' }],
        account_setup: 1,
      });

      socialLoginHelperMock.updateUserSocialAccountDetails.mockResolvedValue({ type: 'success' });

      const result = await service.removeLinkedAccounts(baseReq);

      expect(result).toEqual({
        type: 'success',
        msg: 'Details stored successfully',
      });
    });

    it('should detect redirect responseType if redirect_url is zendesk uri', async () => {
      const zendeskReq = {
        ...baseReq,
        redirect_url: 'https://support.zendesk.com',
      };

      jest.spyOn(Utility, 'checkZendUri').mockReturnValue(true);
      socialLoginHelperMock.updateUserSocialAccountDetails.mockResolvedValue({ type: 'success' });

      const result = await service.removeLinkedAccounts(zendeskReq);

      expect(result).toEqual({
        type: 'success',
        msg: 'Details stored successfully',
      });
      expect(Utility.checkZendUri).toHaveBeenCalledWith(zendeskReq.redirect_url);
    });

    it('should throw error if getUserByEmail throws', async () => {
      userRepositoryMock.getUserByEmail.mockRejectedValue(new Error('DB failed'));

      await expect(service.removeLinkedAccounts(baseReq)).rejects.toThrow('DB failed');
    });
  });

  describe('getUserRoles', () => {
    let roleRepositoryMock: any;

    beforeEach(() => {
      roleRepositoryMock = {
        getRoles: jest.fn(),
        findAll: jest.fn(),
      };

      helperServiceMock.get = jest.fn((token) => {
        if (token === RoleRepository) return roleRepositoryMock;
        return null;
      });
    });

    it('should return paginated roles when page and pageSize are provided', async () => {
      const mockResult = [{ roleName: 'admin' }];
      const pageData = { page: 2, pageSize: 10 };

      roleRepositoryMock.getRoles.mockResolvedValue(mockResult);

      const result = await service.getUserRoles(pageData);

      expect(roleRepositoryMock.getRoles).toHaveBeenCalledWith({
        pageSize: 10,
        skipCount: 10,
        filter: {},
      });
      expect(result).toEqual(mockResult);
    });

    it('should apply prefix filter if provided', async () => {
      const mockResult = [{ roleName: 'authenticated user' }];
      const pageData = { prefix: 'auth', page: 1, pageSize: 5 };

      roleRepositoryMock.getRoles.mockResolvedValue(mockResult);

      const result = await service.getUserRoles(pageData);

      expect(roleRepositoryMock.getRoles).toHaveBeenCalledWith({
        pageSize: 5,
        skipCount: 0,
        filter: {
          roleName: { $regex: '^auth', $options: 'i' },
        },
      });
      expect(result).toEqual(mockResult);
    });

    it('should return all roles when pageData is not provided', async () => {
      const mockResult = [{ roleName: 'admin' }, { roleName: 'user' }];

      roleRepositoryMock.findAll.mockResolvedValue(mockResult);

      const result = await service.getUserRoles();

      expect(roleRepositoryMock.findAll).toHaveBeenCalledWith({});
      expect(result).toEqual(mockResult);
    });

    it('should return all roles when pagination is not provided but prefix is', async () => {
      const mockResult = [{ roleName: 'authenticated user' }];
      const pageData = { prefix: 'auth' };

      roleRepositoryMock.findAll.mockResolvedValue(mockResult);

      const result = await service.getUserRoles(pageData);

      expect(roleRepositoryMock.findAll).toHaveBeenCalledWith({
        roleName: { $regex: '^auth', $options: 'i' },
      });
      expect(result).toEqual(mockResult);
    });

    it('should throw error and log when repository throws exception', async () => {
      const error = new Error('DB failure');
      roleRepositoryMock.findAll.mockRejectedValue(error);

      await expect(service.getUserRoles()).rejects.toThrow('DB failure');
    });

    it('should throw error and log when repository throws exception', async () => {
      const error = new Error('DB failure');

      // ✅ Proper spy: covers both static and instance Logger.error
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation();

      roleRepositoryMock.findAll.mockRejectedValue(error);

      await expect(service.getUserRoles()).rejects.toThrow('DB failure');

      expect(loggerSpy).toHaveBeenCalledWith(
        'getUserRoles',
        expect.objectContaining({
          METHOD: expect.any(String),
          MESSAGE: 'DB failure',
          REQUEST: undefined,
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        })
      );
    });

  });

  describe('assignUserRole', () => {
    const reqBody = { uid: 123, rid: '2' };

    const role = { rid: '2', roleName: 'authenticated user' };
    const userWithoutRole = { uid: 123, roles: [] };
    const userWithRole = { uid: 123, roles: [{ rid: '2', roleName: 'authenticated user' }] };

    const roleRepositoryMock = {
      findOne: jest.fn(),
    };

    const userRepositoryMock = {
      findByUID: jest.fn(),
      findOneAndUpdate: jest.fn(),
    };

    const userHelperMock = {
      syncCloud6SentinelUserRole: jest.fn(),
      syncUserDataWithMySQLDrupal: jest.fn(),
    };

    beforeEach(() => {
      helperServiceMock.get = jest.fn((token) => {
        if (token === RoleRepository) return roleRepositoryMock;
        if (token === UserRepository) return userRepositoryMock;
        return null;
      });

      helperServiceMock.getHelper = jest.fn((token) => {
        if (token === 'UserHelper') return userHelperMock;
        return null;
      });

      configServiceMock.get = jest.fn((key) => {
        if (key === 'enableDrupalSync') return true;
        return '';
      });

      jest.clearAllMocks();
    });

    it('should assign role and sync with sentinel and Drupal if not already assigned', async () => {
      roleRepositoryMock.findOne.mockResolvedValue(role);
      userRepositoryMock.findByUID.mockResolvedValue({ ...userWithoutRole });
      userRepositoryMock.findOneAndUpdate.mockResolvedValue({});
      userHelperMock.syncCloud6SentinelUserRole.mockResolvedValue(true);
      userHelperMock.syncUserDataWithMySQLDrupal.mockResolvedValue(true);

      const result = await service.assignUserRole(reqBody);

      expect(result.roles).toEqual({ '2': 'authenticated user' });
      expect(userHelperMock.syncCloud6SentinelUserRole).toHaveBeenCalledWith([
        { uid: 123, rid: '2' },
      ]);
      expect(userHelperMock.syncUserDataWithMySQLDrupal).toHaveBeenCalledWith({
        uid: 123,
        roles: { '2': 'authenticated user' },
      });
    });

    it('should return early if role is already assigned', async () => {
      roleRepositoryMock.findOne.mockResolvedValue(role);
      userRepositoryMock.findByUID.mockResolvedValue({ ...userWithRole });

      const result = await service.assignUserRole(reqBody);

      expect(result.roles).toEqual({ '2': 'authenticated user' });
      expect(userHelperMock.syncCloud6SentinelUserRole).not.toHaveBeenCalled();
      expect(userHelperMock.syncUserDataWithMySQLDrupal).not.toHaveBeenCalled();
    });

    it('should return only mapped roles if role is not found', async () => {
      roleRepositoryMock.findOne.mockResolvedValue(null);
      userRepositoryMock.findByUID.mockResolvedValue({ ...userWithRole });

      const result = await service.assignUserRole(reqBody);

      expect(result.roles).toEqual({ '2': 'authenticated user' });
      expect(userHelperMock.syncCloud6SentinelUserRole).not.toHaveBeenCalled();
    });

    it('should skip drupal sync if config disabled', async () => {
      configServiceMock.get = jest.fn((key) => {
        if (key === 'enableDrupalSync') return false;
        return '';
      });

      roleRepositoryMock.findOne.mockResolvedValue(role);
      userRepositoryMock.findByUID.mockResolvedValue({ ...userWithoutRole });
      userRepositoryMock.findOneAndUpdate.mockResolvedValue({});
      userHelperMock.syncCloud6SentinelUserRole.mockResolvedValue(true);

      const result = await service.assignUserRole(reqBody);

      expect(result.roles).toEqual({ '2': 'authenticated user' });
      expect(userHelperMock.syncUserDataWithMySQLDrupal).not.toHaveBeenCalled();
    });

    it('should log and throw error on failure', async () => {
      const error = new Error('mock failure');
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation();

      roleRepositoryMock.findOne.mockRejectedValue(error);

      await expect(service.assignUserRole(reqBody)).rejects.toThrow('mock failure');

      expect(loggerSpy).toHaveBeenCalledWith(
        'assignUserRole',
        expect.objectContaining({
          METHOD: expect.stringContaining('@assignUserRole'),
          MESSAGE: 'mock failure',
          REQUEST: reqBody,
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        })
      );
    });
  });

  describe('revokeUserRole', () => {
    const reqBody = { uid: 101, rid: '2' };

    const initialRoles = [
      { rid: '1', roleName: 'admin' },
      { rid: '2', roleName: 'authenticated user' },
      { rid: '3', roleName: 'editor' },
    ];

    const filteredRoles = [
      { rid: '1', roleName: 'admin' },
      { rid: '3', roleName: 'editor' },
    ];

    const userDoc = {
      uid: 101,
      roles: [...initialRoles],
    };

    const userRepositoryMock = {
      findByUID: jest.fn(),
      findOneAndUpdate: jest.fn(),
    };

    const userHelperMock = {
      syncUserDataWithMySQLDrupal: jest.fn(),
    };

    beforeEach(() => {
      helperServiceMock.get = jest.fn((token) => {
        if (token === UserRepository) return userRepositoryMock;
        return null;
      });

      helperServiceMock.getHelper = jest.fn((token) => {
        if (token === 'UserHelper') return userHelperMock;
        return null;
      });

      configServiceMock.get = jest.fn((key) => {
        if (key === 'enableDrupalSync') return true;
        return '';
      });

      userRepositoryMock.findByUID.mockResolvedValue({ ...userDoc });
      userRepositoryMock.findOneAndUpdate.mockResolvedValue({});
      userHelperMock.syncUserDataWithMySQLDrupal.mockResolvedValue(true);

      jest.clearAllMocks();
    });

    it('should revoke a role, update DB, and sync with Drupal if enabled', async () => {
      const result = await service.revokeUserRole(reqBody);

      expect(userRepositoryMock.findByUID).toHaveBeenCalledWith(reqBody.uid);
      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalledWith(
        { uid: reqBody.uid },
        { roles: filteredRoles }
      );

      expect(userHelperMock.syncUserDataWithMySQLDrupal).toHaveBeenCalledWith({
        uid: reqBody.uid,
        roles: {
          '1': 'admin',
          '3': 'editor',
        },
      });

      expect(result.roles).toEqual({
        '1': 'admin',
        '3': 'editor',
      });
    });

    it('should revoke role and skip Drupal sync when disabled', async () => {
      configServiceMock.get = jest.fn((key) => {
        if (key === 'enableDrupalSync') return false;
        return '';
      });

      const result = await service.revokeUserRole(reqBody);

      expect(userHelperMock.syncUserDataWithMySQLDrupal).not.toHaveBeenCalled();
      expect(result.roles).toEqual({
        '1': 'admin',
        '3': 'editor',
      });
    });

    it('should log and rethrow error on failure', async () => {
      const error = new Error('mock failure');
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation();
      userRepositoryMock.findByUID.mockRejectedValue(error);

      await expect(service.revokeUserRole(reqBody)).rejects.toThrow('mock failure');

      expect(loggerSpy).toHaveBeenCalledWith(
        'revokeUserRole',
        expect.objectContaining({
          METHOD: expect.stringContaining('@revokeUserRole'),
          MESSAGE: error.message,
          REQUEST: reqBody,
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        })
      );
    });
  });

  describe('authenticate', () => {
    const baseDto: AuthenticationRequestDto = {
      client_id: 'client-123',
      user_login: '<EMAIL>',
      user_pwd: 'securePassword123',
      from_mobile: '1',
      redirect_url: 'https://example.com',
      device_type: 'mobile',
    };

    const user = {
      uid: 1001,
      email: '<EMAIL>',
      display_name: 'Test User',
    };

    const tokenDetail = {
      _t: 'mocked-token',
      lmsUrl: 'https://lms.example.com',
    };

    const authHelperMock = {
      authenticateUser: jest.fn(),
    };

    beforeEach(() => {
      helperServiceMock.getHelper = jest.fn((token) => {
        if (token === 'AuthHelper') return authHelperMock;
        return null;
      });

      configServiceMock.get = jest.fn((key) => {
        if (key === 'clientSecret') return 'mockSecret';
        return null;
      });

      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);
      jest.spyOn(Utility, 'isEmpty').mockImplementation((val) => !val);

      service.getUserToken = jest.fn().mockResolvedValue(tokenDetail);
      jest.clearAllMocks();
    });

    it('should return success response with token if authentication succeeds', async () => {
      authHelperMock.authenticateUser.mockResolvedValue(user);

      const result = await service.authenticate(baseDto);

      expect(authHelperMock.authenticateUser).toHaveBeenCalledWith({
        email: baseDto.user_login,
        password: baseDto.user_pwd,
      });

      expect(service.getUserToken).toHaveBeenCalledWith(user, expect.any(Object));

      expect(result).toEqual({
        type: 'success',
        _t: 'mocked-token',
        lmsUrl: 'https://lms.example.com',
      });
    });

    it('should return error response if authentication fails (user is null)', async () => {
      authHelperMock.authenticateUser.mockResolvedValue(null);

      const result = await service.authenticate(baseDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });

    it('should return proper message for UserNotFoundException', async () => {
      const err = new BadRequestException('UserNotFoundException');
      authHelperMock.authenticateUser.mockRejectedValue(err);

      const result = await service.authenticate(baseDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'No active account associated with this email address',
      });
    });

    it('should return proper message for InvalidCredentials', async () => {
      const err = new BadRequestException('InvalidCredentials');
      authHelperMock.authenticateUser.mockRejectedValue(err);

      const result = await service.authenticate(baseDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Invalid user credentials.',
      });
    });

    it('should return proper message for UnauthorizedException', async () => {
      const err = new UnauthorizedException('Access denied');
      authHelperMock.authenticateUser.mockRejectedValue(err);

      const result = await service.authenticate(baseDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Access denied',
      });
    });

    it('should return fallback message for unknown errors', async () => {
      const err = new Error('Some unknown failure');
      authHelperMock.authenticateUser.mockRejectedValue(err);

      const result = await service.authenticate(baseDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });
  });

  describe('updateProfile', () => {
    const updateDto = {
      uid: 1001,
      first_name: 'John',
      last_name: 'Doe',
    };

    const user = {
      uid: 1001,
      email: '<EMAIL>',
      status: 1,
    };

    const userRepositoryMock = {
      findByUID: jest.fn(),
      findOneAndUpdate: jest.fn(),
    };

    const userHelperMock = {
      updateCloud6SentinelByUidOrMail: jest.fn(),
      syncUserDataWithMySQLDrupal: jest.fn(),
    };

    const lrsHelperMock = {
      sendDataToKafka: jest.fn(),
    };

    beforeEach(() => {
      helperServiceMock.getHelper = jest.fn((token) => {
        if (token === UserRepository) return userRepositoryMock;
        if (token === 'UserHelper') return userHelperMock;
        if (token === 'lrsHelper') return lrsHelperMock;
        return null;
      });

      configServiceMock.get = jest.fn((key) => {
        if (key === 'profileTopic') return 'user-profile-topic';
        return null;
      });

      jest.clearAllMocks();
    });

    it('should update profile and return success response', async () => {
      const updateDto = {
        uid: 1001,
        first_name: 'John',
        last_name: 'Doe',
      };

      const user = {
        uid: 1001,
        email: '<EMAIL>',
        status: 1,
      };

      const updatedResult = true;

      userRepositoryMock.findByUID.mockResolvedValue(user);
      userRepositoryMock.findOneAndUpdate.mockResolvedValue(updatedResult);
      userHelperMock.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);

      const result = await service.updateProfile(updateDto);

      // ➤ Assert user lookup
      expect(userRepositoryMock.findByUID).toHaveBeenCalledWith(updateDto.uid);

      // ➤ Assert update with only relevant fields (no user_email/user_id here)
      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalledWith(
        { email: user.email },
        expect.objectContaining({
          first_name: 'John',
          last_name: 'Doe',
          display_name: 'John Doe',
        }),
      );

      // ➤ Assert Cloud6 update
      expect(userHelperMock.updateCloud6SentinelByUidOrMail).toHaveBeenCalledWith({
        uid: user.uid,
        first_name: 'John',
        last_name: 'Doe',
        display_name: 'John Doe',
      });

      // ➤ Assert LRS Kafka call with all parameters
      expect(lrsHelperMock.sendDataToKafka).toHaveBeenCalledWith('user-profile-topic', {
        first_name: 'John',
        last_name: 'Doe',
        display_name: 'John Doe',
        user_email: user.email,
        user_id: user.uid,
      });

      // ➤ Assert Drupal sync
      expect(userHelperMock.syncUserDataWithMySQLDrupal).toHaveBeenCalledWith({
        first_name: 'John',
        last_name: 'Doe',
        display_name: 'John Doe',
        user_email: user.email,
        user_id: user.uid,
      });

      // ➤ Assert final response
      expect(result).toEqual({
        type: 'success',
        msg: 'Your profile has been successfully updated.',
      });
    });

    it('should return error response if user is not found', async () => {
      userRepositoryMock.findByUID.mockResolvedValue(null);

      const result = await service.updateProfile(updateDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while updating profile. Please try again.',
      });
    });

    it('should return error response if update fails in DB', async () => {
      userRepositoryMock.findByUID.mockResolvedValue(user);
      userRepositoryMock.findOneAndUpdate.mockResolvedValue(null);
      userHelperMock.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);

      const result = await service.updateProfile(updateDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while updating profile. Please try again.',
      });
    });

    it('should return error response on unexpected error', async () => {
      userRepositoryMock.findByUID.mockRejectedValue(new Error('Unexpected DB error'));

      const result = await service.updateProfile(updateDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while updating profile. Please try again.',
      });
    });
  });

  describe('updateLinkedinStatus', () => {
    const requestData = {
      source: 'linkedin',
      user_id: 1001,
    };

    const userMock = {
      uid: 1001,
      email: '<EMAIL>',
    };

    it('should return success when linkedin status is updated successfully', async () => {
      userRepositoryMock.findByUID.mockResolvedValue(userMock);
      socialLoginHelperMock.updateUserSocialAccountDetails.mockResolvedValue({ type: 'success' });

      const result = await service.updateLinkedinStatus(requestData);

      expect(userRepositoryMock.findByUID).toHaveBeenCalledWith(requestData.user_id);
      expect(socialLoginHelperMock.updateUserSocialAccountDetails).toHaveBeenCalledWith(userMock, requestData.source);

      expect(result).toEqual({
        type: 'success',
        msg: 'Linkedin status updated successfully.',
      });
    });

    it('should return error response if updateUserSocialAccountDetails fails', async () => {
      userRepositoryMock.findByUID.mockResolvedValue(userMock);
      socialLoginHelperMock.updateUserSocialAccountDetails.mockResolvedValue({ type: 'fail' });

      const result = await service.updateLinkedinStatus(requestData);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred. Please try again.',
      });
    });

    it('should return error response if user is not found', async () => {
      userRepositoryMock.findByUID.mockResolvedValue(null);

      const result = await service.updateLinkedinStatus(requestData);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred. Please try again.',
      });
    });

    it('should return error response if updateUserSocialAccountDetails returns undefined', async () => {
      userRepositoryMock.findByUID.mockResolvedValue(userMock);
      socialLoginHelperMock.updateUserSocialAccountDetails.mockResolvedValue(undefined);

      let result;
      try {
        result = await service.updateLinkedinStatus(requestData);
      } catch (err) {
        result = undefined;
      }

      expect(result).toBeUndefined(); // actual function throws, doesn't return
    });

  });

  describe('getOriginalToken', () => {
    const validData = {
      redirect_url: 'https://example.com',
      token: 'dummy-token',
    };

    const encodedToken = 'encoded-dummy-token';

    it('should return success response with encoded token if valid token is provided', async () => {
      const authTokenHelperMock = {
        encodeOriginalToken: jest.fn().mockResolvedValue(encodedToken),
      };

      helperServiceMock.get = jest.fn((token) => {
        if (token === AuthTokenHelper) return authTokenHelperMock;
        return null;
      });

      const result = await service.getOriginalToken(validData);

      expect(helperServiceMock.get).toHaveBeenCalledWith(AuthTokenHelper);
      expect(authTokenHelperMock.encodeOriginalToken).toHaveBeenCalledWith(validData.token);

      expect(result).toEqual({
        type: 'success',
        msg: 'Original Token generated',
        _t: encodedToken,
      });
    });

    it('should return error response if token is not provided', async () => {
      const result = await service.getOriginalToken({
        redirect_url: 'https://example.com',
        token: '',
      });

      expect(result).toEqual({
        type: 'error',
        msg: 'Invalid Token sent.',
      });
    });

    it('should return default error response if exception is thrown', async () => {
      const authTokenHelperMock = {
        encodeOriginalToken: jest.fn().mockRejectedValue(new Error('Unexpected error')),
      };

      helperServiceMock.get = jest.fn((token) => {
        if (token === AuthTokenHelper) return authTokenHelperMock;
        return null;
      });

      const result = await service.getOriginalToken(validData);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });
  });

  describe('updateUserTimezone', () => {
    const mockRequest: UpdateUserTimezoneDto = {
      uid: '1001',
      country: 'IN',
    };

    const mockUser = {
      uid: 1001,
      name: 'John Doe',
      timezone: 'Asia/Kolkata',
    };

    it('should return user object when timezone is updated successfully', async () => {
      userMgmtUtilityHelperMock.updateUserTimezone.mockResolvedValue(mockUser);

      const result = await service.updateUserTimezone(mockRequest);

      expect(helperServiceMock.getHelper).toHaveBeenCalledWith('UserMgmtUtilityHelper');
      expect(userMgmtUtilityHelperMock.updateUserTimezone).toHaveBeenCalledWith(mockRequest);
      expect(result).toEqual(mockUser);
    });

    it('should throw error and log if updateUserTimezone fails', async () => {
      const error = new Error('DB connection failed');
      userMgmtUtilityHelperMock.updateUserTimezone.mockRejectedValue(error);

      await expect(service.updateUserTimezone(mockRequest)).rejects.toThrow('DB connection failed');
    });
  });

  describe('getTaxonomy', () => {
    const mockRequest = {
      category: 'industry,domain',
      name: 'Technology',
    };

    const mockTaxonomyResult = [{
      industry: [{ id: 1, name: 'Technology', category: 'industry' }],
      domain: [{ id: 2, name: 'Technology', category: 'domain' }],
    }];

    const taxonomyRepoMock = {
      aggregate: jest.fn(),
    };

    beforeEach(() => {
      helperServiceMock.get = jest.fn((token) => {
        if (token === TaxonomyRepository) return taxonomyRepoMock;
        return null;
      });
    });

    it('should return success with taxonomy data', async () => {
      taxonomyRepoMock.aggregate.mockResolvedValue(mockTaxonomyResult);

      const result = await service.getTaxonomy(mockRequest);

      expect(helperServiceMock.get).toHaveBeenCalledWith(TaxonomyRepository);
      expect(taxonomyRepoMock.aggregate).toHaveBeenCalledWith(expect.any(Array));
      expect(result).toEqual({
        status: 'success',
        data: mockTaxonomyResult[0],
      });
    });

    it('should return success with empty object if result is empty', async () => {
      taxonomyRepoMock.aggregate.mockResolvedValue([]);

      const result = await service.getTaxonomy(mockRequest);

      expect(result).toEqual({
        status: 'success',
        data: {},
      });
    });

    it('should return error response on exception', async () => {
      taxonomyRepoMock.aggregate.mockRejectedValue(new Error('DB error'));

      const result = await service.getTaxonomy(mockRequest);

      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while updating profile. Please try again.',
      });
    });
  });

  describe('getUserToken', () => {
    it('should return token response if idToken is returned', async () => {
      const user = { email: '<EMAIL>', uid: 1, country_code: 'IN', user_groups: [123] };

      userMgmtUtilityHelperMock.updateUserTimezone.mockResolvedValue(true);
      authHelperMock.getTokenByEmail.mockResolvedValue({ idToken: 'abc123', userData: {} });
      jest.spyOn(service, 'getDomainBasedOnUserGroupId').mockResolvedValue('http://lms.com');

      const result = await service.getUserToken(user, {
        client_id: 'cid',
        from_mobile_flag: 0,
        redirect_url: '',
        lrs_object_type: 'user',
        app_type: 'web',
        client_key: 'key',
      });

      expect(result).toEqual({
        type: 'success',
        msg: 'user logged in successfully',
        _t: 'abc123',
        lmsUrl: 'http://lms.com',
      });
    });

    it('should return error if idToken is missing', async () => {
      authHelperMock.getTokenByEmail.mockResolvedValue({});

      const result = await service.getUserToken({ email: 'a' }, { client_id: 'cid' });
      expect(result).toEqual({ type: 'error', msg: 'Some error occurred while user login.' });
    });

    it('should catch error and return default error', async () => {
      authHelperMock.getTokenByEmail.mockRejectedValue(new Error('Failure'));

      const result = await service.getUserToken({ email: 'a' }, { client_id: 'cid' });
      expect(result).toEqual({ type: 'error', msg: 'Some error occurred while user login.' });
    });
  });

  describe('getDomainBasedOnUserGroupId', () => {
    const defaultDomain = 'https://default.lms.com';
    const defaultLrsAppId = 99;
    const groupDomain = 'https://enterprise-domain.com';

    const enterpriseServiceMock = {
      getGroupDomainByGid: jest.fn(),
    };

    beforeEach(() => {
      helperServiceMock.get = jest.fn((token) => {
        if (token === EnterpriseService) return enterpriseServiceMock;
        return null;
      });

      configServiceMock.get = jest.fn((key) => {
        switch (key) {
          case 'lmsSiteUrl': return defaultDomain;
          case 'defaultLrsApplicationId': return defaultLrsAppId;
          default: return undefined;
        }
      });
    });

    it('should return default domain if gid is not present', async () => {
      const userData = {};

      const result = await service.getDomainBasedOnUserGroupId(userData);

      expect(result).toBe(defaultDomain);
    });

    it('should return default domain if gid contains only defaultLrsApplicationId', async () => {
      const userData = { gid: `${defaultLrsAppId}` };

      const result = await service.getDomainBasedOnUserGroupId(userData);

      expect(result).toBe(defaultDomain);
    });

    it('should remove defaultLrsApplicationId and return enterprise domain if found', async () => {
      const userData = { gid: '1,99,3' };
      enterpriseServiceMock.getGroupDomainByGid.mockResolvedValue({
        data: [{ lmsSiteUrl: groupDomain }],
      });

      const result = await service.getDomainBasedOnUserGroupId(userData);

      expect(enterpriseServiceMock.getGroupDomainByGid).toHaveBeenCalledWith({ gid: 99 });
      expect(result).toBe(groupDomain);
    });

    it('should return default domain if enterprise response is missing lmsSiteUrl', async () => {
      const userData = { gid: `5,${defaultLrsAppId}` };
      enterpriseServiceMock.getGroupDomainByGid.mockResolvedValue({
        data: [{}],
      });

      const result = await service.getDomainBasedOnUserGroupId(userData);

      expect(result).toBe(defaultDomain);
    });

    it('should return default domain if enterprise response is undefined or invalid', async () => {
      const userData = { gid: `5,6` };
      enterpriseServiceMock.getGroupDomainByGid.mockResolvedValue(undefined);

      const result = await service.getDomainBasedOnUserGroupId(userData);

      expect(result).toBe(defaultDomain);
    });
  });

  describe('checkValidation', () => {
    it('should return default error response with msg if type is not success', () => {
      const input = { type: 'fail', msg: 'Something went wrong' };
      const result = service.checkValidation(input);

      expect(result).toEqual({
        type: 'error',
        msg: 'Something went wrong',
      });
    });

    it('should return errorCode 101 if user and social accounts are not present', () => {
      const input = {
        type: 'success',
        msg: 'Base',
        socialAccountStatus: false,
        userAccountStatus: false,
      };

      const result = service.checkValidation(input);

      expect(result).toEqual({
        type: 'error',
        msg: 'User does not exist.',
        userAccountStatus: false,
        socialAccountStatus: false,
        accountSetupStatus: undefined,
        errorCode: 101,
      });
    });

    it('should return errorCode 102 if user exists but social does not', () => {
      const input = {
        type: 'success',
        msg: 'Base',
        socialAccountStatus: false,
        userAccountStatus: true,
      };

      const result = service.checkValidation(input);

      expect(result).toEqual({
        type: 'error',
        msg: 'Email is already exist with different signin method.',
        userAccountStatus: true,
        socialAccountStatus: false,
        accountSetupStatus: undefined,
        errorCode: 102,
      });
    });

    it('should return errorCode 103 if social account exists', () => {
      const input = {
        type: 'success',
        msg: 'Base',
        socialAccountStatus: true,
        userAccountStatus: false,
        accountSetupStatus: 'complete',
      };

      const result = service.checkValidation(input);

      expect(result).toEqual({
        type: 'error',
        msg: 'Same method exist.',
        userAccountStatus: false,
        socialAccountStatus: true,
        accountSetupStatus: 'complete',
        errorCode: 103,
      });
    });

    it('should pass through statuses and setupStatus even if no known case matched', () => {
      const input = {
        type: 'success',
        msg: 'Base message',
        socialAccountStatus: true,
        userAccountStatus: true,
        accountSetupStatus: 'done',
      };

      const result = service.checkValidation(input);

      expect(result).toEqual({
        type: 'error',
        msg: 'Same method exist.',
        userAccountStatus: true,
        socialAccountStatus: true,
        accountSetupStatus: 'done',
        errorCode: 103,
      });
    });
  });

  describe('getLmsSettingsFromGid', () => {
    const mockGroupId = 123;

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return LMS settings successfully when data is present', async () => {
      const enterpriseSettingsResponse = { data: { data: [{ setting: 'mock' }] } };
      const lmsSettings = { language: 'en', theme: 'dark' };

      cloud6ServiceMock.getLmsEnterpriseSettings.mockResolvedValue(enterpriseSettingsResponse);
      userHelperMock.lmsSettingsCacheData.mockResolvedValue(lmsSettings);

      const result = await service.getLmsSettingsFromGid(mockGroupId);

      expect(cloud6ServiceMock.getLmsEnterpriseSettings).toHaveBeenCalledWith({ group_id: mockGroupId });
      expect(userHelperMock.lmsSettingsCacheData).toHaveBeenCalledWith(enterpriseSettingsResponse.data.data);
      expect(result).toEqual({
        status: true,
        msg: 'success',
        data: lmsSettings,
      });
    });

    it('should return error response when LMS settings is empty', async () => {
      const enterpriseSettingsResponse = { data: { data: [] } };

      cloud6ServiceMock.getLmsEnterpriseSettings.mockResolvedValue(enterpriseSettingsResponse);
      userHelperMock.lmsSettingsCacheData.mockResolvedValue(null);

      const result = await service.getLmsSettingsFromGid(mockGroupId);

      expect(cloud6ServiceMock.getLmsEnterpriseSettings).toHaveBeenCalledWith({ group_id: mockGroupId });
      expect(userHelperMock.lmsSettingsCacheData).toHaveBeenCalledWith(enterpriseSettingsResponse.data.data);
      expect(result).toEqual({
        status: false,
        msg: 'No data found',
        data: '',
      });
    });
  });

  describe('processGoogleOneTapResponse', () => {
    const mockRes: any = {
      cookie: jest.fn(),
      redirect: jest.fn(),
    };

    const reqData = {
      credential: 'mock-token',
      is_frs_page: 'false',
      calling_api_from: '/mock-api',
    };

    const redirectUrl = 'https://example.com/mock-api';

    const user = {
      uid: 101,
      email: '<EMAIL>',
    };

    const authResponsePayload = {
      status: true,
      data: {
        data: {
          email: user.email,
          given_name: 'John',
          family_name: 'Doe',
        },
      },
    };

    beforeEach(() => {
      jest.clearAllMocks();
      configServiceMock.get = jest.fn((key) => {
        const map = {
          sheldonSiteUrl: 'https://example.com',
          clientKey: 'client-key',
          ssoCookie: 'sso-cookie',
          ssoClientKey: 'sso-client-id',
        };
        return map[key];
      });
    });

    it('should login existing user and return token redirect', async () => {
      socialLoginServiceMock.fetchSocialUserProfileDetailsFromToken.mockResolvedValue(authResponsePayload);
      userRepositoryMock.getUserByEmail.mockResolvedValue(user);
      tokenServiceMock.generateSessionTokens.mockResolvedValue({ idToken: 'mock-id-token' });

      userHelperMock.updateUserLoginTime.mockReturnValue(undefined);
      cookieHelperMock.setCookie.mockReturnValue(undefined);
      lrsHelperMock.sendDataToLrs.mockReturnValue(undefined);
      authHelperMock.getSignupRedirect = jest.fn();

      const result = await service.processGoogleOneTapResponse(reqData, mockRes);

      expect(socialLoginServiceMock.fetchSocialUserProfileDetailsFromToken).toHaveBeenCalledWith({
        token: 'mock-token',
        type: 'google',
      });
      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith(user.email);
      expect(tokenServiceMock.generateSessionTokens).toHaveBeenCalledWith(user, 'client-key');
      expect(cookieHelperMock.setCookie).toHaveBeenCalledWith(mockRes, 'sso-cookie', 'mock-id-token');
      expect(result).toBeDefined();
    });

    it('should register new user and return token redirect', async () => {
      const newUser = { uid: 2002, email: '<EMAIL>' };

      socialLoginServiceMock.fetchSocialUserProfileDetailsFromToken.mockResolvedValue({
        status: true,
        data: {
          data: {
            email: newUser.email,
            given_name: 'Jane',
            family_name: 'Smith',
          },
        },
      });

      userRepositoryMock.getUserByEmail.mockResolvedValue(null);
      tokenServiceMock.generateSessionTokens.mockResolvedValue({ idToken: 'new-id-token' });

      const mockUser = { uid: 2002, email: newUser.email };
      const handleNewUserSpy = jest
        .spyOn(service as any, 'handleNewUser')
        .mockResolvedValue(mockUser);

      cookieHelperMock.setCookie.mockReturnValue(undefined);
      authHelperMock.getSignupRedirect = jest.fn();
      lrsHelperMock.sendDataToLrs.mockReturnValue(undefined);

      const result = await service.processGoogleOneTapResponse(reqData, mockRes);

      expect(handleNewUserSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          emailAddress: newUser.email,
        }),
        mockRes,
        false,
        expect.any(Object)
      );
      expect(result).toBeDefined();
    });

    it('should return redirect URL if social login fails', async () => {
      socialLoginServiceMock.fetchSocialUserProfileDetailsFromToken.mockResolvedValue({ status: false });

      const result = await service.processGoogleOneTapResponse(reqData, mockRes);

      expect(result).toEqual({ tokenRedirectUrl: redirectUrl });
    });

    it('should return redirect URL on exception', async () => {
      socialLoginServiceMock.fetchSocialUserProfileDetailsFromToken.mockRejectedValue(new Error('network error'));

      const result = await service.processGoogleOneTapResponse(reqData, mockRes);

      expect(result).toEqual({ tokenRedirectUrl: redirectUrl });
    });
  });

  describe('handleNewUser', () => {
    const mockUserData = {
      emailAddress: '<EMAIL>',
      status: 'anf',
      type: 'google',
      referer: 'login',
      firstName: 'John',
      lastName: 'Doe',
    };

    const mockToken = 'signed-jwt-token';
    const mockUser = { uid: 1001, email: '<EMAIL>' };
    const mockUtm = 'utm_source|utm_medium|utm_campaign';

    const mockRes = {
      cookie: {
        sl_su_utmz: mockUtm,
      },
    };

    const cookieHelperMock = {
      setCookie: jest.fn(),
    };

    const authTokenHelperMock = {
      createSignedToken: jest.fn().mockResolvedValue(mockToken),
    };

    const socialHelperMock = {
      registerFrsUser: jest.fn().mockResolvedValue({
        setCookie: true,
        cookieName: 'ssotoken',
        cookieValue: 'abc123',
        user: mockUser,
        email: mockUserData.emailAddress,
      }),
    };

    const userCommunityHelperMock = {
      updateUserSignupUtm: jest.fn(),
    };

    beforeEach(() => {
      jest.clearAllMocks();

      helperServiceMock.getHelper = jest.fn((token) => {
        if (token === 'UsermgmtCommunityHelper') return Promise.resolve(userCommunityHelperMock);
        return null;
      });

      configServiceMock.get = jest.fn((key) => {
        if (key === 'socialLinkToken') return 'social-token-key';
        if (key === 'defaultFreemiumSignupUtm') return mockUtm;
        return null;
      });
    });

    it('should create token, set cookies, update FRS UTM and return user object', async () => {
      const helperBundle = {
        authTokenHelper: authTokenHelperMock,
        cookieHelper: cookieHelperMock,
        socialHelper: socialHelperMock,
      };

      const result = await (service as any).handleNewUser(mockUserData, mockRes, true, helperBundle);

      expect(authTokenHelperMock.createSignedToken).toHaveBeenCalledWith(mockUserData);
      expect(cookieHelperMock.setCookie).toHaveBeenCalledWith(
        mockRes,
        'social-token-key',
        mockToken,
        { expires: 3600 * 1000 }
      );
      expect(socialHelperMock.registerFrsUser).toHaveBeenCalledWith(mockUserData, mockRes.cookie);
      expect(userCommunityHelperMock.updateUserSignupUtm).toHaveBeenCalledWith({
        email: mockUser.email,
        utm_source: expect.anything(), // parsed UTM value
      });
      expect(cookieHelperMock.setCookie).toHaveBeenCalledWith(
        mockRes,
        'ssotoken',
        'abc123'
      );

      expect(result).toEqual(mockUser);
    });

    it('should skip utm update if frsSource is false', async () => {
      const helperBundle = {
        authTokenHelper: authTokenHelperMock,
        cookieHelper: cookieHelperMock,
        socialHelper: socialHelperMock,
      };

      await (service as any).handleNewUser(mockUserData, mockRes, false, helperBundle);

      expect(userCommunityHelperMock.updateUserSignupUtm).not.toHaveBeenCalled();
    });

    it('should not call setCookie for user if userAccount.setCookie is false', async () => {
      socialHelperMock.registerFrsUser.mockResolvedValueOnce({
        setCookie: false,
        user: mockUser,
      });

      const helperBundle = {
        authTokenHelper: authTokenHelperMock,
        cookieHelper: cookieHelperMock,
        socialHelper: socialHelperMock,
      };

      const result = await (service as any).handleNewUser(mockUserData, mockRes, false, helperBundle);

      expect(cookieHelperMock.setCookie).toHaveBeenCalledTimes(1); // only social token
      expect(result).toEqual(mockUser);
    });
  });

  describe('handleTokenRedirect', () => {
    const userResponse = { uid: 1001, email: '<EMAIL>' };
    const mockRes: any = {
      cookie: {
        sl_su_utmz: 'utm_source|utm_medium|utm_campaign',
      },
    };

    const cookieHelperMock = {
      setCookie: jest.fn(),
    };

    const authHelperMock = {
      generateRedirectLinkToManageRedirect: jest.fn().mockResolvedValue('https://app.example.com/redirect'),
    };

    beforeEach(() => {
      jest.clearAllMocks();

      configServiceMock.get = jest.fn((key) => {
        switch (key) {
          case 'sheldonSiteUrl':
            return 'https://sheldon.example.com';
          case 'frsOneTapRedirectCookie':
            return 'frs_cookie';
          case 'slUuUtmz':
            return 'utm_cookie';
          case 'defaultFreemiumSignupUtm':
            return 'utm_source|utm_medium|utm_campaign';
          case 'skillUpOneTapRedirectCookie':
            return 'skillup_cookie';
          default:
            return '';
        }
      });

      jest.spyOn(Utility, 'getFrsUtm').mockReturnValue('utmObjectMock');
    });

    it('should handle FRS source flow and set FRS cookies correctly', async () => {
      const callingApiFrom = '/dashboard';
      const helper = { cookieHelper: cookieHelperMock, authHelper: authHelperMock };

      const result = await (service as any).handleTokenRedirect(userResponse, mockRes, callingApiFrom, true, helper);

      expect(cookieHelperMock.setCookie).toHaveBeenCalledWith(
        mockRes,
        'frs_cookie',
        'https://sheldon.example.com/dashboard',
        { expires: 3600 * 1000 },
      );
      expect(cookieHelperMock.setCookie).toHaveBeenCalledWith(
        mockRes,
        'utm_cookie',
        'utmObjectMock',
        { expires: 3600 * 1000 },
      );

      expect(result).toEqual({ tokenRedirectUrl: 'https://sheldon.example.com/dashboard' });
    });

    it('should handle non-FRS flow and call generateRedirectLinkToManageRedirect', async () => {
      const callingApiFrom = '/login';
      const helper = { cookieHelper: cookieHelperMock, authHelper: authHelperMock };

      const result = await (service as any).handleTokenRedirect(userResponse, mockRes, callingApiFrom, false, helper);

      expect(cookieHelperMock.setCookie).toHaveBeenCalledWith(
        mockRes,
        'skillup_cookie',
        '/login',
        { expires: 3600 * 1000 },
      );

      expect(authHelperMock.generateRedirectLinkToManageRedirect).toHaveBeenCalledWith(
        userResponse,
        'login',
        'email',
      );

      expect(result).toEqual({ tokenRedirectUrl: 'https://app.example.com/redirect' });
    });

    it('should default to skillup-free-online-courses in FRS flow when callingApiFrom is missing', async () => {
      const helper = { cookieHelper: cookieHelperMock, authHelper: authHelperMock };

      const result = await (service as any).handleTokenRedirect(userResponse, mockRes, '', true, helper);

      expect(cookieHelperMock.setCookie).toHaveBeenCalledWith(
        mockRes,
        'frs_cookie',
        'https://sheldon.example.com/skillup-free-online-courses',
        { expires: 3600 * 1000 },
      );

      expect(result).toEqual({
        tokenRedirectUrl: 'https://sheldon.example.com/skillup-free-online-courses',
      });
    });

    it('should fallback to defaultFreemiumSignupUtm if sl_su_utmz cookie is missing', async () => {
      const mockResNoCookie = {
        cookie: {}, // missing sl_su_utmz
      };
      const helper = { cookieHelper: cookieHelperMock, authHelper: authHelperMock };

      const result = await (service as any).handleTokenRedirect(userResponse, mockResNoCookie, '/abc', true, helper);

      expect(configServiceMock.get).toHaveBeenCalledWith('defaultFreemiumSignupUtm');
      expect(Utility.getFrsUtm).toHaveBeenCalledWith(['utm_source', 'utm_medium', 'utm_campaign']);

      expect(cookieHelperMock.setCookie).toHaveBeenCalledWith(
        mockResNoCookie,
        'utm_cookie',
        'utmObjectMock',
        { expires: 3600 * 1000 },
      );

      expect(result).toEqual({
        tokenRedirectUrl: 'https://sheldon.example.com/abc',
      });
    });


    it('should default redirect to skillup URL if callingApiFrom is missing', async () => {
      const helper = { cookieHelper: cookieHelperMock, authHelper: authHelperMock };

      const result = await (service as any).handleTokenRedirect(userResponse, mockRes, '', false, helper);

      expect(cookieHelperMock.setCookie).toHaveBeenCalledWith(
        mockRes,
        'skillup_cookie',
        'https://sheldon.example.com/skillup-free-online-courses',
        { expires: 3600 * 1000 },
      );

      expect(result).toEqual({ tokenRedirectUrl: 'https://app.example.com/redirect' });
    });
  });

  describe('validateHashToken', () => {
    const mockTokenHelper = {
      learnerAuthenticationRehash: jest.fn(),
      teamLearnerAuthenticationRehash: jest.fn(),
    };

    beforeEach(() => {
      helperServiceMock.get = jest.fn().mockImplementation((token) => {
        if (token === AuthTokenHelper) return mockTokenHelper;
        return null;
      });

      configServiceMock.get = jest.fn((key) => {
        switch (key) {
          case 'learnerActivationRequest':
            return '101';
          case 'teamLearnerActivationRequest':
            return '202';
          case 'drupal_hash_salt':
            return 'secret-salt';
          default:
            return undefined;
        }
      });
    });

    it('should not throw error if learner hash token matches', async () => {
      mockTokenHelper.learnerAuthenticationRehash.mockReturnValue('valid-hash');

      await expect(
        service.validateHashToken('101', 'aff1', '123456', '<EMAIL>', 'valid-hash')
      ).resolves.not.toThrow();
    });

    it('should not throw error if team hash token matches', async () => {
      mockTokenHelper.teamLearnerAuthenticationRehash.mockReturnValue('valid-hash');

      await expect(
        service.validateHashToken('202', 'aff1', '123456', '<EMAIL>', 'valid-hash', '321')
      ).resolves.not.toThrow();
    });

    it('should throw error if hash token does not match', async () => {
      mockTokenHelper.learnerAuthenticationRehash.mockReturnValue('invalid-hash');

      await expect(
        service.validateHashToken('101', 'aff1', '123456', '<EMAIL>', 'wrong-hash')
      ).rejects.toThrow('Invalid hash token');
    });
  });

  describe('authenticateSaml', () => {
    const user = { email: '<EMAIL>', status: 1 };
    const mockTokenResponse = {
      type: 'success',
      msg: 'Authenticated successfully',
      _t: 'token',
      lmsUrl: 'https://lms.example.com',
    };

    const requestDto: AuthenticateSaml = {
      client_id: 'client123',
      user_login: '<EMAIL>',
      redirect_url: 'https://redirect.com',
      additional_info: JSON.stringify({
        request_type: '101',
        affiliate_id: 'aff1',
        timestamp: '123456',
        hash_token: 'valid-hash',
        team_id: 'team-1',
      }),
    };

    beforeEach(() => {
      userRepositoryMock.getUserByEmail.mockResolvedValue(user);
      jest.spyOn(service, 'validateHashToken').mockResolvedValue(undefined);
      jest.spyOn(service, 'getUserToken').mockResolvedValue(mockTokenResponse);

      configServiceMock.get = jest.fn((key) => {
        switch (key) {
          case 'clientSecret':
            return 'secret';
          case 'drupal_hash_salt':
            return 'salt';
          case 'learnerActivationRequest':
            return '101';
          case 'teamLearnerActivationRequest':
            return '202';
          default:
            return '';
        }
      });
    });

    it('should return success response with token', async () => {
      const result = await service.authenticateSaml(requestDto);

      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(service.validateHashToken).toHaveBeenCalled();
      expect(service.getUserToken).toHaveBeenCalledWith(user, expect.any(Object));
      expect(result).toEqual({
        type: 'success',
        _t: 'token',
        lmsUrl: 'https://lms.example.com',
      });
    });

    it('should return error if additional_info is missing', async () => {
      const result = await service.authenticateSaml({
        ...requestDto,
        additional_info: undefined,
      });

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });

    it('should return error if user not found', async () => {
      userRepositoryMock.getUserByEmail.mockResolvedValue(null);

      const result = await service.authenticateSaml(requestDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });

    it('should return error if additional_info is an empty string', async () => {
      const result = await service.authenticateSaml({
        ...requestDto,
        additional_info: '',
      });

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });

    it('should default to empty string when redirect_url is not provided', async () => {
      const modifiedRequestDto = {
        ...requestDto,
        redirect_url: undefined,
      };

      jest.spyOn(service, 'getUserToken').mockResolvedValue(mockTokenResponse);

      const result = await service.authenticateSaml(modifiedRequestDto);

      expect(result).toEqual({
        type: 'success',
        _t: 'token',
        lmsUrl: 'https://lms.example.com',
      });
    });

    it('should parse additional_info when present as a string', async () => {
      const dto = {
        ...requestDto,
        additional_info: JSON.stringify({ hash_token: 'token123' }),
      };

      await service.authenticateSaml(dto);

      const validateHashTokenCalls = (service.validateHashToken as jest.Mock).mock.calls;

      expect(validateHashTokenCalls[0][4]).toBe('token123'); // 5th argument is hash_token
    });


    it('should set response_type as redirect and from_mobile_flag as 1 when applicable', async () => {
      const dto = {
        ...requestDto,
        redirect_url: 'https://redirect.com',
        from_mobile: '1',
      };

      const tokenSpy = jest.spyOn(service, 'getUserToken');
      await service.authenticateSaml(dto);

      const additionalInfoPassed = tokenSpy.mock.calls[0][1]; // second arg of getUserToken

      expect(additionalInfoPassed).toMatchObject({
        response_type: 'redirect',
        from_mobile_flag: 1,
      });
    });

    it('should return error response if getUserToken throws generic error', async () => {
      jest.spyOn(service, 'getUserToken').mockImplementation(() => {
        throw new Error('Some error occurred while generating token.');
      });

      const result = await service.authenticateSaml(requestDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });

    it('should return user not found message if BadRequestException is thrown with message UserNotFoundException', async () => {
      userRepositoryMock.getUserByEmail.mockImplementation(() => {
        throw new BadRequestException('UserNotFoundException');
      });

      const result = await service.authenticateSaml(requestDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'No active account associated with this email address',
      });
    });

    it('should return unauthorized message if UnauthorizedException is thrown', async () => {
      userRepositoryMock.getUserByEmail.mockImplementation(() => {
        throw new UnauthorizedException('Unauthorized access');
      });

      const result = await service.authenticateSaml(requestDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Unauthorized access',
      });
    });

    it('should return error if user status is not 1', async () => {
      userRepositoryMock.getUserByEmail.mockResolvedValue({ email: '<EMAIL>', status: 0 });

      const result = await service.authenticateSaml(requestDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });

    it('should return error if client validation fails', async () => {
      jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => {
        throw new Error('Invalid client');
      });

      const result = await service.authenticateSaml(requestDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });
    it('should return error if getUserToken returns invalid token response', async () => {
      jest.spyOn(service, 'getUserToken').mockResolvedValue({
        type: 'mock',
        msg: 'no token',
      }); // no _t to trigger the fallback

      const result = await service.authenticateSaml(requestDto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while authenticating user.',
      });
    });

  });

  describe('getTaxonamyName', () => {
    const uid = '1001';
    let userRepositoryMock: any;

    beforeEach(() => {
      userRepositoryMock = {
        findOne: jest.fn(),
      };

      helperServiceMock.getHelper = jest.fn((token) => {
        if (token === UserRepository) return userRepositoryMock;
      });
    });

    it('should return taxonomy data when user has work_experience and academics', async () => {
      const user = {
        work_experience: [
          { company: 'ABC Corp', designation: 'Engineer' },
          { company: 'XYZ Inc', designation: 'Manager' },
        ],
        academics: [
          { institute: 'IIT Delhi' },
          { institute: 'NIT Trichy' },
        ],
      };
      userRepositoryMock.findOne.mockResolvedValue(user);

      const result = await service.getTaxonamyName(uid);

      expect(result).toEqual({
        company: [
          { tid: 'ABC Corp', name: 'ABC Corp' },
          { tid: 'XYZ Inc', name: 'XYZ Inc' },
        ],
        designation: [
          { tid: 'Engineer', name: 'Engineer' },
          { tid: 'Manager', name: 'Manager' },
        ],
        institute: [
          { tid: 'IIT Delhi', name: 'IIT Delhi' },
          { tid: 'NIT Trichy', name: 'NIT Trichy' },
        ],
      });
      expect(userRepositoryMock.findOne).toHaveBeenCalledWith({ uid });
    });

    it('should return empty arrays if work_experience and academics are missing', async () => {
      const user = {};
      userRepositoryMock.findOne.mockResolvedValue(user);

      const result = await service.getTaxonamyName(uid);

      expect(result).toEqual({
        company: [],
        designation: [],
        institute: [],
      });
    });

    it('should return undefined and log error if uid is not provided', async () => {
      const spy = jest.spyOn(Logger, 'error').mockImplementation();

      const result = await service.getTaxonamyName(undefined as any);

      expect(result).toBeUndefined();
      expect(spy).toHaveBeenCalledWith('Error fetching taxonomy details', expect.objectContaining({
        MESSAGE: 'Please provide user id',
      }));

      spy.mockRestore();
    });

    it('should catch and log error if repository throws', async () => {
      const error = new Error('DB failure');
      userRepositoryMock.findOne.mockRejectedValue(error);

      const spy = jest.spyOn(Logger, 'error').mockImplementation();

      await service.getTaxonamyName(uid);

      expect(spy).toHaveBeenCalledWith('Error fetching taxonomy details', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'DB failure',
        UID: uid,
      }));

      spy.mockRestore();
    });
  });

  describe('getTermsListByTaxonomyCategory', () => {
    const mockTaxonomies = [
      { _id: '1', tid: 101, name: 'Java' },
      { _id: '2', tid: 102, name: 'NodeJS' },
    ];

    beforeEach(() => {
      taxonomyRepositoryMock.find = jest.fn().mockResolvedValue(mockTaxonomies);

      helperServiceMock.get = jest.fn((token) => {
        if (token === TaxonomyRepository) return taxonomyRepositoryMock;
        return null;
      });
    });

    it('should return terms formatted by tid for mobile app', async () => {
      const result = await service.getTermsListByTaxonomyCategory('technology', 'mobile');
      expect(result).toEqual({
        101: 'Java',
        102: 'NodeJS',
      });
      expect(taxonomyRepositoryMock.find).toHaveBeenCalledWith({ category: 'technology' });
    });

    it('should return terms formatted by _id for non-mobile app', async () => {
      const result = await service.getTermsListByTaxonomyCategory('technology', 'web');
      expect(result).toEqual({
        '1': 'Java',
        '2': 'NodeJS',
      });
      expect(taxonomyRepositoryMock.find).toHaveBeenCalledWith({ category: 'technology' });
    });

    it('should return undefined if taxonomy category is missing', async () => {
      const result = await service.getTermsListByTaxonomyCategory(undefined as any, 'web');
      expect(result).toBeUndefined();
    });


    it('should log and return undefined if repository throws error', async () => {
      const error = new Error('DB failed');
      taxonomyRepositoryMock.find.mockRejectedValue(error);
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation();

      const result = await service.getTermsListByTaxonomyCategory('technology', 'web');

      expect(result).toBeUndefined();
      expect(loggerSpy).toHaveBeenCalledWith('Error fetching taxonomy terms', expect.objectContaining({
        METHOD: expect.any(String),
        MESSAGE: 'DB failed',
        CATEGORY: 'technology',
      }));
      loggerSpy.mockRestore();
    });
  });

  describe('forgetPassword', () => {
    const email = '<EMAIL>';
    const user = {
      uid: 1001,
      email,
      display_name: 'John Doe',
      status: 1,
      roles: [{ rid: 1, roleName: 'authenticated user' }],
    };

    const resetResponse = {
      url: 'https://reset.url',
      token: 'resettoken123',
    };

    beforeEach(() => {
      jest.clearAllMocks();

      userRepositoryMock.getUserByEmail.mockResolvedValue(user);
      authHelperMock.getUserPassResetUrl.mockResolvedValue(resetResponse);
      cacheServiceMock.set.mockResolvedValue(true);
      emailHelperMock.sendEmail.mockResolvedValue(true);
      paperclipServiceMock.invalidateTokenForAppUser.mockResolvedValue(true);
      jest.spyOn(service, 'forgetPasswordAttemptLimit').mockResolvedValue({ type: 'ok', msg: '' });

      configServiceMock.get = jest.fn((key) => {
        switch (key) {
          case 'resetResponseTime':
            return 3600;
          default:
            return '';
        }
      });
    });

    it('should return true on successful email send', async () => {
      const result = await service.forgetPassword(email, { client_id: 'client1' });

      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith(email);
      expect(authHelperMock.getUserPassResetUrl).toHaveBeenCalledWith(user.uid);
      expect(emailHelperMock.sendEmail).toHaveBeenCalled();
      expect(cacheServiceMock.set).toHaveBeenNthCalledWith(1, `${user.uid}`, resetResponse.token, 3600);
      expect(result).toBe(true);
    });

    it('should return link if linkOnly is 1', async () => {
      const result = await service.forgetPassword(email, { linkOnly: 1 });

      expect(result).toEqual({ type: 'success', link: resetResponse.url });
      expect(cacheServiceMock.set).toHaveBeenCalledWith(`${user.uid}`, resetResponse.token, 3600);
    });

    it('should return link with fm param if linkOnly=1 and fm=1', async () => {
      const result = await service.forgetPassword(email, {
        linkOnly: 1,
        fm: 1,
        appType: 'app',
      });

      expect(result).toEqual({
        type: 'success',
        link: `${resetResponse.url}?fm=1&appType=app`,
      });
      expect(cacheServiceMock.set).toHaveBeenCalledWith(`${user.uid}`, resetResponse.token, 3600);
    });

    it('should throw if user not found', async () => {
      userRepositoryMock.getUserByEmail.mockResolvedValue(null);

      await expect(service.forgetPassword(email)).rejects.toThrow(BadRequestException);
    });

    it('should throw if user is disabled', async () => {
      userRepositoryMock.getUserByEmail.mockResolvedValue({ ...user, status: 0 });

      await expect(service.forgetPassword(email)).rejects.toThrow(BadRequestException);
    });

    it('should throw if attempt limit exceeded', async () => {
      jest
        .spyOn(service, 'forgetPasswordAttemptLimit')
        .mockResolvedValue({ type: 'notice', msg: '' });

      await expect(service.forgetPassword(email)).rejects.toThrow(BadRequestException);
    });

    it('should return error type if email sending fails', async () => {
      emailHelperMock.sendEmail.mockResolvedValue(false);

      const result = await service.forgetPassword(email);
      expect(result).toEqual({ type: 'error', msg: 'Error occurred while sending email.' });
    });

    it('should return error type if resetUrl missing in linkOnly flow', async () => {
      authHelperMock.getUserPassResetUrl.mockResolvedValue({ url: '', token: 'tok' });

      const result = await service.forgetPassword(email, { linkOnly: 1 });

      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while generating forgot password link.',
      });
    });

    it('should log invalidate failure but continue', async () => {
      paperclipServiceMock.invalidateTokenForAppUser.mockResolvedValue(false);

      const result = await service.forgetPassword(email);

      expect(result).toBe(true); // still succeeds
    });
  });

  describe('resetPassword', () => {
    const uid = '1001';
    const password = 'newPassword123';
    const hashedPassword = 'hashed_password';
    const user = { uid, email: '<EMAIL>' };

    beforeEach(() => {
      jest.clearAllMocks();

      jest.spyOn(drupalHash, 'hashPassword').mockReturnValue(hashedPassword);
      configServiceMock.get = jest.fn((key) => {
        if (key === 'enableDrupalSync') return true;
        return '';
      });

      userRepositoryMock.findOneAndUpdate.mockResolvedValue(user);
      userHelperMock.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
      userHelperMock.syncUserDataWithMySQLDrupal.mockResolvedValue(true);
    });

    it('should reset password successfully', async () => {
      const result = await service.resetPassword({ uid, pass: password });

      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalledWith(
        { uid },
        { user_options: 1, password: hashedPassword }
      );
      expect(userHelperMock.updateCloud6SentinelByUidOrMail).toHaveBeenCalledWith({
        uid,
        pass: hashedPassword,
        user_options: 1,
      });
      expect(userHelperMock.syncUserDataWithMySQLDrupal).toHaveBeenCalledWith({
        uid,
        password,
        user_options: 1,
      });

      expect(result).toEqual({
        success: true,
        msg: 'Password reset successful.',
      });
    });

    it('should return error if user is not found', async () => {
      userRepositoryMock.findOneAndUpdate.mockResolvedValue(null);

      const result = await service.resetPassword({ uid, pass: password });

      expect(result).toEqual({
        success: false,
        msg: 'User account does not exist.',
      });
    });

    it('should return error if updateCloud6SentinelByUidOrMail fails', async () => {
      userHelperMock.updateCloud6SentinelByUidOrMail.mockResolvedValue(false);

      const result = await service.resetPassword({ uid, pass: password });

      expect(result).toEqual({
        success: false,
        msg: 'Password not updated.',
      });
    });

    it('should skip Drupal sync if config disabled', async () => {
      configServiceMock.get = jest.fn((key) => {
        if (key === 'enableDrupalSync') return false;
        return '';
      });

      await service.resetPassword({ uid, pass: password });

      expect(userHelperMock.syncUserDataWithMySQLDrupal).not.toHaveBeenCalled();
    });

    it('should throw error and log if exception occurs', async () => {
      userRepositoryMock.findOneAndUpdate.mockRejectedValue(new Error('DB failure'));

      await expect(service.resetPassword({ uid, pass: password })).rejects.toThrow('DB failure');
    });
  });

  describe('validateToken', () => {
    const clientId = 'testClient';
    const token = 'valid.jwt.token';
    const email = '<EMAIL>';
    const user = { uid: 101, email };
    const idToken = 'generated.id.token';

    const tokenPayload = {
      email,
      ckey: clientId,
    };

    beforeEach(() => {
      jest.clearAllMocks();

      configServiceMock.get = jest.fn((key) => {
        switch (key) {
          case 'clientSecret':
            return 'secret';
          case 'jwtCookieName':
            return 'jwt';
          case 'ssoCookieDomain':
            return 'example.com';
          default:
            return '';
        }
      });

      helperServiceMock.get.mockImplementation((token) => {
        if (token === AuthTokenHelper) return tokenServiceMock;
        if (token === UserRepository) return userRepositoryMock;
      });

      helperServiceMock.getHelper.mockImplementation((token) => {
        if (token === 'UserHelper') return Promise.resolve(userHelperMock);
        if (token === 'CookieHelper') return Promise.resolve(cookieHelperMock);
      });

      tokenServiceMock.decodeToken.mockResolvedValue(tokenPayload);
      tokenServiceMock.generateSessionTokens.mockResolvedValue({ idToken });
      userRepositoryMock.getUserByEmail.mockResolvedValue(user);
      userRepositoryMock.findOneAndUpdate.mockResolvedValue(true);
      userHelperMock.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
      cookieHelperMock.setCookie = jest.fn();
    });

    it('should return success and idToken for valid token', async () => {
      const result = await service.validateToken({
        client_id: clientId,
        token,
      });

      expect(tokenServiceMock.decodeToken).toHaveBeenCalledWith(token);
      expect(userRepositoryMock.getUserByEmail).toHaveBeenCalledWith(email);
      expect(tokenServiceMock.generateSessionTokens).toHaveBeenCalledWith(user, clientId);
      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalledWith({ uid: user.uid }, expect.any(Object));
      expect(userHelperMock.updateCloud6SentinelByUidOrMail).toHaveBeenCalled();
      expect(result).toEqual({ type: 'success', _t: idToken });
    });

    it('should return error if token is missing', async () => {
      const result = await service.validateToken({
        client_id: clientId,
        token: '',
      });

      expect(result).toEqual({
        type: 'error',
        msg: 'please provide valid token',
      });
    });

    it('should return error if ckey mismatch', async () => {
      tokenServiceMock.decodeToken.mockResolvedValue({ ...tokenPayload, ckey: 'wrongClient' });

      const result = await service.validateToken({ client_id: clientId, token });

      expect(cookieHelperMock.setCookie).toHaveBeenCalledWith(
        'jwt',
        '',
        expect.objectContaining({ domain: 'example.com' }),
      );
      expect(result).toEqual({ type: 'error', msg: 'Invalid request.' });
    });

    it('should return error on exception', async () => {
      tokenServiceMock.decodeToken.mockRejectedValue(new Error('Token failure'));

      const result = await service.validateToken({ client_id: clientId, token });

      expect(result).toEqual({
        type: 'error',
        msg: 'Token failure',
      });
    });

    it('should set responseType based on redirect_url', async () => {
      const consoleSpy = jest.spyOn(Logger, 'info').mockImplementation();
      await service.validateToken({ client_id: clientId, token, redirect_url: 'https://app' });
      expect(consoleSpy).toHaveBeenCalledWith('validateToken', { responseType: 'redirect' });

      await service.validateToken({ client_id: clientId, token });
      expect(consoleSpy).toHaveBeenCalledWith('validateToken', { responseType: 'json' });
      consoleSpy.mockRestore();
    });
  });

  describe('saveProfileAndFormData', () => {
    const userId = 123;
    const user = {
      uid: userId,
      user_groups: [99],
    };

    const updateDetails = {
      userId,
      btnSignup: 'basic',
      userEmail: '<EMAIL>',
      isB2bStudent: true,
    };

    const inputParams = {
      controller: 'UserController',
      module: 'UserModule',
      action: 'save',
      q: 'queryString',
      name: 'John',
    };

    beforeEach(() => {
      jest.clearAllMocks();

      userRepositoryMock.findByUID.mockResolvedValue(user);
      userApiV1HelperMock.getLmsSetting.mockResolvedValue('yes');
      userHelperMock.syncUserDataWithMySQLDrupal.mockResolvedValue(true);
      lrsHelperMock.sendDataToKafka.mockResolvedValue(true);
      profileHelperMock.saveProfileData.mockResolvedValue(true);
      profileHelperMock.fetchProfileData.mockResolvedValue({});

      configServiceMock.get = jest.fn((key: string) => {
        switch (key) {
          case 'enableDrupalSync':
            return true; // ✅ ensures sync methods are called
          case 'profileTopic':
            return 'profile-topic';
          case 'profileLmsSettingIdentifier':
            return 'profile-setting';
          default:
            return '';
        }
      });

      service['handleBasic'] = jest.fn().mockResolvedValue({
        profileUpdateObj: { name: 'John' },
        postKafkaObj: { key: 'value' },
      });
      service['handleContact'] = jest.fn().mockResolvedValue({});
      service['handleOutcome'] = jest.fn().mockResolvedValue({});
      service['handleProfessional'] = jest.fn().mockResolvedValue({});
      service['handleAcademics'] = jest.fn().mockResolvedValue({});
    });

    it('should successfully update profile with "basic" step', async () => {
      const result = await service.saveProfileAndFormData(updateDetails, { ...inputParams });

      expect(userRepositoryMock.findByUID).toHaveBeenCalledWith(userId);
      expect(service['handleBasic']).toHaveBeenCalled();
      expect(profileHelperMock.saveProfileData).toHaveBeenCalledWith(
        expect.objectContaining({ name: 'John', edit_type: 'basic' }),
        user
      );
      expect(lrsHelperMock.sendDataToKafka).toHaveBeenCalled();
      expect(result).toEqual({
        type: 'success',
        msg: 'Your profile has been successfully updated.',
      });
    });

    it('should handle unsupported btnSignup values', async () => {
      await expect(
        service.saveProfileAndFormData({ ...updateDetails, btnSignup: 'unsupported' }, inputParams),
      ).resolves.toEqual({
        type: 'error',
        msg: 'Invalid Request.',
      });
    });

    it('should return error when user not found', async () => {
      userRepositoryMock.findByUID.mockResolvedValue(null);
      const result = await service.saveProfileAndFormData(updateDetails, inputParams);

      expect(result).toEqual({
        type: 'error',
        msg: 'UserNotFoundException',
      });
    });

    it('should return generic error if saveProfileData returns false', async () => {
      profileHelperMock.saveProfileData.mockResolvedValue(false);
      const result = await service.saveProfileAndFormData(updateDetails, inputParams);

      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while updating profile. Please try again.',
      });
    });

    it('should call syncTaxonomyDataWithMySQLDrupal for professional/academics/outcome', async () => {
      const btnSteps = ['professional', 'academics', 'outcome'];

      for (const step of btnSteps) {
        profileHelperMock.saveProfileData.mockResolvedValue(true);
        profileHelperMock.fetchProfileData.mockResolvedValue({}); // if used inside
        userHelperMock.syncUserDataWithMySQLDrupal.mockResolvedValue(true);
        profileHelperMock.syncTaxonomyDataWithMySQLDrupal.mockClear();

        service[`handle${step.charAt(0).toUpperCase() + step.slice(1)}`] = jest.fn().mockResolvedValue({
          profileUpdateObj: { name: 'John' },
          postKafkaObj: { key: 'value' },
        });

        await service.saveProfileAndFormData({ ...updateDetails, btnSignup: step }, inputParams);

        expect(profileHelperMock.syncTaxonomyDataWithMySQLDrupal).toHaveBeenCalled();
      }
    });

    it('should skip taxonomy sync for "contact"/"basic" btnSignup', async () => {
      profileHelperMock.saveProfileData.mockResolvedValue(true);
      await service.saveProfileAndFormData({ ...updateDetails, btnSignup: 'contact' }, inputParams);

      expect(profileHelperMock.syncTaxonomyDataWithMySQLDrupal).not.toHaveBeenCalled();
    });
  });

  describe('handleBasic', () => {
    const updateDetails = {
      userId: 123,
      userEmail: '<EMAIL>',
    };

    const inputParams = {
      first_name: 'John',
      last_name: 'Doe',
      title: 'Mr.',
      middle_name: 'E',
      training_funded_by: 'CompanyX',
      user_linkedin_url: 'https://linkedin.com/in/john',
      profile_picture: JSON.stringify({ attachment: 'base64image==' }),
      dob: '1990-01-01',
      gender: 'M',
    };

    it('should handle profile image and return profile and kafka data with showProfile = true', async () => {
      profileImageHelperMock.uploadMobileUserProfilePic.mockResolvedValue({
        isFileUploaded: true,
        profileImage: 'profile.jpg',
      });

      const result = await service.handleBasic(
        inputParams,
        updateDetails,
        true, // showProfile
        {},
        {},
        userApiV1HelperMock
      );

      expect(userApiV1HelperMock.validateEditProfileBasic).toHaveBeenCalledWith(inputParams, true);
      expect(result.profileUpdateObj).toEqual(
        expect.objectContaining({
          first_name: 'John',
          last_name: 'Doe',
          display_name: 'John Doe',
          title: 'Mr.',
          middle_name: 'E',
          training_funded_by: 'CompanyX',
          linkedin_url: 'https://linkedin.com/in/john',
          profile_picture: 'profile.jpg',
        })
      );
      expect(result.postKafkaObj).toEqual(
        expect.objectContaining({
          user_email: '<EMAIL>',
          user_id: 123,
          first_name: 'John',
          last_name: 'Doe',
          display_name: 'John Doe',
          training_funded_by: 'CompanyX',
        })
      );
    });

    it('should return extra fields when showProfile = false', async () => {
      profileImageHelperMock.uploadMobileUserProfilePic.mockResolvedValue({
        isFileUploaded: true,
        profileImage: 'profile.jpg',
      });

      const result = await service.handleBasic(
        inputParams,
        updateDetails,
        false,
        {},
        {},
        userApiV1HelperMock
      );

      expect(result.profileUpdateObj.dob).toBe('1990-01-01');
      expect(result.profileUpdateObj.gender).toBe('Male');
    });

    it('should fallback to raw profile picture string if JSON parsing fails', async () => {
      const input = {
        ...inputParams,
        profile_picture: 'rawBase64Image',
      };

      profileImageHelperMock.uploadMobileUserProfilePic.mockResolvedValue({
        isFileUploaded: true,
        profileImage: 'uploaded.jpg',
      });

      const result = await service.handleBasic(input, updateDetails, true, {}, {}, userApiV1HelperMock);

      expect(result.profileUpdateObj.profile_picture).toBe('uploaded.jpg');
    });

    it('should throw and log if error occurs', async () => {
      userApiV1HelperMock.validateEditProfileBasic.mockRejectedValue(new Error('validation failed'));

      await expect(
        service.handleBasic(inputParams, updateDetails, true, {}, {}, userApiV1HelperMock)
      ).rejects.toThrow('validation failed');
    });
  });

  describe('handleContact', () => {
    const inputParams = {
      phone_no: '9999999999',
      country_code: 'IN',
      country_of_residence: 'India',
      state: 'Karnataka',
      location: 'Bangalore',
      correspondence_address: '123 MG Road',
      timezone: '', // trigger fallback
    };

    const updateDetails = {
      userId: 101,
      userEmail: '<EMAIL>',
      isB2bStudent: true,
    };

    const lgid = 5;
    const profile = 'yes';

    const mockTimezone = 'Asia/Kolkata';

    beforeEach(() => {
      jest.clearAllMocks();

      configServiceMock.get.mockImplementation((key) => {
        if (key === 'phoneLmsSettingIdentifier') return 'phone_setting';
        return null;
      });

      userApiV1HelperMock.getLmsSetting.mockResolvedValue('yes');
      userApiV1HelperMock.validateEditProfileContact.mockResolvedValue(true);
      userMgmtUtilityHelperMock.getTimezoneFromCountryCode.mockResolvedValue('Asia/Kolkata');

      helperServiceMock.getHelper.mockImplementation((token) => {
        if (token === 'UserMgmtUtilityHelper') return Promise.resolve(userMgmtUtilityHelperMock);
        return Promise.resolve(null);
      });
    });


    it('should build profile and kafka objects correctly with fallback timezone', async () => {
      const result = await service.handleContact(
        inputParams,
        updateDetails,
        true,
        profile,
        lgid,
        {},
        {},
        userApiV1HelperMock,
      );

      expect(userApiV1HelperMock.getLmsSetting).toHaveBeenCalledWith(expect.anything(), lgid);
      expect(userApiV1HelperMock.validateEditProfileContact).toHaveBeenCalledWith(
        inputParams,
        'yes',
        true
      );

      expect(userMgmtUtilityHelperMock.getTimezoneFromCountryCode).toHaveBeenCalledWith('IN');

      expect(result.profileUpdateObj).toMatchObject({
        phone_no: '9999999999',
        country_code: 'IN',
        country_of_residence: 'India',
        state: 'Karnataka',
        location: 'Bangalore',
        correspondence_address: '123 MG Road',
        timezone: mockTimezone,
      });

      expect(result.postKafkaObj).toMatchObject({
        user_email: '<EMAIL>',
        user_id: 101,
        timezone: mockTimezone,
        country: 'IN',
        mobile_no: '9999999999',
      });
    });

    it('should not call timezone fallback if input timezone is provided', async () => {
      const inputWithTimezone = { ...inputParams, timezone: 'Asia/Kolkata' };

      const result = await service.handleContact(
        inputWithTimezone,
        updateDetails,
        true,
        profile,
        lgid,
        {},
        {},
        userApiV1HelperMock,
      );

      expect(userMgmtUtilityHelperMock.getTimezoneFromCountryCode).not.toHaveBeenCalled();
      expect(result.profileUpdateObj.timezone).toBe('Asia/Kolkata');
    });

    it('should throw and log error on failure', async () => {
      const error = new Error('Boom!');
      userApiV1HelperMock.getLmsSetting.mockRejectedValue(error);

      await expect(
        service.handleContact(
          inputParams,
          updateDetails,
          true,
          profile,
          lgid,
          {},
          {},
          userApiV1HelperMock,
        )
      ).rejects.toThrow('Boom!');
    });

    it('should update contact if profile is empty and isB2bStudent is true', async () => {
      const input = { ...inputParams };
      const updateDetails = {
        userId: 101,
        userEmail: '<EMAIL>',
        isB2bStudent: true,
      };
      const profile = ''; // ✅ Utility.isEmpty(profile) → true
      const lgid = 1;

      userApiV1HelperMock.getLmsSetting.mockResolvedValue('yes');
      userApiV1HelperMock.validateEditProfileContact.mockResolvedValue(true);
      userMgmtUtilityHelperMock.getTimezoneFromCountryCode.mockResolvedValue('Asia/Kolkata');

      const result = await service.handleContact(
        input,
        updateDetails,
        true,
        profile,
        lgid,
        {},
        {},
        userApiV1HelperMock,
      );

      expect(result.profileUpdateObj.phone_no).toBe('9999999999');
      expect(result.postKafkaObj.mobile_no).toBe('9999999999');
      expect(result.postKafkaObj.country).toBe('IN');
    });

  });

  describe('handleOutcome', () => {
    const inputParams = {
      objective_of_taking_course: 'Get Certified',
    };

    const updateDetails = {
      userId: 123,
      userEmail: '<EMAIL>',
    };

    beforeEach(() => {
      jest.clearAllMocks();
      userApiV1HelperMock.validateEditProfileOutcome.mockResolvedValue(true);
      profileHelperMock.getOneTaxonomy.mockReturnValue(true);
    });

    it('should update profile and kafka objects if valid objective is provided', async () => {
      const result = await service.handleOutcome(
        inputParams,
        updateDetails,
        {},
        {},
        userApiV1HelperMock,
        profileHelperMock,
      );

      expect(userApiV1HelperMock.validateEditProfileOutcome).toHaveBeenCalledWith(inputParams);
      expect(profileHelperMock.getOneTaxonomy).toHaveBeenCalledWith('Get Certified');

      expect(result.profileUpdateObj).toEqual({
        objective_taking_course: 'Get Certified',
      });

      expect(result.postKafkaObj).toEqual({
        objective_of_training: 'Get Certified',
        user_email: '<EMAIL>',
        user_id: 123,
      });
    });

    it('should not update profile if objective is invalid', async () => {
      profileHelperMock.getOneTaxonomy.mockReturnValue(false); // invalid

      const result = await service.handleOutcome(
        inputParams,
        updateDetails,
        {},
        {},
        userApiV1HelperMock,
        profileHelperMock,
      );

      expect(result.profileUpdateObj).toEqual({});
      expect(result.postKafkaObj).toEqual({});
    });

    it('should throw and log error if validation fails', async () => {
      const error = new Error('Validation failed');
      userApiV1HelperMock.validateEditProfileOutcome.mockRejectedValue(error);

      await expect(
        service.handleOutcome(
          inputParams,
          updateDetails,
          {},
          {},
          userApiV1HelperMock,
          profileHelperMock,
        )
      ).rejects.toThrow('Validation failed');
    });
  });

  describe('handleProfessional', () => {
    const inputParams = {
      company_name: JSON.stringify(['OpenAI']),
      company_designation: JSON.stringify(['Researcher']),
      industry: JSON.stringify(['AI']),
      job_function: JSON.stringify(['ML']),
      exp_from_month: JSON.stringify(['01']),
      exp_from_year: JSON.stringify(['2020']),
      exp_to_month: JSON.stringify(['']),
      exp_to_year: JSON.stringify(['']),
      current_company: ['on'],
      where_are_you_in_career: 'early-career',
    };

    const updateDetails = {
      userId: 1001,
      userEmail: '<EMAIL>',
      btnSignup: 'professional',
    };

    beforeEach(() => {
      jest.clearAllMocks();
      userApiV1HelperMock.validateEditProfileProfessional = jest.fn().mockResolvedValue(true);
      profileHelperMock.getOneTaxonomy = jest.fn().mockResolvedValue({ _id: 'careerId123' });
    });

    it('should return profileUpdateObj and postKafkaObj for valid data', async () => {
      const result = await service.handleProfessional(
        inputParams,
        updateDetails,
        {},
        {},
        userApiV1HelperMock,
        profileHelperMock,
      );

      expect(userApiV1HelperMock.validateEditProfileProfessional).toHaveBeenCalledWith(inputParams);
      expect(profileHelperMock.getOneTaxonomy).toHaveBeenCalledWith('early-career');
      expect(result.profileUpdateObj).toMatchObject({
        company_name: ['OpenAI'],
        company_designation: ['Researcher'],
        industry: ['AI'],
        job_function: ['ML'],
        current_role: ['1'],
        where_are_you_in_career: 'careerId123',
      });
      expect(result.postKafkaObj).toMatchObject({
        user_email: '<EMAIL>',
        user_id: 1001,
        designation: 'Researcher',
        company: 'OpenAI',
        department: 'ML',
        industry: 'AI',
      });
    });

    it('should return error object if validation fails', async () => {
      userApiV1HelperMock.validateEditProfileProfessional.mockResolvedValue(['Invalid input']);

      const result = await service.handleProfessional(
        inputParams,
        updateDetails,
        {},
        {},
        userApiV1HelperMock,
        profileHelperMock,
      );

      expect(result).toEqual({
        type: 'error',
        status: false,
        msg: ['Invalid input'],
      });
    });

    it('should handle missing where_are_you_in_career field', async () => {
      const modifiedInput = { ...inputParams };
      delete modifiedInput.where_are_you_in_career;

      const result = await service.handleProfessional(
        modifiedInput,
        updateDetails,
        {},
        {},
        userApiV1HelperMock,
        profileHelperMock,
      );

      expect(result.profileUpdateObj.where_are_you_in_career).toBeUndefined();
    });

    it('should throw error and log if exception occurs', async () => {
      userApiV1HelperMock.validateEditProfileProfessional.mockRejectedValue(new Error('Unexpected error'));

      await expect(service.handleProfessional(
        inputParams,
        updateDetails,
        {},
        {},
        userApiV1HelperMock,
        profileHelperMock,
      )).rejects.toThrow('Unexpected error');
    });
  });

  describe('handleAcademics', () => {
    const inputParams = {
      institute_name: JSON.stringify(['IIT']),
      field_specialization: JSON.stringify(['AI']),
      field_qualification: JSON.stringify(['B.Tech']),
      course_from_month: JSON.stringify(['06']),
      course_from_year: JSON.stringify(['2018']),
      course_to_month: JSON.stringify(['05']),
      course_to_year: JSON.stringify(['2022']),
      highest_level_of_education: 'postgraduate'
    };

    const updateDetails = {
      userId: 1001,
      userEmail: '<EMAIL>',
      btnSignup: 'academics'
    };

    beforeEach(() => {
      jest.clearAllMocks();
      userApiV1HelperMock.validateEditProfileAcademics = jest.fn().mockResolvedValue(true);
      profileHelperMock.getOneTaxonomy = jest.fn().mockResolvedValue({ _id: 'taxonomy123' });
    });

    it('should return profileUpdateObj and postKafkaObj for valid input', async () => {
      const result = await service.handleAcademics(
        inputParams,
        updateDetails,
        {},
        {},
        userApiV1HelperMock,
        profileHelperMock
      );

      expect(userApiV1HelperMock.validateEditProfileAcademics).toHaveBeenCalledWith(inputParams);
      expect(profileHelperMock.getOneTaxonomy).toHaveBeenCalledWith('postgraduate');

      expect(result.profileUpdateObj).toMatchObject({
        institute_name: ['IIT'],
        field_specialization: ['AI'],
        field_qualification: ['B.Tech'],
        course_from_month: ['06'],
        course_from_year: ['2018'],
        course_to_month: ['05'],
        course_to_year: ['2022'],
        highest_level_of_education: 'taxonomy123',
        edit_type: 'academics'
      });

      expect(result.postKafkaObj).toMatchObject({
        user_email: '<EMAIL>',
        user_id: 1001,
        specialization: 'AI',
        college: 'IIT'
      });
    });

    it('should return error response if validation fails', async () => {
      userApiV1HelperMock.validateEditProfileAcademics.mockResolvedValue(['Invalid academic data']);

      const result = await service.handleAcademics(
        inputParams,
        updateDetails,
        {},
        {},
        userApiV1HelperMock,
        profileHelperMock
      );

      expect(result).toEqual({
        type: 'error',
        status: false,
        msg: ['Invalid academic data']
      });
    });

    it('should skip taxonomy call if highest_level_of_education is not present', async () => {
      const inputWithoutEdu = { ...inputParams };
      delete inputWithoutEdu.highest_level_of_education;

      const result = await service.handleAcademics(
        inputWithoutEdu,
        updateDetails,
        {},
        {},
        userApiV1HelperMock,
        profileHelperMock
      );

      expect(profileHelperMock.getOneTaxonomy).not.toHaveBeenCalled();
      expect(result.profileUpdateObj.highest_level_of_education).toBeUndefined();
    });

    it('should throw and log error if exception occurs', async () => {
      userApiV1HelperMock.validateEditProfileAcademics.mockRejectedValue(new Error('Unexpected failure'));

      await expect(service.handleAcademics(
        inputParams,
        updateDetails,
        {},
        {},
        userApiV1HelperMock,
        profileHelperMock
      )).rejects.toThrow('Unexpected failure');
    });
  });

  describe('updateAndFetchUserRoles', () => {
    const uid = 1001;
    const existingRoles = [
      { _id: 'role1', rid: 1, roleName: 'authenticated user' },
      { _id: 'role2', rid: 2, roleName: 'learner' },
    ];
    const newRole = { _id: 'role3', rid: 3, roleName: 'admin' };

    const reqBody = {
      uid,
      roles: ['role2', 'role3'], // remove role1, retain role2, add role3
    };

    beforeEach(() => {
      jest.clearAllMocks();

      // Mocks
      userRepositoryMock.findByUID.mockResolvedValue({
        uid,
        roles: existingRoles,
      });
      roleRepositoryMock.findAll.mockResolvedValue([newRole]);
      userRepositoryMock.findOneAndUpdate.mockResolvedValue(true);
      userHelperMock.clearCloud6SentinelUserRole.mockResolvedValue(true);
      userHelperMock.syncCloud6SentinelUserRole.mockResolvedValue(true);
      userHelperMock.syncUserDataWithMySQLDrupal.mockResolvedValue(true);
      configServiceMock.get = jest.fn(key => {
        if (key === 'enableDrupalSync') return true;
        return null;
      });
    });

    it('should update roles and sync successfully', async () => {
      const result = await service.updateAndFetchUserRoles(reqBody);

      expect(userRepositoryMock.findByUID).toHaveBeenCalledWith(uid);
      expect(roleRepositoryMock.findAll).toHaveBeenCalledWith({ _id: { $in: ['role3'] } });
      expect(userRepositoryMock.findOneAndUpdate).toHaveBeenCalled();
      expect(userHelperMock.clearCloud6SentinelUserRole).toHaveBeenCalledWith(uid, [1]); // role1 removed
      expect(userHelperMock.syncCloud6SentinelUserRole).toHaveBeenCalledWith([
        { uid, rid: 2 },
        { uid, rid: 3 },
      ]);
      expect(userHelperMock.syncUserDataWithMySQLDrupal).toHaveBeenCalledWith({
        uid,
        roles: {
          '2': 'learner',
          '3': 'admin',
        },
      });
      expect(result).toEqual(['role2', 'role3']);
    });

    it('should skip clearing roles if none are deleted', async () => {
      userRepositoryMock.findByUID.mockResolvedValue({
        uid,
        roles: [existingRoles[1]], // role2 only
      });
      roleRepositoryMock.findAll.mockResolvedValue([]);

      await service.updateAndFetchUserRoles({
        uid,
        roles: ['role2'], // same as current
      });

      expect(userHelperMock.clearCloud6SentinelUserRole).not.toHaveBeenCalled();
      expect(userHelperMock.syncCloud6SentinelUserRole).toHaveBeenCalled();
    });

    it('should throw BadRequestException if user not found', async () => {
      userRepositoryMock.findByUID.mockResolvedValue(null);

      await expect(service.updateAndFetchUserRoles(reqBody)).rejects.toThrow(BadRequestException);
    });

    it('should log and rethrow unexpected errors', async () => {
      userRepositoryMock.findByUID.mockRejectedValue(new Error('Unexpected DB Error'));

      await expect(service.updateAndFetchUserRoles(reqBody)).rejects.toThrow('Unexpected DB Error');
    });
  });

  describe('removeUserRoles', () => {
    const uid = 1001;
    const rid = 2;
    const reqBody = { uid, rid };

    const roleDocs = [{ _id: 'role-id-2', rid: 2, roleName: 'authenticated user' }];
    const userDoc = {
      uid,
      roles: [
        { _id: 'role-id-1', rid: 1, roleName: 'admin' },
        { _id: 'role-id-2', rid: 2, roleName: 'authenticated user' },
      ],
    };

    beforeEach(() => {
      jest.clearAllMocks();
      helperServiceMock.get.mockImplementation((token: any) => {
        switch (token) {
          case UserRepository:
            return userRepositoryMock;
          case RoleRepository:
            return roleRepositoryMock;
          case 'UserHelper':
            return userHelperMock;
          default:
            return {};
        }
      });

      userRepositoryMock.findOne.mockResolvedValue(userDoc);
      userRepositoryMock.findByUID.mockResolvedValue({
        ...userDoc,
        roles: [userDoc.roles[0]], // only 'admin' role remains
      });

      roleRepositoryMock.findAll.mockResolvedValue(roleDocs);
      userHelperMock.removeRoles.mockResolvedValue(true);
      userHelperMock.syncUserDataWithMySQLDrupal.mockResolvedValue(true);

      configServiceMock.get.mockImplementation((key: string) => {
        if (key === 'enableDrupalSync') return true;
        return '';
      });
    });

    it('should return false if user not found', async () => {
      userRepositoryMock.findOne.mockResolvedValue(null);

      const result = await service.removeUserRoles(reqBody);
      expect(result).toBe(false);
    });

    it('should return false if role not found', async () => {
      roleRepositoryMock.findAll.mockResolvedValue(null);

      const result = await service.removeUserRoles(reqBody);
      expect(result).toBe(false);
    });

    it('should return false if removeRoles fails', async () => {
      userHelperMock.removeRoles.mockResolvedValue(false);

      const result = await service.removeUserRoles(reqBody);
      expect(result).toBe(false);
    });

    it('should return false on exception', async () => {
      userRepositoryMock.findOne.mockRejectedValue(new Error('DB error'));

      const result = await service.removeUserRoles(reqBody);
      expect(result).toBe(false);
    });

    it('should skip Drupal sync if enableDrupalSync is false', async () => {
      configServiceMock.get.mockImplementation((key: string) => {
        if (key === 'enableDrupalSync') return false;
        return '';
      });

      const result = await service.removeUserRoles(reqBody);

      expect(userHelperMock.syncUserDataWithMySQLDrupal).not.toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should handle user with only one role being removed (resulting in zero roles)', async () => {
      userRepositoryMock.findOne.mockResolvedValue({
        uid,
        roles: [{ _id: 'role-id-2', rid: 2, roleName: 'authenticated user' }],
      });

      userRepositoryMock.findByUID.mockResolvedValue({
        uid,
        roles: [],
      });

      roleRepositoryMock.findAll.mockResolvedValue([]); // should not be used

      const result = await service.removeUserRoles(reqBody);

      expect(roleRepositoryMock.findAll).toHaveBeenCalledWith({ rid: 2 }); // only initial role fetch
      expect(userHelperMock.syncUserDataWithMySQLDrupal).toHaveBeenCalledWith({
        uid: 1001,
        roles: {},
      });

      expect(result).toBe(true);
    });


  });

  describe('createRole', () => {
    const roleDto = { rid: 3, roleName: 'manager' };

    beforeEach(() => {
      jest.clearAllMocks();

      helperServiceMock.get.mockImplementation((token: any) => {
        switch (token) {
          case RoleRepository:
            return roleRepositoryMock;
          case 'UserHelper':
            return userHelperMock;
          default:
            return {};
        }
      });

      configServiceMock.get.mockReturnValue('');
    });

    it('should return false if role already exists', async () => {
      roleRepositoryMock.findOne.mockResolvedValue({ rid: roleDto.rid }); // Simulate existing role

      const result = await service.createRole(roleDto);

      expect(result).toBe(false); // ✅ Expect false, not a thrown error
      expect(roleRepositoryMock.findOne).toHaveBeenCalledWith({
        where: { rid: roleDto.rid },
      });
      expect(userHelperMock.createCloud6SentinelRole).not.toHaveBeenCalled(); // Should not attempt to create
      expect(roleRepositoryMock.saveRoles).not.toHaveBeenCalled();            // Should not attempt to save
    });

    it('should return false if MySQL or Mongo save fails', async () => {
      roleRepositoryMock.findOne.mockResolvedValue(null);
      userHelperMock.createCloud6SentinelRole.mockResolvedValue(true);
      roleRepositoryMock.saveRoles.mockResolvedValue({}); // Missing `rid` to simulate failure

      const result = await service.createRole(roleDto);

      expect(result).toBe(false);
      expect(Logger.error).toHaveBeenCalled();
    });

    it('should return false if exception is thrown', async () => {
      roleRepositoryMock.findOne.mockRejectedValue(new Error('DB error'));

      const result = await service.createRole(roleDto);

      expect(result).toBe(false);
      expect(Logger.error).toHaveBeenCalled();
    });
  });

  describe('updateRole', () => {
    const roleDto = { rid: 1, roleName: 'Updated Role' };

    beforeEach(() => {
      jest.clearAllMocks();
      jest.spyOn(Logger, 'error').mockImplementation();

      helperServiceMock.get.mockImplementation((token: any) => {
        switch (token) {
          case RoleRepository:
            return roleRepositoryMock;
          case 'UserHelper':
            return userHelperMock;
          default:
            return {};
        }
      });
    });

    it('should update role in MySQL and Mongo and return updated data', async () => {
      roleRepositoryMock.findOne.mockResolvedValue({ rid: roleDto.rid, roleName: 'Old Role' });
      userHelperMock.updateCloud6SentinelRole.mockResolvedValue(true);
      roleRepositoryMock.updateRole.mockResolvedValue({
        rid: roleDto.rid,
        roleName: roleDto.roleName,
      });

      const result = await service.updateRole(roleDto);

      expect(roleRepositoryMock.findOne).toHaveBeenCalledWith({ rid: roleDto.rid });
      expect(userHelperMock.updateCloud6SentinelRole).toHaveBeenCalledWith({
        rid: roleDto.rid,
        name: roleDto.roleName,
      });
      expect(roleRepositoryMock.updateRole).toHaveBeenCalledWith(
        { rid: roleDto.rid },
        { roleName: roleDto.roleName }
      );

      expect(result).toEqual({
        rid: roleDto.rid,
        roleName: roleDto.roleName,
      });
    });

    it('should throw error if role does not exist', async () => {
      roleRepositoryMock.findOne.mockResolvedValue(null);

      await expect(service.updateRole(roleDto)).resolves.toBe(false);

      expect(Logger.error).toHaveBeenCalled();
      expect(roleRepositoryMock.findOne).toHaveBeenCalledWith({ rid: roleDto.rid });
    });

    it('should return false if MySQL or Mongo update fails', async () => {
      roleRepositoryMock.findOne.mockResolvedValue({ rid: roleDto.rid });
      userHelperMock.updateCloud6SentinelRole.mockResolvedValue(true);
      roleRepositoryMock.updateRole.mockResolvedValue({}); // missing `rid` → fails check

      const result = await service.updateRole(roleDto);

      expect(result).toBe(false);
      expect(Logger.error).toHaveBeenCalled();
    });

    it('should return false and log error on exception', async () => {
      roleRepositoryMock.findOne.mockRejectedValue(new Error('DB failure'));

      const result = await service.updateRole(roleDto);

      expect(result).toBe(false);

      // Expect Logger.error to be called with two arguments
      expect(Logger.error).toHaveBeenCalledWith(
        'updateRole',
        expect.objectContaining({
          METHOD: expect.stringContaining('updateRole'),
          MESSAGE: 'DB failure',
        }),
      );
    });

  });

});
