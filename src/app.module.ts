import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from './user/user.module';
import { AuthModule } from './auth/auth.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { InternalModule } from './internal/internal.module';
import { UserApiModule } from './user-api/user-api.module';
import { HelperModule } from './helper/helper.module';
import { CachingModule } from './caching/caching.module';
import { HttpLoggerMiddleware } from './logging/logging.middleware.httplogger';
import { MongoModule } from './db/mongo/mongo.module';
import { MysqlModule } from './db/mysql/mysql.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import config from './config/config';
import { SamlModule } from './saml/saml.module';
import { FirebaseModule } from './db/firebase/firebase.module';
/**
 * The main application module for the NestJS application.
 * 
 * This module is responsible for bootstrapping the application and configuring
 * its dependencies, middleware, and global settings. It imports various feature
 * modules, sets up configuration management, and registers global middleware.
 * 
 * @module AppModule
 * 
 * @description
 * - Loads environment variables using `ConfigModule` with support for multiple environments.
 * - Imports feature modules such as `MongoModule`, `MysqlModule`, `UserModule`, etc.
 * - Registers asynchronous modules like `CachingModule` and helper utilities.
 * - Configures the `EventEmitterModule` for event-driven architecture.
 * - Applies global middleware, such as `HttpLoggerMiddleware`, for request logging.
 * 
 * @implements {NestModule}
 * 
 * @method configure
 * Configures middleware for the application.
 * 
 * @param {MiddlewareConsumer} consumer - The middleware consumer used to apply middleware globally.
 */
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [config],
    }),
    MongoModule,
    MysqlModule,
    FirebaseModule,
    CachingModule.registerAsync(),
    UserModule,
    AuthModule,
    SamlModule,
    InternalModule,
    UserApiModule,
    EventEmitterModule.forRoot(),
    HelperModule,
    HelperModule.registerHelpersAsync()
  ],
  controllers: [AppController],
  providers: [AppService, ConfigService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer): void {
    consumer.apply(HttpLoggerMiddleware);
  }
}
