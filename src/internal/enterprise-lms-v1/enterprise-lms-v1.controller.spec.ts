import { Test, TestingModule } from '@nestjs/testing';
import { EnterpriseLmsV1Controller } from './enterprise-lms-v1.controller';
import { EnterpriseLMSService } from '../services/enterprise-lms.service';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import { BadRequestException, HttpStatus, InternalServerErrorException, UnauthorizedException } from '@nestjs/common';
import { CachingService } from '../../caching/caching.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { SetLearnerPasswordDto } from '../dto/set-learner-password.dto';
import { ChangePasswordDto } from '../dto/change-password.dto';
import { SetupManagerAccountDto } from '../dto/setup-manager-account-dto';
import { ResetPasswordDto } from '../dto/reset.password.dto';
import { AddMemberToGroupWithRoleDto } from '../dto/add-member-to-group-with-role.dto';
import { UserApiInternalService } from '../services/user.api.internal.service';

describe('EnterpriseLmsV1Controller', () => {
  let controller: EnterpriseLmsV1Controller;
  let enterpriseLMSService: EnterpriseLMSService;
  let configService: ConfigService;
  let helperService: HelperService;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EnterpriseLmsV1Controller],
      providers: [
        EnterpriseLMSService,
        ConfigService,
        HelperService,
        CachingService,
        { provide: CACHE_MANAGER, useValue: {} },
        { provide: 'CRYPTO_HELPER', useValue: {} },
      ],
    }).compile();

    controller = module.get<EnterpriseLmsV1Controller>(EnterpriseLmsV1Controller);
    enterpriseLMSService = module.get<EnterpriseLMSService>(EnterpriseLMSService);
    configService = module.get<ConfigService>(ConfigService);
    helperService = module.get<HelperService>(HelperService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('forgotPassword', () => {
    it('should return success response for valid input', async () => {
      const res: any = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      const dto = {
        affiliateLmsUrl: 'https://valid.affiliate.com',
        email: '<EMAIL>',
      };

      jest.spyOn(enterpriseLMSService, 'forgotPassword').mockResolvedValue(true);

      await controller.forgotPassword(dto as any, res);

      expect(res.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(res.json).toHaveBeenCalledWith({
        type: 'success',
        msg: 'Reset password link sent on your email.',
      });
    });

    it('should throw BadRequestException for invalid affiliateLmsUrl', async () => {
      const dto = {
        affiliateLmsUrl: 'invalid-url',
        email: '<EMAIL>',
      };

      await expect(controller.forgotPassword(dto as any, {} as any)).rejects.toThrow(
        new BadRequestException('Please provide a valid affiliate URL.'),
      );
    });

    it('should throw BadRequestException for invalid email', async () => {
      const dto = {
        affiliateLmsUrl: 'https://valid.url.com',
        email: 'not-an-email',
      };

      await expect(controller.forgotPassword(dto as any, {} as any)).rejects.toThrow(
        new BadRequestException('Please provide valid email id.'),
      );
    });

    it('should throw BadRequestException for InvalidGroupId error', async () => {
      const dto = {
        affiliateLmsUrl: 'https://valid.url.com',
        email: '<EMAIL>',
      };

      jest
        .spyOn(enterpriseLMSService, 'forgotPassword')
        .mockResolvedValue(new Error('InvalidGroupId'));

      await expect(controller.forgotPassword(dto as any, {} as any)).rejects.toThrow(
        new BadRequestException('Please provide valid group id.'),
      );
    });

    it('should throw BadRequestException for UserNotFoundException', async () => {
      const dto = {
        affiliateLmsUrl: 'https://valid.url.com',
        email: '<EMAIL>',
      };

      jest
        .spyOn(enterpriseLMSService, 'forgotPassword')
        .mockResolvedValue(new Error('UserNotFoundException'));

      await expect(controller.forgotPassword(dto as any, {} as any)).rejects.toThrow(
        new BadRequestException('User account does not exist for the specified email-id.'),
      );
    });

    it('should throw BadRequestException for UserDisabled', async () => {
      const dto = {
        affiliateLmsUrl: 'https://valid.url.com',
        email: '<EMAIL>',
      };

      jest
        .spyOn(enterpriseLMSService, 'forgotPassword')
        .mockResolvedValue(new Error('UserDisabled'));

      await expect(controller.forgotPassword(dto as any, {} as any)).rejects.toThrow(
        new BadRequestException('User account has been blocked.'),
      );
    });

    it('should throw BadRequestException for unknown response error message (default case)', async () => {
      const dto = {
        affiliateLmsUrl: 'https://valid.url.com',
        email: '<EMAIL>',
      };

      jest
        .spyOn(enterpriseLMSService, 'forgotPassword')
        .mockResolvedValue(new Error('SomeOtherError'));

      await expect(controller.forgotPassword(dto as any, {} as any)).rejects.toThrow(
        new BadRequestException('some error occurred while sending reset password email.'),
      );
    });


    it('should throw BadRequestException for BlockedUser', async () => {
      const dto = {
        affiliateLmsUrl: 'https://valid.url.com',
        email: '<EMAIL>',
      };

      jest
        .spyOn(enterpriseLMSService, 'forgotPassword')
        .mockResolvedValue(new Error('BlockedUser'));

      await expect(controller.forgotPassword(dto as any, {} as any)).rejects.toThrow(
        new BadRequestException('User account has been blocked.'),
      );
    });

    it('should throw BadRequestException for NotAGroupMember', async () => {
      const dto = {
        affiliateLmsUrl: 'https://valid.url.com',
        email: '<EMAIL>',
      };

      jest
        .spyOn(enterpriseLMSService, 'forgotPassword')
        .mockResolvedValue(new Error('NotAGroupMember'));

      await expect(controller.forgotPassword(dto as any, {} as any)).rejects.toThrow(
        new BadRequestException('User is not the member of group.'),
      );
    });

    it('should throw UnauthorizedException for Unauthorized access request', async () => {
      const dto = {
        affiliateLmsUrl: 'https://valid.url.com',
        email: '<EMAIL>',
      };

      jest
        .spyOn(enterpriseLMSService, 'forgotPassword')
        .mockResolvedValue(new Error('Unauthorized access request.'));

      await expect(controller.forgotPassword(dto as any, {} as any)).rejects.toThrow(
        new UnauthorizedException('Unauthorized access request.'),
      );
    });

    it('should throw InternalServerErrorException for unknown error', async () => {
      const dto = {
        affiliateLmsUrl: 'https://valid.url.com',
        email: '<EMAIL>',
      };

      jest
        .spyOn(enterpriseLMSService, 'forgotPassword')
        .mockRejectedValue(new Error('Unhandled error'));

      await expect(controller.forgotPassword(dto as any, {} as any)).rejects.toThrow(
        new InternalServerErrorException(),
      );
    });


  });

  describe('resetPassword', () => {
    const res: any = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    const dto: ResetPasswordDto = {
      client_id: 'sl_looper',
      gid: 1001,
      requestToken: 'valid-reset-token',
      userId: 1003663,
      user_pwd: 'StrongPass123!',
      confirm_pwd: 'StrongPass123!',
    };

    it('should return success response for valid input', async () => {
      const mockResponse = { type: 'success', roles: ['learner'] };
      jest.spyOn(enterpriseLMSService, 'resetPassword').mockResolvedValue(mockResponse);

      await controller.resetPassword(dto, res);

      expect(res.json).toHaveBeenCalledWith(mockResponse);
    });

    it.each([
      ['InvalidGroupId', 'Please provide valid group id.'],
      ['BlockedUser', 'User account has been blocked.'],
      ['InvalidToken', 'Invalid password reset URL OR it is expired.'],
      ['NotMemberOfGroup', 'User is not a member of the group.'],
      ['InvalidPasswdUrl', 'Invalid password reset URL.'],
      ['UnhandledError', 'Some error occurred while resetting your password.'],
    ])('should throw BadRequestException for service error: %s', async (message, expectedMsg) => {
      jest.spyOn(enterpriseLMSService, 'resetPassword').mockResolvedValue(new Error(message));

      await expect(controller.resetPassword(dto, res)).rejects.toThrow(
        new BadRequestException(expectedMsg),
      );
    });

    it('should throw UnauthorizedException from catch block', async () => {
      jest
        .spyOn(enterpriseLMSService, 'resetPassword')
        .mockRejectedValue(new UnauthorizedException('Session expired'));

      await expect(controller.resetPassword(dto, res)).rejects.toThrow(
        new UnauthorizedException('Session expired'),
      );
    });

    it('should throw BadRequestException from catch block with correct message', async () => {
      jest
        .spyOn(enterpriseLMSService, 'resetPassword')
        .mockRejectedValue(new BadRequestException('Invalid input'));

      await expect(controller.resetPassword(dto, res)).rejects.toThrow(
        new BadRequestException('Invalid input'),
      );
    });

    it('should throw InternalServerErrorException for unknown errors', async () => {
      jest.spyOn(enterpriseLMSService, 'resetPassword').mockRejectedValue(new Error('Unhandled'));

      await expect(controller.resetPassword(dto, res)).rejects.toThrow(
        new InternalServerErrorException(),
      );
    });
  });


  describe('getUidByName', () => {
    it('getUidByName should return a response when valid input is provided', async () => {
      const reqBody = { uname: 'username' };
      const response = { status: 'success', msg: 'User not found', data: null };

      jest.spyOn(enterpriseLMSService, 'getUidByName').mockResolvedValue(response); // Mock the expected response

      const result = await controller.getUidByName(reqBody);

      expect(result).toBe(response);
    });

    it('getUidByName should throw an error when an error occurs', async () => {
      const reqBody = { uname: 'username' };

      // Mock the expected error to be thrown
      jest
        .spyOn(enterpriseLMSService, 'getUidByName')
        .mockRejectedValue(new InternalServerErrorException('Internal Server Error'));

      await expect(controller.getUidByName(reqBody)).rejects.toThrow(
        new InternalServerErrorException('An unexpected error occurred while retrieving the UID.'),
      );
    });
    it('getUidByName should throw BadRequestException when service throws BadRequestException', async () => {
      const reqBody = { uname: 'username' };

      jest
        .spyOn(enterpriseLMSService, 'getUidByName')
        .mockRejectedValue(new BadRequestException('Invalid request'));

      
        await expect(controller.getUidByName(reqBody)).rejects.toMatchObject({
          message: 'Invalid request',
          status: 400,
        });

      await expect(controller.getUidByName(reqBody)).rejects.toThrow(
        new BadRequestException('Invalid request'),
      );
    });

  });

  describe('setLearnerPassword', () => {
    it('should return success response when valid input is provided', async () => {
      const res: any = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      const mockSetLearnerPasswordDto: SetLearnerPasswordDto = {
        email: '<EMAIL>',
        user_pwd: 'newPassword123',
        gids: ['2381'],
        rids: ['2345'],
        access: **********,
        login: **********,
        user_options: 0
      };

      const mockUserService = {
        setLearnerPassword: jest.fn().mockResolvedValue(true),
      };

      jest.spyOn(helperService, 'get').mockResolvedValue(mockUserService);
      jest.spyOn(configService, 'get').mockImplementation((key: string) => {
        if (key === 'passwordSuccess') return 'Password set successfully';
      });

      await controller.setLearnerPassword(mockSetLearnerPasswordDto, res);

      expect(mockUserService.setLearnerPassword).toHaveBeenCalledWith(mockSetLearnerPasswordDto);
      expect(res.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(res.json).toHaveBeenCalledWith({
        type: 'success',
        msg: 'Password has been reset successfully. You will be logged out from all web devices. Kindly login again',
      });
    });

    it('should throw BadRequestException when user is not found', async () => {
      const res: any = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };
      const mockSetLearnerPasswordDto: SetLearnerPasswordDto = {
        email: '<EMAIL>',
        user_pwd: 'newPassword123',
        gids: ['2381'],
        rids: ['2345'],
        access: **********,
        login: **********,
        user_options: 0
      };

      const mockUserService = {
        setLearnerPassword: jest.fn().mockResolvedValue(new Error('UserNotFoundException')),
      };

      jest.spyOn(helperService, 'get').mockResolvedValue(mockUserService);
      jest.spyOn(configService, 'get').mockImplementation((key: string) => {
        if (key === 'userNotExist') return 'User does not exist';
      });

      await expect(controller.setLearnerPassword(mockSetLearnerPasswordDto, res)).rejects.toThrow(
        new BadRequestException('User account does not exist for the specified email-id.'),
      );
    });

    it('should throw BadRequestException when user is not a group member', async () => {
      const res: any = {
        status: jest.fn(),
        json: jest.fn(),
      };
      const mockSetLearnerPasswordDto: SetLearnerPasswordDto = {
        email: '<EMAIL>',
        user_pwd: 'newPassword123',
        gids: ['2381'],
        rids: ['2345'],
        access: **********,
        login: **********,
        user_options: 0
      };

      const mockUserService = {
        setLearnerPassword: jest.fn().mockResolvedValue(new Error('UserNotGroupMember')),
      };

      jest.spyOn(helperService, 'get').mockResolvedValue(mockUserService);
      jest.spyOn(configService, 'get').mockImplementation((key: string) => {
        if (key === 'userGroupMember') return 'User is not a group member';
      });

      await expect(controller.setLearnerPassword(mockSetLearnerPasswordDto, res)).rejects.toThrow(
        new BadRequestException('User is not a member of the group.'),
      );
    });

    it('should throw InternalServerErrorException for unexpected errors', async () => {
      const res: any = {
        status: jest.fn(),
        json: jest.fn(),
      };
      const mockSetLearnerPasswordDto: SetLearnerPasswordDto = {
        email: '<EMAIL>',
        user_pwd: 'newPassword123',
        gids: ['2381'],
        rids: ['2345'],
        access: **********,
        login: **********,
        user_options: 0
      };

      const mockUserService = {
        setLearnerPassword: jest.fn().mockRejectedValue(new Error('Unexpected error')),
      };

      jest.spyOn(helperService, 'get').mockResolvedValue(mockUserService);

      await expect(controller.setLearnerPassword(mockSetLearnerPasswordDto, res)).rejects.toThrow(
        new InternalServerErrorException(),
      );
    });

    it('should throw BadRequestException for unknown service error message', async () => {
      const res: any = {
        status: jest.fn(),
        json: jest.fn(),
      };

      const mockSetLearnerPasswordDto: SetLearnerPasswordDto = {
        email: '<EMAIL>',
        user_pwd: 'newPassword123',
        gids: ['2381'],
        rids: ['2345'],
        access: **********,
        login: **********,
        user_options: 0,
      };

      const mockUserService = {
        setLearnerPassword: jest.fn().mockResolvedValue(new Error('UnknownErrorMessage')),
      };

      jest.spyOn(helperService, 'get').mockResolvedValue(mockUserService);

      await expect(controller.setLearnerPassword(mockSetLearnerPasswordDto, res)).rejects.toThrow(
        new BadRequestException('Some error occurred while changing password.'),
      );
    });

  });

  describe('changePassword', () => {
    const res: any = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    const dto: ChangePasswordDto = {
      user_id: 123,
      cur_passwd: 'oldPass',
      new_passwd: 'newPass',
      confirm_passwd: 'newPass',
      gid: 1234,
    };

    const mockUserService = {
      changePassword: jest.fn(),
    };

    beforeEach(() => {
      jest.clearAllMocks();
      res.status.mockReturnThis();
      res.json.mockClear();
    });

    it('should return success response when password is changed successfully', async () => {
      jest.spyOn(helperService, 'get').mockResolvedValue(mockUserService);
      mockUserService.changePassword.mockResolvedValue(true);

      await controller.changePassword(dto, res);

      expect(res.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(res.json).toHaveBeenCalledWith({
        type: 'success',
        msg: 'Password has been reset successfully. You will be logged out from all web devices. Kindly login again',
      });
    });

    it.each([
      ['UserNotFoundException', 'User account does not exist for the specified email-id.'],
      ['NotAuthorizedException', 'Old password is incorrect.'],
      ['UserNotGroupMember', 'User is not a member of the group.'],
      ['GroupIdMustBePositive', 'Group ID must be positive'],
      ['SomethingElse', 'Some error occurred while changing password.'],
    ])('should return error JSON when response is Error: %s', async (errorMsg, expectedMsg) => {
      jest.spyOn(helperService, 'get').mockResolvedValue(mockUserService);
      mockUserService.changePassword.mockResolvedValue(new Error(errorMsg));

      await controller.changePassword(dto, res);

      expect(res.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(res.json).toHaveBeenCalledWith({
        type: 'error',
        msg: expectedMsg,
      });
    });

    it('should throw InternalServerErrorException for unexpected exceptions', async () => {
      jest.spyOn(helperService, 'get').mockRejectedValue(new Error('unexpected'));

      await expect(controller.changePassword(dto, res)).rejects.toThrow(
        new InternalServerErrorException(),
      );
    });

    it('should throw BadRequestException for known BadRequest error', async () => {
      jest.spyOn(helperService, 'get').mockRejectedValue(new BadRequestException('bad request'));

      await expect(controller.changePassword(dto, res)).rejects.toThrow(
        new BadRequestException('bad request'),
      );
    });
  });

  describe('setupManagerAccount', () => {
    const dto: SetupManagerAccountDto = {
      client_id: 'sl_looper',
      gid: 1234,
      affiliateLmsUrl: 'https://affiliate.com',
      affiliateLogoUrl: 'https://logo.url',
      userId: 1003663,
      requestTime: '2024-06-25T10:00:00Z',
      requestToken: 'valid-token',
      reqId: 'req-001',
      providerName: 'TestProvider',
      affiliateName: 'TestAffiliate',
      isWhiteLabelingEnabled: false,
      fromEmailAddress: '<EMAIL>',
      user_pwd: 'Password@123',
      confirm_pwd: 'Password@123',
    };

    it('should return success response from service', async () => {
      const expectedResponse = { type: 'success', msg: 'Manager account set up' };

      jest.spyOn(enterpriseLMSService, 'setupManagerAccount').mockResolvedValue(expectedResponse);

      const result = await controller.setupManagerAccount(dto);

      expect(result).toEqual(expectedResponse);
    });

    it.each([
      ['UserNotFoundException', 'Please provide valid user Id.'],
      ['UserNotInGroup', 'User is not the member of group.'],
      ['InvalidAffiliateUrl', 'Please provide a valid affiliate URL.'],
      ['InvalidToken', 'Invalid password reset URL.'],
      ['Unauthorized access request.', 'Unauthorized access request.'],
      ['SomeOtherError', 'Some error occurred while resetting your password.'],
    ])('should throw proper exception for error message: %s', async (errorMsg, expectedMsg) => {
      const error = new Error(errorMsg);
      jest.spyOn(enterpriseLMSService, 'setupManagerAccount').mockResolvedValue(error);

      if (errorMsg === 'Unauthorized access request.') {
        await expect(controller.setupManagerAccount(dto)).rejects.toThrow(
          new UnauthorizedException(error.message),
        );
      } else {
        await expect(controller.setupManagerAccount(dto)).rejects.toThrow(
          new BadRequestException(expectedMsg),
        );
      }
    });

    it('should throw BadRequestException with isData when error is BadRequestException', async () => {
      const error = new BadRequestException('Validation error');
      jest.spyOn(enterpriseLMSService, 'setupManagerAccount').mockRejectedValue(error);

      await expect(controller.setupManagerAccount(dto)).rejects.toThrowError(
        new BadRequestException({ message: 'Validation error', isData: { data: null } }),
      );
    });

    it('should throw InternalServerErrorException for unexpected errors', async () => {
      const error = new Error('Unexpected failure');
      jest.spyOn(enterpriseLMSService, 'setupManagerAccount').mockRejectedValue(error);

      await expect(controller.setupManagerAccount(dto)).rejects.toThrow(
        new InternalServerErrorException(),
      );
    });
  });

  describe('addMemberToGroupWithRole', () => {
    const reqBody: AddMemberToGroupWithRoleDto = {
      email: '<EMAIL>',
      gids: [456],
      rids: [789],
    };
  
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
  
    it('should return success response from service', async () => {
      const expectedResponse = {
        type: 'success',
        msg: 'User assigned to group with role successfully',
      };
  
      const userApiService = {
        addMemberToGroupWithRole: jest.fn().mockResolvedValue(expectedResponse),
      };
  
      jest
        .spyOn(helperService, 'get')
        .mockResolvedValue(userApiService as any);
  
      await controller.addMemberToGroupWithRole(reqBody, mockResponse as any);
  
      expect(helperService.get).toHaveBeenCalledWith(UserApiInternalService);
      expect(userApiService.addMemberToGroupWithRole).toHaveBeenCalledWith(reqBody);
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockResponse.json).toHaveBeenCalledWith(expectedResponse);
    });
  
    it.each([
      ['UserNotFoundException', 'User not found'],
      ['GroupDoesNotExist', 'Group not found'],
      ['InvalidRole', 'Invalid role specified'],
      ['AlreadyAssigned', 'User already assigned to this group with the same role'],
      ['InvalidData', 'Invalid request data'],
      ['RandomError', 'Some error occurred while assigning role to user.'],
    ])('should return generic error response for service failure with message: %s', async (errorMessage) => {
      const userApiService = {
        addMemberToGroupWithRole: jest.fn().mockRejectedValue(new Error(errorMessage)),
      };
  
      jest
        .spyOn(helperService, 'get')
        .mockResolvedValue(userApiService as any);
  
      await controller.addMemberToGroupWithRole(reqBody, mockResponse as any);
  
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockResponse.json).toHaveBeenCalledWith({
        type: 'error',
        msg: 'Some error occurred while assigning role to user.',
      });
    });
  
    it('should handle specific BadRequestException with structured data', async () => {
      const error = new BadRequestException('Invalid role data');
      const userApiService = {
        addMemberToGroupWithRole: jest.fn().mockRejectedValue(error),
      };
  
      jest.spyOn(helperService, 'get').mockResolvedValue(userApiService as any);
  
      await controller.addMemberToGroupWithRole(reqBody, mockResponse as any);
  
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockResponse.json).toHaveBeenCalledWith({
        type: 'error',
        msg: 'Some error occurred while assigning role to user.',
      });
    });
  
    it('should handle unexpected InternalServerErrorException', async () => {
      const userApiService = {
        addMemberToGroupWithRole: jest.fn().mockRejectedValue(new Error('Unexpected crash')),
      };
  
      jest.spyOn(helperService, 'get').mockResolvedValue(userApiService as any);
  
      await controller.addMemberToGroupWithRole(reqBody, mockResponse as any);
  
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockResponse.json).toHaveBeenCalledWith({
        type: 'error',
        msg: 'Some error occurred while assigning role to user.',
      });
    });
  });
  

});
