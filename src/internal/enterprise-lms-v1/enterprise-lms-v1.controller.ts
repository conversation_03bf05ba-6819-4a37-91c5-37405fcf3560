import {
  Body,
  Controller,
  Inject,
  Post,
  Res,
  BadRequestException,
  HttpCode,
  UsePipes,
  HttpStatus,
  ValidationPipe,
  UseFilters,
  InternalServerErrorException,
  UnauthorizedException,
} from '@nestjs/common';
import { ApiBody, ApiProperty, ApiResponse, ApiTags } from '@nestjs/swagger';
import { APILog } from '../../logging/logger';
import { EnterpriseLMSService } from '../services/enterprise-lms.service';
import { ChangePasswordDto } from '../dto/change-password.dto';
import { SetLearnerPasswordDto } from '../dto/set-learner-password.dto';
import { HelperService } from '../../helper/helper.service';
import { HttpExceptionFilter } from '../../common/filters/http.exception.filter';
import { ForgotPasswordB2B } from '../../common/typeDef/auth.type';
import { ResetPasswordDto } from '../dto/reset.password.dto';
import { UserService } from '../../user/services/user.service';
import {  Response } from 'express';
import { UserApiInternalService } from '../services/user.api.internal.service';
import { Utility } from './../../common/util/utility';
import { SetupManagerAccountDto } from '../dto/setup-manager-account-dto';
import { AddMemberToGroupWithRoleDto } from '../dto/add-member-to-group-with-role.dto';

/**
 * Controller for handling internal Enterprise LMS V1 operations.
 * This controller provides endpoints for user management, password operations,
 * and group membership functionalities.
 */
@ApiTags('Internal Enterprise LMS V1')
@Controller('internal/enterprise-lms-v1')
export class EnterpriseLmsV1Controller {
  @Inject() private readonly enterpriseLMSService: EnterpriseLMSService;
  @Inject() private readonly helperService: HelperService;

  /**
   * Endpoint to retrieve the UID of a user by their username.
   * @param reqBody - The request body containing the username.
   * @returns The UID of the user if found.
   * @throws BadRequestException if the username is invalid or not provided.
   * @throws InternalServerErrorException for unexpected errors.
   * This api is consumed by Paperclip.
   */
  @ApiProperty()
  @ApiResponse({ status: 200, description: 'UID retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiBody({ schema: { type: 'object', properties: { uname: { type: 'string' } } }, description: 'Username to retrieve UID' })
  @HttpCode(HttpStatus.OK)
  @UseFilters(HttpExceptionFilter)
  @Post('get-uid-by-name')
  async getUidByName(@Body() reqBody: { uname: string }) {
    APILog.log('get-uid-by-name');
    try {
      // Call the service method to get the UID by username
      const response = await this.enterpriseLMSService.getUidByName(reqBody?.uname);
      return response
      // Return the response if successful
    } catch (error: any) {
      // Log the error details for debugging
      APILog.error('getUidByName', {
        METHOD: this.constructor.name + '@' + this.getUidByName.name,
        MESSAGE: error.message,
        REQUEST: reqBody?.uname,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      // Throw appropriate exceptions based on the error type
      throw error instanceof BadRequestException
        ? new BadRequestException(error.message)
        : new InternalServerErrorException('An unexpected error occurred while retrieving the UID.');
    }
  }

  /**
   * Endpoint to handle forgot password functionality for B2B users.
   * @param forgotPassword - DTO containing user email and affiliate LMS URL.
   * @param res - The HTTP response object.
   * @returns A success message if the forgot password process is initiated successfully.
   * @throws BadRequestException for validation errors or if the operation fails.
   * @throws InternalServerErrorException for unexpected errors.
   * 
   * This api is consumed by Paperclip
   */
  @ApiProperty()
  @ApiResponse({ status: 200, description: 'Forgot password process initiated successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiBody({ schema: { type: 'object', properties: { email: { type: 'string' }, affiliateLmsUrl: { type: 'string' } } }, description: 'User data for forgot password' })
  @HttpCode(HttpStatus.OK)
  @UseFilters(HttpExceptionFilter)
  @Post('/forgot-password')
  async forgotPassword(
    @Body() forgotPassword: ForgotPasswordB2B,
    @Res() res: Response,
  ) {
    APILog.log('forgot-password');
    try {
      // Validate the affiliate LMS URL
      if (!Utility.checkZendUri(forgotPassword?.affiliateLmsUrl)) {
        throw new BadRequestException('Please provide a valid affiliate URL.');
      }

      // Validate the email format
      if (!forgotPassword?.email || !Utility.isValidEmail(forgotPassword?.email.trim())) {
        throw new BadRequestException('Please provide valid email id.');
      }

      // Call the service method to handle forgot password logic
      const response = await this.enterpriseLMSService.forgotPassword(forgotPassword);

      // Handle specific error cases based on the response
      if (response instanceof Error) {
        switch (response?.message) {
          case 'InvalidGroupId':
            throw new BadRequestException('Please provide valid group id.');
          case 'UserNotFoundException':
            throw new BadRequestException('User account does not exist for the specified email-id.');
          case 'BlockedUser':
            throw new BadRequestException('User account has been blocked.');
          case 'UserDisabled':
            throw new BadRequestException('User account has been blocked.');
          case 'NotAGroupMember':
            throw new BadRequestException('User is not the member of group.');
          case 'Unauthorized access request.':
            throw new UnauthorizedException(response.message);
          default:
            throw new BadRequestException('some error occurred while sending reset password email.');
        }
      }

      // If the response is successful, send a success message
      if (response) {
        res.status(HttpStatus.OK).json({
          type: 'success',
          msg: 'Reset password link sent on your email.',
        });
      }

      return response;
    } catch (error: any) {
      // Log the error details for debugging
      APILog.error('forgotPassword', {
        METHOD: this.constructor.name + '@' + this.forgotPassword.name,
        MESSAGE: error.message,
        REQUEST: forgotPassword,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      if (error instanceof UnauthorizedException) {
        throw new UnauthorizedException(error.message);
      }

      // Throw appropriate exceptions based on the error type
      if (error instanceof BadRequestException) {
        throw new BadRequestException(error.message);
      } else {
        throw new InternalServerErrorException();
      }
    }
  }

  /**
   * Endpoint to reset the user's password.
   * @param resetPassword - DTO containing user details and new password.
   * @returns A success message if the password is reset successfully.
   * @throws BadRequestException if the token is expired, client validation fails, or other validation errors occur.
   * @throws InternalServerErrorException for unexpected errors.
   * 
   * This api is consumed by Paperclip
   */
  @ApiProperty()
  @ApiResponse({ status: 200, description: 'Password reset successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiBody({ type: ResetPasswordDto, description: 'User data for resetting password' })
  @HttpCode(HttpStatus.OK)
  @UseFilters(HttpExceptionFilter)
  @UsePipes(new ValidationPipe())
  @Post('/reset-password')
  async resetPassword(
    @Body()
    resetPassword : ResetPasswordDto,
    @Res() res
  ) {
    APILog.log('reset-password');
    try {
      // Call the service method to reset the password
      const user = await this.enterpriseLMSService.resetPassword(resetPassword);

      if (user instanceof Error) {
        switch (user?.message) {
          case 'InvalidGroupId':
            throw new BadRequestException('Please provide valid group id.');            
          case 'BlockedUser':
            throw new BadRequestException('User account has been blocked.');
          case 'InvalidToken':
            throw new BadRequestException('Invalid password reset URL OR it is expired.');
          case 'NotMemberOfGroup':
            throw new BadRequestException('User is not a member of the group.');
          case 'InvalidPasswdUrl':
            throw new BadRequestException('Invalid password reset URL.');
          default:
            throw new BadRequestException('Some error occurred while resetting your password.');
        }
      }
      // Return a success response with user roles
      res.json(user)
    } catch (error: any) {
      // Log the error details for debugging
      APILog.error('reset password', {
        METHOD: this.constructor.name + '@' + this.resetPassword.name,
        MESSAGE: error.message,
        REQUEST: resetPassword,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      if (error instanceof UnauthorizedException){
        throw new UnauthorizedException(error.message);
      }

      // Throw appropriate exceptions based on the error type
      throw error instanceof BadRequestException
        ? new BadRequestException(error.message)
        : new InternalServerErrorException();
    }
  }

  /**
   * Endpoint to add a member to a group with a specific role.
   * @param reqBody - The request body containing user and group details.
   * @param res - The HTTP response object.
   * @returns A success message if the member is added successfully.
   * @throws BadRequestException for validation errors or if the operation fails.
   * @throws InternalServerErrorException for unexpected errors.
   */
  @ApiProperty()
  @ApiResponse({ status: 200, description: 'Member added to group with role successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiBody({ type: AddMemberToGroupWithRoleDto, description: 'User and group data for adding a member with a role' })
  @HttpCode(HttpStatus.OK)
  @UseFilters(HttpExceptionFilter)
  @UsePipes(new ValidationPipe())
  @Post('/add-member-to-group-with-role')
  async addMemberToGroupWithRole(
    @Body() reqBody: AddMemberToGroupWithRoleDto,
    @Res() res: Response
  ) {
    APILog.log('add-member-to-group-with-role');
    try {
      const userApiService = await this.helperService.get<UserApiInternalService>(UserApiInternalService);
      const response = await userApiService.addMemberToGroupWithRole(reqBody);
      
      return res.status(HttpStatus.OK).json(response);
    } catch (error: any) {
      APILog.error('add member to group with role', {
        METHOD: this.constructor.name + '@' + this.addMemberToGroupWithRole.name,
        MESSAGE: error.message,
        REQUEST: reqBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      return res.status(HttpStatus.OK).json({
        type: 'error',
        msg: 'Some error occurred while assigning role to user.'
      });
    }
  }
  /**
   * Endpoint to set the learner's password.
   * @param setLearnerPasswordDto - DTO containing user details and new password.
   * @param req - The HTTP request object.
   * @param res - The HTTP response object.
   * @returns A success message if the password is set successfully.
   * @throws BadRequestException if the user is not found, not a group member, or other validation errors occur.
   * @throws InternalServerErrorException for unexpected errors.
   */
  @ApiProperty()
  @ApiResponse({ status: 200, description: 'Password set successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiBody({ type: SetLearnerPasswordDto, description: 'User data for setting learner password' })
  @HttpCode(HttpStatus.OK)
  @UseFilters(HttpExceptionFilter)
  @UsePipes(new ValidationPipe())
  @Post('/set-learner-password')
  async setLearnerPassword(
    @Body() setLearnerPasswordDto: SetLearnerPasswordDto,
    @Res() res: Response,
  ) {
    APILog.log('set-learner-password');
    try {
      // Retrieve the UserService instance from the helper service
      const userService = await this.helperService.get<UserService>(UserService);

      // Call the service method to set the learner's password
      const response = await userService.setLearnerPassword(setLearnerPasswordDto);

      // Handle specific error cases based on the response
      if (response instanceof Error) {
        switch (response?.message) {
          case 'UserNotFoundException':
            throw new BadRequestException('User account does not exist for the specified email-id.');
          case 'UserNotGroupMember':
            throw new BadRequestException('User is not a member of the group.');
          default:
            throw new BadRequestException('Some error occurred while changing password.');
        }
      }

      // If the response is successful, send a success message
      if (response) {
        res.status(HttpStatus.OK).json({
          type: 'success',
          msg: 'Password has been reset successfully. You will be logged out from all web devices. Kindly login again',
        });
      }
    } catch (error: any) {
      // Log the error details for debugging
      APILog.error('set learner password', {
        METHOD: this.constructor.name + '@' + this.setLearnerPassword.name,
        MESSAGE: error.message,
        REQUEST: setLearnerPasswordDto,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      // Throw appropriate exceptions based on the error type
      throw error instanceof BadRequestException
        ? new BadRequestException(error.message)
        : new InternalServerErrorException();
    }
  }

  /**
   * Endpoint to change the password for a user.
   * @param changePasswordDto - DTO containing user details and new password.
   * @param req - The HTTP request object.
   * @param res - The HTTP response object.
   * @returns A success message if the password is changed successfully.
   * @throws BadRequestException if the user is not found, not authorized, or other validation errors occur.
   * @throws InternalServerErrorException for unexpected errors.
   * This api is consumed by Paperclip.
   */
  @ApiProperty()
  @ApiResponse({ status: 200, description: 'Password changed successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiBody({ type: ChangePasswordDto, description: 'User data for changing password' })
  @HttpCode(HttpStatus.OK)
  @UseFilters(HttpExceptionFilter)
  @UsePipes(new ValidationPipe())
  @Post('/change-password')
  async changePassword(
    @Body() changePasswordDto: ChangePasswordDto,
    @Res() res: Response,
  ) {
    APILog.log('change-password');
    try {
      // Retrieve the UserService instance from the helper service
      const userService = await this.helperService.get<UserService>(UserService);

      // Call the service method to change the password
      const response = await userService.changePassword(changePasswordDto);
      // Handle specific error cases based on the response
      if (response instanceof Error) {
        let msg 
        switch (response?.message) {
          case 'UserNotFoundException':
            msg = 'User account does not exist for the specified email-id.';
            break;
          case 'NotAuthorizedException':
            msg = 'Old password is incorrect.';
            break;
          case 'UserNotGroupMember':
            msg = 'User is not a member of the group.';
            break;
          case 'GroupIdMustBePositive':
            msg = 'Group ID must be positive';
            break;
          default:
            msg = 'Some error occurred while changing password.';
        }
        return res.status(HttpStatus.OK).json({
          type: 'error',
          msg,
        });
      }

      // If the response is successful, send a success message
      if (response) {
        res.status(HttpStatus.OK).json({
          type: 'success',
          msg: 'Password has been reset successfully. You will be logged out from all web devices. Kindly login again',
        });
      }
    } catch (error: any) {
      // Log the error details for debugging
      APILog.error('change password', {
        METHOD: this.constructor.name + '@' + this.changePassword.name,
        MESSAGE: error.message,
        REQUEST: changePasswordDto,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      // Throw appropriate exceptions based on the error type
      throw error instanceof BadRequestException
        ? new BadRequestException(error.message)
        : new InternalServerErrorException();
    }
  }

  /* This api is consumed by Paperclip
  */
  @ApiProperty()
  @ApiResponse({ status: 200, description: 'Setup manager account process initiated successfully' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 500, description: 'Internal Server Error' })
  @ApiBody({ type: SetupManagerAccountDto, description: 'Request data for setup manager account' })
  @UseFilters(HttpExceptionFilter)
  @UsePipes(new ValidationPipe())
  @Post('/setup-manager-account')
  async setupManagerAccount(@Body() reqBody: SetupManagerAccountDto) {
    try {
        // Validate the request body using ValidationPipe
        const validatedReqBody = await new ValidationPipe().transform(reqBody, {
          type: 'body',
          metatype: SetupManagerAccountDto,
        });

        const response = await this.enterpriseLMSService.setupManagerAccount(validatedReqBody);
        if (response instanceof Error){
          switch (response?.message) {
            case 'UserNotFoundException':
              throw new BadRequestException('Please provide valid user Id.');
            case 'UserNotInGroup':
              throw new BadRequestException('User is not the member of group.');
            case 'InvalidAffiliateUrl':
              throw new BadRequestException('Please provide a valid affiliate URL.');
            case 'InvalidToken':
              throw new BadRequestException('Invalid password reset URL.');
            case 'Unauthorized access request.':
              throw new UnauthorizedException(response.message);
            default:
              throw new BadRequestException('Some error occurred while resetting your password.');
          }
        }
        return response;
    } catch (error: any) {
       // Log the error details for debugging
       APILog.error('setupManagerAccount', {
        METHOD: this.constructor.name + '@' + this.setupManagerAccount.name,
        MESSAGE: error.message,
        REQUEST: reqBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      if(error instanceof UnauthorizedException){
        throw new UnauthorizedException(error.message);
      }
      // Throw appropriate exceptions based on the error type
      throw error instanceof BadRequestException
        ? new BadRequestException({message: error.message, isData: {data:null}})
        : new InternalServerErrorException();
    }
  }
}
