import { BadRequestException, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import { Logger } from '../../logging/logger';
import { User } from '../../db/mongo/schema/user/user.schema';
import { IRoleRepository, RoleRepository } from '../../user/repositories/role/role.repository';
import { IUserRepository, UserRepository } from '../../user/repositories/user/user.repository';
import { UpdateUserProfileDto } from '../dto/update-user-profile.dto';
import { Utility } from '../../common/util/utility';
import { DeviantsService } from '../../common/services/communication/deviants/deviants.service';
import { GdprRequestDto } from '../dto/gdpr-request.dto';
import { FirebaseService } from '../../db/firebase/firebase.service';
import { EnterpriseLmsPreferences } from '../../db/mysql/entity/enterprise-lms-preferences.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EnterpriseLmsSetting } from '../../db/mysql/entity/enterprise-lms-settings.entity';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
import { ForgotPasswordB2B } from '../../common/typeDef/auth.type';
import { AuthTokenHelper } from '../../auth/helper/auth.tokenhelper';

enum possibleVisibilitySettings {
  Simplilearn = 0,
  Public = 1,
}
export class UserApiV1Helper {
  @Inject() private readonly helperService: HelperService;
  @Inject() private readonly configService: ConfigService;
  @InjectRepository(EnterpriseLmsPreferences) private cloud6EnterpriseLmsPreferences: Repository<EnterpriseLmsPreferences>;
  @InjectRepository(EnterpriseLmsSetting) private cloud6EnterpriseLmsSetting: Repository<EnterpriseLmsSetting>;


  /**
   * Updates the Engagex role for a user.
   * - Removes any existing Engagex roles not in allowedEngagexRoles.
   * - Assigns the new Engagex role if valid and not already assigned.
   * @param userId - The user's UID.
   * @param updatedRole - The new Engagex role to assign.
   * @returns The updated user object or throws error.
   */
  async updateEngagexRole(
    userId: number,
    updatedRole: string
  ): Promise<Partial<User> | Error> {
    try {
      const allowedEngagexRoles = this.configService.get('allowedEngagexRoles') as Record<string, string>; 
      const listOfAllowedUserRoles = Object.values(allowedEngagexRoles);

      // Validate new role
      if (!listOfAllowedUserRoles.includes(updatedRole)) {
        throw new BadRequestException('RoleUserInvalid');
      }

      // Check if user exists
      const [userRepository, roleRepository, userHelper] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
         this.helperService.get<IRoleRepository>(RoleRepository),
        this.helperService.getHelper('UserHelper'),
      ]);

      const user = await userRepository.findByUID(userId);

      if (!user) {
        throw new BadRequestException('RoleUserNotExist');
      }

      // Fetch current roles from sentinel
      const currentRoles = await userHelper.getAllRolesofUser(userId);
      //fetching current user roles details from RoleRepository
      const newRoleDoc = await roleRepository.findOne({ roleName: updatedRole });

      //intersecting current roles with allowed roles
      const intersectionRoles = user.roles?.filter((role) => 
        !listOfAllowedUserRoles.includes(role.roleName)
      );

      if (intersectionRoles.length > 0) {
        intersectionRoles.push(newRoleDoc);
      }

      // Build map roleName -> rid to find the rid of the provided role name
      const allowedRoleIds: Record<string, string> = {};
      for (const [rid, roleName] of Object.entries(allowedEngagexRoles)) {
        allowedRoleIds[roleName] = rid;
      }

      const updatedRid = allowedRoleIds[updatedRole];

      // Check if role already assigned
       if (currentRoles.some(r => r.rid === updatedRid)) {
        throw new BadRequestException('RoleAlreadyAssign');
      }

      // Find existing engagex role for this user
      const existingEngagexRole = currentRoles.find(r =>
        Object.keys(allowedEngagexRoles).includes(r.rid.toString())
      );

      if (existingEngagexRole) {
        // Update the engagex role
        await userHelper.updateExistingRole(userId, updatedRid, existingEngagexRole.rid);
      } else {
        // Insert new engagex role
        await userHelper.createEngagexRole(userId, updatedRid);
      }
      const roleMapping = await userHelper.prepareRoleListForDrupalSync(intersectionRoles);
      if (this.configService.get('enableDrupalSync')) {
        await userHelper.syncUserDataWithMySQLDrupal({ uid: userId, roles: roleMapping });
      }
      
    return await userRepository.findOneAndUpdate(
        { uid: Number(userId) },
        { roles: intersectionRoles }
      );
    } catch (error: any) {
      Logger.error('updateEngagexRole', {
        METHOD: this.constructor.name + '@' + this.updateEngagexRole.name,
        MESSAGE: error.message,
        REQUEST: { userId, updatedRole },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async deleteAccountByUidForGDPR(user: Partial<User>) {
    const response = {
      status: 'failed',
      msg: 'Invalid request.'
    };

    try {
      // Validate user object
      if (!user || !user.uid) {
        return response;
      }

      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const gdprRequestHelper = await this.helperService.getHelper('GdprRequestHelper');
      const deviantsService = await this.helperService.get<DeviantsService>(DeviantsService);
      const userHelper = await this.helperService.getHelper('UserHelper');

      // Check if user is already deactivated
      if (user.status === 0) {
        return {
          status: 'failed',
          msg: 'User already Deactivated'
        };
      }

      // Update MongoDB user status
      const updateResult = await userRepository.findOneAndUpdate(
        { uid: user.uid, status: 1 },
        { status: 0 }
      );

      if (!updateResult) {
        Logger.debug('deleteAccountByUidForGDPR', {
          METHOD: this.constructor.name + '@deleteAccountByUidForGDPR',
          MESSAGE: 'GDPR Request failed',
          REQUEST: { uid: user.uid },
          TIMESTAMP: new Date().getTime(),
        });
        return {
          status: 'failed',
          msg: 'Some error occurred. Please try again after sometime.'
        };
      }

      // Update MySQL sentinel_users status
      const mysqlUpdateResult = await userHelper.updateCloud6SentinelByUidOrMail({
        uid: user.uid,
        status: 0  // Set status to 0 for deactivated
      });

      if (!mysqlUpdateResult) {
        return {
            status: 'failed',
            msg: 'Failed to update user status in database.'
        };
    }

      // Add GDPR request log
      const logId = await gdprRequestHelper.addRequest(Number(user.uid), 'delete');
      if (!logId) {
        Logger.warn('deleteAccountByUidForGDPR', {
          METHOD: this.constructor.name + '@deleteAccountByUidForGDPR',
          MESSAGE: 'Failed to create GDPR request log',
          REQUEST: { userId: user.uid },
          TIMESTAMP: new Date().getTime(),
        });
      }

      // Push message to queue using DeviantsService
      const queueData = {
        Email: user.email,
        Q: this.configService.get('qNameGdprUserDelete')
      };

      const queueResult = await deviantsService.pushMessageToQueue(queueData);
      if (queueResult.status !== 'success') {
        Logger.error('deleteAccountByUidForGDPR', {
          METHOD: this.constructor.name + '@deleteAccountByUidForGDPR',
          MESSAGE: 'Failed to push message to queue',
          REQUEST: { userId: user.uid, queueData },
          RESPONSE: queueResult,
          TIMESTAMP: new Date().getTime(),
        });
      }

      return {
        status: 'success',
        msg: 'User account has been Deactivated successfully.'
      };

    } catch (error: any) {
      Logger.error('deleteAccountByUidForGDPR', {
        METHOD: this.constructor.name + '@deleteAccountByUidForGDPR',
        MESSAGE: error.message,
        REQUEST: { userId: user.uid },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return {
        status: 'failed',
        msg: 'Some error occurred. Please try again after sometime.'
      };
    }
  }
  async updateUserOnboardingData(data: Partial<UpdateUserProfileDto>) :Promise <Object | Error>{
    let response = {type: "error", msg: 'Error occurred while updating profile. Please try again.'};
    try{
      const [userRepository, profileHelper, userHelper, userMgmtUtilityHelperInstance] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.getHelper('ProfileHelper'),
        this.helperService.getHelper('UserHelper'),
        this.helperService.getHelper('UserMgmtUtilityHelper')
      ]);
      const titleGenderList : { title : any , gender : any , industry_disable : any } = await userMgmtUtilityHelperInstance.getTitleGenderList();      
      let updateUserObj = {};
      // Map fields from input data to user object
      const fieldsMapping: Record<string, any> = {
        display_name: data?.first_name && data?.last_name 
          ? `${data.first_name} ${data.last_name}` 
          : '',
        profile_visibility: data?.visibility !== undefined 
          ? possibleVisibilitySettings[data.visibility] 
          : '',
        title: data?.title,
        first_name: data?.first_name,
        last_name: data?.last_name,
        gender: titleGenderList.gender[data?.gender] ? titleGenderList.gender[data?.gender] : data?.gender,
        dob: Utility.convertToTimestampUnix(data?.dob),
        linkedin_url: data?.linkedin_url,
        timezone: data?.timezone,
        highest_level_of_education: data?.highest_level_of_education,
        where_are_you_in_career: data?.where_are_you_in_career,
        objective_taking_course: data?.objective_of_taking_course,
        training_funded_by: data?.training_funded_by,
        updated_on: Date.now(),
      };

      // Prepare additional data if industry is provided
      if (data?.industry) {
        const taxonomyData = await profileHelper.prepareProfileData({ industry: data.industry });
        if (taxonomyData?.work_experience) {
          fieldsMapping.work_experience = taxonomyData.work_experience;
        }
      }

      // Filter out undefined values and create the update object
      // the below is mongo profile data payload
      updateUserObj = Object.entries(fieldsMapping)
        .reduce((acc, [key, value]) => {
          if (value !== undefined) {
        acc[key] = value;
          }
          return acc;
        }, {});

      // the below only has static profile data to be updated in mysql
      const userSqlObj = Object.assign({}, updateUserObj);
      userSqlObj['uid'] = data?.user_id;

      // removing work_experience key and other taxonomy data as it is handled in handleOobtaxonomyData.
      delete userSqlObj['work_experience'];
      delete userSqlObj['where_are_you_in_career']; 
      delete userSqlObj['highest_level_of_education'];
      delete userSqlObj['objective_taking_course']; 

      
      const [updatedUser, updateSentinelUsers, updateProfessionalData] = await Promise.all([

        // Update MongoDB user profile Complete data
        userRepository.findOneAndUpdate(
          { uid: Number(data?.user_id) },
          updateUserObj
        ),

        // Update MySQL (Static data)
        userHelper.updateCloud6SentinelByUidOrMail(userSqlObj),

        // Update MySQL (taxonomy data) 
        profileHelper.handleOobtaxonomyData({
          uid: data?.user_id,
          industry: data?.industry,
          where_are_you_in_career: data?.where_are_you_in_career,
          highest_level_of_education: data?.highest_level_of_education,
          objective_of_taking_course: data?.objective_of_taking_course,
        })
      ]);

      if (updatedUser && updateSentinelUsers && updateProfessionalData) {
        // drupal profile table sync
        if (this.configService.get('enableDrupalSync')) {
           const edit_type = ["professional","academics","outcome"];
           const updatedUserData = await profileHelper.fetchProfileData(data?.user_id , 1);
           await userHelper.syncUserDataWithMySQLDrupal({...userSqlObj, uid: data?.user_id});
           profileHelper.syncTaxonomyDataWithMySQLDrupal(updatedUserData, edit_type); 
        }

        response.type = 'success'
        response.msg = 'Successfully updated user profile.'
      } else {
        throw new BadRequestException('Error while updating data.')
      }
      return response;
    } catch (error: any){
      Logger.error('updateUserOnboardingData', {
        METHOD: this.constructor.name + '@' + this.updateUserOnboardingData.name,
        MESSAGE: error.message,
        REQUEST: { data: data },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return response;
    }
  }

  /**
   * Updates the GDPR delete status for a user
   * @param user - The user object containing uid and status
   * @param responseData - The response data from the GDPR deletion process
   * @returns Object containing status and message
   */
  async updateGdprDeleteStatus(user: Partial<User>, responseData: string): Promise<Boolean | Error> {
    try {
     // Get GdprRequestHelper to update the status
      const gdprRequestHelper = await this.helperService.getHelper('GdprRequestHelper');
      const updateResult = await gdprRequestHelper.updateActionStatus(
        Number(user.uid),
        'delete',
        responseData
      );

      if (updateResult.status === 'success') {
        return true;
      }

      return false;
    } catch (error: any) {
      Logger.error('updateGdprDeleteStatus', {
        METHOD: `${this.constructor.name}@${this.updateGdprDeleteStatus.name}`,
        MESSAGE: error.message,
        REQUEST: { uid: user.uid, responseData },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return false;
    }
  }
  /**
 * Validates GDPR API request data against required keys
 * @param keys - Array of required keys in format 'key' or 'key.subkey'
 * @param data - Request data object to validate
 * @returns Array of error messages, empty if validation passes
 */
async gdprApiValidation(keys: string[], data: Record<string, any>): Promise<string[]> {
  if (!data || Object.keys(data).length === 0) {
    return ['params are empty'];
  }

  const errors: string[] = [];

  for (const key of keys) {
    const columns = key.split('.');

    if (!(columns[0] in data)) {
      errors.push(`${columns[0]} is empty.`);
    } else if (columns[1] && (!data[columns[0]] || !data[columns[0]][columns[1]])) {
      errors.push(`${columns[0]}.${columns[1]} is empty.`);
    }
  }

  return errors;
}
/**
 * Handles GDPR export request
 * @param requestBody - The request body containing user details and action
 * @returns Promise with status, data, message, and optional error
 */
async gdprExport(requestBody: GdprRequestDto): Promise<{ status: number; data: string; message: string; error?: string }> {
  try {
    // Check if user exists
    const userHelper = await this.helperService.getHelper('UserHelper');
    const userExists = await userHelper.getCloud6SentinelUserByUidOrMail({ uid: requestBody.usid });
    
    if (!userExists.status) {
      return { status: 0, data: '', message: 'User not found' };
    }

    // Get Firebase service instance
    const firebaseInstance = await this.helperService.get<FirebaseService>(FirebaseService);

    // Use gdprAddRequestCouch for Firebase operations
    const firebaseResult = await this.gdprAddRequestCouch(requestBody, firebaseInstance);
    if (firebaseResult.status === 0) {
      return { status: 0, data: '', message: firebaseResult.message };
    }

    // Add request to MySQL using UserHelper
    const currentTime = Math.floor(Date.now() / 1000);
    const inputParams = {
      uid: requestBody.usid,
      action: 'export',
      state: 'inqueue',
      requestedOn: currentTime,
      modifiedOn: currentTime,
    };

    const addResultMySQL = await userHelper.createGdprRequest(inputParams);

    if (!addResultMySQL) {
      return { status: 0, data: '', message: 'Unable to save in couch' };
    }

    return { status: 1, data: '', message: 'success' };
  } catch (error: any) {
    console.error('Error in gdprExport:', error);
    return { status: 0, data: '', message: 'Unable to save in couch' };
  }
}

async gdprAddRequestCouch(requestBody: GdprRequestDto, firebaseInstance: FirebaseService): Promise<{ status: number; data: string; message: string }> {
  try {
    // Get existing document or create new one
    const path = firebaseInstance.getGdprPath(requestBody.useremail, requestBody.usid);
    let document = await firebaseInstance.find(path);

    if (!document) {
      document = firebaseInstance.createGdprDocument();
    }

    document = firebaseInstance.addGdprExportAttempt(document);
    document = firebaseInstance.setGdprExportStatus(document, 1);

    // Save to Firebase
    const firebaseResult = await firebaseInstance.save(path, document);

    if (!firebaseResult) {
      return { status: 0, data: '', message: 'unable to set start export status in Document' };
    }

    return { status: 1, data: '', message: 'success' };
  } catch (error: any) {
    console.error('Error in gdprAddRequestCouch:', error);
    return { status: 0, data: '', message: 'Unable to save in couch' };
  }
}


/**
 * Validates the basic profile information provided by the user.
 * @param inputParams - The input parameters containing user profile data.
 * @param showProfile - Flag to indicate if profile-specific fields should be validated.
 * @returns A promise resolving to true if validation passes, otherwise throws an error.
 */
async validateEditProfileBasic(inputParams: Record<string, any>, showProfile = true): Promise<boolean> {
  try {
    if (!inputParams.title) {
      throw new BadRequestException('Please provide title.');
    }
    if (!inputParams.first_name) {
      throw new BadRequestException('Please provide first name.');
    }
    const nameRegex = /^[a-zA-Z\s\-_]+$/i;
    if (!nameRegex.test(inputParams.first_name)) {
      throw new BadRequestException('Please provide valid first name.');
    }
    if (!inputParams.last_name) {
      throw new BadRequestException('Please provide last name.');
    }
    if (!nameRegex.test(inputParams.last_name)) {
      throw new BadRequestException('Please provide valid last name.');
    }
    if (showProfile && !inputParams.dob) {
      throw new BadRequestException('Please provide date of birth.');
    }
    if (showProfile && !inputParams.gender) {
      throw new BadRequestException('Please provide gender.');
    }
    if (inputParams.user_linkedin_url && !inputParams.user_linkedin_url.includes('linkedin.com')) {
      throw new BadRequestException('Please provide valid Linkedin URL.');
    }
    if (!inputParams.training_funded_by) {
      throw new BadRequestException('Please provide training funded.');
    }
    return true;
  } catch (error: any) {
    Logger.error('validateEditProfileBasic', {
      METHOD: `${this.constructor.name}@validateEditProfileBasic`,
      MESSAGE: error.message,
      REQUEST: inputParams,
      TIMESTAMP: new Date().getTime(),
    });
    throw error;
  }
}

/**
 * Validates the desired learning outcome provided by the user.
 * @param inputParams - The input parameters containing user profile data.
 * @returns A promise resolving to true if validation passes, otherwise throws an error.
 */
async validateEditProfileOutcome(inputParams: Record<string, any>): Promise<boolean> {
  try {
    if (!inputParams.objective_of_taking_course) {
      throw new BadRequestException('Please provide desired learning outcome.');
    }
    return true;
  } catch (error: any) {
    Logger.error('validateEditProfileOutcome', {
      METHOD: `${this.constructor.name}@validateEditProfileOutcome`,
      MESSAGE: error.message,
      REQUEST: inputParams,
      TIMESTAMP: new Date().getTime(),
    });
    throw error;
  }
}

/**
 * Validates the academic information provided by the user.
 * @param inputParams - The input parameters containing user profile data.
 * @returns A promise resolving to true if validation passes, otherwise throws an error.
 */
async validateEditProfileAcademics(inputParams: Record<string, any>): Promise<boolean> {
  try {
    if (!inputParams.highest_level_of_education) {
      throw new BadRequestException('Please provide highest level of education.');
    }
    return true;
  } catch (error: any) {
    Logger.error('validateEditProfileAcademics', {
      METHOD: `${this.constructor.name}@validateEditProfileAcademics`,
      MESSAGE: error.message,
      REQUEST: inputParams,
      TIMESTAMP: new Date().getTime(),
    });
    throw error;
  }
}

/**
 * Validates the professional information provided by the user.
 * @param inputParams - The input parameters containing user profile data.
 * @returns A promise resolving to true if validation passes, otherwise throws an error.
 */
async validateEditProfileProfessional(inputParams: Record<string, any>): Promise<boolean> {
  try {
    if (!inputParams.where_are_you_in_career) {
      throw new BadRequestException('Please provide total years of experience.');
    }
    return true;
  } catch (error: any) {
    Logger.error('validateEditProfileProfessional', {
      METHOD: `${this.constructor.name}@validateEditProfileProfessional`,
      MESSAGE: error.message,
      REQUEST: inputParams,
      TIMESTAMP: new Date().getTime(),
    });
    throw error;
  }
}

/**
 * Validates the contact information provided by the user.
 * @param inputParams - The input parameters containing user profile data.
 * @param validity - Optional validity checks for phone number and country code.
 * @param showProfile - Flag to indicate if profile-specific fields should be validated.
 * @returns A promise resolving to true if validation passes, otherwise throws an error.
 */
async validateEditProfileContact(
  inputParams: Record<string, any>,
  validity: string | null = null,
  showProfile = true,
): Promise<boolean> {
  try {
    if (validity === 'Yes') {
      const phoneValidation = Utility.validatePhoneNumber(inputParams.phone_no);
      if (!phoneValidation) {
        throw new BadRequestException('Please provide valid phone no. (atleast 7 digits required)');
      }
      const countryCodeValidation = Utility.validateCountryCode(inputParams.country_code);
      if (!countryCodeValidation) {
        throw new BadRequestException('Please select the country code for your phone number.');
      }
    }
    if (showProfile && !inputParams.country_of_residence) {
      throw new BadRequestException('Please provide valid Country of residence.');
    }
    if (showProfile && !inputParams.state) {
      throw new BadRequestException('Please provide valid State.');
    }
    const locationRegex = /^[a-zA-Z0-9\s\-_]+$/i;
    if (inputParams.location && !locationRegex.test(inputParams.location)) {
      throw new BadRequestException('Please provide valid location.');
    }
    if (!inputParams.timezone) {
      throw new BadRequestException('Please provide valid timezone.');
    }
    return true;
  } catch (error: any) {
    Logger.error('validateEditProfileContact', {
      METHOD: `${this.constructor.name}@validateEditProfileContact`,
      MESSAGE: error.message,
      REQUEST: inputParams,
      TIMESTAMP: new Date().getTime(),
    });
    throw error;
  }
}

async getLmsSetting( identifier : string , gid: string) {

  if(!gid) {
    return null;
  }
  const preferences_id = await this.cloud6EnterpriseLmsPreferences.findOne({ where : { preference_name : identifier }})
  const lmsSettings = await this.cloud6EnterpriseLmsSetting.findOne({ where : { group_id : Number(gid) , preference_id : Number(preferences_id.id)} });
  return lmsSettings;
}

async getHelpSupportSettings(gid: number): Promise<{ show: string; url: string }> {
  const helpSupportIdentifier = this.configService.get('helpAndSupportIdentifier')
  const setting = await this.getLmsSetting(helpSupportIdentifier, String(gid));
  return {
    show: setting?.value || 'Yes',
    url: setting?.custom_value || 'Yes',
  };
}

async isShowMyResources(gid: string): Promise<string> {
  const resourcesIdentifier = this.configService.get('myResourcesIdentifier')
  const setting = await this.getLmsSetting(resourcesIdentifier, gid);
  return setting?.value || 'Yes';
}

async isShowCommunity(gid: string): Promise<string> {
  const communityIdentifier = this.configService.get('communityIdentifier')
  const setting = await this.getLmsSetting(communityIdentifier, gid);
  return setting?.value || 'Yes';
}

async getViewSettings(userType: string, gid: string) {
  const [helpSupport, myResources, community] = await Promise.all([
    this.getHelpSupportSettings(Number(gid)),
    this.isShowMyResources(gid),
    this.isShowCommunity(gid),
  ]);

  return {
    isShowResources: myResources,
    isShowCommunity: community,
    showHelpSupport: userType === this.configService.get('freeUserType') ? 'No' : helpSupport.show,
    helpSupportUrl:  helpSupport.url,
  };
}

/**
 * Loads LMS view-related data based on the provided group ID (lgid).
 */
async loadViewData(lgid: number) {
  try {
    const defaultLrsApplicationId = await this.configService.get('defaultLrsApplicationId'); 
    const isValidGid =
      lgid !== undefined &&
      lgid !== null &&
      !isNaN(lgid) &&
      Number(lgid) !== defaultLrsApplicationId;

    let showCertificateTab = 0;
    let isEnableSamlAuthentication = 0;
    let lmsUrl = '';
    let atpLogoUrl = '';
    let userEmail = '';
    let affiliateId = '';
    let groupName = '';
    let userId: number | null = null;

    if (isValidGid) {
      const enterpriseService = await this.helperService.get<EnterpriseService>(EnterpriseService);
      const groupDetails = await enterpriseService.getGroupByGid(lgid);
      const lmsGroupUrl = await enterpriseService.getGroupDomainByGid({gid : lgid});
      // Service returns array directly, not wrapped in data property
      const groupData = Array.isArray(groupDetails) && groupDetails.length ? groupDetails[0] : null;
      if (groupData) {
        showCertificateTab = groupData.enable_certificates || 0;
        isEnableSamlAuthentication = Number(groupData.enable_saml_authentication) || 0;
        lmsUrl = lmsGroupUrl || '';
        // Use same field as Cloud6: logo_url
        atpLogoUrl = groupData.logo_url || '';
        userEmail = groupData.billing_email || '';
        userId = groupData.userId;
        affiliateId = groupData.affiliateId || '';
        groupName = groupData.displayName || '';

        // Validate logo URL using existing utility patterns
        if (!Utility.isEmpty(atpLogoUrl)) {
          const cleaned = atpLogoUrl.trim().toLowerCase();
          if (cleaned === 'undefined' || cleaned === 'null' || cleaned === '/' || !atpLogoUrl.startsWith('http')) {
            atpLogoUrl = '';
          }
        }
      }
    }
    
    const isPgLearner = await this.setLearnerType(userId, userEmail, 0);

    return {
      showCertificateTab,
      isEnableSamlAuthentication,
      lmsUrl,
      atpLogoUrl,
      isPgLearner,
      affiliateId,
      groupName
    };
  } catch (error: any) {
    Logger.error('loadViewData', {
      METHOD: `${this.constructor.name}@loadViewData`,
      MESSAGE: error.message,
      REQUEST: lgid,
      TIMESTAMP: new Date().toISOString(),
    });
    throw error;
  }
}


/**
 * Determines the learner type (PG or selected PG) based on the flag.
 */
async setLearnerType(
  userId: number,
  userEmail: string,
  flag: 0,
): Promise<any> {
  try{
  if (!userId || !userEmail) {
    return null;
  }

   let firebaseInstance = await this.helperService.get<FirebaseService>(FirebaseService);

  const helpAndSupportResult =
      flag === 0
        ? await firebaseInstance.getPgLearnerFromCache(userId, userEmail)
        : await firebaseInstance.getSelectedPgLearnerFromCache(userId, userEmail);
  if (helpAndSupportResult?.status === 'success') {
    return helpAndSupportResult.data;
  }
  return false;
}catch (error: any) {
  Logger.error('setLearnerType', {
    METHOD: `${this.constructor.name}@setLearnerType`,
    MESSAGE: error.message,
    TIMESTAMP: new Date().getTime(),
  });
  throw error;
}}

async userPasswordRehash(forgotPasswordDto: ForgotPasswordB2B, userData: Partial<User>){
  try {
    const tokenHelper = await this.helperService.getHelper<AuthTokenHelper>(AuthTokenHelper);

    const data = `${userData.login}${userData.uid}`;
    const key = `${this.configService.get('drupal_hash_salt')}${userData.password}`;
    const token = tokenHelper.drupalHmacBase64(data, key);
    return token;
  } catch(error:any) {
    Logger.error('userPasswordRehash', {
      METHOD: `${this.constructor.name}@userPasswordRehash`,
      MESSAGE: error.message,
      REQUEST: { forgotPasswordDto },
      TIMESTAMP: new Date().getTime(),
    });
    throw new BadRequestException(error.message);
  }
}

}
