import { GdprRequestHelper } from './gdpr-request.helper';
import { GdprRequest } from '../../db/mysql/entity/gdpr-request.entity';
import { Repository } from 'typeorm';
import { HelperService } from '../../helper/helper.service';
import { Logger } from '../../logging/logger';
import { Test } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';

jest.mock('../../logging/logger');

describe('GdprRequestHelper', () => {
  let helper: GdprRequestHelper;
let mockHelperService: any;
let mockRepository: Partial<Repository<GdprRequest>>;
let mockUserHelper: any;

beforeEach(async () => {
  mockUserHelper = {
    createGdprRequest: jest.fn(),
  };

  mockHelperService = {
    getHelper: jest.fn().mockResolvedValue(mockUserHelper),
  };

  mockRepository = {
    // optionally mock TypeORM repository methods if used in GdprRequestHelper
  };

  const moduleRef = await Test.createTestingModule({
    providers: [
      GdprRequestHelper,
      {
        provide: getRepositoryToken(GdprRequest),
        useValue: mockRepository,
      },
      {
        provide: HelperService,
        useValue: mockHelperService,
      },
    ],
  }).compile();

  helper = moduleRef.get<GdprRequestHelper>(GdprRequestHelper);
});

  describe('addRequest', () => {
    it('should return false if uid or action is missing or invalid', async () => {
      await expect(helper.addRequest(null as any, 'export')).resolves.toBe(false);
      await expect(helper.addRequest(1, '')).resolves.toBe(false);
      await expect(helper.addRequest(1, 'invalid')).resolves.toBe(false);
    });

    it('should return id if createGdprRequest succeeds', async () => {
  mockUserHelper.createGdprRequest.mockResolvedValue({ id: 999 });

  const result = await helper.addRequest(1, 'export');

  expect(result).toBe(999);
  expect(mockUserHelper.createGdprRequest).toHaveBeenCalledWith(
    expect.objectContaining({
      uid: 1,
      action: 'export',
      state: 'inqueue',
    })
  );
});


    it('should return false if createGdprRequest fails or returns no id', async () => {
      mockUserHelper.createGdprRequest.mockResolvedValue({});

      const result = await helper.addRequest(1, 'delete');

      expect(result).toBe(false);
    });

    it('should return false and log error if exception occurs', async () => {
      mockHelperService.getHelper.mockRejectedValue(new Error('DB error'));

      const result = await helper.addRequest(1, 'edit');

      expect(result).toBe(false);
      expect(Logger.error).toHaveBeenCalledWith('addRequest', expect.objectContaining({
        METHOD: expect.stringContaining('addRequest'),
        MESSAGE: 'DB error',
      }));
    });
  });

  describe('updateActionStatus', () => {
    it('should return error result if input is invalid', async () => {
      const result = await helper.updateActionStatus(null as any, 'export', 'json');
      expect(result.status).toBe('failed');

      const result2 = await helper.updateActionStatus(1, 'invalid', 'json');
      expect(result2.status).toBe('failed');
    });

    it('should return success if update affects rows', async () => {
      mockRepository.update = jest.fn().mockResolvedValue({ affected: 1 });

      const result = await helper.updateActionStatus(1, 'delete', 'some_data');

      expect(result).toEqual({
        status: 'success',
        msg: 'OK',
        data: 1,
      });
    });

    it('should log debug if no rows are updated', async () => {
      mockRepository.update = jest.fn().mockResolvedValue({ affected: 0 });

      const result = await helper.updateActionStatus(1, 'edit', 'result');

      expect(result.status).toBe('failed');
      expect(Logger.debug).toHaveBeenCalledWith('updateActionStatus', expect.objectContaining({
        MESSAGE: 'GDPR entry may not exist.',
      }));
    });

    it('should return failed and log error on exception', async () => {
      mockRepository.update = jest.fn().mockRejectedValue(new Error('MySQL failure'));

      const result = await helper.updateActionStatus(2, 'export', 'output');

      expect(result.status).toBe('failed');
      expect(Logger.error).toHaveBeenCalledWith('updateActionStatus', expect.objectContaining({
        MESSAGE: 'MySQL failure',
      }));
    });
  });
});
