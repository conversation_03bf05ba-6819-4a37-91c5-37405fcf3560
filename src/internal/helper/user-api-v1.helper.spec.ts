import { Test, TestingModule } from '@nestjs/testing';
import { UserApiV1Helper } from './user-api-v1.helper';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';
import { BadRequestException } from '@nestjs/common';
import { UpdateUserProfileDto } from '../dto/update-user-profile.dto';
import { FirebaseService } from '../../db/firebase/firebase.service';
import { UserRepository } from '../../user/repositories/user/user.repository';
import { RoleRepository } from '../../user/repositories/role/role.repository';
import { EnterpriseLmsPreferences } from '../../db/mysql/entity/enterprise-lms-preferences.entity';
import { EnterpriseLmsSetting } from '../../db/mysql/entity/enterprise-lms-settings.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DeviantsService } from '../../common/services/communication/deviants/deviants.service';
import { GdprRequestHelper } from './gdpr-request.helper';
import { Logger } from '../../logging/logger';
import { Utility } from '../../common/util/utility';
import { EnterpriseService } from '../../common/services/communication/enterprise/enterprise.service';
import { ForgotPasswordB2B } from '../../common/typeDef/auth.type';
import { User } from '../../db/mongo/schema/user/user.schema';


let helper: UserApiV1Helper;
let mockConfigService: any;
let mockHelperService: any;
let mockUserRepository: any;
let mockRoleRepository: any;
let mockUserHelper: any;
let mockLmsPreferencesRepo: any;
let mockLmsSettingsRepo: any;

// ✅ Initialize mocks for all dependencies
mockConfigService = {
  get: jest.fn(),
};

const mockProfileHelper = {
  prepareProfileData: jest.fn(),
  handleOobtaxonomyData: jest.fn(),
  fetchProfileData: jest.fn(),
  syncTaxonomyDataWithMySQLDrupal: jest.fn(),
};

mockUserRepository = {
  findByUID: jest.fn(),
  findOneAndUpdate: jest.fn(),
};

mockRoleRepository = {
  findOne: jest.fn(),
};

mockUserHelper = {
  getAllRolesofUser: jest.fn(),
  updateExistingRole: jest.fn(),
  createEngagexRole: jest.fn(),
  prepareRoleListForDrupalSync: jest.fn(),
  syncUserDataWithMySQLDrupal: jest.fn(),
  updateCloud6SentinelByUidOrMail: jest.fn(),
};

mockLmsPreferencesRepo = {
  findOne: jest.fn(),
};

mockLmsSettingsRepo = {
  findOne: jest.fn(),
};

const mockDeviantsService = {
  pushMessageToQueue: jest.fn(),
};

const mockGdprHelper = {
  addRequest: jest.fn(),
};

const mockFirebaseService = {
  getGdprPath: jest.fn() as jest.Mock<string, [string, number]>,
  createGdprDocument: jest.fn() as jest.Mock<any, []>,
  addGdprExportAttempt: jest.fn() as jest.Mock<any, [any]>,
  setGdprExportStatus: jest.fn() as jest.Mock<any, [any, number]>,
  save: jest.fn() as jest.Mock<Promise<boolean>, [string, any]>,
  find: jest.fn() as jest.Mock<Promise<any>, [string]>,
};

mockHelperService = {
  get: jest.fn((token) => {
    if (token === UserRepository) return mockUserRepository;
    if (token === RoleRepository) return mockRoleRepository;
  }),
  getHelper: jest.fn(() => mockUserHelper),
};

describe('UserApiV1Helper', () => {

  beforeEach(async () => {

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserApiV1Helper,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: HelperService, useValue: mockHelperService },
        {
          provide: getRepositoryToken(EnterpriseLmsPreferences),
          useValue: mockLmsPreferencesRepo,
        },
        {
          provide: getRepositoryToken(EnterpriseLmsSetting),
          useValue: mockLmsSettingsRepo,
        },
        {
          provide: DeviantsService,
          useValue: mockDeviantsService,
        },
        {
          provide: GdprRequestHelper,
          useValue: mockGdprHelper,
        }
      ],
    }).compile();

    helper = module.get<UserApiV1Helper>(UserApiV1Helper);
    mockHelperService.get.mockImplementation((token) => {
      if (token === UserRepository) return mockUserRepository;
    });

    mockHelperService.getHelper.mockImplementation((name) => {
      if (name === 'ProfileHelper') return mockProfileHelper;
      if (name === 'UserHelper') return mockUserHelper;
    });

    jest.clearAllMocks();
  });

  describe('updateEngagexRole', () => {
    it('should throw RoleUserInvalid if role is not allowed', async () => {
      mockConfigService.get.mockReturnValue({ '105': 'engagex_ta' });

      await expect(helper.updateEngagexRole(1, 'invalid_role')).rejects.toThrow(
        new BadRequestException('RoleUserInvalid'),
      );
    });

    it('should throw RoleUserNotExist if user not found', async () => {
      mockConfigService.get.mockReturnValue({ '105': 'engagex_ta' });
      mockUserRepository.findByUID.mockResolvedValue(null);

      await expect(helper.updateEngagexRole(1, 'engagex_ta')).rejects.toThrow(
        new BadRequestException('RoleUserNotExist'),
      );
    });

    it('should throw RoleAlreadyAssign if role already exists', async () => {
      mockConfigService.get.mockReturnValue({ '105': 'engagex_ta' });

      mockUserRepository.findByUID.mockResolvedValue({ uid: 1, roles: [] });
      mockUserHelper.getAllRolesofUser.mockResolvedValue([{ rid: '105' }]);

      // ✅ FIX: add missing roleRepository mock setup
      mockHelperService.get.mockImplementation((token: any) => {
        if (token === UserRepository) return mockUserRepository;
        if (token === RoleRepository) return mockRoleRepository;
      });

      // Also return a dummy role doc to avoid findOne crashing
      mockRoleRepository.findOne.mockResolvedValue({ roleName: 'engagex_ta' });

      await expect(helper.updateEngagexRole(1, 'engagex_ta')).rejects.toThrow(
        new BadRequestException('RoleAlreadyAssign'),
      );
    });


    it('should update existing engagex role successfully', async () => {
      mockHelperService.get.mockImplementation((token: any) => {
        if (token === UserRepository) return mockUserRepository;
        if (token === RoleRepository) return mockRoleRepository; // ✅ fix here
        if (token === DeviantsService) return mockDeviantsService;
      });


      mockConfigService.get.mockImplementation((key) => {
        if (key === 'allowedEngagexRoles') return { '105': 'engagex_ta', '104': 'engagex_cd' };
        if (key === 'enableDrupalSync') return true;
      });

      const user = {
        uid: 1,
        roles: [{ roleName: 'authenticated user' }],
      };

      const currentRoles = [{ rid: '104', roleName: 'engagex_cd' }];
      const newRoleDoc = { roleName: 'engagex_ta' }; // new engagex role

      mockUserRepository.findByUID.mockResolvedValue(user);
      mockUserHelper.getAllRolesofUser.mockResolvedValue(currentRoles);
      mockRoleRepository.findOne.mockResolvedValue(newRoleDoc);
      mockUserHelper.updateExistingRole.mockResolvedValue(undefined);
      mockUserHelper.prepareRoleListForDrupalSync.mockResolvedValue(['someRole']);
      mockUserHelper.syncUserDataWithMySQLDrupal.mockResolvedValue(undefined);
      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 1, roles: [newRoleDoc] });

      const result = await helper.updateEngagexRole(1, 'engagex_ta');

      expect(result).toEqual({ uid: 1, roles: [newRoleDoc] });
      expect(mockUserHelper.updateExistingRole).toHaveBeenCalledWith(1, '105', '104');
      expect(mockUserHelper.createEngagexRole).not.toHaveBeenCalled();
    });
    it('should create engagex role if not already present', async () => {
      mockHelperService.get.mockImplementation((token: any) => {
        if (token === UserRepository) return mockUserRepository;
        if (token === RoleRepository) return mockRoleRepository;
        if (token === DeviantsService) return mockDeviantsService;
      });

      mockConfigService.get.mockImplementation((key) => {
        if (key === 'allowedEngagexRoles') return { '105': 'engagex_ta', '104': 'engagex_cd' };
        if (key === 'enableDrupalSync') return true;
      });

      const user = {
        uid: 1,
        roles: [{ roleName: 'authenticated user' }],
      };

      const currentRoles = []; // 🔥 No existing engagex role
      const newRoleDoc = { roleName: 'engagex_ta' };

      mockUserRepository.findByUID.mockResolvedValue(user);
      mockUserHelper.getAllRolesofUser.mockResolvedValue(currentRoles);
      mockRoleRepository.findOne.mockResolvedValue(newRoleDoc);
      mockUserHelper.createEngagexRole.mockResolvedValue(undefined);
      mockUserHelper.prepareRoleListForDrupalSync.mockResolvedValue(['someRole']);
      mockUserHelper.syncUserDataWithMySQLDrupal.mockResolvedValue(undefined);
      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 1, roles: [newRoleDoc] });

      const result = await helper.updateEngagexRole(1, 'engagex_ta');

      expect(result).toEqual({ uid: 1, roles: [newRoleDoc] });
      expect(mockUserHelper.createEngagexRole).toHaveBeenCalledWith(1, '105');
      expect(mockUserHelper.updateExistingRole).not.toHaveBeenCalled();
    });
    it('should skip drupal sync if enableDrupalSync is false', async () => {
      mockHelperService.get.mockImplementation((token: any) => {
        if (token === UserRepository) return mockUserRepository;
        if (token === RoleRepository) return mockRoleRepository; // ✅ include this line
      });

      mockConfigService.get.mockImplementation((key) => {
        if (key === 'allowedEngagexRoles') return { '105': 'engagex_ta' };
        if (key === 'enableDrupalSync') return false; // ✅ Drupal sync disabled
      });

      const user = {
        uid: 1,
        roles: [{ roleName: 'authenticated user' }],
      };

      const currentRoles = [];
      const newRoleDoc = { roleName: 'engagex_ta' };

      mockUserRepository.findByUID.mockResolvedValue(user);
      mockUserHelper.getAllRolesofUser.mockResolvedValue(currentRoles);
      mockRoleRepository.findOne.mockResolvedValue(newRoleDoc);
      mockUserHelper.createEngagexRole.mockResolvedValue(undefined);
      mockUserHelper.prepareRoleListForDrupalSync.mockResolvedValue(['someRole']);
      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 1, roles: [newRoleDoc] });

      const result = await helper.updateEngagexRole(1, 'engagex_ta');

      expect(result).toEqual({ uid: 1, roles: [newRoleDoc] });
      expect(mockUserHelper.syncUserDataWithMySQLDrupal).not.toHaveBeenCalled(); // ❌ skipped
    });


  });

  describe('deleteAccountByUidForGDPR', () => {
    const validUser = { uid: 123, email: '<EMAIL>', status: 1 };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return failed if user is null', async () => {
      const result = await helper.deleteAccountByUidForGDPR(null);
      expect(result).toEqual({
        status: 'failed',
        msg: 'Invalid request.'
      });
    });

    it('should return failed if uid is missing', async () => {
      const result = await helper.deleteAccountByUidForGDPR({ email: '<EMAIL>' });
      expect(result).toEqual({
        status: 'failed',
        msg: 'Invalid request.'
      });
    });

    it('should return failed if user is already deactivated', async () => {
      const result = await helper.deleteAccountByUidForGDPR({ uid: 123, status: 0 });
      expect(result).toEqual({
        status: 'failed',
        msg: 'User already Deactivated'
      });
    });

    it('should return failed if Mongo update fails', async () => {
      mockHelperService.get.mockImplementation((token: any) => {
        if (token === UserRepository) return mockUserRepository;
        if (token === DeviantsService) return mockDeviantsService;
      });
      mockHelperService.getHelper.mockImplementation((name: string) => {
        if (name === 'UserHelper') return mockUserHelper;
        if (name === 'GdprRequestHelper') return mockGdprHelper;
      });

      mockUserRepository.findOneAndUpdate.mockResolvedValue(null);

      const result = await helper.deleteAccountByUidForGDPR(validUser);

      expect(mockUserRepository.findOneAndUpdate).toHaveBeenCalledWith(
        { uid: validUser.uid, status: 1 },
        { status: 0 }
      );
      expect(result).toEqual({
        status: 'failed',
        msg: 'Some error occurred. Please try again after sometime.'
      });
    });

    it('should return failed if MySQL update fails', async () => {
      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 123 });
      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(null);

      const result = await helper.deleteAccountByUidForGDPR(validUser);

      expect(result).toEqual({
        status: 'failed',
        msg: 'Failed to update user status in database.'
      });
    });

    it('should succeed even if GDPR log fails and queue succeeds', async () => {
      mockHelperService.get.mockImplementation((token: any) => {
        if (token === UserRepository) return mockUserRepository;
        if (token === DeviantsService) return mockDeviantsService;
      });

      mockHelperService.getHelper.mockImplementation((name: string) => {
        if (name === 'GdprRequestHelper') return mockGdprHelper;
        if (name === 'UserHelper') return mockUserHelper;
      });

      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 123 });
      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
      mockGdprHelper.addRequest.mockResolvedValue(null);
      mockDeviantsService.pushMessageToQueue.mockResolvedValue({ status: 'success' });

      const result = await helper.deleteAccountByUidForGDPR(validUser);

      expect(result).toEqual({
        status: 'success',
        msg: 'User account has been Deactivated successfully.'
      });
    });

    it('should succeed even if queue push fails', async () => {
      mockHelperService.get.mockImplementation((token: any) => {
        if (token === UserRepository) return mockUserRepository;
        if (token === DeviantsService) return mockDeviantsService;
      });

      mockHelperService.getHelper.mockImplementation((name: string) => {
        if (name === 'GdprRequestHelper') return mockGdprHelper;
        if (name === 'UserHelper') return mockUserHelper;
      });

      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 123 });
      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
      mockGdprHelper.addRequest.mockResolvedValue(1001);
      mockDeviantsService.pushMessageToQueue.mockResolvedValue({ status: 'failed' });

      const result = await helper.deleteAccountByUidForGDPR(validUser);

      expect(result).toEqual({
        status: 'success',
        msg: 'User account has been Deactivated successfully.'
      });
    });

    it('should return success when all operations succeed', async () => {
      mockHelperService.get.mockImplementation((token: any) => {
        if (token === UserRepository) return mockUserRepository;
        if (token === DeviantsService) return mockDeviantsService;
      });

      mockHelperService.getHelper.mockImplementation((name: string) => {
        if (name === 'GdprRequestHelper') return mockGdprHelper;
        if (name === 'UserHelper') return mockUserHelper;
      });

      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 123 });
      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
      mockGdprHelper.addRequest.mockResolvedValue(9999);
      mockDeviantsService.pushMessageToQueue.mockResolvedValue({ status: 'success' });

      const result = await helper.deleteAccountByUidForGDPR(validUser);

      expect(result).toEqual({
        status: 'success',
        msg: 'User account has been Deactivated successfully.'
      });
    });


    it('should handle exception and return failed', async () => {
      mockUserRepository.findOneAndUpdate.mockRejectedValue(new Error('DB error'));

      const result = await helper.deleteAccountByUidForGDPR(validUser);

      expect(result).toEqual({
        status: 'failed',
        msg: 'Some error occurred. Please try again after sometime.'
      });
    });
  });

  describe('updateUserOnboardingData', () => {
    const mockInput: UpdateUserProfileDto = {
      client_id: 'sl_looper',
      user_id: '101', // string as per DTO
      first_name: 'John',
      last_name: 'Doe',
      title: 'Mr.',
      gender: 'male',
      dob: '1990-01-01',
      linkedin_url: 'https://linkedin.com/in/test',
      timezone: 'UTC',
      industry: {
        name: 'Technology',
        tid: '1234',
      },
      visibility: 1, // ✅ number; e.g., 0 for Simplilearn, 1 for Public
      highest_level_of_education: 'Masters',
      where_are_you_in_career: 'Mid',
      objective_of_taking_course: 'Upskilling',
      training_funded_by: 'Self',
    };


    it('should update user onboarding data successfully with drupal sync', async () => {
      mockConfigService.get.mockImplementation((key) => {
        if (key === 'enableDrupalSync') return true;
      });

      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 101 });
      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
      mockProfileHelper.handleOobtaxonomyData.mockResolvedValue(true);
      mockProfileHelper.prepareProfileData.mockResolvedValue({
        work_experience: '5 years',
      });
      mockProfileHelper.fetchProfileData.mockResolvedValue({ profile: 'data' });
      mockProfileHelper.syncTaxonomyDataWithMySQLDrupal.mockResolvedValue(true);
      mockUserHelper.syncUserDataWithMySQLDrupal.mockResolvedValue(true);

      const result = await helper.updateUserOnboardingData(mockInput);

      expect(result).toEqual({
        type: 'success',
        msg: 'Successfully updated user profile.',
      });

      expect(mockUserRepository.findOneAndUpdate).toHaveBeenCalled();
      expect(mockProfileHelper.prepareProfileData).toHaveBeenCalledWith({
        industry: {
          name: 'Technology',
          tid: '1234',
        },
      });
      expect(mockProfileHelper.handleOobtaxonomyData).toHaveBeenCalled();
      expect(mockUserHelper.updateCloud6SentinelByUidOrMail).toHaveBeenCalled();
      expect(mockProfileHelper.fetchProfileData).toHaveBeenCalled();
      expect(mockProfileHelper.syncTaxonomyDataWithMySQLDrupal).toHaveBeenCalled();
      expect(mockUserHelper.syncUserDataWithMySQLDrupal).toHaveBeenCalled();
    });

    it('should return error type if one of the update steps fails', async () => {
      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 101 });
      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(null); // ❌ fails
      mockProfileHelper.handleOobtaxonomyData.mockResolvedValue(true);

      const result = await helper.updateUserOnboardingData(mockInput);

      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while updating profile. Please try again.',
      });
    });

    it('should skip optional industry mapping if not provided', async () => {
      const inputWithoutIndustry = { ...mockInput };
      delete inputWithoutIndustry.industry;

      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 101 });
      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
      mockProfileHelper.handleOobtaxonomyData.mockResolvedValue(true);

      const result = await helper.updateUserOnboardingData(inputWithoutIndustry);

      expect((result as any).type).toBe('success');
      expect(mockProfileHelper.prepareProfileData).not.toHaveBeenCalled();
    });

    it('should handle unexpected errors and log them', async () => {
      mockHelperService.get.mockRejectedValue(new Error('DB Down'));

      const result = await helper.updateUserOnboardingData(mockInput);

      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while updating profile. Please try again.',
      });
    });

    it('should skip work_experience if prepareProfileData does not return it', async () => {
      const inputWithIndustry = { ...mockInput };
      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 101 });
      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
      mockProfileHelper.handleOobtaxonomyData.mockResolvedValue(true);
      mockProfileHelper.prepareProfileData.mockResolvedValue({}); // no work_experience

      const result = await helper.updateUserOnboardingData(inputWithIndustry);

      expect(result).toEqual({
        type: 'success',
        msg: 'Successfully updated user profile.',
      });

      expect(mockProfileHelper.prepareProfileData).toHaveBeenCalledWith({
        industry: {
          name: 'Technology',
          tid: '1234',
        },
      });
    });

    it('should skip profile_visibility if visibility is not provided', async () => {
      const inputWithoutVisibility = { ...mockInput };
      delete inputWithoutVisibility.visibility;

      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 101 });
      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
      mockProfileHelper.handleOobtaxonomyData.mockResolvedValue(true);

      const result = await helper.updateUserOnboardingData(inputWithoutVisibility);

      expect(result).toEqual({
        type: 'success',
        msg: 'Successfully updated user profile.',
      });

      expect(mockUserRepository.findOneAndUpdate).toHaveBeenCalled();
    });

    it('should skip display_name if first_name or last_name is missing', async () => {
      const input = {
        ...mockInput,
        first_name: 'John',
      };
      delete input.last_name; // simulate partial name

      mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 101 });
      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
      mockProfileHelper.handleOobtaxonomyData.mockResolvedValue(true);

      const result = await helper.updateUserOnboardingData(input);

      expect(result).toEqual({
        type: 'success',
        msg: 'Successfully updated user profile.',
      });

      expect(mockUserRepository.findOneAndUpdate).toHaveBeenCalled();
    });

  });

  describe('updateGdprDeleteStatus', () => {
    it('should return true if GDPR delete status update is successful', async () => {
      const mockUser = { uid: 123 };
      const responseData = 'delete successful';

      // Setup the mock
      mockHelperService.getHelper.mockImplementation((helperName: string) => {
        if (helperName === 'GdprRequestHelper') {
          return {
            updateActionStatus: jest.fn().mockResolvedValue({ status: 'success' }),
          };
        }
      });

      const result = await helper.updateGdprDeleteStatus(mockUser, responseData);
      expect(result).toBe(true);
    });

    it('should return false if GDPR delete status update fails', async () => {
      const mockUser = { uid: 123 };
      const responseData = 'delete failed';

      mockHelperService.getHelper.mockImplementation((helperName: string) => {
        if (helperName === 'GdprRequestHelper') {
          return {
            updateActionStatus: jest.fn().mockResolvedValue({ status: 'failed' }),
          };
        }
      });

      const result = await helper.updateGdprDeleteStatus(mockUser, responseData);
      expect(result).toBe(false);
    });

    it('should return false if an exception is thrown', async () => {
      const mockUser = { uid: 123 };
      const responseData = 'error case';

      mockHelperService.getHelper.mockImplementation(() => {
        throw new Error('Mock error');
      });

      const result = await helper.updateGdprDeleteStatus(mockUser, responseData);
      expect(result).toBe(false);
    });

  });

  describe('gdprApiValidation', () => {
    it('should return error if data is empty', async () => {
      const result = await helper.gdprApiValidation(['email'], {});
      expect(result).toEqual(['params are empty']);
    });

    it('should return error for missing top-level field', async () => {
      const result = await helper.gdprApiValidation(['email'], { name: 'John' });
      expect(result).toEqual(['email is empty.']);
    });

    it('should return error for missing nested field', async () => {
      const data = { user: {} };
      const result = await helper.gdprApiValidation(['user.name'], data);
      expect(result).toEqual(['user.name is empty.']);
    });

    it('should return empty array if all fields are valid', async () => {
      const data = { email: '<EMAIL>', user: { name: 'John' } };
      const result = await helper.gdprApiValidation(['email', 'user.name'], data);
      expect(result).toEqual([]);
    });

    it('should return error if data is undefined', async () => {
      const result = await helper.gdprApiValidation(['email'], undefined);
      expect(result).toEqual(['params are empty']);
    });

  });

  describe('gdprExport', () => {
    const requestBody = {
      usid: 123,
      useremail: '<EMAIL>',
      action: 'export', // ✅ required and validated by @IsEnum
    };

    it('should return success if all steps succeed', async () => {
      mockHelperService.getHelper.mockImplementation((helperName: string) => {
        if (helperName === 'UserHelper') {
          return {
            getCloud6SentinelUserByUidOrMail: jest.fn().mockResolvedValue({ status: true }),
            createGdprRequest: jest.fn().mockResolvedValue(true),
          };
        }
      });

      mockHelperService.get.mockResolvedValue(mockFirebaseService);

      // ✅ Include `data` field here
      jest.spyOn(helper, 'gdprAddRequestCouch').mockResolvedValue({
        status: 1,
        data: '',
        message: 'Success',
      });

      const result = await helper.gdprExport({
        usid: 123,
        useremail: '<EMAIL>',
        action: 'export',
      });

      expect(result).toEqual({ status: 1, data: '', message: 'success' });
    });


    it('should return user not found if user lookup fails', async () => {
      mockHelperService.getHelper.mockImplementation((helperName: string) => {
        if (helperName === 'UserHelper') {
          return {
            getCloud6SentinelUserByUidOrMail: jest.fn().mockResolvedValue({ status: false }),
          };
        }
      });

      const result = await helper.gdprExport(requestBody);

      expect(result).toEqual({ status: 0, data: '', message: 'User not found' });
    });

    it('should return success if all steps succeed', async () => {
      mockHelperService.getHelper.mockImplementation((helperName: string) => {
        if (helperName === 'UserHelper') {
          return {
            getCloud6SentinelUserByUidOrMail: jest.fn().mockResolvedValue({ status: true }),
            createGdprRequest: jest.fn().mockResolvedValue(true),
          };
        }
      });

      mockHelperService.get.mockResolvedValue(mockFirebaseService);

      // ✅ Include `data` field here
      jest.spyOn(helper, 'gdprAddRequestCouch').mockResolvedValue({
        status: 1,
        data: '',
        message: 'Success',
      });

      const result = await helper.gdprExport({
        usid: 123,
        useremail: '<EMAIL>',
        action: 'export',
      });

      expect(result).toEqual({ status: 1, data: '', message: 'success' });
    });


    it('should return failure if MySQL insert fails', async () => {
      mockHelperService.getHelper.mockImplementation((helperName: string) => {
        if (helperName === 'UserHelper') {
          return {
            getCloud6SentinelUserByUidOrMail: jest.fn().mockResolvedValue({ status: true }),
            createGdprRequest: jest.fn().mockResolvedValue(false), // Simulate MySQL insert failure
          };
        }
      });

      mockHelperService.get.mockResolvedValue(mockFirebaseService);

      // ✅ Proper return structure for gdprAddRequestCouch
      jest.spyOn(helper, 'gdprAddRequestCouch').mockResolvedValue({
        status: 1,
        data: '', // required field
        message: 'Success',
      });

      const result = await helper.gdprExport({
        usid: 123,
        useremail: '<EMAIL>',
        action: 'export',
      });

      expect(result).toEqual({ status: 0, data: '', message: 'Unable to save in couch' });
    });

    it('should return failure if exception is thrown', async () => {
      mockHelperService.getHelper.mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      const result = await helper.gdprExport(requestBody);

      expect(result).toEqual({ status: 0, data: '', message: 'Unable to save in couch' });
    });

    it('should return error if gdprAddRequestCouch fails', async () => {
      mockHelperService.getHelper.mockResolvedValue({
        getCloud6SentinelUserByUidOrMail: jest.fn().mockResolvedValue({ status: true }),
      });

      mockHelperService.get.mockResolvedValue(mockFirebaseService);
      jest.spyOn(helper, 'gdprAddRequestCouch').mockResolvedValue({
        status: 0,
        data: '',
        message: 'Firebase write failed',
      });

      const result = await helper.gdprExport(requestBody);

      expect(result).toEqual({ status: 0, data: '', message: 'Firebase write failed' });
    });

  });

  describe('gdprAddRequestCouch', () => {
    const requestBody = {
      usid: 123,
      useremail: '<EMAIL>',
      action: 'export',
    };

    const mockDocument = { existing: 'doc' };
    const modifiedDoc = { modified: 'doc' };

    beforeEach(() => {
      mockFirebaseService.getGdprPath = jest.fn().mockReturnValue('gdpr/test/path');
      mockFirebaseService.createGdprDocument = jest.fn().mockReturnValue(mockDocument);
      mockFirebaseService.addGdprExportAttempt = jest.fn().mockReturnValue(modifiedDoc);
      mockFirebaseService.setGdprExportStatus = jest.fn().mockReturnValue(modifiedDoc);
      mockFirebaseService.save = jest.fn();
      mockFirebaseService.find = jest.fn();
    });

    it('should return success when document is created and saved successfully', async () => {
      mockFirebaseService.find.mockResolvedValue(null); // document not found
      mockFirebaseService.save.mockResolvedValue(true); // save successful

      const result = await helper.gdprAddRequestCouch(requestBody, mockFirebaseService as unknown as FirebaseService);

      expect(result).toEqual({ status: 1, data: '', message: 'success' });
      expect(mockFirebaseService.getGdprPath).toHaveBeenCalledWith('<EMAIL>', 123);
      expect(mockFirebaseService.createGdprDocument).toHaveBeenCalled();
      expect(mockFirebaseService.addGdprExportAttempt).toHaveBeenCalledWith(mockDocument);
      expect(mockFirebaseService.setGdprExportStatus).toHaveBeenCalledWith(modifiedDoc, 1);
      expect(mockFirebaseService.save).toHaveBeenCalledWith('gdpr/test/path', modifiedDoc);
    });

    it('should return failure if save fails', async () => {
      mockFirebaseService.find.mockResolvedValue(null);
      mockFirebaseService.save.mockResolvedValue(false); // save failed

      const result = await helper.gdprAddRequestCouch(requestBody as any, mockFirebaseService as unknown as FirebaseService);

      expect(result).toEqual({
        status: 0,
        data: '',
        message: 'unable to set start export status in Document',
      });
    });

    it('should return failure if an exception is thrown', async () => {
      mockFirebaseService.find.mockRejectedValue(new Error('Mock Firebase Error'));

      const result = await helper.gdprAddRequestCouch(requestBody as any, mockFirebaseService as unknown as FirebaseService);

      expect(result).toEqual({
        status: 0,
        data: '',
        message: 'Unable to save in couch',
      });
    });
  });

  describe('validateEditProfileBasic', () => {
    const validInput = {
      title: 'Mr.',
      first_name: 'John',
      last_name: 'Doe',
      dob: '1990-01-01',
      gender: 'Male',
      user_linkedin_url: 'https://linkedin.com/in/johndoe',
      training_funded_by: 'Company',
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return true for valid input', async () => {
      await expect(helper.validateEditProfileBasic(validInput)).resolves.toBe(true);
    });

    it('should throw if title is missing', async () => {
      const input = { ...validInput };
      delete input.title;

      await expect(helper.validateEditProfileBasic(input)).rejects.toThrow('Please provide title.');
    });

    it('should throw if first_name is missing', async () => {
      const input = { ...validInput };
      delete input.first_name;

      await expect(helper.validateEditProfileBasic(input)).rejects.toThrow('Please provide first name.');
    });

    it('should throw if first_name is invalid', async () => {
      const input = { ...validInput, first_name: 'J0hn@123' };

      await expect(helper.validateEditProfileBasic(input)).rejects.toThrow('Please provide valid first name.');
    });

    it('should throw if last_name is missing', async () => {
      const input = { ...validInput };
      delete input.last_name;

      await expect(helper.validateEditProfileBasic(input)).rejects.toThrow('Please provide last name.');
    });

    it('should throw if last_name is invalid', async () => {
      const input = { ...validInput, last_name: 'D03@' };

      await expect(helper.validateEditProfileBasic(input)).rejects.toThrow('Please provide valid last name.');
    });

    it('should throw if dob is missing when showProfile is true', async () => {
      const input = { ...validInput };
      delete input.dob;

      await expect(helper.validateEditProfileBasic(input, true)).rejects.toThrow('Please provide date of birth.');
    });

    it('should throw if gender is missing when showProfile is true', async () => {
      const input = { ...validInput };
      delete input.gender;

      await expect(helper.validateEditProfileBasic(input, true)).rejects.toThrow('Please provide gender.');
    });

    it('should pass if gender and dob are missing but showProfile is false', async () => {
      const input = { ...validInput };
      delete input.dob;
      delete input.gender;

      await expect(helper.validateEditProfileBasic(input, false)).resolves.toBe(true);
    });

    it('should throw if linkedin url is invalid', async () => {
      const input = { ...validInput, user_linkedin_url: 'https://fake.com/profile' };

      await expect(helper.validateEditProfileBasic(input)).rejects.toThrow('Please provide valid Linkedin URL.');
    });

    it('should throw if training_funded_by is missing', async () => {
      const input = { ...validInput };
      delete input.training_funded_by;

      await expect(helper.validateEditProfileBasic(input)).rejects.toThrow('Please provide training funded.');
    });

    it('should log the error and rethrow it', async () => {
      const input = { ...validInput };
      delete input.title;

      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation(undefined as any);

      await expect(helper.validateEditProfileBasic(input)).rejects.toThrow('Please provide title.');
      expect(loggerSpy).toHaveBeenCalledWith('validateEditProfileBasic', expect.objectContaining({
        METHOD: expect.stringContaining('validateEditProfileBasic'),
        MESSAGE: 'Please provide title.',
        REQUEST: input,
        TIMESTAMP: expect.any(Number),
      }));
    });
  });

  describe('validateEditProfileOutcome', () => {
    it('should return true if objective_of_taking_course is present', async () => {
      const validInput = {
        objective_of_taking_course: 'Gain practical knowledge',
      };

      await expect(helper.validateEditProfileOutcome(validInput)).resolves.toBe(true);
    });

    it('should throw error and log if objective_of_taking_course is missing', async () => {
      const invalidInput = {};

      const loggerSpy = jest
        .spyOn(Logger, 'error')
        .mockReturnValue(undefined as any); // Avoid type error

      await expect(helper.validateEditProfileOutcome(invalidInput)).rejects.toThrow(
        'Please provide desired learning outcome.',
      );

      expect(loggerSpy).toHaveBeenCalledWith(
        'validateEditProfileOutcome',
        expect.objectContaining({
          METHOD: expect.stringContaining('validateEditProfileOutcome'),
          MESSAGE: 'Please provide desired learning outcome.',
          REQUEST: invalidInput,
          TIMESTAMP: expect.any(Number),
        }),
      );
    });
  });

  describe('validateEditProfileAcademics', () => {
    it('should return true if highest_level_of_education is present', async () => {
      const input = { highest_level_of_education: 'Bachelor’s Degree' };

      await expect(helper.validateEditProfileAcademics(input)).resolves.toBe(true);
    });

    it('should throw BadRequestException and log error if highest_level_of_education is missing', async () => {
      const input = {};

      const loggerSpy = jest
        .spyOn(Logger, 'error')
        .mockReturnValue(undefined as any); // Prevent type error

      await expect(helper.validateEditProfileAcademics(input)).rejects.toThrow(
        'Please provide highest level of education.',
      );

      expect(loggerSpy).toHaveBeenCalledWith(
        'validateEditProfileAcademics',
        expect.objectContaining({
          METHOD: expect.stringContaining('validateEditProfileAcademics'),
          MESSAGE: 'Please provide highest level of education.',
          REQUEST: input,
          TIMESTAMP: expect.any(Number),
        }),
      );
    });
  });

  describe('validateEditProfileProfessional', () => {
    it('should return true when where_are_you_in_career is provided', async () => {
      const input = { where_are_you_in_career: '3-5 years' };

      await expect(helper.validateEditProfileProfessional(input)).resolves.toBe(true);
    });

    it('should throw BadRequestException and log error if where_are_you_in_career is missing', async () => {
      const input = {};

      const loggerSpy = jest
        .spyOn(Logger, 'error')
        .mockReturnValue(undefined as any);

      await expect(helper.validateEditProfileProfessional(input)).rejects.toThrow(
        'Please provide total years of experience.',
      );

      expect(loggerSpy).toHaveBeenCalledWith(
        'validateEditProfileProfessional',
        expect.objectContaining({
          METHOD: expect.stringContaining('validateEditProfileProfessional'),
          MESSAGE: 'Please provide total years of experience.',
          REQUEST: input,
          TIMESTAMP: expect.any(Number),
        }),
      );
    });
  });

  describe('validateEditProfileContact', () => {
    const validInput = {
      phone_no: '9876543210',
      country_code: '+91',
      country_of_residence: 'India',
      state: 'Karnataka',
      location: 'Bangalore',
      timezone: 'Asia/Kolkata',
    };

    beforeEach(() => {
      jest.clearAllMocks();
      jest.spyOn(Logger, 'error').mockReturnValue(undefined as any);
    });

    it('should return true with all valid inputs and validity Yes', async () => {
      jest.spyOn(Utility, 'validatePhoneNumber').mockReturnValue(true);
      jest.spyOn(Utility, 'validateCountryCode').mockReturnValue(true);

      await expect(helper.validateEditProfileContact(validInput, 'Yes')).resolves.toBe(true);
    });

    it('should throw if phone number is invalid', async () => {
      jest.spyOn(Utility, 'validatePhoneNumber').mockReturnValue(false);

      await expect(helper.validateEditProfileContact(validInput, 'Yes')).rejects.toThrow(
        'Please provide valid phone no. (atleast 7 digits required)',
      );
    });

    it('should throw if country code is invalid', async () => {
      jest.spyOn(Utility, 'validatePhoneNumber').mockReturnValue(true);
      jest.spyOn(Utility, 'validateCountryCode').mockReturnValue(false);

      await expect(helper.validateEditProfileContact(validInput, 'Yes')).rejects.toThrow(
        'Please select the country code for your phone number.',
      );
    });

    it('should throw if country_of_residence is missing', async () => {
      const input = { ...validInput };
      delete input.country_of_residence;

      await expect(helper.validateEditProfileContact(input)).rejects.toThrow(
        'Please provide valid Country of residence.',
      );
    });

    it('should throw if state is missing', async () => {
      const input = { ...validInput };
      delete input.state;

      await expect(helper.validateEditProfileContact(input)).rejects.toThrow(
        'Please provide valid State.',
      );
    });

    it('should throw if location contains invalid characters', async () => {
      const input = { ...validInput, location: 'Invalid@@Location!' };

      await expect(helper.validateEditProfileContact(input)).rejects.toThrow(
        'Please provide valid location.',
      );
    });

    it('should throw if timezone is missing', async () => {
      const input = { ...validInput };
      delete input.timezone;

      await expect(helper.validateEditProfileContact(input)).rejects.toThrow(
        'Please provide valid timezone.',
      );
    });

    it('should log error when exception is thrown', async () => {
      const input = { ...validInput };
      delete input.timezone;

      try {
        await helper.validateEditProfileContact(input);
      } catch (err) {
        expect(Logger.error).toHaveBeenCalledWith(
          'validateEditProfileContact',
          expect.objectContaining({
            METHOD: expect.stringContaining('validateEditProfileContact'),
            MESSAGE: 'Please provide valid timezone.',
            REQUEST: input,
            TIMESTAMP: expect.any(Number),
          }),
        );
      }
    });
  });

  describe('getLmsSetting', () => {
    it('should return null if gid is not provided', async () => {
      const result = await helper.getLmsSetting('some_id', '');
      expect(result).toBeNull();
    });

    it('should return setting when preference and setting exist', async () => {
      mockLmsPreferencesRepo.findOne.mockResolvedValue({ id: 100 });
      const expectedSetting = { group_id: 1, preference_id: 100 };
      mockLmsSettingsRepo.findOne.mockResolvedValue(expectedSetting);

      const result = await helper.getLmsSetting('custom_learning', '1');
      expect(mockLmsPreferencesRepo.findOne).toHaveBeenCalledWith({
        where: { preference_name: 'custom_learning' },
      });
      expect(mockLmsSettingsRepo.findOne).toHaveBeenCalledWith({
        where: { group_id: 1, preference_id: 100 },
      });
      expect(result).toEqual(expectedSetting);
    });

    it('should return null if preference is not found', async () => {
      // simulate "not found" by returning { id: undefined } to avoid crash
      mockLmsPreferencesRepo.findOne.mockResolvedValue({ id: undefined });
      mockLmsSettingsRepo.findOne.mockResolvedValue(null); // optional: ensure fallback

      const result = await helper.getLmsSetting('invalid_preference', '1');

      expect(mockLmsPreferencesRepo.findOne).toHaveBeenCalledWith({
        where: { preference_name: 'invalid_preference' },
      });
      expect(mockLmsSettingsRepo.findOne).toHaveBeenCalledWith({
        where: { group_id: 1, preference_id: NaN },
      });
      expect(result).toBeNull(); // handles null result from .findOne()
    });


    it('should handle missing preference_id.id gracefully', async () => {
      mockLmsPreferencesRepo.findOne.mockResolvedValue({});
      mockLmsSettingsRepo.findOne.mockResolvedValue(null);

      const result = await helper.getLmsSetting('something', '2');
      expect(mockLmsPreferencesRepo.findOne).toHaveBeenCalled();
      expect(mockLmsSettingsRepo.findOne).toHaveBeenCalledWith({
        where: { group_id: 2, preference_id: NaN },
      });
      expect(result).toBeNull();
    });
  });

  describe('getHelpSupportSettings', () => {
    it('should return values from getLmsSetting if available', async () => {
      const mockSetting = {
        id: 1,
        group_id: 101,
        preference_id: 5,
        value: 'No',
        status: 1,
        custom_value: 'https://support.example.com',
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockConfigService.get.mockReturnValue('help_support');
      const getLmsSettingSpy = jest
        .spyOn(helper, 'getLmsSetting')
        .mockResolvedValue(mockSetting);

      const result = await helper.getHelpSupportSettings(101);

      expect(mockConfigService.get).toHaveBeenCalledWith('helpAndSupportIdentifier');
      expect(getLmsSettingSpy).toHaveBeenCalledWith('help_support', '101');
      expect(result).toEqual({
        show: 'No',
        url: 'https://support.example.com',
      });
    });

    it('should return default values if setting is undefined', async () => {
      mockConfigService.get.mockReturnValue('help_support');
      jest.spyOn(helper, 'getLmsSetting').mockResolvedValue(undefined);

      const result = await helper.getHelpSupportSettings(102);

      expect(result).toEqual({ show: 'Yes', url: 'Yes' });
    });
  });

  describe('isShowMyResources', () => {
    it('should return the value from LMS setting if available', async () => {
      mockConfigService.get.mockReturnValue('my_resources_pref_id');

      const mockSetting = {
        id: 1,
        group_id: 1001,
        preference_id: 123,
        value: 'No',
        status: 1,
        custom_value: '',
        created_at: new Date(),
        updated_at: new Date(),
      };

      const getLmsSettingSpy = jest
        .spyOn(helper, 'getLmsSetting')
        .mockResolvedValue(mockSetting);

      const result = await helper.isShowMyResources('1001');

      expect(mockConfigService.get).toHaveBeenCalledWith('myResourcesIdentifier');
      expect(getLmsSettingSpy).toHaveBeenCalledWith('my_resources_pref_id', '1001');
      expect(result).toBe('No');
    });

    it('should return "Yes" if setting is not available', async () => {
      mockConfigService.get.mockReturnValue('my_resources_pref_id');

      const getLmsSettingSpy = jest
        .spyOn(helper, 'getLmsSetting')
        .mockResolvedValue(undefined);

      const result = await helper.isShowMyResources('1001');

      expect(mockConfigService.get).toHaveBeenCalledWith('myResourcesIdentifier');
      expect(getLmsSettingSpy).toHaveBeenCalledWith('my_resources_pref_id', '1001');
      expect(result).toBe('Yes');
    });
  });

  describe('isShowCommunity', () => {
    it('should return the value from LMS setting if available', async () => {
      mockConfigService.get.mockReturnValue('community_pref_id');

      const mockSetting = {
        id: 1,
        group_id: 1001,
        preference_id: 321,
        value: 'No',
        status: 1,
        custom_value: '',
        created_at: new Date(),
        updated_at: new Date(),
      };

      const getLmsSettingSpy = jest
        .spyOn(helper, 'getLmsSetting')
        .mockResolvedValue(mockSetting);

      const result = await helper.isShowCommunity('1001');

      expect(mockConfigService.get).toHaveBeenCalledWith('communityIdentifier');
      expect(getLmsSettingSpy).toHaveBeenCalledWith('community_pref_id', '1001');
      expect(result).toBe('No');
    });

    it('should return "Yes" if setting is not available', async () => {
      mockConfigService.get.mockReturnValue('community_pref_id');

      const getLmsSettingSpy = jest
        .spyOn(helper, 'getLmsSetting')
        .mockResolvedValue(undefined);

      const result = await helper.isShowCommunity('1001');

      expect(mockConfigService.get).toHaveBeenCalledWith('communityIdentifier');
      expect(getLmsSettingSpy).toHaveBeenCalledWith('community_pref_id', '1001');
      expect(result).toBe('Yes');
    });
  });

  describe('getViewSettings', () => {
    const gid = '1001';

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return settings for a non-free user', async () => {
      const userType = 'enterprise';

      mockConfigService.get.mockImplementation((key: string) => {
        if (key === 'freeUserType') return 'free';
      });

      jest.spyOn(helper, 'getHelpSupportSettings').mockResolvedValue({
        show: 'Yes',
        url: 'https://help.url',
      });
      jest.spyOn(helper, 'isShowMyResources').mockResolvedValue('Yes');
      jest.spyOn(helper, 'isShowCommunity').mockResolvedValue('No');

      const result = await helper.getViewSettings(userType, gid);

      expect(helper.getHelpSupportSettings).toHaveBeenCalledWith(Number(gid));
      expect(helper.isShowMyResources).toHaveBeenCalledWith(gid);
      expect(helper.isShowCommunity).toHaveBeenCalledWith(gid);
      expect(result).toEqual({
        isShowResources: 'Yes',
        isShowCommunity: 'No',
        showHelpSupport: 'Yes',
        helpSupportUrl: 'https://help.url',
      });
    });

    it('should return settings with showHelpSupport as No for free user', async () => {
      const userType = 'free';

      mockConfigService.get.mockImplementation((key: string) => {
        if (key === 'freeUserType') return 'free';
      });

      jest.spyOn(helper, 'getHelpSupportSettings').mockResolvedValue({
        show: 'Yes',
        url: 'https://support.example.com',
      });
      jest.spyOn(helper, 'isShowMyResources').mockResolvedValue('No');
      jest.spyOn(helper, 'isShowCommunity').mockResolvedValue('Yes');

      const result = await helper.getViewSettings(userType, gid);

      expect(result).toEqual({
        isShowResources: 'No',
        isShowCommunity: 'Yes',
        showHelpSupport: 'No',
        helpSupportUrl: 'https://support.example.com',
      });
    });
  });

  describe('loadViewData', () => {
    const lgid = 1001;

    const mockGroupDetails = {
      data: [
        {
          enable_certificates: 1,
          enable_saml_authentication: 1,
          affiliateLogoUrl: 'https://logo.com/logo.png',
          billing_email: '<EMAIL>',
          userId: 123,
          affiliateId: 'affiliate123',
          displayName: 'Sample Group',
        },
      ],
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return view data when lgid is valid and group data exists', async () => {
      mockConfigService.get.mockResolvedValue(999); // defaultLrsApplicationId

      mockHelperService.get.mockImplementation((token: any) => {
        if (token === EnterpriseService) {
          return {
            getGroupByGid: jest.fn().mockResolvedValue(mockGroupDetails),
            getGroupDomainByGid: jest.fn().mockResolvedValue('https://lms.url'),
          };
        }
      });

      jest.spyOn(helper, 'setLearnerType').mockResolvedValue(true);

      const result = await helper.loadViewData(lgid);

      expect(result).toEqual({
        showCertificateTab: 1,
        isEnableSamlAuthentication: 1,
        lmsUrl: 'https://lms.url',
        atpLogoUrl: 'https://logo.com/logo.png',
        isPgLearner: true,
        affiliateId: 'affiliate123',
        groupName: 'Sample Group',
      });
    });

    it('should return default values when lgid is invalid', async () => {
      mockConfigService.get.mockResolvedValue(1001); // lgid equals defaultLrsApplicationId → invalid

      jest.spyOn(helper, 'setLearnerType').mockResolvedValue(false);

      const result = await helper.loadViewData(lgid);

      expect(result).toEqual({
        showCertificateTab: 0,
        isEnableSamlAuthentication: 0,
        lmsUrl: '',
        atpLogoUrl: '',
        isPgLearner: false,
        affiliateId: '',
        groupName: '',
      });
    });

    it('should log and throw error if an exception occurs', async () => {
      mockConfigService.get.mockRejectedValue(new Error('Config error'));
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation(undefined as any);

      await expect(helper.loadViewData(lgid)).rejects.toThrow('Config error');
      expect(loggerSpy).toHaveBeenCalledWith('loadViewData', expect.objectContaining({
        METHOD: expect.stringContaining('loadViewData'),
        MESSAGE: 'Config error',
        REQUEST: lgid,
      }));
    });

    it('should return default group data when groupDetails.data is empty', async () => {
      mockConfigService.get.mockResolvedValue(999); // defaultLrsApplicationId !== lgid

      mockHelperService.get.mockImplementation((token: any) => {
        if (token === EnterpriseService) {
          return {
            getGroupByGid: jest.fn().mockResolvedValue({ data: [] }), // empty group data
            getGroupDomainByGid: jest.fn().mockResolvedValue('https://lms.url'),
          };
        }
      });

      jest.spyOn(helper, 'setLearnerType').mockResolvedValue(false);

      const result = await helper.loadViewData(1001);

      expect(result).toEqual({
        showCertificateTab: 0,
        isEnableSamlAuthentication: 0,
        lmsUrl: '',
        atpLogoUrl: '',
        isPgLearner: false,
        affiliateId: '',
        groupName: '',
      });
    });

    it('should set lmsUrl to empty string if getGroupDomainByGid returns null', async () => {
      mockConfigService.get.mockResolvedValue(999);

      mockHelperService.get.mockImplementation((token: any) => {
        if (token === EnterpriseService) {
          return {
            getGroupByGid: jest.fn().mockResolvedValue(mockGroupDetails),
            getGroupDomainByGid: jest.fn().mockResolvedValue(null), // null LMS URL
          };
        }
      });

      jest.spyOn(helper, 'setLearnerType').mockResolvedValue(true);

      const result = await helper.loadViewData(1001);

      expect(result.lmsUrl).toBe('');
    });

    it('should return default values if groupDetails.data is undefined', async () => {
      mockConfigService.get.mockResolvedValue(999); // Valid gid

      mockHelperService.get.mockImplementation((token: any) => {
        if (token === EnterpriseService) {
          return {
            getGroupByGid: jest.fn().mockResolvedValue({}), // ❌ data missing
            getGroupDomainByGid: jest.fn().mockResolvedValue('https://lms.url'),
          };
        }
      });

      jest.spyOn(helper, 'setLearnerType').mockResolvedValue(false);

      const result = await helper.loadViewData(1001);

      expect(result).toEqual({
        showCertificateTab: 0,
        isEnableSamlAuthentication: 0,
        lmsUrl: '',
        atpLogoUrl: '',
        isPgLearner: false,
        affiliateId: '',
        groupName: '',
      });
    });

    it('should call setLearnerType with null userId if groupData.userId is missing', async () => {
      mockConfigService.get.mockResolvedValue(999); // valid

      mockHelperService.get.mockImplementation((token: any) => {
        if (token === EnterpriseService) {
          return {
            getGroupByGid: jest.fn().mockResolvedValue({
              data: [
                {
                  enable_certificates: 1,
                  enable_saml_authentication: 1,
                  affiliateLogoUrl: 'https://logo.com/logo.png',
                  billing_email: '<EMAIL>',
                  affiliateId: 'affiliate123',
                  displayName: 'Sample Group',
                  // ❌ userId missing
                },
              ],
            }),
            getGroupDomainByGid: jest.fn().mockResolvedValue('https://lms.url'),
          };
        }
      });

      const spy = jest.spyOn(helper, 'setLearnerType').mockResolvedValue(false);

      await helper.loadViewData(1001);

      expect(spy).toHaveBeenCalledWith(undefined, '<EMAIL>', 0);
    });

    it('should apply default values when groupData fields are missing', async () => {
      mockConfigService.get.mockResolvedValue(999);

      mockHelperService.get.mockImplementation((token: any) => {
        if (token === EnterpriseService) {
          return {
            getGroupByGid: jest.fn().mockResolvedValue({
              data: [{}], // ⛔ all fields missing
            }),
            getGroupDomainByGid: jest.fn().mockResolvedValue(undefined),
          };
        }
      });

      jest.spyOn(helper, 'setLearnerType').mockResolvedValue(false);

      const result = await helper.loadViewData(1001);

      expect(result).toEqual({
        showCertificateTab: 0,
        isEnableSamlAuthentication: 0,
        lmsUrl: '',
        atpLogoUrl: '',
        isPgLearner: false,
        affiliateId: '',
        groupName: '',
      });
    });

  });

  describe('setLearnerType', () => {
    const userId = 123;
    const userEmail = '<EMAIL>';

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return null if userId or userEmail is missing', async () => {
      const result1 = await helper.setLearnerType(null, userEmail, 0);
      const result2 = await helper.setLearnerType(userId, '', 0);
      expect(result1).toBeNull();
      expect(result2).toBeNull();
    });

    it('should return data if flag is 0 and firebase response is successful', async () => {
      const mockResponse = { status: 'success', data: true };
      const mockFirebaseService = {
        getPgLearnerFromCache: jest.fn().mockResolvedValue(mockResponse),
      };

      mockHelperService.get.mockResolvedValue(mockFirebaseService);

      const result = await helper.setLearnerType(userId, userEmail, 0);

      expect(result).toBe(true);
      expect(mockFirebaseService.getPgLearnerFromCache).toHaveBeenCalledWith(userId, userEmail);
    });

    it('should return data if firebase response is successful', async () => {
      const mockResponse = { status: 'success', data: true };

      const mockFirebaseService = {
        getPgLearnerFromCache: jest.fn().mockResolvedValue(mockResponse),
      };

      mockHelperService.get.mockResolvedValue(mockFirebaseService);

      const result = await helper.setLearnerType(userId, userEmail, 0); // ✅ must be 0

      expect(result).toBe(true);
      expect(mockFirebaseService.getPgLearnerFromCache).toHaveBeenCalledWith(userId, userEmail);
    });


    it('should return false if firebase status is not success', async () => {
      const mockResponse = { status: 'failed', data: null };
      const mockFirebaseService = {
        getPgLearnerFromCache: jest.fn().mockResolvedValue(mockResponse),
      };

      mockHelperService.get.mockResolvedValue(mockFirebaseService);

      const result = await helper.setLearnerType(userId, userEmail, 0);

      expect(result).toBe(false);
    });

    it('should log and throw error if exception occurs', async () => {
      const mockError = new Error('Firebase failed');
      mockHelperService.get.mockRejectedValue(mockError);
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation(undefined as any);

      await expect(helper.setLearnerType(userId, userEmail, 0)).rejects.toThrow('Firebase failed');
      expect(loggerSpy).toHaveBeenCalledWith('setLearnerType', expect.objectContaining({
        METHOD: expect.stringContaining('setLearnerType'),
        MESSAGE: 'Firebase failed',
      }));
    });
  });

  describe('userPasswordRehash', () => {
    const forgotPasswordDto: ForgotPasswordB2B = {
      client_id: 'client123',
      gid: '123',
      affiliateLmsUrl: 'https://affiliate.url',
      affiliateLogoUrl: 'https://logo.url',
      providerName: 'ProviderX',
      affiliateName: 'AffiliateY',
      isWhiteLabelingEnabled: '',
      fromEmailAddress: '<EMAIL>',
      email: '<EMAIL>',
      planType: 'standard',
      displayName: 'John Doe',
      uid: 101,
    };

    const userData: Partial<User> = {
      login: new Date('2023-01-01'), // ✅ Use a string or a Date object
      uid: 101,
      password: 'securePass123',
    };

    it('should return token if inputs are valid', async () => {
      const expectedToken = 'mocked-token';

      const mockTokenHelper = {
        drupalHmacBase64: jest.fn().mockReturnValue(expectedToken),
      };

      mockHelperService.getHelper.mockResolvedValue(mockTokenHelper);
      mockConfigService.get.mockReturnValue('drupalSaltValue');

      const userData: Partial<User> = {
        login: new Date('2023-01-01'),
        uid: 101,
        password: 'securePass123',
      };

      const result = await helper.userPasswordRehash(forgotPasswordDto, userData);

      // Construct expected data and key
      const expectedData = `${userData.login}${userData.uid}`; // this will be a string like "Sun Jan 01 2023...101"
      const expectedKey = `drupalSaltValue${userData.password}`;

      expect(result).toBe(expectedToken);
      expect(mockTokenHelper.drupalHmacBase64).toHaveBeenCalledWith(expectedData, expectedKey);
    });



    it('should log error and throw BadRequestException on failure', async () => {
      const mockError = new Error('Helper not found');
      mockHelperService.getHelper.mockRejectedValue(mockError);

      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation(undefined as any);

      await expect(helper.userPasswordRehash(forgotPasswordDto, userData)).rejects.toThrow(BadRequestException);
      expect(loggerSpy).toHaveBeenCalledWith('userPasswordRehash', expect.objectContaining({
        METHOD: `${helper.constructor.name}@userPasswordRehash`,
        MESSAGE: mockError.message,
      }));
    });
  });
});
