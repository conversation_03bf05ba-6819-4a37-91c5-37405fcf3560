import {BadRequestException, Body, Controller, Inject, Post, UseFilters } from "@nestjs/common";
import { HttpExceptionFilter } from "../../common/filters/http.exception.filter";
import { APILog } from "../../logging/logger";
import { CommunicationService } from "../services/communication.service";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

@ApiTags('Internal Communication API')
@Controller('internal/communication')
export class CommunicationController{
  @Inject() private readonly communicationService: CommunicationService;

  //Consumed by communication
  @Post('/get-display-names-by-ids')
  @ApiOperation({
      summary: 'Gets all display names by ids',
      description: 'This API fetches all display names by ids and provides the response with uid and display name',
    })
  @ApiResponse({ status: 200, description: 'Display name fetch successfully' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async getDisplayNamesByIds(@Body() reqData) {
    let result = {
      'status': 'failed',
      'data': [],
      'message': 'Some error occured while fetching the records.'
    };
    try {
      const displayNames = await this.communicationService.getDisplayNamesByIds(reqData);
      return displayNames;
    } catch (error: any) {
        APILog.error('getDisplayNamesByIds', {
        METHOD: `${this.constructor.name}@${this.getDisplayNamesByIds.name}`,
        MESSAGE: error.message,
        REQUEST: reqData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
        });
        if (error instanceof BadRequestException) {
          return result.data;
        } else {
          return result;
        }
    }
  }
  
//Consumed by Communication
  @Post('/get-user-id-by-email')
  @ApiOperation({
      summary: 'Gets all User Ids by email',
      description: 'This API fetches all user IDs by email and provides the response with uid',
    })
  @ApiResponse({ status: 200, description: 'User Ids fetch successfully' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async getUserIdByEmail(@Body() reqData) {
    let result = {
      'status': 'failed',
      'data': [],
      'message': 'Some error occured while fetching the records.'
    };
    try {
      const userIds = await this.communicationService.getUserIdByEmail(reqData);
      return userIds;
    } catch (error: any) {
        APILog.error('getUserIdByEmail', {
        METHOD: `${this.constructor.name}@${this.getUserIdByEmail.name}`,
        MESSAGE: error.message,
        REQUEST: reqData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
        });

        if (error instanceof BadRequestException) {
          return result.data;
        }
        return result;
    }
  }


}