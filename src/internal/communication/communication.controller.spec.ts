import { Test, TestingModule } from '@nestjs/testing';
import { CommunicationController } from './communication.controller';
import { CommunicationService } from '../services/communication.service';
import { HelperService } from '../../helper/helper.service';
import { APILog } from '../../logging/logger';
import { BadRequestException } from '@nestjs/common';

describe('CommunicationController', () => {
  let controller: CommunicationController;
  let communicationService: CommunicationService;


  beforeEach(async () => {
    const mockCloud6Service = {
      getGroupNameByIds: jest.fn(),
      getUserNameByUids: jest.fn(),
      getUserTeamByEmail: jest.fn(),
      getUserGroupByEmail: jest.fn(),

    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CommunicationController],
      providers: [
        {
          provide: CommunicationService,
          useValue: {
            getDisplayNamesByIds: jest.fn(),
            getUserIdByEmail: jest.fn(),
          },
        },
        {
          provide: HelperService,
          useValue: {
            get: jest.fn().mockResolvedValue(mockCloud6Service),
          },
        },
      ],
    }).compile();

    controller = module.get<CommunicationController>(CommunicationController);
    communicationService = module.get<CommunicationService>(CommunicationService);

  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getDisplayNamesByIds', () => {
    it('should return display names when communicationService resolves data', async () => {
      const reqData = { ids: ['1', '2', '3'] };
      const mockResponse = [{ uid: '1', displayName: 'John Doe' }];
      jest.spyOn(communicationService, 'getDisplayNamesByIds').mockResolvedValue(mockResponse);

      const result = await controller.getDisplayNamesByIds(reqData);
      expect(result).toEqual(mockResponse);
    });

    it('should return failure response when communicationService throws an error', async () => {
      const reqData = { ids: ['1', '2', '3'] };
      jest.spyOn(communicationService, 'getDisplayNamesByIds').mockRejectedValue(new Error('Unexpected error'));

      const result = await controller.getDisplayNamesByIds(reqData);
      expect(result).toEqual({
        status: 'failed',
        data: [],
        message: 'Some error occured while fetching the records.',
      });
    });

    it('should return empty data array when BadRequestException is thrown', async () => {
      const reqData = { ids: ['1', '2', '3'] };
      const mockError = new BadRequestException('Invalid input');

      jest.spyOn(communicationService, 'getDisplayNamesByIds').mockRejectedValue(mockError);
      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();

      const result = await controller.getDisplayNamesByIds(reqData);

      expect(result).toEqual([]); // Because return result.data when it's BadRequestException
      expect(logSpy).toHaveBeenCalledWith('getDisplayNamesByIds', expect.objectContaining({
        METHOD: expect.stringContaining('getDisplayNamesByIds'),
        MESSAGE: mockError.message,
        REQUEST: reqData,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });

  });

  describe('getUserIdByEmail', () => {
    it('should return user IDs on successful service response', async () => {
      const reqData = { emails: ['<EMAIL>'] };
      const mockResponse = { uid: 1 };
      jest.spyOn(communicationService, 'getUserIdByEmail').mockResolvedValue(mockResponse);

      const result = await controller.getUserIdByEmail(reqData);
      expect(result).toEqual(mockResponse);
    });

    it('should return failure response when an error occurs', async () => {
      const reqData = { emails: ['<EMAIL>'] };
      jest.spyOn(communicationService, 'getUserIdByEmail').mockRejectedValue(new Error('Fetch failed'));

      const result = await controller.getUserIdByEmail(reqData);
      expect(result).toEqual({
        status: 'failed',
        data: [],
        message: 'Some error occured while fetching the records.',
      });
    });

    it('should return empty data array when BadRequestException is thrown', async () => {
      const reqData = { emails: ['<EMAIL>'] };
      const mockError = new BadRequestException('Invalid email');

      jest.spyOn(communicationService, 'getUserIdByEmail').mockRejectedValue(mockError);
      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();

      const result = await controller.getUserIdByEmail(reqData);

      expect(result).toEqual([]); // Because result.data is returned in BadRequestException
      expect(logSpy).toHaveBeenCalledWith('getUserIdByEmail', expect.objectContaining({
        METHOD: expect.stringContaining('getUserIdByEmail'),
        MESSAGE: mockError.message,
        REQUEST: reqData,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });

  });






});
