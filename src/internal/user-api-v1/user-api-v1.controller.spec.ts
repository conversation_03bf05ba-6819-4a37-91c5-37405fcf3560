import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { APILog } from '../../logging/logger';
import { UserApiV1Controller } from './user-api-v1.controller';
import { HelperService } from '../../helper/helper.service';
import { UserApiInternalService } from '../services/user.api.internal.service';
import { UpdateUsernameVisibilityDto } from '../dto/update-username-visibility.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  BadRequestException,
  HttpStatus,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthService } from '../../auth/services/auth/auth.service';
import { UpdateUserRoleDto } from '../dto/update-user-role.dto';
import { UpdateUserProfileDto } from '../dto/update-user-profile.dto';
import { Utility } from '../../common/util/utility';
import { Response } from 'express';
import { UpdateGdprStatusDto } from '../dto/update-gdpr-status.dto';
import { GdprRequestDto } from '../dto/gdpr-request.dto';
import { NpsFeedbackService } from '../../common/services/communication/nps-feedback/nps-feedback.service';
import { Logger } from '../../logging/logger';
import { ResetPassDto } from '../dto/reset-password.dto';

enum CareerType {
  Professional = 'professional',
  Student = 'student',
}


describe('UserApiV1Controller', () => {
  let controller: UserApiV1Controller;
  let helperService: HelperService;
  let configService: ConfigService;
  let userApiService: UserApiInternalService;

  // Mock AuthService
  const authServiceMock = {
    getTokenByEmail: jest.fn(),
  };
  // const cryptoHelperMock = {
  //   encryptDecrypt: jest.fn((type, value) => `encrypted_${type}_${value}`),
  // };

  const configServiceMock = {
    get: jest.fn(),
  };
  const mockEventEmitter = {
    emit: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserApiV1Controller],
      providers: [
        HelperService,
        UserApiInternalService,
        {
          provide: AuthService, // Replace with your AuthService class name
          useValue: authServiceMock,
        },
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
        {
          provide: 'CRYPTO_HELPER',
          useValue: {},
        },
        { provide: EventEmitter2, useValue: mockEventEmitter },
      ],
    }).compile();

    controller = module.get<UserApiV1Controller>(UserApiV1Controller);
    helperService = module.get<HelperService>(HelperService);
    configService = module.get<ConfigService>(ConfigService);
    userApiService = module.get<UserApiInternalService>(UserApiInternalService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('registerByEmail', () => {
    it('should call registerByEmail on UserHelper and return response', async () => {
      const userHelper = {
        registerByEmail: jest.fn().mockReturnValue('mocked-response'),
      };
      jest.spyOn(helperService, 'getHelper').mockResolvedValue(userHelper);

      const body = {
        /* Your request body here */
      };
      const response = await controller.registerByEmail(body);

      expect(helperService.getHelper).toHaveBeenCalledWith('UserHelper');
      expect(userHelper.registerByEmail).toHaveBeenCalledWith(body);
      expect(response).toEqual('mocked-response');
    });
    it('should throw InternalServerErrorException for unexpected errors', async () => {
      const mockBody = {
        email: '<EMAIL>',
        password: 'Password@123',
      };
      const unexpectedError = new Error('Unexpected failure');
      const userHelper = {
        registerByEmail: jest.fn().mockRejectedValue(unexpectedError),
      };
  
      jest.spyOn(helperService, 'getHelper').mockResolvedValue(userHelper);
  
      await expect(controller.registerByEmail(mockBody)).rejects.toThrow(Error);
      await expect(controller.registerByEmail(mockBody)).rejects.toThrow('Unexpected failure');
    });
  });

  describe('assignUserRole', () => {
    it('should assign user roles successfully', async () => {
      const reqBody = { client_id: '123', user_id: 456, user_roles: [] };
      const mockResponse = { type: 'success', msg: 'Update successful' };
      jest.spyOn(userApiService, 'assignUserRole').mockResolvedValue(mockResponse);
      const result = await controller.assignUserRole(reqBody);

      expect(result).toEqual({ status: HttpStatus.OK, ...mockResponse });
      expect(userApiService.assignUserRole).toHaveBeenCalledWith(reqBody);
    });

    it('should handle errors when assign user roles', async () => {
      const reqBody = { client_id: '123', user_id: 456, user_roles: [] };
      const mockError = new Error('Update failed');
      jest.spyOn(userApiService, 'assignUserRole').mockRejectedValue(mockError);

      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();

      try {
        await controller.assignUserRole(reqBody);
        fail('Expected error to be thrown');
      } catch (e) {
        expect(e).toBe(mockError);
        expect(logSpy).toHaveBeenCalledWith('assignUserRole', expect.objectContaining({
          METHOD: expect.stringContaining('assignUserRole'),
          MESSAGE: mockError.message,
          REQUEST: reqBody,
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        }));
      }
    });

  });

  describe('getProfileCompletionStats', () => {
    it('should get profile completion stats successfully', async () => {
      const reqBody = { client_id: '123', user_id: 456 };
      const mockResponse = { type: 'success', status: 200, msg: 'Update successful' };
      jest.spyOn(userApiService, 'getProfileCompletionStats').mockResolvedValue(mockResponse);
      const result = await controller.getProfileCompletionStats(reqBody);

      expect(result).toEqual({ status: HttpStatus.OK, ...mockResponse });
      expect(userApiService.getProfileCompletionStats).toHaveBeenCalledWith(reqBody);
    });

    it('should handle errors when getting profile completion stats', async () => {
      const reqBody = { client_id: '123', user_id: 456 };
      const mockError = new Error('Update failed');
      jest.spyOn(userApiService, 'getProfileCompletionStats').mockRejectedValue(mockError);

      try {
        await controller.getProfileCompletionStats(reqBody);
        // The test should not reach this point; it should throw an error.
        expect(true).toBe(false);
      } catch (e) {
        expect(e).toBe(mockError);
      }
    });
  });

  describe('updateUsernameVisibility', () => {
    it('should return a response with status 200 on successful update', async () => {
      // Mock the updateUsernameVisibility method in the service to return a successful response
      const mockResponse = { status: 200, message: 'Update successful' };
      jest.spyOn(userApiService, 'updateUsernameVisibility').mockResolvedValue(mockResponse);

      const updateDto: UpdateUsernameVisibilityDto = {
        user_id: 1003678,
        client_id: 'sl_looper',
        username: 'sumitraghav',
        visibility: 1,
      };
      const result = await controller.updateUsernameVisibility(updateDto);

      expect(result).toEqual({ status: HttpStatus.OK, ...mockResponse });
    });

    it('should handle errors and return status 500 on failure', async () => {
      const mockError = new Error('Update failed');
      jest.spyOn(userApiService, 'updateUsernameVisibility').mockRejectedValue(mockError);

      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();

      const updateDto: UpdateUsernameVisibilityDto = {
        user_id: 1234,
        client_id: 'sl_looper',
        username: 'sumitraghav',
        visibility: 1,
      };

      try {
        await controller.updateUsernameVisibility(updateDto);
        fail('Expected error to be thrown');
      } catch (error) {
        expect(error).toEqual(mockError);
        expect(logSpy).toHaveBeenCalledWith('updateUsernameVisibility', expect.objectContaining({
          METHOD: expect.stringContaining('updateUsernameVisibility'),
          MESSAGE: mockError.message,
          REQUEST: updateDto,
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        }));
      }
    });

  });

  describe('updateUserProfileData', () => {
    const validData: UpdateUserProfileDto = {
      client_id: 'sl_looper',
      email: '',
      username: '',
      visibility: 0,
      user_id: '',
      dob: '',
      country_code: '',
      phone: '',
      city: '',
      user_career_type: CareerType.Professional,
      objective: '',
      training_funded_by: '',
      qualification: '',
    };

    it('should return success response when updateUserProfileData succeeds', async () => {
      const mockResponse = { type: 'success', msg: 'Profile updated' };

      jest
        .spyOn(userApiService, 'updateUserProfileData')
        .mockResolvedValue(mockResponse);

      const result = await controller.updateUserProfileData(validData);

      expect(result).toEqual(mockResponse);
      expect(userApiService.updateUserProfileData).toHaveBeenCalledWith(validData);
    });

    it('should handle errors and return error response', async () => {
      const mockError = new Error('Something went wrong');
      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();

      jest
        .spyOn(userApiService, 'updateUserProfileData')
        .mockRejectedValue(mockError);

      const result = await controller.updateUserProfileData(validData);

      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while updating profile. Please try again.',
      });

      expect(logSpy).toHaveBeenCalledWith('updateUserProfileData', expect.objectContaining({
        METHOD: expect.stringContaining('updateUserProfileData'),
        MESSAGE: mockError.message,
        REQUEST: validData,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });
  });


  describe('getTokenByEmail', () => {
    it('should return a token when the service call is successful', async () => {
      const mockToken = { idToken: 'mocked-token' };
      jest.spyOn(userApiService, 'getTokenByEmail').mockResolvedValue(mockToken);

      const requestBody = {
        user_email: '<EMAIL>',
        client_id: 'client123',
        redirect_url: 'https://example.com',
      };

      const mockRes = {
        redirect: jest.fn(),
      };

      const result = await controller.getTokenByEmail(requestBody, mockRes);

      expect(result).toBeUndefined();
      expect(mockRes.redirect).toHaveBeenCalledWith(
        'https://example.com/?type=success&_t=mocked-token'
      );
      expect(userApiService.getTokenByEmail).toHaveBeenCalledWith(requestBody);
    });

    it('should return a token in the response when redirect_url is not provided', async () => {
      const mockToken = { idToken: 'mocked-token' };
      jest.spyOn(userApiService, 'getTokenByEmail').mockResolvedValue(mockToken);

      const requestBody = {
        user_email: '<EMAIL>',
        client_id: 'client123',
        redirect_url: null,
      };

      const result = await controller.getTokenByEmail(requestBody, {});

      expect(result).toEqual({
        type: 'success',
        _t: mockToken.idToken,
      });
      expect(userApiService.getTokenByEmail).toHaveBeenCalledWith(requestBody);
    });

    it('should throw UnauthorizedException for invalid redirect_url', async () => {
      const mockToken = { idToken: 'mocked-token' };
      jest.spyOn(userApiService, 'getTokenByEmail').mockResolvedValue(mockToken);

      const requestBody = {
        user_email: '<EMAIL>',
        client_id: 'client123',
        redirect_url: 'invalid-url',
      };

      const mockRes = {
        redirect: jest.fn(),
      };
      jest.spyOn(Utility, 'sanitizeRedirectUrl').mockReturnValue(null);

      await expect(controller.getTokenByEmail(requestBody, mockRes)).rejects.toThrow('Unauthorized access request.');
    });

    it('should log an error and re-throw it when the service call fails', async () => {
      const mockError = new Error('Token retrieval failed');
      jest.spyOn(userApiService, 'getTokenByEmail').mockRejectedValue(mockError);

      const requestBody = {
        user_email: '<EMAIL>',
        client_id: 'client123',
        redirect_url: 'https://example.com',
      };

      const logSpy = jest.spyOn(APILog, 'error');

      try {
        await controller.getTokenByEmail(requestBody, {});
      } catch (error) {
        expect(error).toBe(mockError);
        expect(logSpy).toHaveBeenCalledWith('getTokenByEmail', {
          METHOD: `${controller.constructor.name}@getTokenByEmail`,
          MESSAGE: mockError.message,
          REQUEST: requestBody,
          RESPONSE: mockError.stack,
          TIMESTAMP: expect.any(Number),
        });
      }
    });

    it('should throw UnauthorizedException with a custom message for unauthorized access', async () => {
      const mockError = new UnauthorizedException();
      jest.spyOn(userApiService, 'getTokenByEmail').mockRejectedValue(mockError);

      const requestBody = {
        user_email: '<EMAIL>',
        client_id: 'client123',
        redirect_url: 'https://example.com',
      };

      jest.spyOn(configService, 'get').mockReturnValue('Unauthorized access request.');

      await expect(controller.getTokenByEmail(requestBody, {})).rejects.toThrow(
        new UnauthorizedException('Unauthorized access request.')
      );
    });

    it('should throw InternalServerErrorException when idToken is missing', async () => {
      const mockToken = {};
      jest.spyOn(userApiService, 'getTokenByEmail').mockResolvedValue(mockToken);

      const requestBody = {
        user_email: '<EMAIL>',
        client_id: 'client123',
        redirect_url: null,
      };

      await expect(controller.getTokenByEmail(requestBody, {})).rejects.toThrow(
        InternalServerErrorException
      );
    });
  });

  describe('deactivation user status', () => {
    const requestBody = {
      user_email: '<EMAIL>',
      client_id: 'client123',
      redirect_url: 'https://example.com',
    };

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should return success response when deactivation is successful', async () => {
      const mockResponse = { message: 'User deactivated successfully' };
      jest.spyOn(userApiService, 'deactivateUserStatus').mockResolvedValue(mockResponse);

      const result = await controller.deactivateUserByEmail(requestBody);

      expect(result).toEqual({
        type: 'success',
        msg: 'User account has been blocked successfully .',
      });
    });

    it('should return error message for UserAlreadyBlock', async () => {
      const errorInstance = new Error('UserAlreadyBlock');
      jest.spyOn(userApiService, 'deactivateUserStatus').mockResolvedValue(errorInstance);

      const result = await controller.deactivateUserByEmail(requestBody);

      expect(result).toEqual({
        type: 'error',
        msg: 'User account is already in blocked state.',
      });
    });

    it('should return error message for UserNotFoundException', async () => {
      const errorInstance = new Error('UserNotFoundException');
      jest.spyOn(userApiService, 'deactivateUserStatus').mockResolvedValue(errorInstance);

      const result = await controller.deactivateUserByEmail(requestBody);

      expect(result).toEqual({
        type: 'error',
        msg: 'No active account associated with this email address.',
      });
    });

    it('should return default error message for unknown error object', async () => {
      const errorInstance = new Error('UnknownError');
      jest.spyOn(userApiService, 'deactivateUserStatus').mockResolvedValue(errorInstance);

      const result = await controller.deactivateUserByEmail(requestBody);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred.',
      });
    });

    it('should handle BadRequestException with array message', async () => {
      const badRequest = new BadRequestException({ message: ['Invalid email format'] });
      jest.spyOn(userApiService, 'deactivateUserStatus').mockRejectedValue(badRequest);

      const result = await controller.deactivateUserByEmail(requestBody);

      expect(result).toEqual({
        type: 'error',
        msg: 'Invalid email format',
      });
    });

    it('should handle BadRequestException with string message', async () => {
      const badRequest = new BadRequestException('Invalid request');
      jest.spyOn(userApiService, 'deactivateUserStatus').mockRejectedValue(badRequest);

      const result = await controller.deactivateUserByEmail(requestBody);

      expect(result).toEqual({
        type: 'error',
        msg: 'Invalid request',
      });
    });

    it('should handle unexpected errors and return fallback message', async () => {
      const genericError = new Error('Unexpected DB failure');
      jest.spyOn(userApiService, 'deactivateUserStatus').mockRejectedValue(genericError);

      const result = await controller.deactivateUserByEmail(requestBody);

      expect(result).toEqual({
        type: 'error',
        msg: 'An unexpected error occurred.',
      });
    });

    it('should call APILog.error when an exception is thrown', async () => {
      const mockError = new Error('Some crash');
      jest.spyOn(userApiService, 'deactivateUserStatus').mockRejectedValue(mockError);

      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();

      await controller.deactivateUserByEmail(requestBody);

      expect(logSpy).toHaveBeenCalledWith('deactivateUserByEmail', expect.objectContaining({
        METHOD: expect.stringContaining('deactivateUserByEmail'),
        MESSAGE: mockError.message,
        REQUEST: requestBody,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });
    it('should return success message when activation is successful (enable = "1")', async () => {
      const activatedRequestBody = {
        ...requestBody,
        enable: "1",
      };
    
      const mockResponse = { message: 'User activated successfully' };
      jest.spyOn(userApiService, 'deactivateUserStatus').mockResolvedValue(mockResponse);
    
      const result = await controller.deactivateUserByEmail(activatedRequestBody);
    
      expect(result).toEqual({
        type: 'success',
        msg: 'User account has been activated successfully.',
      });
    });
    
  });


  describe('updateEngagexUserRole', () => {
    it('should return success response when updateRoleResponse is not an error', async () => {
      // Arrange
      const updateRoleResponse = { type: 'success', idToken: 'mocked-token' };
      jest.spyOn(controller['userApiService'], 'updateEngagexRole').mockResolvedValue(updateRoleResponse);

      // Act
      const requestBody: UpdateUserRoleDto = {
        client_id: 'sl_looper',
        user_id: 1003663,
        updated_role: 'engagex_csm',
      };
      const result = await controller.updateEngagexUserRole(requestBody);

      // Assert
      expect(result).toEqual({ type: 'success', msg: "User role successfully updated", _t: 'mocked-token' });
    });

    it('should throw BadRequestException when updateRoleResponse is RoleUserInvalid', async () => {
      // Arrange
      const updateRoleResponse = new Error('RoleUserInvalid');
      jest.spyOn(controller['userApiService'], 'updateEngagexRole').mockResolvedValue(updateRoleResponse);

      // Act and Assert
      const requestBody: UpdateUserRoleDto = {
        client_id: 'sl_looper',
        user_id: 1003663,
        updated_role: 'engagex_csm',
      };
      await expect(controller.updateEngagexUserRole(requestBody)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException when updateRoleResponse is RoleUserNotExist', async () => {
      // Arrange
      const updateRoleResponse = new Error('RoleUserNotExist');
      jest.spyOn(controller['userApiService'], 'updateEngagexRole').mockResolvedValue(updateRoleResponse);

      // Act and Assert
      const requestBody: UpdateUserRoleDto = {
        client_id: 'sl_looper',
        user_id: 1003663,
        updated_role: 'engagex_csm',
      };
      await expect(controller.updateEngagexUserRole(requestBody)).rejects.toThrow(BadRequestException);
    });

    it('should return success response when role is already assigned', async () => {
      // Arrange
      const updateRoleResponse = {
        type: 'success',
        msg: 'User role successfully updated',
        _t: undefined,
      };
      jest
        .spyOn(controller['userApiService'], 'updateEngagexRole')
        .mockResolvedValue(updateRoleResponse);

      const requestBody: UpdateUserRoleDto = {
        client_id: 'sl_looper',
        user_id: 1003663,
        updated_role: 'engagex_csm',
      };

      // Act
      const result = await controller.updateEngagexUserRole(requestBody);

      // Assert
      expect(result).toEqual(updateRoleResponse);
    });


    it('should throw BadRequestException with default message for unknown error', async () => {
      // Arrange
      const updateRoleResponse = new Error('SomeUnexpectedError');
      jest.spyOn(controller['userApiService'], 'updateEngagexRole').mockResolvedValue(updateRoleResponse);

      // Act and Assert
      const requestBody: UpdateUserRoleDto = {
        client_id: 'sl_looper',
        user_id: 1003663,
        updated_role: 'engagex_csm',
      };
      await expect(controller.updateEngagexUserRole(requestBody)).rejects.toThrow(BadRequestException);
    });

    it('should throw InternalServerErrorException when an unknown error occurs', async () => {
      // Arrange
      const updateRoleResponse = new Error('SomeUnexpectedError');
      jest.spyOn(controller['userApiService'], 'updateEngagexRole').mockRejectedValue(updateRoleResponse);

      // Act and Assert
      const requestBody: UpdateUserRoleDto = {
        client_id: 'sl_looper',
        user_id: 1003663,
        updated_role: 'engagex_csm',
      };
      await expect(controller.updateEngagexUserRole(requestBody)).rejects.toThrow(InternalServerErrorException);
    });
    it('should return error object for UnauthorizedException', async () => {
      const requestBody: UpdateUserRoleDto = {
        client_id: 'sl_looper',
        user_id: 1003663,
        updated_role: 'engagex_csm',
      };
      const mockError = new UnauthorizedException('Unauthorized access request.');
      jest.spyOn(controller['userApiService'], 'updateEngagexRole').mockRejectedValue(mockError);

      const result = await controller.updateEngagexUserRole(requestBody);

      expect(result).toEqual({
        type: 'error',
        msg: 'Unauthorized access request.',
      });
    });
    it('should return success response with custom message when role is already assigned (RoleAlreadyAssign)', async () => {
      // Arrange
      const updateRoleResponse = new Error('RoleAlreadyAssign');
      jest
        .spyOn(controller['userApiService'], 'updateEngagexRole')
        .mockResolvedValue(updateRoleResponse);
    
      const requestBody: UpdateUserRoleDto = {
        client_id: 'sl_looper',
        user_id: 1003663,
        updated_role: 'engagex_csm',
      };
    
      // Act
      const result = await controller.updateEngagexUserRole(requestBody);
    
      // Assert
      expect(result).toEqual({
        type: 'success',
        msg: 'Role already assigned to user',
        _t: undefined,
      });
    });
    
  });

  describe('checkAccountSetup', () => {
    it('should return a response with status 200 on successful account setup check', async () => {
      // Mock the checkAccountSetup method in the service to return a successful response
      const mockResponse = { status: 200, message: 'Account setup check successful' };
      jest.spyOn(userApiService, 'checkAccountSetup').mockResolvedValue(mockResponse);

      const requestBody = { user_email: '<EMAIL>' };
      const result = await controller.checkAccountSetup(requestBody);

      expect(result).toEqual({ status: HttpStatus.OK, ...mockResponse });
    });

    it('should handle errors and return status 400 on failure', async () => {
      // Arrange
      const mockError = new Error('Account setup check failed');
      jest.spyOn(userApiService, 'checkAccountSetup').mockRejectedValue(mockError);
      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();

      const requestBody = { user_email: '<EMAIL>' };

      try {
        await controller.checkAccountSetup(requestBody);
        fail('Expected error to be thrown');
      } catch (error) {
        // Assert error is rethrown
        expect(error).toEqual(mockError);

        // Assert APILog.error is called
        expect(logSpy).toHaveBeenCalledWith('checkAccountSetup', expect.objectContaining({
          METHOD: expect.stringContaining('checkAccountSetup'),
          MESSAGE: mockError.message,
          REQUEST: requestBody,
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        }));
      }
    });

  });

  describe('encryptDecryptJwtPayload', () => {
    it('should return encrypted payload for valid input', async () => {
      const requestBody = {
        api_key: 'your_api_key',
        id: '12345',
        uname: 'username',
        email: '<EMAIL>',
        gid: '56789',
        type: 'encrypt',
      };

      const expectedResponse = {
        status: 200,
        message: 'Encryption successful',
        data: requestBody,
      };

      configServiceMock.get.mockReturnValue('your_api_key');

      jest
        .spyOn(userApiService, 'encryptDecryptJwtPayload')
        .mockResolvedValue(expectedResponse);

      const result = await controller.encryptDecryptJwtPayload(requestBody);

      expect(result).toEqual(expectedResponse);
      expect(userApiService.encryptDecryptJwtPayload).toHaveBeenCalledWith(requestBody);
    });

    it('should throw UnauthorizedException for invalid API key', async () => {
      const requestBody = {
        api_key: 'invalid_api_key',
        id: '12345',
        uname: 'username',
        email: '<EMAIL>',
        gid: '56789',
        type: 'encrypt',
      };

      configServiceMock.get.mockReturnValue('valid_api_key');

      await expect(controller.encryptDecryptJwtPayload(requestBody)).rejects.toThrow(
        UnauthorizedException,
      );
    });

    it('should throw NotFoundException for unknown type', async () => {
      const requestBody = {
        api_key: 'your_api_key',
        id: '12345',
        uname: 'username',
        email: '<EMAIL>',
        gid: '56789',
        type: 'unknown', // Unknown type
      };

      configServiceMock.get.mockReturnValue('your_api_key');

      await expect(controller.encryptDecryptJwtPayload(requestBody)).rejects.toThrow(
        NotFoundException,
      );
    });
  });


  describe('userProfileData', () => {
    it('should return user profile data and set response status to OK', async () => {
      // Arrange
      const mockProfileData = {
        /* Your sample profile data */
      };
      jest.spyOn(controller['userApiService'], 'userProfileData').mockResolvedValue(mockProfileData);
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      // Act
      await controller.userProfileData({ uid: 123456 }, mockRes);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(mockRes.json).toHaveBeenCalledWith(mockProfileData);
    });

    it('should log error and re-throw when an error occurs', async () => {
      // Arrange
      const mockError = new Error('User ID is required.');
      jest.spyOn(controller['userApiService'], 'userProfileData').mockRejectedValue(mockError);

      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      // Act & Assert
      await expect(async () => {
        await controller.userProfileData({}, mockRes);
      }).rejects.toThrowError(mockError);

      // Verify logging
      // expect(APILog.error).toHaveBeenCalledWith('updateUserProfileData', {
      //   METHOD: expect.stringContaining('userProfileData'),
      //   MESSAGE: mockError.message,
      //   REQUEST: {uid: 123456},
      //   RESPONSE: mockError.stack,
      //   TIMESTAMP: expect.any(Number),
      // });
    });
  });

  describe('updateSkillupRecommendationInProfile', () => {
    it('should respond with status 200 on successful update', async () => {
      const reqBody = {
        user_id: '101',
        client_id: 'sl_looper',
        goal: 'Get AWS Certified',
        profession: 'Cloud Engineer',
      };

      const resMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      const serviceResponse = { type: 'success', msg: 'Updated successfully' };
      jest.spyOn(userApiService, 'updateSkillupRecommendationInProfile').mockResolvedValue(serviceResponse);

      await controller.updateSkillupRecommendationInProfile(reqBody, resMock as any);

      expect(resMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(resMock.json).toHaveBeenCalledWith(serviceResponse);
      expect(userApiService.updateSkillupRecommendationInProfile).toHaveBeenCalledWith(reqBody);
    });

    it('should log and throw error if service fails', async () => {
      const reqBody = {
        user_id: '101',
        client_id: 'sl_looper',
        goal: 'Get AWS Certified',
        profession: 'Cloud Engineer',
      };

      const resMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      const error = new Error('Update failed');
      jest.spyOn(userApiService, 'updateSkillupRecommendationInProfile').mockRejectedValue(error);
      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();

      await expect(controller.updateSkillupRecommendationInProfile(reqBody, resMock as any)).rejects.toThrow(error);

      expect(logSpy).toHaveBeenCalledWith('updateSkillupRecommendationInProfile', expect.objectContaining({
        METHOD: expect.stringContaining('updateSkillupRecommendationInProfile'),
        MESSAGE: error.message,
        REQUEST: reqBody,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });
  });

  describe('updateSkillupUserProfileData', () => {
    it('should respond with status 200 on success', async () => {
      const reqBody = {
        user_id: '101',
        client_id: 'sl_looper',
        username: 'sumit',
        email: '<EMAIL>',
      };

      const resMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      const mockResponse = { type: 'success', msg: 'Profile updated' };
      jest.spyOn(userApiService, 'updateSkillupUserProfileData').mockResolvedValue(mockResponse);

      await controller.updateSkillupUserProfileData(reqBody as any, resMock as any);

      expect(resMock.status).toHaveBeenCalledWith(HttpStatus.OK);
      expect(resMock.json).toHaveBeenCalledWith(mockResponse);
      expect(userApiService.updateSkillupUserProfileData).toHaveBeenCalledWith(reqBody);
    });

    it('should log and throw error if service throws', async () => {
      const reqBody = {
        user_id: '101',
        client_id: 'sl_looper',
        username: 'sumit',
        email: '<EMAIL>',
      };

      const resMock = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      };

      const error = new Error('Failed to update');
      jest.spyOn(userApiService, 'updateSkillupUserProfileData').mockRejectedValue(error);

      await expect(controller.updateSkillupUserProfileData(reqBody as any, resMock as any)).rejects.toThrow(error);
    });
  });

  describe('verifyAuthTokenByEmail', () => {
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    } as unknown as Response;

    const requestBody = {
      user_email: '<EMAIL>',
      client_id: 'client123',
      authToken: 'auth-token-xyz',
    };
    const mockServiceResponse = { type: 'success', msg: 'Token verified',  data: 'some-data-or-token', };
    afterEach(() => {
      jest.clearAllMocks();
    });
  
    it('should return 200 with success response from service', async () => {
      jest
      .spyOn(userApiService, 'verifyAuthTokenByEmail')
      .mockResolvedValue(mockServiceResponse);
      await controller.verifyAuthTokenByEmail(requestBody, mockResponse);
  
      expect(userApiService.verifyAuthTokenByEmail).toHaveBeenCalledWith(requestBody);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(mockServiceResponse);
    });
  
    it('should log and throw error on failure', async () => {
      const mockError = new Error('Something went wrong');

  
      jest
        .spyOn(userApiService, 'verifyAuthTokenByEmail')
        .mockRejectedValue(mockError);
  
  
      await expect(
        controller.verifyAuthTokenByEmail(requestBody, mockResponse),
      ).rejects.toThrow(mockError);
  
      expect(APILog.error).toHaveBeenCalledWith(
        'verifyAuthTokenByEmail',
        expect.objectContaining({
          METHOD: expect.stringContaining('verifyAuthTokenByEmail'),
          MESSAGE: mockError.message,
          REQUEST: requestBody,
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        }),
      );
    });
  });


  describe('getDataByEmailOrUid', () => {
    const mockResponse = {
      type: 'success',
      msg: 'User data fetched successfully',
      data: {
        name: 'Test User',
        email: '<EMAIL>',
      },
    };
    
  
    afterEach(() => {
      jest.clearAllMocks();
    });
  
    it('should return data when email is provided', async () => {
      jest.spyOn(userApiService, 'getDataByEmailOrUid').mockResolvedValue(mockResponse);
  
      const result = await controller.getDataByEmailOrUid('<EMAIL>', undefined);
  
      expect(userApiService.getDataByEmailOrUid).toHaveBeenCalledWith({ email: '<EMAIL>', uid: undefined });
      expect(result).toEqual({ status: 200, data: mockResponse });
    });
  
    it('should return data when uid is provided', async () => {
      jest.spyOn(userApiService, 'getDataByEmailOrUid').mockResolvedValue(mockResponse);
  
      const result = await controller.getDataByEmailOrUid(undefined, 12345);
  
      expect(userApiService.getDataByEmailOrUid).toHaveBeenCalledWith({ email: undefined, uid: 12345 });
      expect(result).toEqual({ status: 200, data: mockResponse });
    });
  
    it('should throw BadRequestException when neither email nor uid is provided', async () => {
      await expect(controller.getDataByEmailOrUid(undefined, undefined)).rejects.toThrow(
        new BadRequestException('Either email or uid must be provided.'),
      );
    });
  
    it('should log and re-throw unexpected error from the service', async () => {
      const error = new Error('Unexpected DB failure');
      jest.spyOn(userApiService, 'getDataByEmailOrUid').mockRejectedValue(error);
      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();
  
      await expect(controller.getDataByEmailOrUid('<EMAIL>', undefined)).rejects.toThrow(error);
  
      expect(logSpy).toHaveBeenCalledWith(
        'getDataByEmailOrUid',
        expect.objectContaining({
          METHOD: expect.stringContaining('getDataByEmailOrUid'),
          MESSAGE: error.message,
          REQUEST: { email: '<EMAIL>', uid: undefined },
          RESPONSE: error.stack,
          TIMESTAMP: expect.any(Number),
        }),
      );
    });
  });

  describe('deactivateUserForGdpr', () => {
    const requestBody = { user_email: '<EMAIL>' };
  
    afterEach(() => {
      jest.clearAllMocks();
    });
  
    it('should return success message when user is deactivated successfully', async () => {
      const mockResponse = { status: 'success', msg: 'User is deactivated' };
      jest
        .spyOn(userApiService, 'deactivateUserForGdpr')
        .mockResolvedValue(mockResponse);
  
      const result = await controller.deactivateUserForGdpr(requestBody);
  
      expect(userApiService.deactivateUserForGdpr).toHaveBeenCalledWith(requestBody.user_email);
      expect(result).toEqual({ status: 'success', msg: 'User successfully deactivated' });
    });
  
    it('should throw BadRequestException when service response status is failed', async () => {
      const mockResponse = { status: 'failed', msg: 'Invalid user email' };
      jest
        .spyOn(userApiService, 'deactivateUserForGdpr')
        .mockResolvedValue(mockResponse);
  
      await expect(controller.deactivateUserForGdpr(requestBody)).rejects.toThrow(
        new BadRequestException('Invalid user email')
      );
    });
  
    it('should throw InternalServerErrorException on unexpected error', async () => {
      const mockError = new Error('DB down');
      jest
        .spyOn(userApiService, 'deactivateUserForGdpr')
        .mockRejectedValue(mockError);
  
      await expect(controller.deactivateUserForGdpr(requestBody)).rejects.toThrow(
        new InternalServerErrorException('Something went wrong while deactivating user')
      );
    });
  
    it('should rethrow BadRequestException if it is caught directly', async () => {
      const badRequest = new BadRequestException('Invalid format');
      jest
        .spyOn(userApiService, 'deactivateUserForGdpr')
        .mockRejectedValue(badRequest);
  
      await expect(controller.deactivateUserForGdpr(requestBody)).rejects.toThrow(badRequest);
    });
  });
  
  describe('registerUserFrs', () => {
    const userParams = {
      email: '<EMAIL>',
      name: 'Test User',
    };
  
    afterEach(() => {
      jest.clearAllMocks();
    });
  
    it('should return success response from service', async () => {
      const mockResponse: {
        type: string;
        msg: string;
        message: string;
        status: boolean;
        refCode: string;
      } = {
        type: 'success',
        msg: 'User registered successfully',
        message: 'User registered successfully',
        status: true,
        refCode: '200_OK',
      };
      
  
      jest
        .spyOn(userApiService, 'registerUserFrs')
        .mockResolvedValue(mockResponse);
  
      const result = await controller.registerUserFrs(userParams);
  
      expect(userApiService.registerUserFrs).toHaveBeenCalledWith(userParams);
      expect(result).toEqual(mockResponse);
    });
  
    it('should log and throw error on service failure', async () => {
      const mockError = new Error('Database error');
      jest
        .spyOn(userApiService, 'registerUserFrs')
        .mockRejectedValue(mockError);
  
      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();
  
      const result = await controller.registerUserFrs(userParams);
  
      expect(userApiService.registerUserFrs).toHaveBeenCalledWith(userParams);
      expect(logSpy).toHaveBeenCalledWith(
        'registerUserFrs',
        expect.objectContaining({
          METHOD: expect.stringContaining('registerUserFrs'),
          MESSAGE: mockError.message,
          REQUEST: userParams,
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(Number),
        }),
      );
  
      expect(result).toBeUndefined(); 
    });
  });
  
  describe('frsCompleteSignup', () => {
    const requestBody = {
      email: '<EMAIL>',
      client_id: 'client_123',
    };
  
    const mockReq = {
      cookies: {
        session: 'mock-session-cookie',
      },
    };
  
    afterEach(() => {
      jest.clearAllMocks();
    });
  
    it('should call userApiService.frsCompleteSignup with correct params', async () => {
      const spy = jest
        .spyOn(userApiService, 'frsCompleteSignup')
        .mockResolvedValue(undefined);
  
      await controller.frsCompleteSignup(requestBody, mockReq);
  
      expect(spy).toHaveBeenCalledWith(requestBody, mockReq.cookies);
    });
  
    it('should log error if userApiService.frsCompleteSignup throws', async () => {
      const error = new Error('Signup failed');
      jest
        .spyOn(userApiService, 'frsCompleteSignup')
        .mockRejectedValue(error);
  
      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();
  
      await controller.frsCompleteSignup(requestBody, mockReq);
  
      expect(logSpy).toHaveBeenCalledWith('frsCompleteSignup', expect.objectContaining({
        METHOD: expect.stringContaining('frsCompleteSignup'),
        MESSAGE: error,
        REQUEST: requestBody,
        TIMESTAMP: expect.any(Number),
      }));
    });
  });
  describe('updateGdprDeleteStatus', () => {
    const requestBody: UpdateGdprStatusDto = {
      email: '<EMAIL>',
      response_data: 'some-data',
    };
  
    afterEach(() => {
      jest.clearAllMocks();
    });
  
    it('should return service response when successful', async () => {
      const mockResponse = { type: 'success', msg: 'Status updated' };
      jest.spyOn(userApiService, 'updateGdprDeleteStatus').mockResolvedValue(mockResponse);
  
      const result = await controller.updateGdprDeleteStatus(requestBody);
      expect(result).toEqual(mockResponse);
    });
  
    it('should throw BadRequestException for UserNotFoundException', async () => {
      const errorResponse = new Error('UserNotFoundException');
      jest.spyOn(userApiService, 'updateGdprDeleteStatus').mockResolvedValue(errorResponse);
      jest.spyOn(configService, 'get').mockReturnValue('User does not exist');
  
      await expect(controller.updateGdprDeleteStatus(requestBody)).rejects.toThrow(
        new BadRequestException('User does not exist'),
      );
    });
  
    it('should throw BadRequestException for UserAlreadyBlock', async () => {
      const errorResponse = new Error('UserAlreadyBlock');
      jest.spyOn(userApiService, 'updateGdprDeleteStatus').mockResolvedValue(errorResponse);
      jest.spyOn(configService, 'get').mockReturnValue('User already blocked');
  
      await expect(controller.updateGdprDeleteStatus(requestBody)).rejects.toThrow(
        new BadRequestException('User already blocked'),
      );
    });
  
    it('should throw BadRequestException for unknown error message', async () => {
      const errorResponse = new Error('UnknownError');
      jest.spyOn(userApiService, 'updateGdprDeleteStatus').mockResolvedValue(errorResponse);
      jest.spyOn(configService, 'get').mockReturnValue('Some error occurred');
  
      await expect(controller.updateGdprDeleteStatus(requestBody)).rejects.toThrow(
        new BadRequestException('Some error occurred'),
      );
    });
  
    it('should log and throw InternalServerErrorException for unexpected errors', async () => {
      const unexpectedError = new Error('Database crash');
      jest.spyOn(userApiService, 'updateGdprDeleteStatus').mockRejectedValue(unexpectedError);
  
      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();
  
      await expect(controller.updateGdprDeleteStatus(requestBody)).rejects.toThrow(
        new InternalServerErrorException(),
      );
  
      expect(logSpy).toHaveBeenCalledWith('updateGdprDeleteStatus', expect.objectContaining({
        METHOD: expect.stringContaining('updateGdprDeleteStatus'),
        MESSAGE: unexpectedError.message,
        REQUEST: requestBody,
        RESPONSE: unexpectedError.stack,
        TIMESTAMP: expect.any(Number),
      }));
    });
  });
  
  describe('gdpr', () => {
    const requestBody: GdprRequestDto = {
      usid: 12345,
      useremail: '<EMAIL>',
      action: 'export',
    };
    
  
    afterEach(() => {
      jest.clearAllMocks();
    });
  
    it('should return successful response when service succeeds', async () => {
      const mockResponse = { status: 1, message: 'Export started' };
      jest.spyOn(userApiService, 'gdprAction').mockResolvedValue(mockResponse);
  
      const result = await controller.gdpr(requestBody);
  
      expect(result).toEqual(mockResponse);
    });
  
    it.each([
      ['ApiValidationFailed', 'Api validation failed'],
      ['MethodNotExists', 'Method does not exists'],
      ['unable to set start export status in Document', 'unable to set start export status in Document'],
      ['Unable to save in couch', 'Unable to save in couch'],
      ['UnknownError', 'An error occurred while processing the GDPR request'],
    ])('should throw BadRequestException for error: %s', async (errorMessage, expectedMsg) => {
      const error = new Error(errorMessage);
      jest.spyOn(userApiService, 'gdprAction').mockResolvedValue(error);
  
      await expect(controller.gdpr(requestBody)).rejects.toThrow(new BadRequestException(expectedMsg));
    });
  
    it('should return unauthorized response if UnauthorizedException is thrown', async () => {
      const error = new UnauthorizedException();
      jest.spyOn(userApiService, 'gdprAction').mockRejectedValue(error);
  
      const result = await controller.gdpr(requestBody);
      expect(result).toEqual({
        status: 0,
        data: '',
        message: 'Unauthorized access request.',
      });
    });
  
    it('should throw BadRequestException when service throws it', async () => {
      const error = new BadRequestException('Validation error');
      jest.spyOn(userApiService, 'gdprAction').mockRejectedValue(error);
  
      await expect(controller.gdpr(requestBody)).rejects.toThrow(new BadRequestException('Validation error'));
    });
  
    it('should throw InternalServerErrorException for unknown errors', async () => {
      const error = new Error('Something went wrong');
      jest.spyOn(userApiService, 'gdprAction').mockRejectedValue(error);
  
      await expect(controller.gdpr(requestBody)).rejects.toThrow(InternalServerErrorException);
    });
  
    it('should log the error using APILog on exception', async () => {
      const error = new Error('DB crash');
      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();
      jest.spyOn(userApiService, 'gdprAction').mockRejectedValue(error);
  
      await expect(controller.gdpr(requestBody)).rejects.toThrow(InternalServerErrorException);
  
      expect(logSpy).toHaveBeenCalledWith('gdpr', expect.objectContaining({
        METHOD: expect.stringContaining('@gdpr'),
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });
  });
  
  describe('sentinelSyncUserUpdate', () => {
    const requestBody = {
      uid: 1001,
      email: '<EMAIL>',
      roles: ['admin'],
    };
  
    afterEach(() => {
      jest.clearAllMocks();
    });
  
    it('should return response on successful update', async () => {
      const mockResponse = { success: true };
      jest.spyOn(userApiService, 'sentinelSyncUserUpdate').mockResolvedValue(mockResponse);
  
      const result = await controller.sentinelSyncUserUpdate(requestBody);
  
      expect(result).toEqual(mockResponse);
      expect(userApiService.sentinelSyncUserUpdate).toHaveBeenCalledWith(requestBody);
    });
  
    it('should throw BadRequestException when service throws BadRequestException', async () => {
      const mockError = new BadRequestException('Invalid input');
      jest.spyOn(userApiService, 'sentinelSyncUserUpdate').mockRejectedValue(mockError);
  
      await expect(controller.sentinelSyncUserUpdate(requestBody)).rejects.toThrow(BadRequestException);
    });
  
    it('should throw InternalServerErrorException for unexpected errors', async () => {
      const mockError = new Error('Unexpected DB failure');
      jest.spyOn(userApiService, 'sentinelSyncUserUpdate').mockRejectedValue(mockError);
  
      await expect(controller.sentinelSyncUserUpdate(requestBody)).rejects.toThrow(InternalServerErrorException);
    });
  
    it('should log error to APILog', async () => {
      const mockError = new Error('Some failure');
      const logSpy = jest.spyOn(APILog, 'error').mockImplementation();
      jest.spyOn(userApiService, 'sentinelSyncUserUpdate').mockRejectedValue(mockError);
  
      try {
        await controller.sentinelSyncUserUpdate(requestBody);
      } catch {}
  
      expect(logSpy).toHaveBeenCalledWith('sentinelSyncUserUpdate', expect.objectContaining({
        METHOD: expect.stringContaining('@sentinelSyncUserUpdate'),
        MESSAGE: mockError.message,
        REQUEST: requestBody,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));
    });
  });
  
  describe('getFeedbackForm', () => {
    let mockNpsService: Partial<NpsFeedbackService>;
  
    beforeEach(() => {
      mockNpsService = {
        getFeedbackForms: jest.fn(),
      };
      jest.clearAllMocks();
    });
  
    it('should return error if formId is missing', async () => {
      const query = { formId: '', formModule: 'module1' };
      const result = await controller.getFeedbackForm(query);
      expect(result).toEqual({
        status: 0,
        msg: 'Error - Param formId/formModule is empty',
      });
    });
  
    it('should return error if formModule is missing', async () => {
      const query = { formId: '123', formModule: '' };
      const result = await controller.getFeedbackForm(query);
      expect(result).toEqual({
        status: 0,
        msg: 'Error - Param formId/formModule is empty',
      });
    });
  
    it('should return error if service returns null', async () => {
      jest.spyOn(helperService, 'get').mockResolvedValue(mockNpsService as NpsFeedbackService);
      (mockNpsService.getFeedbackForms as jest.Mock).mockResolvedValue(null);
    
      const query = { formId: '123', formModule: 'module1' };
      const result = await controller.getFeedbackForm(query);
    
      expect(result).toEqual({
        status: 0,
        msg: 'Error - Failed to fetch nps form details',
      });
    });
    
    it('should return error if result has no data field', async () => {
      jest.spyOn(helperService, 'get').mockResolvedValue(mockNpsService as NpsFeedbackService);
      (mockNpsService.getFeedbackForms as jest.Mock).mockResolvedValue({});
    
      const query = { formId: '123', formModule: 'module1' };
      const result = await controller.getFeedbackForm(query);
    
      expect(result).toEqual({
        status: 0,
        msg: 'Error - Failed to fetch nps form details',
      });
    });
    
    
  
    it('should return form data if service returns valid data', async () => {
      const formData = { id: '123', title: 'Feedback Form' };
      jest.spyOn(helperService, 'get').mockResolvedValue(mockNpsService as NpsFeedbackService);
      (mockNpsService.getFeedbackForms as jest.Mock).mockResolvedValue({ data: formData });
  
      const query = { formId: '123', formModule: 'module1' };
      const result = await controller.getFeedbackForm(query);
      expect(result).toEqual(formData);
    });
  
    it('should log error and return fallback msg on exception', async () => {
      const mockError = new Error('DB failed');
      jest.spyOn(helperService, 'get').mockRejectedValue(mockError);
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation();
  
      const query = { formId: '123', formModule: 'module1' };
      const result = await controller.getFeedbackForm(query);
  
      expect(result).toEqual({
        status: 0,
        msg: 'Error - Failed to fetch nps form details',
      });
  
      expect(loggerSpy).toHaveBeenCalledWith(
        'getFeedbackForm',
        expect.objectContaining({
          METHOD: expect.stringContaining('@getFeedbackForm'),
          MESSAGE: mockError.message,
          REQUEST: query,
          RESPONSE: expect.any(String),
          TIMESTAMP: expect.any(String),
        })
      );
    });
  });


describe('UserApiV1Controller - saveUserFeedback', () => {
  let controller: UserApiV1Controller;
  let helperService: any;
  let mockNpsService: any;

  beforeEach(async () => {
    mockNpsService = {
      saveUserFeedback: jest.fn(),
    };

    helperService = {
      get: jest.fn().mockResolvedValue(mockNpsService),
    };

    controller = new UserApiV1Controller();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should call service and return result if feedback is valid', async () => {
    const body = { 'nps-save': 'test-feedback' };
    const expectedResult = { status: 1, msg: 'Feedback saved' };
    mockNpsService.saveUserFeedback.mockResolvedValue(expectedResult);

    const result = await controller.saveUserFeedback(body);

    expect(helperService.get).toHaveBeenCalledWith(NpsFeedbackService);
    expect(mockNpsService.saveUserFeedback).toHaveBeenCalledWith('test-feedback');
    expect(result).toEqual(expectedResult);
  }); 

  it('should return error if feedback data is missing', async () => {
    const body = { 'nps-save': '' };

    const result = await controller.saveUserFeedback(body);

    expect(result).toEqual({
      status: 0,
      msg: 'Error - Invalid feedback data format',
    });
    expect(helperService.get).not.toHaveBeenCalled();
  });

  it('should return error if service throws', async () => {
    const body = { 'nps-save': 'test-feedback' };
    mockNpsService.saveUserFeedback.mockRejectedValue(new Error('Service failure'));

    const result = await controller.saveUserFeedback(body);

    expect(result).toEqual({
      status: 0,
      msg: 'Error - Failed to save user feedback',
    });
  });
});


describe('UserApiV1Controller - resetPassword', () => {
  let controller: UserApiV1Controller;
  let userApiService: UserApiInternalService;
  let mockResponse: Partial<Response>;

  const mockResetDto: ResetPassDto = {
    user_email: '<EMAIL>',
    new_password: 'new-password',
    current_password: 'old-password',
    client_id: 'sl_looper',
    user_id: 1
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserApiV1Controller],
      providers: [
        {
          provide: UserApiInternalService,
          useValue: {
            resetPassword: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UserApiV1Controller>(UserApiV1Controller);
    userApiService = module.get<UserApiInternalService>(UserApiInternalService);

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
  });

  it('should return error response for known error message', async () => {
    (userApiService.resetPassword as jest.Mock).mockResolvedValue({
      type: 'error',
      msg: 'UserNotFoundException',
    });

    await controller.resetPassword(mockResponse as Response, mockResetDto);

    expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
    expect(mockResponse.json).toHaveBeenCalledWith({
      type: 'error',
      msg: 'User account does not exist.',
    });
  });

  it('should return error response for unknown error message', async () => {
    (userApiService.resetPassword as jest.Mock).mockResolvedValue({
      type: 'error',
      msg: 'UnexpectedError',
    });

    await controller.resetPassword(mockResponse as Response, mockResetDto);

    expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
    expect(mockResponse.json).toHaveBeenCalledWith({
      type: 'error',
      msg: 'UnexpectedError',
    });
  });

  it('should return success response when password reset succeeds', async () => {
    (userApiService.resetPassword as jest.Mock).mockResolvedValue({
      type: 'success',
      msg: 'Password reset successful',
    });

    await controller.resetPassword(mockResponse as Response, mockResetDto);

    expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.OK);
    expect(mockResponse.json).toHaveBeenCalledWith({
      type: 'success',
      msg: 'Password reset successful',
    });
  });

  it('should log error if exception is thrown', async () => {
    const error = new Error('Something went wrong');
    (userApiService.resetPassword as jest.Mock).mockRejectedValue(error);

    const spy = jest.spyOn(console, 'error').mockImplementation(() => {});

    await controller.resetPassword(mockResponse as Response, mockResetDto);

    expect(spy).toHaveBeenCalled();
    spy.mockRestore();
  });
});



});
