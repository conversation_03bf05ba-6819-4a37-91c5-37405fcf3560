import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  InternalServerErrorException,
  NotFoundException,
  Post,
  Query,
  Res,
  Req,
  UnauthorizedException,
  UseFilters,
  ValidationPipe,
  UseInterceptors
} from '@nestjs/common';
import {  ApiTags, ApiBody, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { APILog, Logger } from '../../logging/logger';
import { HelperService } from '../../helper/helper.service';
import { ConfigService } from '@nestjs/config';
import { UserApiInternalService } from '../services/user.api.internal.service';
import { CryptoHelper } from '../../helper/helper.crypto';
import { HttpExceptionFilter } from '../../common/filters/http.exception.filter';
import { UpdateUserRoleDto } from '../dto/update-user-role.dto';
import { UpdateUsernameVisibilityDto } from '../dto/update-username-visibility.dto';
import { UseAPIGuard } from '../../auth/guards/auth.guards';
import { AssignUserRoleDto } from '../dto/assign-user-role.dto';
import { UpdateUserProfileDto } from '../dto/update-user-profile.dto';
import { UpdateSkillUpUserDTO } from '../dto/update-skillip-user.dto';
import { deactivateUserByEmailDto } from '../dto/deactivate-user-byemail.dto';
import { DeactivateUserDto } from '../dto/deactivate-user-for-gdpr.dto';
import { GetTokenByEmailDto } from '../dto/get-token-by-email.dto';
import { Utility } from '../../common/util/utility';
import { UpdateGdprStatusDto } from '../dto/update-gdpr-status.dto';
import { GdprRequestDto } from '../dto/gdpr-request.dto';
import { SentinelSyncUserUpdateDto } from '../dto/sentinel-synch-user-update.dto';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';
import { NpsFeedbackService } from '../../common/services/communication/nps-feedback/nps-feedback.service';
import { ResetPassDto } from '../dto/reset-password.dto';
import {  Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
interface LogClientErrorDto {
  user_id: string;
  user_email: string;
  group_id: string;
  error_type: string;
}
@ApiTags('Internal User API V1')
@Controller('internal/user-api-v1')
export class UserApiV1Controller {
  @Inject() private readonly helperService: HelperService;
  @Inject() private readonly configService: ConfigService;
  @Inject() private readonly userApiService: UserApiInternalService;
  @Inject('CRYPTO_HELPER') private readonly cryptoHelper: CryptoHelper;

  /**
   * Generate Cloud6 Authorization Header
   * @description Generates an authorization header for Cloud6 API using HMAC.
   * @returns JSON object containing the authorization header and timestamp
   */
  @Get('/auth-token')
  @ApiOperation({
    summary: 'Generate Cloud6 Authorization Header',
    description: 'Generates an authorization header for Cloud6 API using HMAC.',
  })
  @ApiResponse({ status: 200, description: 'Authorization header generated successfully' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async generateCloud6AuthorizationHeader(): Promise<{
    'Content-Type': string;
    Authorization: string;
    timestamp: number;
  }> {
    try {
      // Generate the current timestamp in seconds
      const reqTimestamp = Math.round(new Date().getTime() / 1000);

      // Create the HMAC authorization header
      const authorization = this.cryptoHelper.createHmac(
        reqTimestamp.toString(),
        this.configService.get<string>('cloud6ApiAuthSecretSalt'),
        this.configService.get<string>('hmacAlgo'),
        this.configService.get('hmacEncoding'),
      );
      
      // Return the authorization header and timestamp
      return {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: authorization,
        timestamp: reqTimestamp,
      };
    } catch (error: any) {
      // Log the error for debugging and monitoring
      APILog.error('generateCloud6AuthorizationHeader', {
        METHOD: `${this.constructor.name}@${this.generateCloud6AuthorizationHeader.name}`,
        MESSAGE: error.message,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      // Rethrow the error to be handled by the exception filter
      throw new InternalServerErrorException('Failed to generate authorization header.');
    }
  }

  /**
   * Register User by Email
   * @description Registers a user using their email address. This API is used by multiple sources:
   * - internal/user-api-v1/register-by-email
   * - serverless/OrderProcessing
   * - serverless/B2BOrderProcessing
   * - ice9/library/BaseApp/Communication/Accounts.php
   * @param body - Object containing user registration details
   * @returns JSON response with the registration result
   * 
   * This api is consumed by Ice9, Serverless and Xenia.
   */
  @Post('/register-by-email')
  @ApiOperation({
    summary: 'Register User by Email',
    description: 'Registers a user using their email address.',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        email: { type: 'string', description: 'User email address' },
        password: { type: 'string', description: 'User password' },
        client_id: { type: 'string', description: 'Client ID' },
      },
      required: ['email', 'password', 'client_id'],
    },
  })
  @ApiResponse({ status: 201, description: 'User registered successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseInterceptors(FileInterceptor(''))
  @UseFilters(HttpExceptionFilter)
  @UseAPIGuard()
  async registerByEmail(@Body() body) {
    try {
      // Retrieve the UserHelper service
      const userHelper = await this.helperService.getHelper('UserHelper');

      // Call the helper method to register the user
      const response = await userHelper.registerByEmail(body);

      // Return the response with HTTP status
      return response;
    } catch (error: any) {
      // Log the error for debugging and monitoring
      APILog.error('registerByEmail', {
        METHOD: `${this.constructor.name}@${this.registerByEmail.name}`,
        MESSAGE: error.message,
        REQUEST: body,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      // Rethrow the error to be handled by the exception filter
      throw error;
    }
  }

  /**
   * Get Profile Completion Stats
   * @param reqBody - Object containing client_id and user_id
   * @returns JSON response with profile completion statistics
   * 
   * This api is consumed by Paperclip
   */
  @Post('/get-profile-completion-stats')
  @ApiOperation({
    summary: 'Get Profile Completion Stats',
    description: 'Retrieves the profile completion statistics for a given user.',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        client_id: { type: 'string', description: 'Client ID' },
        user_id: { type: 'string', description: 'User ID' },
      },
      required: ['client_id', 'user_id'],
    },
  })
  @ApiResponse({ status: 200, description: 'Profile completion stats retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  @HttpCode(HttpStatus.OK)
  @UseAPIGuard()
  async getProfileCompletionStats(
    @Body(new ValidationPipe({ whitelist: true })) reqBody: { client_id: string; user_id: number },
  ) {
    try {
      // Call the service to get profile completion stats
      const response = await this.userApiService.getProfileCompletionStats(reqBody);

      // Return the response with HTTP status
      return response;
    } catch (error: any) {
      // Log the error for debugging and monitoring
      APILog.error('getProfileCompletionStats', {
        METHOD: `${this.constructor.name}@${this.getProfileCompletionStats.name}`,
        MESSAGE: error.message,
        REQUEST: reqBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      // Rethrow the error to be handled by the exception filter
      throw error;
    }
  }

  /**
   * Update Username Visibility
   * @param reqBody - Data required to update the username visibility
   * @returns JSON response with the update result
   * 
   * This api is consumed by paperclip.
   */
  @Post('/update-username-visibility')
  @ApiOperation({
    summary: 'Update Username Visibility',
    description: 'Updates the visibility of the username based on the provided information.',
  })
  @ApiBody({
    type: UpdateUsernameVisibilityDto,
    description: 'Data required to update the username visibility',
  })
  @ApiResponse({ status: 200, description: 'Username visibility updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  @UseAPIGuard()
  async updateUsernameVisibility(
    @Body(new ValidationPipe()) reqBody: UpdateUsernameVisibilityDto,
  ) {
    try {
      // Call the service to update the username visibility
      const response = await this.userApiService.updateUsernameVisibility(reqBody);
      return response;
    } catch (error: any) {
      // Log the error for debugging and monitoring
      APILog.error('updateUsernameVisibility', {
        METHOD: `${this.constructor.name}@${this.updateUsernameVisibility.name}`,
        MESSAGE: error.message,
        REQUEST: reqBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      // Rethrow the error to be handled by the exception filter
      throw error;
    }
  }

  /**
   * Update User Profile Data
   * @param data - Data required to update the user profile
   * @param res - Response object
   * @returns JSON response with the update result
   * 
   * This api is consumed by Paperclip
   */
  @Post('/update-user-profile-data')
  @ApiOperation({
    summary: 'Update User Profile Data',
    description: 'Updates the user profile data with the provided information.',
  })
  @ApiBody({
    type: UpdateUserProfileDto,
    description: 'Data required to update the user profile',
  })
  @ApiResponse({ status: 200, description: 'User profile updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async updateUserProfileData(
    @Body(new ValidationPipe()) data: UpdateUserProfileDto,
  ) {
    try {
      // Call the service to update the user profile data      
      return  await this.userApiService.updateUserProfileData(data);
    } catch (error: any) {
      // Log the error for debugging and monitoring
      APILog.error('updateUserProfileData', {
        METHOD: `${this.constructor.name}@${this.updateUserProfileData.name}`,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      return { type: 'error', msg: 'Error occurred while updating profile. Please try again.'};
    }
  }

  /**
   * Get Token by Email
   * @param requestBody - Object containing user_email, client_id, and redirect_url
   * @returns JSON response with the generated token
   * 
   * This api is consumed by Ice9, Xenia, Paperclip and EngageX
   */
  @Post('/get-token-by-email')
  @ApiOperation({
    summary: 'Get Token by Email',
    description: 'Generates a token for a user based on the provided email, client ID, and redirect URL.',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        user_email: { type: 'string', description: 'User email address' },
        client_id: { type: 'string', description: 'Client ID' },
        redirect_url: { type: 'string', description: 'Redirect URL after token generation' },
      },
      required: ['user_email', 'client_id', 'redirect_url'],
    },
  })
  @ApiResponse({ status: 200, description: 'Token generated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  @UseAPIGuard()
  async getTokenByEmail(
    @Body(new ValidationPipe({ whitelist: true })) requestBody: GetTokenByEmailDto,
    @Res({ passthrough: true }) res,
  ) {
    try {
      const { redirect_url } = requestBody;

      // Generate the token
      const token = await this.userApiService.getTokenByEmail(requestBody);
      if (!token?.idToken) {
        throw new InternalServerErrorException('Failed to generate token.');
      }

      const responseObj = { type: 'success', _t: token.idToken };

      // Handle redirect if redirect_url is provided
      if (redirect_url) {
        const validUrl = Utility.sanitizeRedirectUrl(redirect_url);
        if (!validUrl) {
          throw new UnauthorizedException();
        }

        const url = new URL(validUrl);
        Object.entries(responseObj).forEach(([key, value]) => {
          url.searchParams.append(key, value);
        });

        return res.redirect(url.toString());
      }

      // Return the token in the response
      return responseObj;
    } catch (error: any) {
      APILog.error('getTokenByEmail', {
        METHOD: `${this.constructor.name}@${this.getTokenByEmail.name}`,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error?.stack,
        TIMESTAMP: new Date().getTime(),
      });

      if(error instanceof UnauthorizedException){
        throw new UnauthorizedException('Unauthorized access request.');
      }
      // Rethrow the error to be handled by the exception filter
      throw error;
    }
  }

  /**
   * Deactivate User by Email
   * @param requestBody - Object containing user_email, client_id, and redirect_url
   * @param res - Response object
   * @returns JSON response indicating the result of the deactivation
   * 
   * This api is consumed by Paperclip
   */
  @Post('/deactivate-user-by-email')
  @ApiOperation({
    summary: 'Deactivate User by Email',
    description: 'Deactivates a user account based on the provided email and client ID.',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        user_email: { type: 'string', description: 'User email address' },
        client_id: { type: 'string', description: 'Client ID' },
        redirect_url: { type: 'string', description: 'Redirect URL after deactivation' },
      },
      required: ['user_email', 'client_id', 'redirect_url'],
    },
  })
  @ApiResponse({ status: 200, description: 'User deactivated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data or user-related errors' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  @UseAPIGuard()
  async deactivateUserByEmail(
    @Body(new ValidationPipe()) requestBody: deactivateUserByEmailDto) {
    try {
      let response : {
        type : string,
        msg: string,
      } = {
        type: 'error',
        msg: 'Some error occurred.',
      }
      let defaultMsg = (requestBody?.enable && requestBody?.enable == "1") ? 'User account has been activated successfully.' : 'User account has been blocked successfully .';
      const deactivateResponse = await this.userApiService.deactivateUserStatus(requestBody);

      // Handle specific error cases returned by the service
      if (deactivateResponse instanceof Error) {
        switch (deactivateResponse?.message) {
          case 'UserAlreadyBlock':
            response.msg = 'User account is already in blocked state.';
            break;
          case 'UserNotFoundException':
            response.msg = 'No active account associated with this email address.';
            break;
          default:
            response.msg = 'Some error occurred.';
        }
      } else if(deactivateResponse){
          response = { type: 'success', msg: defaultMsg };
      }

      return response;
    } catch (error: any) {
      // Log the error for debugging and monitoring
      APILog.error('deactivateUserByEmail', {
        METHOD: `${this.constructor.name}@${this.deactivateUserByEmail.name}`,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
     
    if (error instanceof BadRequestException) {
        if (typeof error.getResponse() === 'object' && Array.isArray((error.getResponse() as any).message)) {
          return { type: 'error', msg: (error.getResponse() as any).message[0] };
        } else {
          return { type: 'error', msg: error.message };
        }
      }
      return {type: 'error', msg: "An unexpected error occurred."}
    }
  }

  /**
   * Assign User Role
   * @param reqBody - Data required to assign a user role
   * @returns JSON response with the assignment result
   * 
   * This api is consumed by Xenia and Paperclip
   */
  @Post('/assign-user-role')
  @ApiOperation({
    summary: 'Assign User Role',
    description: 'Assigns a role to a user based on the provided information.',
  })
  @ApiBody({
    type: AssignUserRoleDto,
    description: 'Data required to assign a user role',
  })
  @ApiResponse({ status: 200, description: 'User role assigned successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  @UseAPIGuard()
  async assignUserRole(
    @Body(new ValidationPipe({ whitelist: true })) reqBody: AssignUserRoleDto,
  ){
    try {
      // Call the service to assign the user role
      const response = await this.userApiService.assignUserRole(reqBody);

      // Return the response with HTTP status
      return { status: HttpStatus.OK, ...response };
    } catch (error: any) {
      // Log the error for debugging and monitoring
      APILog.error('assignUserRole', {
        METHOD: `${this.constructor.name}@${this.assignUserRole.name}`,
        MESSAGE: error.message,
        REQUEST: reqBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      // Rethrow the error to be handled by the exception filter
      throw error;
    }
  }

  /**
   * Check Account Setup
   * @param requestBody - Object containing the user email
   * @returns JSON response with account setup information
   * 
   * This api is consumed by Paperclip
   */
  @Post('/check-account-setup')
  @ApiOperation({
    summary: 'Check Account Setup',
    description: 'Checks the account setup information for a given user email.',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        user_email: {
          type: 'string',
          example: '<EMAIL>',
          description: 'The email address of the user to check account setup for',
        },
      },
      required: ['user_email'],
    },
  })
  @ApiResponse({ status: 200, description: 'Account setup information retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  @UseAPIGuard()
  async checkAccountSetup(
    @Body(new ValidationPipe({ whitelist: true })) requestBody: { user_email: string },
  ) {
    try {
      const response = await this.userApiService.checkAccountSetup(requestBody?.user_email);
      return response;
    } catch (error: any) {
      // Log the error for debugging and monitoring
      APILog.error('checkAccountSetup', {
        METHOD: `${this.constructor.name}@${this.checkAccountSetup.name}`,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      // Rethrow the error to be handled by the exception filter
      throw error;
    }
  }

  /**
   * Encrypt or Decrypt JWT Payload
   * @param requestBody - Object containing API key, user details, and operation type (encrypt/decrypt)
   * @returns Encrypted or decrypted JWT payload
   * 
   * This api is consumed by Xenia
   */
  @Post('/encrypt-decrypt-jwt-payload')
  @ApiOperation({
    summary: 'Encrypt or Decrypt JWT Payload',
    description: 'Encrypts or decrypts a JWT payload based on the provided type (encrypt/decrypt).',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        api_key: { type: 'string', description: 'API key for authorization' },
        id: { type: 'string', description: 'User ID' },
        uname: { type: 'string', description: 'Username' },
        email: { type: 'string', description: 'User email address' },
        gid: { type: 'string', description: 'Group ID' },
        type: { type: 'string', enum: ['encrypt', 'decrypt'], description: 'Operation type (encrypt or decrypt)' },
      },
      required: ['api_key', 'id', 'uname', 'email', 'gid', 'type'],
    },
  })
  @ApiResponse({ status: 200, description: 'Operation completed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 401, description: 'Unauthorized access' })
  @ApiResponse({ status: 404, description: 'Unknown operation type' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  @UseAPIGuard()
  async encryptDecryptJwtPayload(
    @Body(new ValidationPipe({ whitelist: true }))
    requestBody: {
      api_key: string;
      id: string;
      uname: string;
      email: string;
      gid: string;
      type: string;
    },
  ) {
    try {
      // Validate API key
      if (requestBody.api_key !== this.configService.get<string>('JwtPayloadEncryptionApiKey')) {
        throw new UnauthorizedException('Unauthorized access.');
      }

      // Validate operation type
      if (requestBody.type !== 'encrypt' && requestBody.type !== 'decrypt') {
        throw new NotFoundException('Unknown operation type.');
      }

      // Perform encryption or decryption
      return await this.userApiService.encryptDecryptJwtPayload(requestBody);
    } catch (error: any) {
      // Log the error and rethrow
      APILog.error('encryptDecryptJwtPayload', {
        METHOD: `${this.constructor.name}@${this.encryptDecryptJwtPayload.name}`,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  /**
   * Update Engagex User Role
   * @param requestBody - Data required to update the user role
   * @returns JSON response with the update result
   * 
   * This api is consumed by Paperclip
   */
  @Post('/update-engagex-user-role')
  @ApiOperation({
    summary: 'Update Engagex User Role',
    description: 'Updates the role of a user in the Engagex system based on the provided information.',
  })
  @ApiBody({
    type: UpdateUserRoleDto,
    description: 'Data required to update the user role',
  })
  @ApiResponse({ status: 200, description: 'User role updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data or role-related errors' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  @UseAPIGuard()
  async updateEngagexUserRole(@Body(new ValidationPipe({ whitelist: true })) requestBody: UpdateUserRoleDto) {
    try {
      const updateRoleResponse = await this.userApiService.updateEngagexRole(requestBody);
      let errorMsg = 'User role successfully updated'
      if (updateRoleResponse instanceof Error) {
        switch (updateRoleResponse?.message) {
          case 'RoleUserInvalid':
        throw new BadRequestException('User role is invalid');
          case 'RoleUserNotExist':
        throw new BadRequestException('User account does not exist');
          case 'RoleAlreadyAssign':
        errorMsg = 'Role already assigned to user';
        break;
          default:
        throw new BadRequestException('Some error occurred');
        }
      }
      return { type: 'success',msg: errorMsg, _t: updateRoleResponse['idToken'] };
    } catch (error: any) {
      APILog.error('updateEngagexUserRole', {
        METHOD: `${this.constructor.name}@${this.updateEngagexUserRole.name}`,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      if (error instanceof UnauthorizedException){
        return {type: 'error', msg: 'Unauthorized access request.'};
      }

      throw error instanceof BadRequestException
        ? new BadRequestException(error.message)
        : new InternalServerErrorException();
    }
  }

  /**
   * Get User Profile Data
   * @param params - Query parameters containing user details
   * @param res - Response object
   * @returns JSON response with user profile data
   */
  @Get('/user-profile-data')
  @ApiOperation({
    summary: 'Get User Profile Data',
    description: 'Fetches the user profile data based on the provided query parameters.',
  })
  @ApiResponse({ status: 200, description: 'User profile data retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Invalid query parameters' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async userProfileData(@Query() params, @Res() res){
    try {
      if (!params?.uid) {
        throw new BadRequestException('User ID is required.');
      }
      const response = await this.userApiService.userProfileData(params);
      res.status(HttpStatus.OK).json(response);
    } catch (error: any) {
      APILog.error('userProfileData', {
        METHOD: `${this.constructor.name}@${this.userProfileData.name}`,
        MESSAGE: error.message,
        REQUEST: params,
        RESPONSE: error?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  /**
   * Update SkillUp Recommendation in Profile
   * @param reqData - Object containing user_id and client_id
   * @param res - Response object
   * @returns JSON response with the update result
   * 
   * This api is consumed by Ice9
   */
  @Post('/update-skillup-recommendation-in-profile')
  @ApiOperation({
    summary: 'Update SkillUp Recommendation in Profile',
    description: 'Updates the SkillUp recommendation in the user profile based on the provided information.',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        user_id: { type: 'string', description: 'User ID' },
        client_id: { type: 'string', description: 'Client ID' },
      },
      required: ['user_id', 'client_id'],
    },
  })
  @ApiResponse({ status: 200, description: 'SkillUp recommendation updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async updateSkillupRecommendationInProfile(
    @Body(new ValidationPipe({ whitelist: true })) reqData: { 
      user_id: string; client_id: string, goal: string, profession: string },
    @Res() res,
  ){
    try {
      const response = await this.userApiService.updateSkillupRecommendationInProfile(reqData);
      res.status(HttpStatus.OK).json(response);
    } catch (error: any) {
      APILog.error('updateSkillupRecommendationInProfile', {
        METHOD: `${this.constructor.name}@${this.updateSkillupRecommendationInProfile.name}`,
        MESSAGE: error.message,
        REQUEST: reqData,
        RESPONSE: error?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  /**
   * Update SkillUp User Profile Data
   * @param reqData - Data required to update SkillUp user profile
   * @param res - Response object
   * @returns JSON response with the update result
   * 
   * Consumed by Paperclip.
   */
  @Post('/update-skillup-user-profile-data')
  @ApiOperation({
    summary: 'Update SkillUp User Profile Data',
    description: 'Updates the SkillUp user profile data with the provided information.',
  })
  @ApiBody({
    type: UpdateSkillUpUserDTO,
    description: 'Data required to update SkillUp user profile',
  })
  @ApiResponse({ status: 200, description: 'SkillUp user profile updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async updateSkillupUserProfileData(
    @Body(new ValidationPipe()) reqData: UpdateSkillUpUserDTO,
    @Res() res,
  ){
    try {
      const response = await this.userApiService.updateSkillupUserProfileData(reqData);
      res.status(HttpStatus.OK).json(response);
    } catch (error: any) {
      APILog.error('updateSkillupUserProfileData', {
        METHOD: `${this.constructor.name}@${this.updateSkillupUserProfileData.name}`,
        MESSAGE: error.message,
        REQUEST: reqData,
        RESPONSE: error?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  /**
   * Verify Auth Token by Email
   * @param reqData - Object containing user_email and client_id
   * @param res - Response object
   * @returns JSON response with verification result
   * Consumed by Ice9.
   */
  @Post('/verify-auth-token-by-email')
  @ApiOperation({ summary: 'Verify Auth Token by Email', description: 'Verifies the authentication token for a user by email.' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        user_email: { type: 'string', description: 'User email address' },
        client_id: { type: 'string', description: 'Client ID' },
      },
      required: ['user_email', 'client_id'],
    },
  })
  @ApiResponse({ status: 200, description: 'Token verified successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async verifyAuthTokenByEmail(
    @Body() reqData: { user_email: string; client_id: string, authToken: string },
    @Res() res,
  ) {
    try {
      const response = await this.userApiService.verifyAuthTokenByEmail(reqData);
      res.status(HttpStatus.OK).json(response);
    } catch (error: any) {
      APILog.error('verifyAuthTokenByEmail', {
        METHOD: `${this.constructor.name}@${this.verifyAuthTokenByEmail.name}`,
        MESSAGE: error.message,
        REQUEST: reqData,
        RESPONSE: error?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  /**
   * Get Data by Email or UID
   * @param query - Query parameters containing email or uid
   * @returns JSON response with the requested data
   */
  @Get('/get-data-by-email-or-uid')
  @ApiOperation({
    summary: 'Get Data by Email or UID',
    description: 'Fetches data based on the provided email or UID.',
  })
  @ApiResponse({ status: 200, description: 'Data retrieved successfully' })
  @ApiResponse({ status: 400, description: 'Invalid query parameters' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async getDataByEmailOrUid(@Query('email') email?: string, @Query('uid') uid?: number) {
    try {
      if (!email && !uid) {
        throw new BadRequestException('Either email or uid must be provided.');
      }

      const response = await this.userApiService.getDataByEmailOrUid({ email, uid });
      return { status: HttpStatus.OK, data: response };
    } catch (error: any) {
      APILog.error('getDataByEmailOrUid', {
        METHOD: `${this.constructor.name}@${this.getDataByEmailOrUid.name}`,
        MESSAGE: error.message,
        REQUEST: { email, uid },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
  

  
  /**
   * Deactivate user for GDPR request
   * @param requestBody - The email of the user to deactivate
   * @returns JSON response with status and message
   * 
   * This api is consumed by ice9
   */
  @Post('/deactivate-user-for-gdpr')
  @ApiOperation({
    summary: 'Deactivate User for GDPR',
    description: 'Deactivates the user based on email for GDPR compliance.',
  })
  @ApiBody({
    type: DeactivateUserDto,
    description: 'The email of the user to deactivate',
  })
  @ApiResponse({ status: 200, description: 'User successfully deactivated' })
  @ApiResponse({ status: 400, description: 'Invalid email or request data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  async deactivateUserForGdpr(
    @Body(new ValidationPipe()) requestBody: DeactivateUserDto,
  ) {
    try {
      const response = await this.userApiService.deactivateUserForGdpr(requestBody.user_email);

      if (response.status === 'failed') {
        throw new BadRequestException(response.msg);
      }
      return { status: 'success', msg: 'User successfully deactivated' };
    } catch (error) {
      throw error instanceof BadRequestException
        ? error
        : new InternalServerErrorException('Something went wrong while deactivating user');
    }
  }

/**
 * @summary Register User via FRS
 * @description This endpoint registers a user using the FRS (Federated Registration System).
 *
 * @param {object} userParams - The user registration details passed in the request body.
 * @returns {object} The registration result from the service layer.
 *
 * @throws {Error} Throws when the registration process fails.
 * This api is consumed by Ice9.
 */
@ApiOperation({ summary: 'Register User via FRS' })
@Post('/register-user-frs')
  async registerUserFrs(@Body() userParams: any) {
    try{
    const result = await this.userApiService.registerUserFrs(userParams);

    return result
  } catch(error: any){
    APILog.error('registerUserFrs', {
      METHOD: this.constructor.name + '@' + this.registerUserFrs.name,
      MESSAGE: error.message,
      REQUEST: userParams,
      RESPONSE: error.stack,
      TIMESTAMP: new Date().getTime(),
    });

  }
}

//This api is consumed by Ice9
@Post('/frs-complete-signup')
async frsCompleteSignup(
  @Body() body: { email: string, client_id: string },
 @Req() req,

) {
  try {
    const requestBody = {
      email: body.email,
      client_id: body.client_id
    };
    const cookieBody = req.cookies;
    await this.userApiService.frsCompleteSignup(requestBody, cookieBody);
  } catch (error) {
    APILog.error('frsCompleteSignup', {
      METHOD: this.constructor.name + '@' + this.frsCompleteSignup.name,
      MESSAGE: error,
      REQUEST: body,
      TIMESTAMP: new Date().getTime(),
    });
  }
}

  /** 
   * Update GDPR Delete Status
   * @param requestBody - Email and response data for GDPR status update
   * @returns JSON response with the update result
   * 
   * This api is consumed by Deviants
   */
  @Post('/update-gdpr-delete-status')
  @ApiOperation({
    summary: 'Update GDPR Delete Status',
    description: 'Updates the GDPR deletion status for a user based on email.',
  })
  @ApiBody({
    type: UpdateGdprStatusDto,
    description: 'Email and response data for GDPR status update',
  })
  @ApiResponse({ status: 200, description: 'GDPR status updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data or user not found/not blocked' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  @UseAPIGuard()
  async updateGdprDeleteStatus(
    @Body(new ValidationPipe()) requestBody: UpdateGdprStatusDto,
  ) {
    try {
      const response = await this.userApiService.updateGdprDeleteStatus(requestBody);

      if (response instanceof Error) {
        switch (response?.message) {
          case 'UserNotFoundException':
            throw new BadRequestException(this.configService.get('roleUserNotExist'));
          case 'UserAlreadyBlock':
            throw new BadRequestException(this.configService.get('userNotBlocked'));
          default:
            throw new BadRequestException(this.configService.get('errorOccuredResData'));
        }
      }

      return response;
    } catch (error: any) {
      APILog.error('updateGdprDeleteStatus', {
        METHOD: `${this.constructor.name}@${this.updateGdprDeleteStatus.name}`,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      throw error instanceof BadRequestException
        ? new BadRequestException(error.message)
        : new InternalServerErrorException();
    }
  }

  @Post('/gdpr')
  @ApiOperation({
    summary: 'Handle GDPR actions',
    description: 'Processes various GDPR-related actions based on the action parameter'
  })
  @ApiBody({
    type: GdprRequestDto,
    description: 'GDPR action request data'
  })
  @ApiResponse({ status: 200, description: 'GDPR action processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  @UseAPIGuard()
  async gdpr(
    @Body(new ValidationPipe({ whitelist: true })) requestBody: GdprRequestDto,
  ) {
    try {
      const response = await this.userApiService.gdprAction(requestBody);
      
      if (response instanceof Error) {
        switch (response?.message) {
          case 'ApiValidationFailed':
            throw new BadRequestException('Api validation failed');
          case 'MethodNotExists':
            throw new BadRequestException('Method does not exists');
          case 'unable to set start export status in Document':
            throw new BadRequestException('unable to set start export status in Document');
          case 'Unable to save in couch':
            throw new BadRequestException('Unable to save in couch');
          default:
            throw new BadRequestException('An error occurred while processing the GDPR request');
        }
      }

      return response;
    } catch (error: any) {
      APILog.error('gdpr', {
        METHOD: `${this.constructor.name}@${this.gdpr.name}`,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      if (error instanceof UnauthorizedException) {
        return { status: 0, data: '', message: 'Unauthorized access request.' };
      }

      throw error instanceof BadRequestException
        ? new BadRequestException(error.message)
        : new InternalServerErrorException();
    }
  }


  /**
   * Sentinel Sync User Update
   * @param reqBody - Data required to update user information
   * @returns JSON response with the update result
   */
  @Post('/sentinel-sync-user-update')
  @ApiOperation({
    summary: 'Sentinel Sync User Update',
    description: 'Updates user information in the Sentinel system based on the provided data.',
  })
  @ApiBody({
    type: SentinelSyncUserUpdateDto,
    description: 'Data required to update user information',
  })
  @ApiResponse({ status: 200, description: 'User information updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  @UseFilters(HttpExceptionFilter)
  @UseAPIGuard()
  async sentinelSyncUserUpdate(
    @Body(new ValidationPipe({ whitelist: true })) reqBody: SentinelSyncUserUpdateDto,
  ) {
    try {
      const response = await this.userApiService.sentinelSyncUserUpdate(reqBody);
      return response;
    } catch (error: any) {
      APILog.error('sentinelSyncUserUpdate', {
        METHOD: `${this.constructor.name}@${this.sentinelSyncUserUpdate.name}`,
        MESSAGE: error.message,
        REQUEST: reqBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error instanceof BadRequestException
        ? new BadRequestException(error.message)
        : new InternalServerErrorException();
    }
  }

    /**
   * Get feedback form details by form ID and module
   * @param query Query parameters containing formId and formModule
   * @returns Feedback form details
   */
    @Get('nps/get-feedback-form')
    @ApiOperation({ summary: 'Get feedback form details' })
    @ApiQuery({ name: 'formId', required: true, description: 'Form ID' })
    @ApiQuery({ name: 'formModule', required: true, description: 'Form module name' })
    @ApiResponse({ status: 200, description: 'Feedback form details retrieved successfully' })
    @ApiResponse({ status: 400, description: 'Invalid request parameters' })
    @HttpCode(HttpStatus.OK)
    async getFeedbackForm(@Query() query: { formId: string; formModule: string }) {
      try {
        // Validate required parameters
        if (!query.formId || !query.formModule) {
          Logger.error('getFeedbackForm', {
            METHOD: `${this.constructor.name}@${this.getFeedbackForm.name}`,
            MESSAGE: 'Missing required parameters',
            REQUEST: query,
            TIMESTAMP: new Date().toISOString(),
          });
          
          return {
            status: 0,
            msg: 'Error - Param formId/formModule is empty'
          };
        }
        const npsService = await this.helperService.get<NpsFeedbackService>(NpsFeedbackService);
        // Call NPS feedback service to get form details
        const result = await npsService.getFeedbackForms(query.formId, query.formModule);
        // Check if result is valid
        if (!result && result?.data) {
          return {
            status: 0,
            msg: 'Error - Failed to fetch nps form details'
          };
        }
        return result?.data;
      } catch (error: any) {
        Logger.error('getFeedbackForm', {
          METHOD: `${this.constructor.name}@${this.getFeedbackForm.name}`,
          MESSAGE: error.message,
          REQUEST: query,
          RESPONSE: error.stack,
          TIMESTAMP: new Date().toISOString(),
        });
        
        return {
          status: 0,
          msg: 'Error - Failed to fetch nps form details'
        };
      }
    }

      /**
   * Logs client-side errors from help and support system
   * @param data Error information including user ID, email, group ID, and error type
   * @returns Status response indicating success or failure
   */
  @Post('/help-and-support/log-client-error')
  @HttpCode(HttpStatus.OK)
  @ApiTags('Error Logging')
  @ApiOperation({ summary: 'Log client-side errors from help and support' })
  @ApiBody({ 
    schema: {
      type: 'object',
      required: ['user_id', 'user_email', 'error_type'],
      properties: {
        user_id: { type: 'string', description: 'User ID' },
        user_email: { type: 'string', description: 'User email address' },
        group_id: { type: 'string', description: 'Group ID' },
        error_type: { type: 'string', description: 'Type of error encountered' }
      }
    }
  })
  @ApiResponse({ status: 200, description: 'Error logged successfully' })
  @ApiResponse({ status: 400, description: 'Invalid parameters' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async logClientError(@Body() data: LogClientErrorDto) {
    try {
      Logger.info('Logging client error', { errorType: data.error_type });
      
      // Validate required parameters
      if (!data.user_id || !data.user_email || !data.error_type) {
        throw new BadRequestException('Missing required parameters');
      }
      
      // Call the paperclip service to log the error
      const paperclipService = await this.helperService.get<PaperclipService>(PaperclipService);
      const response = await paperclipService.logClientError(data);
      return response;
      
    } catch (error: any) {
      APILog.error('logClientError', {
        METHOD: `${this.constructor.name}@${this.logClientError.name}`,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      
      // Re-throw BadRequestException, but wrap other errors
      if (error instanceof BadRequestException) {
        throw error;
      }
      
      return { 
        status: false, 
        message: 'Failed to log client error' 
      };
    }
  }

  /**
   * Save user feedback responses
   * @param feedbackData - Array of feedback data objects
   * @returns Response indicating success or failure of the operation
   */
  @Post('nps/save-user-feedback')
  @ApiOperation({ summary: 'Save user feedback responses' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        'nps-save': {
          type: 'string',
          description: 'JSON string of feedback data array'
        }
      }
    }
  })
  @ApiResponse({ status: 200, description: 'User feedback saved successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  @HttpCode(HttpStatus.OK)
  async saveUserFeedback(@Body() body : { 'nps-save' : string}) {
    try {
      const feedbackData = body['nps-save'];
      
      // Validate feedback data
      if (!feedbackData) {
        Logger.error('saveUserFeedback', {
          METHOD: `${this.constructor.name}@${this.saveUserFeedback.name}`,
          MESSAGE: 'Invalid feedback data format',
          REQUEST: body,
          TIMESTAMP: new Date().toISOString(),
        });
        
        return {
          status: 0,
          msg: 'Error - Invalid feedback data format'
        };
      }
      
      // Call Cloud6 service to save feedback
      const npsService = await this.helperService.get<NpsFeedbackService>(NpsFeedbackService);
      const result = await npsService.saveUserFeedback(feedbackData);
      
      // Return the result
      return result;
    } catch (error: any) {
      Logger.error('saveUserFeedback', {
        METHOD: `${this.constructor.name}@${this.saveUserFeedback.name}`,
        MESSAGE: error.message,
        REQUEST: body,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().toISOString(),
      });
      
      return {
        status: 0,
        msg: 'Error - Failed to save user feedback'
      };
    }
  }


/**
 * Resets the password for a user account.
 * 
 * Validates client ID, user ID, current password, and new password format.
 * If the password reset is successful, the user will be logged out from all web devices.
 * Returns 200 OK status with a structured success or error message.
 */
@Post('/reset-password')
@HttpCode(HttpStatus.OK)
@ApiOperation({ summary: 'Reset user password' })
@ApiBody({ type: ResetPassDto })
@ApiResponse({ status: 200, description: 'Password reset result' })
@ApiResponse({ status: 400, description: 'Invalid request parameters' })
@ApiResponse({ status: 500, description: 'Internal server error' })
async resetPassword(
  @Res() res: Response,
  @Body() resetPassword: ResetPassDto,
): Promise<any> {
  APILog.log('reset-password');
  try {
   
    const result = await this.userApiService.resetPassword(resetPassword);
    if (result?.type === 'error') {
      let msg;
      switch (result.msg) {
        case 'InvalidUserParameter':
          msg = 'Invalid user parameters in request';
          break;
        case 'UserNotFoundException':
          msg = 'User account does not exist.';
          break;
        case 'UserAccountBlocked':
          msg = 'User account blocked';
          break;
        case 'NotAuthorizedException':
          msg = 'Current password is incorrect.';
          break;
        default:
          msg = result.msg || 'Some error occurred while resetting password.';
      }
      return res.status(HttpStatus.OK).json({
        type: 'error',
        msg,
      });
    }
    if (result) {
      res.status(HttpStatus.OK).json({
        type: 'success',
        msg: result.msg,
      });
    }

  } catch (error: any) {
    Logger.error('reset-password', {
      METHOD: this.constructor.name + '@resetPassword',
      MESSAGE: error.message,
      REQUEST: resetPassword,
      RESPONSE: error.stack,
      TIMESTAMP: new Date().getTime(),
    });
  }
}


}
