import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString } from "class-validator";
export class SetupManagerAccountDto {
    @IsString()                             
    @ApiProperty({ default: 'sl_looper' })
    client_id: string;

    @IsNotEmpty({ message: 'Please provide valid group id.' })
    @IsOptional()
    gid: number;

    affiliateLmsUrl: string;

    affiliateLogoUrl: string;

    @IsNotEmpty({ message: 'User id is invalid' })
    @ApiProperty({ default: 1003663 })
    userId: number;

    @IsNotEmpty({message: 'Please provide valid request time.'})
    requestTime: string;

    @IsNotEmpty({message: 'Please provide valid token.'})
    requestToken: string;

    reqId: string;
    providerName: string;
    affiliateName: string;
    isWhiteLabelingEnabled: boolean;
    fromEmailAddress: string;
    user_pwd?: string;
    confirm_pwd?: string;
}