import { IsNotEmpty, <PERSON>S<PERSON>, Is<PERSON><PERSON>, Allow, IsOptional } from 'class-validator';

export class GetTokenByEmailDto {

  @IsNotEmpty()
  @IsString()
  @IsEmail({}, { message: 'Please provide valid email id.' })
  user_email: string;

  @IsNotEmpty({ message: 'Unauthorized access request.' })
  client_id: string;

  @IsOptional()
  @Allow()
  redirect_url?: string;

  @IsOptional()
  @Allow()
  actor_email?: string;
}
