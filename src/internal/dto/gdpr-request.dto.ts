import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsEnum, IsString, IsEmail, Min } from 'class-validator';

export class GdprRequestDto {
  @ApiProperty({
    description: 'User ID',
    example: 12345,
    required: true
  })
  @IsNotEmpty({ message: 'Required parameters are missing' })
  @Min(1, { message: 'Invalid user ID' })
  usid: number;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
    required: true
  })
  @IsNotEmpty({ message: 'Required parameters are missing' })
  @IsString()
  @IsEmail({}, { message: 'Invalid email format' })
  useremail: string;

  @ApiProperty({
    description: 'GDPR action type',
    enum: ['export', 'delete'],
    example: 'export',
    required: true
  })
  @IsNotEmpty({ message: 'Required params are missing' })
  @IsEnum(['export', 'delete'], { message: 'Invalid action type' })
  action: string;
}