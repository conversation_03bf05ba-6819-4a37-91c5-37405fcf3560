import { IsString, <PERSON>NotEmpty, IsOptional } from 'class-validator';
import { IsPhoneNoValid } from '../../common/validators/phone.validator';
import { IsCountryCodeValid } from '../../common/validators/country-code.validator';

export class UpdateSkillUpUserDTO {
  @IsNotEmpty()
  @IsString()
  user_id: number;

  @IsString()
  client_id: string;

  @IsNotEmpty()
  @IsString()
  first_name: string;

  @IsNotEmpty()
  @IsString()
  last_name: string;

  @IsNotEmpty()
  @IsPhoneNoValid()
  phone: string;

  @IsOptional()
  gender: string;

  @IsOptional()
  @IsCountryCodeValid()
  country_code: string;

  @IsOptional()
  profile_picture: string;

  @IsOptional()
  objective: string;

  @IsOptional()
  your_career: string;
}
