import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEmail, IsArray, ArrayNotEmpty } from 'class-validator';

export class AddMemberToGroupWithRoleDto {
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  email: string;

  @ApiProperty({})
  @IsNotEmpty()
  @IsArray()
  @ArrayNotEmpty()
  rids?: Array<number>;

  @ApiProperty({})
  @IsNotEmpty()
  @IsArray()
  @ArrayNotEmpty()
  gids?: Array<number>;
}
