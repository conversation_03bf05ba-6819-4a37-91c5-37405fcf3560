import { IsNotEmpty, <PERSON><PERSON><PERSON>al, IsString, Matches, Validate } from 'class-validator';
import { PasswordsMatchConstraint } from '../../common/validators/passwords-match-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SHARED_CONSTANT } from '../../auth/config/shared.config';
import { Transform } from 'class-transformer';
export class ResetPasswordDto {
  @IsString()                             
  @ApiProperty({ default: 'sl_looper' })
  client_id: string;

  // @IsNotEmpty({ message: 'Please provide valid group id.' })
  // @IsPositive({ message: 'Please provide valid group id.' })
  @Transform(({ value }) => value.trim())
  @IsOptional()
  gid: number;

  @IsNotEmpty({ message: 'Please provide valid user Id.' })
  @ApiProperty({ default: '1003663' })
  @Transform(({ value }) => value.trim())
  userId: number;

  @IsNotEmpty({ message: 'Please provide valid token.' })
  @Transform(({ value }) => value.trim())
  requestToken: string;

  reqId?: string;
  
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value.trim())
  @Matches(SHARED_CONSTANT.PASSWORD_PATTERN, {
          message:
            'Your password should be minimum 8 characters long, with at least one uppercase, one lowercase letter, one number and length cannot be longer than 128 characters.',
        })
  user_pwd: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => value.trim())
  @Validate(PasswordsMatchConstraint, ['user_pwd'])
  @Matches(SHARED_CONSTANT.PASSWORD_PATTERN, {
          message:
            'Your password should be minimum 8 characters long, with at least one uppercase, one lowercase letter, one number and length cannot be longer than 128 characters.',
        })
  confirm_pwd: string;
}
