import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEmail } from 'class-validator';

export class DeactivateUserDto {
  @IsNotEmpty({ message: 'Email parameter is empty' })
  @IsString({ message: 'Email must be a string' })
  @IsEmail({}, { message: 'Invalid email format' })
  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email address of the user to be added to the group',
  })
  user_email: string;
}
