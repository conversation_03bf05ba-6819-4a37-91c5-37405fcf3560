import { IsNotEmpty, IsOptional, IsPositive, IsString, Matches } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsPasswordValid } from '../../common/validators/password.validator';
const passwordPattern =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$&+,:;=?@#|'<>.^*()%!-])[A-Za-z\d@$&+,:;=?@#|'<>.^*()%!-]{10,}$/;

export class SetLearnerPasswordDto {
  @ApiProperty({ description: 'Email of the learner' })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiProperty({ description: 'Array of role IDs', type: [String] })
  @IsNotEmpty()
  @IsString({ each: true })
  rids: string[];

  @ApiProperty({ description: 'Array of group IDs', type: [String] })
  @IsNotEmpty()
  @IsString({ each: true })
  gids: string[];

  @ApiProperty({ description: 'User password' })
  @IsNotEmpty()
  @IsString()
  @IsPasswordValid()
  @Matches(passwordPattern, {
    message:
      'Your password should be minimum 8 characters long, with at least one uppercase, one lowercase letter, one number and length cannot be longer than 128 characters.',
  })
  user_pwd: string;

  @ApiPropertyOptional({ description: 'User options', type: Number })
  @IsOptional()
  user_options: number;

  @ApiProperty({ description: 'Login ID', type: Number })
  @IsNotEmpty()
  @IsPositive()
  login: number;

  @ApiProperty({ description: 'Access level', type: Number })
  @IsNotEmpty()
  @IsPositive()
  access: number;

  @IsOptional()
  client_id?: string;
}
