import { IsNotEmpty, <PERSON>Optional, IsPositive, IsString, Matches, Validate } from 'class-validator';
import { PasswordsMatchConstraint } from '../../common/validators/passwords-match-validator';
import { IsPasswordValid } from '../../common/validators/password.validator';
import { PasswordNotSameConstraint } from '../../common/validators/password-not-same.validator';
import { Type } from 'class-transformer';
const passwordPattern =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$&+,:;=?@#|'<>.^*()%!-])[A-Za-z\d@$&+,:;=?@#|'<>.^*()%!-]{10,}$/;

export class ChangePasswordDto {
  @IsNotEmpty()
  @IsPositive({ message: 'User Id must be a positive value.' })
  @Type(() => Number) 
  user_id: number;

  @IsNotEmpty()
  @IsPositive({ message: 'Group Id must be a positive value.' })
  @Type(() => Number) 
  @IsOptional()
  gid: number;

  @IsNotEmpty()
  @IsString()
  cur_passwd: string;

  @Validate(PasswordNotSameConstraint, ['cur_passwd'])
  @IsNotEmpty()
  @IsString()
  @IsPasswordValid()
  @Matches(passwordPattern, {
    message:
      'Your password should be minimum 8 characters long, with at least one uppercase, one lowercase letter, one number and length cannot be longer than 128 characters.',
  })
  new_passwd: string;

  @IsNotEmpty()
  @IsString()
  @Validate(PasswordsMatchConstraint, ['new_passwd'])
  @Matches(passwordPattern, {
    message:
      'Your password should be minimum 8 characters long, with at least one uppercase, one lowercase letter, one number and length cannot be longer than 128 characters.',
  })
  confirm_passwd: string;
}


