import { ApiProperty } from '@nestjs/swagger';
import {  IsString, IsNotEmpty } from 'class-validator';

export class UpdateUsernameVisibilityDto {

  @IsNotEmpty({ message: 'User id is invalid' })
  @ApiProperty({ default: '1003663' })
  user_id: number;

  @IsString()
  @ApiProperty({ default: 'sl_looper' })
  client_id: string;

  @ApiProperty({ default: 'username' })
  @IsNotEmpty({ message: 'Username is mandatory' })
  username: string;

  @ApiProperty({ default: 'visibility' })
  @IsNotEmpty({ message: 'Visibility is mandatory' })
  visibility: number;
}
