import { IsInt, IsString, IsOptional, IsUrl, MaxLength } from 'class-validator';

export class SentinelSyncUserUpdateDto {
    
    @IsOptional()
    uid: number;

    @IsOptional()
    client_id?: string;

    @IsOptional()
    @IsString()
    @MaxLength(60)
    name?: string;

    @IsOptional()
    @IsString()
    @MaxLength(128)
    pass?: string;

    @IsOptional()
    @IsString()
    @MaxLength(254)
    mail?: string;

    @IsOptional()
    @IsString()
    @MaxLength(400)
    display_name?: string;

    @IsOptional()
    @IsString()
    @MaxLength(60)
    timezone?: string;

    @IsOptional()
    @IsString()
    @MaxLength(10)
    country_code?: string;

    @IsOptional()
    @IsString()
    @MaxLength(110)
    phone_no?: string;

    @IsOptional()
    @IsString()
    @MaxLength(200)
    location?: string;

    @IsOptional()
    @IsString()
    @MaxLength(20)
    gender?: string;

    @IsOptional()
    @IsString()
    @MaxLength(15)
    profile_visibility?: string;

    @IsOptional()
    @IsUrl()
    @MaxLength(255)
    linkedin_url?: string;

    @IsOptional()
    @IsUrl()
    @MaxLength(255)
    blog_url?: string;

    @IsOptional()
    @IsUrl()
    @MaxLength(255)
    facebook_url?: string;

    @IsOptional()
    @IsUrl()
    @MaxLength(255)
    website_url?: string;

    @IsOptional()
    @IsUrl()
    @MaxLength(255)
    twitter_url?: string;

    @IsOptional()
    @IsString()
    @MaxLength(200)
    other_url?: string;

    @IsOptional()
    @IsInt()
    newsletter?: number;

    @IsOptional()
    @IsInt()
    accept_agreement?: number;

    @IsOptional()
    @IsInt()
    dob?: number;

    @IsOptional()
    @IsString()
    @MaxLength(15)
    training_funded_by?: string;

    @IsOptional()
    @IsString()
    @MaxLength(15)
    user_career_type?: string;

    @IsOptional()
    @IsInt()
    user_options?: number;

    @IsOptional()
    @IsString()
    sso_attributes?: string;

    @IsOptional()
    @IsString()
    @MaxLength(400)
    first_name?: string;

    @IsOptional()
    @IsString()
    @MaxLength(400)
    last_name?: string;

    @IsOptional()
    linkedin_status?: number;

    @IsOptional()
    account_setup?: number;

    @IsOptional()
    password_created?: number;

    @IsOptional()
    @IsInt()
    total_work_experience?: number;

    @IsOptional()
    @IsString()
    user_type?: string;

    @IsOptional()
    @IsString()
    @MaxLength(8)
    user_category?: string;

    @IsOptional()
    @IsString()
    @MaxLength(50)
    middle_name?: string;

    @IsOptional()
    @IsString()
    @MaxLength(5)
    title?: string;

    @IsOptional()
    @IsString()
    @MaxLength(35)
    state?: string;

    @IsOptional()
    @IsString()
    correspondence_address?: string;

    @IsOptional()
    @IsString()
    @MaxLength(20)
    country_of_residence?: string;

    @IsOptional()
    @IsString()
    profile_pic?: string;

    @IsOptional()
    @IsString()
    @MaxLength(50)
    objective_taking_course?: string;

    @IsOptional()
    @IsString()
    @MaxLength(20)
    where_are_you_in_career?: string;

    @IsOptional()
    @IsString()
    @MaxLength(50)
    highest_level_of_education?: string;

    @IsOptional()
    @IsInt()
    created?: number;

    @IsOptional()
    @IsInt()
    access?: number;

    @IsOptional()
    @IsInt()
    login?: number;

    @IsOptional()
    status?: number;

    @IsOptional()
    updated_on?: number;

    @IsOptional()
    gid?: number;

    @IsOptional()
    roles?: string[];

    @IsOptional()
    user_name?: string;

    @IsOptional()
    user_email?: string;

    
    @IsOptional()
    user_roles?: string[];


    @IsOptional()
    send_email?: string;

    @IsOptional()
    overwrite?: boolean;

    @IsOptional()
    email_block?: string;

    @IsOptional()
    user_id?: string;
   
  
}