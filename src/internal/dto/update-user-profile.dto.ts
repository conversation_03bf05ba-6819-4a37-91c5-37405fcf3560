import { IsNotEmpty, IsString, IsOptional, IsEnum} from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

enum CareerType {
  Professional = 'professional',
  Student = 'student',
}

class Taxonomy {
  @IsString()
  @Transform(({ value }) => value.trim())
  name: string;

  @IsString()
  @Transform(({ value }) => value.trim())
  tid: string;
}

export class UpdateUserProfileDto {
  @IsString()                             
  @ApiProperty({ default: 'sl_looper' })
  client_id: string;

  @IsOptional()
  step?: number;

  @IsString()
  @IsOptional()
  email?: string;

  @IsString()
  @IsOptional()
  username?: string;

  @IsString()
  @Transform(({ value }) => value.trim())
  title?: string;

  @IsString()
  @Transform(({ value }) => value.trim())
  first_name?: string;

  @IsString()
  @Transform(({ value }) => value.trim())
  last_name?: string;

  @IsOptional()
  gender?: string;


  @IsNotEmpty({ message: 'Please provide valid visibility.' })
  @IsOptional()
  visibility?: number;

  @IsString()
  user_id: string;

  @IsString()
  dob: string;

  @IsString()
  @IsOptional()
  country_code?: string;

  @IsString()
  @IsOptional()
  phone?: string;

  @IsString()
  @IsOptional()
  city?: string;

  @IsEnum(CareerType)
  @IsOptional()
  user_career_type?: CareerType;

  @IsString()
  @IsOptional()
  objective?: string;

  @IsString()
  @IsOptional()
  qualification?: string;

  @IsOptional()
  industry?: Taxonomy;

  job_function?: Taxonomy;

  @IsString()
  @IsOptional()
  designation?: string;

  @IsString()
  @IsOptional()
  company?: string;

  @IsString()
  @IsOptional()
  institute?: string;

  @IsString()
  @IsOptional()
  specialization?: string;

  @IsString()
  @IsOptional()
  total_work_experience?: string;

  @IsOptional()
  is_enterprise_learner?: number;

  @IsString()
  @IsOptional()
  linkedin_url?: string;

  @IsOptional()
  timezone?: string;

  @IsString()
  highest_level_of_education?: string;

  @IsString()
  where_are_you_in_career?: string;

  @IsString()
  objective_of_taking_course?: string;

  @IsString()
  @IsOptional()
  training_funded_by?: string;
  
}
