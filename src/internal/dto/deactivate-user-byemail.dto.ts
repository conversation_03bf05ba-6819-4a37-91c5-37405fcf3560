import { ApiProperty } from "@nestjs/swagger";
import { IsE<PERSON>, <PERSON>NotEmpty, <PERSON>Optional, IsString } from "class-validator";
export class deactivateUserByEmailDto{
    @IsNotEmpty()
    @IsString()
    @IsEmail({}, { message: 'Please provide valid email id.' })
    @ApiProperty({example: '<EMAIL>'})
    user_email: string;
    
    @IsString()
    @ApiProperty({ default: 'sl_looper' })
    client_id: string;

    @IsOptional()
    @IsString()
    @ApiProperty({default: 'test'})
    redirect_url?: string;

    @IsOptional()
    @ApiProperty({ default: 0, required: false })
    enable?: string;
}