import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateGdprStatusDto {
  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Response data from the GDPR deletion process',
    example: 'Deletion completed successfully',
  })
  @IsString()
  @IsNotEmpty()
  response_data: string;
} 