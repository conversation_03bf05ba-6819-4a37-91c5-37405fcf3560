import { ApiProperty } from '@nestjs/swagger';
import {  IsString, IsNotEmpty } from 'class-validator';

export class UpdateUserRoleDto {
  @IsNotEmpty({ message: 'User id is invalid' })
  @ApiProperty({ default: '1003663' })
  user_id: number;

  @IsString()
  @ApiProperty({ default: 'sl_looper' })
  client_id: string;

  @ApiProperty({ default: 'engagex_trainer' })
  @IsString({ message: 'Please provide updated role' })
  updated_role: string;
}
