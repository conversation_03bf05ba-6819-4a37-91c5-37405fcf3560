import { IsNotEmpty, IsString, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SHARED_CONSTANT } from '../../auth/config/shared.config';
import { Transform } from 'class-transformer';
export class ResetPassDto {
  @IsString()                             
  @ApiProperty({ default: 'sl_looper' })
  client_id: string;


  @IsNotEmpty({ message: 'Please provide valid user Id.' })
  @ApiProperty({ default: '1003663' })
  @Transform(({ value }) => value.trim())
  user_id: number;



  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value.trim())
  @ApiProperty({ default: 'Simpli@123' })
  current_password: string;


  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value.trim())
  @Matches(SHARED_CONSTANT.PASSWORD_PATTERN, {
    message:
      'Your password should be minimum 8 characters long, with at least one uppercase, one lowercase letter, one number and length cannot be longer than 128 characters.',
  })
  @ApiProperty({ default: 'Simpli@1234' })
  new_password: string;
  

@IsString()
@Transform(({ value }) => value.trim())
@ApiProperty({ default: '<EMAIL>' })
user_email?: string;

}
