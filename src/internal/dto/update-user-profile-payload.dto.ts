import { IsString, IsOptional, IsObject, IsArray} from 'class-validator';
import { Type } from 'class-transformer';
import { PartialType } from '@nestjs/swagger';
import { User } from '../../db/mongo/schema/user/user.schema';
import { Optional } from '@nestjs/common';

export class UpdateUserPayloadDto extends PartialType(User) {
  @IsObject()
  @IsOptional()
  @Type(() => Object)
  mysql?: Record<string, any> = {};

  @IsString()
  @IsOptional()
  edit_type?: string;
}
export class ProfessionalPayloadDto {

  @IsString()
  @IsOptional()
  edit_type?: string;

  @IsArray()
  @IsString({ each: true })
  company_designation: string[];

  @IsArray()
  @IsString({ each: true })
  company_name: string[];

  @IsArray()
  @IsString({ each: true })
  job_function: string[];

  @IsArray()
  @IsString({ each: true })
  industry: string[];

  @IsArray()
  @IsString({ each: true })
  exp_from_month: string[];

  @IsArray()
  @IsString({ each: true })
  exp_from_year: string[];

  @IsArray()
  @IsString({ each: true })
  exp_to_year: string[];

  @IsArray()
  @IsString({ each: true })
  exp_to_month: string[];

  @Optional()
  @IsArray()
  current_role: string[];

  @IsString()
  where_are_you_in_career: string;
}
export class AcademicPayloadDto {

  @IsString()
  @IsOptional()
  edit_type?: string;

  @IsArray()
  @IsString({ each: true })
  field_qualification: string[];

  @IsArray()
  @IsString({ each: true })
  institute_name: string[];

  @IsArray()
  @IsString({ each: true })
  field_specialization: string[];

  @IsArray()
  @IsString({ each: true })
  course_from_month: string[];

  @IsArray()
  @IsString({ each: true })
  course_from_year: string[];

  @IsArray()
  @IsString({ each: true })
  course_to_month: string[];

  @IsArray()
  @IsString({ each: true })
  course_to_year: string[];

  @IsString()
  highest_level_of_education: string;
}
