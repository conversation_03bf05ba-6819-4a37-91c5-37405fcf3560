import { Test, TestingModule } from '@nestjs/testing';
import { EnterpriseLMSService } from './enterprise-lms.service';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import { CachingService } from '../../caching/caching.service';
import { UserRepository } from '../../user/repositories/user/user.repository';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { ForgotPasswordB2B } from '../../common/typeDef/auth.type';
import { ResetPasswordDto } from '../dto/reset.password.dto';
import { SetupManagerAccountDto } from '../dto/setup-manager-account-dto';
import { AuthTokenHelper } from '../../auth/helper/auth.tokenhelper';
import { Utility } from '../../common/util/utility';
import validator from 'validator';
import { Logger } from '../../logging/logger';

const mockForgotPasswordDto: ForgotPasswordB2B = {
  client_id: 'client-id',
  gid: '1',
  affiliateLmsUrl: 'https://affiliate.example.com',
  affiliateLogoUrl: 'https://affiliate.example.com/logo.png',
  providerName: 'provider',
  affiliateName: 'affiliate',
  isWhiteLabelingEnabled: 'true',
  fromEmailAddress: '<EMAIL>',
  email: '<EMAIL>',
  planType: 'standard',
};

const mockResetPasswordDto: ResetPasswordDto = {
  client_id: 'client-id',
  gid: 1,
  requestToken: 'token',
  userId: 123,
  user_pwd: 'Password@123',
  confirm_pwd: 'Password@123',
};

const validDto = {
  reqId: '1',
  client_id: 'valid_client_id',
  gid: 1,
  userId: 123,
  email: '<EMAIL>',
  requestToken: '', // To be filled below after generating
  requestTime: '**********',
  affiliateLmsUrl: 'https://url.com',
  affiliateLogoUrl: 'https://logo.com',
  providerName: 'provider',
  affiliateName: 'affiliate',
  isWhiteLabelingEnabled: true,
  fromEmailAddress: '',
  user_pwd: 'Password@123',
  confirm_pwd: 'Password@123',
} as SetupManagerAccountDto;
describe('EnterpriseLMSService', () => {
  let service: EnterpriseLMSService;
  let helperService: any;
  let configService: any;
  let cachingService: any;
  let cryptoHelper: any;

  beforeEach(async () => {
    helperService = { get: jest.fn(), getHelper: jest.fn() };
    configService = { get: jest.fn() };
    cachingService = { set: jest.fn(), get: jest.fn() };
    cryptoHelper = { encrypt: jest.fn().mockReturnValue('encrypted') };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EnterpriseLMSService,
        { provide: 'CRYPTO_HELPER', useValue: cryptoHelper },
        { provide: ConfigService, useValue: configService },
        { provide: CachingService, useValue: cachingService },
        { provide: HelperService, useValue: helperService },
      ],
    }).compile();

    service = module.get<EnterpriseLMSService>(EnterpriseLMSService);
  });

  describe('forgotPassword', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);
    });

    it('should throw BadRequestException if gid is empty', async () => {
      const dto = { ...mockForgotPasswordDto, gid: '' };
      const result = await service.forgotPassword(dto);
      expect(result).toBeInstanceOf(BadRequestException);
      if (result instanceof BadRequestException) {
        expect(result.message).toBe('InvalidGroupId');
      }
    });

    it('should throw BadRequestException if user not found', async () => {
      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { getUserByEmail: jest.fn().mockResolvedValue(null) };
      });

      const result = await service.forgotPassword(mockForgotPasswordDto);
      expect(result).toBeInstanceOf(BadRequestException);
      if (result instanceof BadRequestException) {
        expect(result.message).toBe('UserNotFoundException');
      }
    });

    it('should throw BadRequestException if user is blocked', async () => {
      const user = { status: 0 };
      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { getUserByEmail: jest.fn().mockResolvedValue(user) };
      });

      const result = await service.forgotPassword(mockForgotPasswordDto);
      expect(result).toBeInstanceOf(BadRequestException);
      if (result instanceof BadRequestException) {
        expect(result.message).toBe('BlockedUser');
      }
    });
    it('should use default mailIdentifier if planType not found', async () => {
      const user = {
        uid: 123,
        email: '<EMAIL>',
        status: 1,
        user_groups: [1],
        display_name: 'Name',
        roles: [],
      };

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) {
          return { getUserByEmail: jest.fn().mockResolvedValue(user) };
        }
      });

      helperService.getHelper.mockImplementation(token => {
        if (token === 'AuthHelper') {
          return { forgotPasswordAttemptLimit: jest.fn().mockResolvedValue({}) };
        }
        if (token === 'EmailHelper') {
          return {
            getMailTokens: jest.fn().mockReturnValue({}),
            sendEmail: jest.fn().mockResolvedValue(true),
          };
        }
        if (token === 'lrsHelper') {
          return { sendDataToLrs: jest.fn() };
        }
      });

      jest.spyOn(service, 'getB2bResetPasswordUrl').mockResolvedValue({
        url: 'https://url/reset/token',
        token: 'tok123',
      });

      jest.spyOn(validator, 'isURL').mockReturnValue(true);

      configService.get.mockImplementation(key => {
        if (key === 'planTypes') return { standard: 'someOtherValue' }; // planType key missing
        if (key === 'ssoClientId') return 'client-id';
        return '';
      });

      const result = await service.forgotPassword(mockForgotPasswordDto);
      expect(result).toBe(true);
    });


    it('should throw BadRequestException if user not in group', async () => {
      const user = { status: 1, user_groups: [] };
      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { getUserByEmail: jest.fn().mockResolvedValue(user) };
      });

      const result = await service.forgotPassword(mockForgotPasswordDto);
      expect(result).toBeInstanceOf(BadRequestException);
      if (result instanceof BadRequestException) {
        expect(result.message).toBe('NotAGroupMember');
      }
    });

    it('should throw BadRequestException if rate limit exceeded', async () => {
      const user = { status: 1, user_groups: [1], display_name: 'Test' };
      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { getUserByEmail: jest.fn().mockResolvedValue(user) };
      });
      helperService.getHelper.mockImplementation(token => {
        if (token === 'AuthHelper') return {
          forgotPasswordAttemptLimit: jest.fn().mockResolvedValue({ status: 'limit_exceeded' }),
        };
      });

      const result = await service.forgotPassword(mockForgotPasswordDto);
      expect(result).toBeInstanceOf(BadRequestException);
      if (result instanceof BadRequestException) {
        expect(result.message).toBe('AttemptLimitExceed');
      }
    });

    it('should return false if reset password URL is empty', async () => {
      const user = {
        status: 1,
        user_groups: [1],
        uid: 123,
        email: '<EMAIL>',
        display_name: 'Name',
      };

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { getUserByEmail: jest.fn().mockResolvedValue(user) };
      });

      helperService.getHelper.mockImplementation(token => {
        if (token === 'AuthHelper') {
          return { forgotPasswordAttemptLimit: jest.fn().mockResolvedValue({}) };
        }
        if (token === 'EmailHelper') {
          return { getMailTokens: jest.fn().mockReturnValue({}) };
        }
      });

      jest.spyOn(service, 'getB2bResetPasswordUrl').mockResolvedValue({ url: '', token: '' });

      const result = await service.forgotPassword(mockForgotPasswordDto);
      expect(result).toBe(false);
    });

    it('should return false if sendEmail fails', async () => {
      const user = {
        uid: 123,
        email: '<EMAIL>',
        status: 1,
        user_groups: [1],
        display_name: 'Name',
        roles: [],
      };

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) {
          return { getUserByEmail: jest.fn().mockResolvedValue(user) };
        }
      });

      helperService.getHelper.mockImplementation(token => {
        if (token === 'AuthHelper') {
          return { forgotPasswordAttemptLimit: jest.fn().mockResolvedValue({}) };
        }
        if (token === 'EmailHelper') {
          return {
            getMailTokens: jest.fn().mockReturnValue({}),
            sendEmail: jest.fn().mockResolvedValue(false), // <- simulate failure
          };
        }
        if (token === 'lrsHelper') {
          return { sendDataToLrs: jest.fn() }; // included in case LRS is called later
        }
      });

      jest.spyOn(service, 'getB2bResetPasswordUrl').mockResolvedValue({
        url: 'http://example.com/reset/token',
        token: 'reset123',
      });
      jest.spyOn(validator, 'isURL').mockReturnValue(true);
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);

      // ✅ Mock configService.get('planTypes') correctly
      configService.get.mockImplementation(key => {
        if (key === 'planTypes') return { standard: 'forgotPasswordStandard' };
        return '';
      });

      const result = await service.forgotPassword(mockForgotPasswordDto);
      expect(result).toBe(false);
    });

    it('should return reset password URL and token if token is generated', async () => {
      const mockToken = 'generated-token';
      const forgotPasswordDto = {
        gid: '1',
        uid: 123,
        affiliateLmsUrl: 'https://test.com',
      } as any;

      const user = { email: '<EMAIL>' };

      helperService.getHelper.mockResolvedValue({
        userPasswordRehash: jest.fn().mockResolvedValue(mockToken),
      });

      cryptoHelper.encrypt = jest.fn(value => `enc-${value}`);

      const result = await service.getB2bResetPasswordUrl(forgotPasswordDto, user);

      expect(result).toEqual({
        url: 'https://test.com/affiliate/user/reset-password-v1/gid/enc-1/token/generated-token/uid/enc-123',
        token: 'generated-token',
      });
    });


    it('should return empty url and token if userPasswordRehash returns empty', async () => {
      helperService.getHelper.mockResolvedValue({
        userPasswordRehash: jest.fn().mockResolvedValue(''),
      });

      const forgotPasswordDto = {
        gid: '1',
        uid: 123,
        affiliateLmsUrl: 'https://test.com',
      } as any;

      const user = {};

      const result = await service.getB2bResetPasswordUrl(forgotPasswordDto, user);
      expect(result).toEqual({ url: '', token: '' });
    });

    it('should return true on successful email send and log LRS event', async () => {
      const user = {
        uid: 123,
        email: '<EMAIL>',
        status: 1,
        user_groups: [1],
        display_name: 'Name',
        roles: ['admin']
      };
      const token = 'resettoken';
      const url = `https://reset.url/token/${token}/uid/encrypted`;

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { getUserByEmail: jest.fn().mockResolvedValue(user) };
        return null;
      });

      helperService.getHelper.mockImplementation(token => {
        if (token === 'AuthHelper') {
          return { forgotPasswordAttemptLimit: jest.fn().mockResolvedValue({}) };
        }
        if (token === 'EmailHelper') {
          return {
            getMailTokens: jest.fn().mockReturnValue({}),
            sendEmail: jest.fn().mockResolvedValue(true),
          };
        }
        if (token === 'lrsHelper') {
          return { sendDataToLrs: jest.fn() };
        }
      });

      jest.spyOn(service, 'getB2bResetPasswordUrl').mockResolvedValue({ url, token });
      jest.spyOn(validator, 'isURL').mockReturnValue(true);
      configService.get.mockImplementation(key => {
        if (key === 'planTypes') return { standard: 'standard' };
        if (key === 'ssoClientId') return 'client-id';
        return '';
      });

      const result = await service.forgotPassword(mockForgotPasswordDto);
      expect(result).toBe(true);
    });

    it('should handle unexpected error and return it', async () => {
      helperService.get.mockRejectedValue(new Error('Unexpected'));
      const result = await service.forgotPassword(mockForgotPasswordDto);
      expect(result).toBeInstanceOf(Error);
      if (result instanceof Error) {
        expect(result.message).toBe('Unexpected');
      }
    });
  });


  describe('resetPassword', () => {

    it('should return BadRequestException object if gid is empty', async () => {
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);
      jest.spyOn(Utility, 'isEmpty').mockReturnValue(true); // gid empty

      const result = await service.resetPassword(mockResetPasswordDto);
      expect(result).toBeInstanceOf(BadRequestException);
      if (result instanceof BadRequestException) {
        expect(result.message).toBe('InvalidGroupId');
      }
    });

    it('should return BadRequestException if request token does not match cache', async () => {
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);
      jest.spyOn(Utility, 'isEmpty').mockReturnValue(false);

      const user = {
        uid: 123,
        email: '<EMAIL>',
        status: 1,
        user_groups: ['1'],
        password: 'pwd',
        login: 'login',
      };

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { findOne: jest.fn().mockResolvedValue(user) };
        if (token === CachingService) return { get: jest.fn().mockResolvedValue('wrong-token') }; // mismatch
        if (token === 'UserHelper') return { updateUserOptions: jest.fn().mockResolvedValue(true) };
        if (token === AuthTokenHelper) return {
          drupalHmacBase64: jest.fn().mockReturnValue('token'),
          getUserInfoForJwt: jest.fn().mockResolvedValue({})
        };
      });

      configService.get.mockReturnValue('salt');

      const result = await service.resetPassword(mockResetPasswordDto);
      expect(result).toBeInstanceOf(BadRequestException);
      if (result instanceof BadRequestException) {
        expect(result.message).toBe('InvalidToken');
      }
    });

    it('should return BadRequestException if user is not a member of the group', async () => {
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);
      jest.spyOn(Utility, 'isEmpty').mockReturnValue(false);

      const user = {
        uid: 123,
        email: '<EMAIL>',
        status: 1,
        user_groups: ['99'], // not matching gid
        password: 'pwd',
        login: 'login',
      };

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { findOne: jest.fn().mockResolvedValue(user) };
        if (token === CachingService) return { get: jest.fn().mockResolvedValue('token') };
        if (token === 'UserHelper') return { updateUserOptions: jest.fn().mockResolvedValue(true) };
        if (token === AuthTokenHelper) return {
          drupalHmacBase64: jest.fn().mockReturnValue('token'),
          getUserInfoForJwt: jest.fn().mockResolvedValue({})
        };
      });

      configService.get.mockReturnValue('salt');

      const result = await service.resetPassword(mockResetPasswordDto);
      expect(result).toBeInstanceOf(BadRequestException);
      if (result instanceof BadRequestException) {
        expect(result.message).toBe('NotMemberOfGroup');
      }
    });

    it('should throw BadRequestException if drupal HMAC token does not match requestToken', async () => {
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);
      jest.spyOn(Utility, 'isEmpty').mockReturnValue(false);

      const user = {
        uid: 123,
        email: '<EMAIL>',
        status: 1,
        user_groups: [1], // ✅ Ensure this is a number
        password: 'pwd',
        login: 'login',
      };

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { findOne: jest.fn().mockResolvedValue(user) };
        if (token === CachingService) return { get: jest.fn().mockResolvedValue('token') };
      });

      helperService.getHelper.mockImplementation(token => {
        if (token === AuthTokenHelper) {
          return {
            drupalHmacBase64: jest.fn().mockReturnValue('invalid-token'), // ❌ mismatch
            getUserInfoForJwt: jest.fn().mockResolvedValue({}),
          };
        }
        if (token === 'UserHelper') return { updateUserOptions: jest.fn().mockResolvedValue(true) };
      });

      configService.get.mockImplementation(key => {
        if (key === 'drupal_hash_salt') return 'salt';
        if (key === 'resetPasswordRequestSave') return 1;
        if (key === 'resetPasswordRequestValidate') return 2;
        return '';
      });

      const dto = {
        ...mockResetPasswordDto,
        gid: 1, // ✅ Also number (same type as in user_groups)
        userId: 123,
        requestToken: 'token',
      };

      const result = await service.resetPassword(dto);

      expect(result).toBeInstanceOf(BadRequestException);
      if (result instanceof BadRequestException) {
        expect(result.message).toBe('InvalidPasswdUrl');
      }
    });
    it('should return JWT user data if reqType is validate', async () => {
      const user = {
        uid: 123,
        email: '<EMAIL>',
        status: 1,
        user_groups: [1],
        password: 'pwd',
        login: 'login',
      };

      const mockTokenHelper = {
        drupalHmacBase64: jest.fn().mockReturnValue('token'),
        getUserInfoForJwt: jest.fn().mockResolvedValue({ data: 'jwt-data' }),
      };

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { findOne: jest.fn().mockResolvedValue(user) };
        if (token === CachingService) return { get: jest.fn().mockResolvedValue('token') };
      });

      helperService.getHelper.mockImplementation(token => {
        if (token === AuthTokenHelper) return mockTokenHelper;
        if (token === 'UserHelper') return { updateUserOptions: jest.fn() };
      });

      configService.get.mockImplementation(key => {
        if (key === 'drupal_hash_salt') return 'salt';
        if (key === 'resetPasswordRequestSave') return 1;
        if (key === 'resetPasswordRequestValidate') return 2;
        return '';
      });

      const dto = {
        ...mockResetPasswordDto,
        reqId: '2', // validate mode
        gid: 1,
        userId: 123,
        requestToken: 'token',
      };

      const result = await service.resetPassword(dto);

      expect(result).toEqual({
        type: 'success',
        userData: { data: 'jwt-data' },
      });
    });

    it('should clear session cache and return JWT when password is reset successfully', async () => {
      const user = {
        uid: 123,
        email: '<EMAIL>',
        status: 1,
        user_groups: [1],
        password: 'hashed-password',
        login: 'testuser',
        user_options: 1,
        roles: ['user'],
        display_name: 'Test User',
      };

      const mockJwt = { token: 'jwt-token' };

      const mockTokenHelper = {
        drupalHmacBase64: jest.fn().mockReturnValue('valid-token'),
        getUserInfoForJwt: jest.fn().mockResolvedValue(mockJwt),
      };

      const mockUserHelper = {
        updateUserOptions: jest.fn().mockResolvedValue(true),
      };

      const mockCacheService = {
        get: jest.fn().mockResolvedValue('valid-token'),
        set: jest.fn(),
      };

      const mockLrsHelper = {
        sendDataToLrs: jest.fn(),
      };

      // Mocks
      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { findOne: jest.fn().mockResolvedValue(user) };
        if (token === CachingService) return mockCacheService;
      });

      helperService.getHelper.mockImplementation(token => {
        if (token === AuthTokenHelper) return mockTokenHelper;
        if (token === 'UserHelper') return mockUserHelper;
        if (token === 'lrsHelper') return mockLrsHelper;
      });

      configService.get.mockImplementation(key => {
        if (key === 'resetPasswordRequestSave') return 1;
        if (key === 'resetPasswordRequestValidate') return 2;
        if (key === 'drupal_hash_salt') return 'salt';
        if (key === 'sessionCache') return 'sess_';
        if (key === 'ssoClientId') return 'client-id';
        return '';
      });

      const dto = {
        ...mockResetPasswordDto,
        userId: 123,
        gid: 1,
        reqId: '1', // save
        requestToken: 'valid-token',
      };

      const result = await service.resetPassword(dto);

      expect(mockCacheService.set).toHaveBeenCalledWith('sess_123', null, 0);
      expect(mockTokenHelper.getUserInfoForJwt).toHaveBeenCalledWith(user, 'client-id');

      expect(result).toEqual({
        type: 'success',
        msg: 'Password has been reset successfully. You will be logged out from all devices.',
        userData: mockJwt,
      });
    });

    it('should return fallback error response if updateUserOptions returns false', async () => {
      const user = {
        uid: 123,
        email: '<EMAIL>',
        status: 1,
        user_groups: [1],
        password: 'pwd',
        login: 'login',
        user_options: 1,
      };

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { findOne: jest.fn().mockResolvedValue(user) };
        if (token === CachingService) return {
          get: jest.fn().mockResolvedValue('token'),
          set: jest.fn(),
        };
      });

      helperService.getHelper.mockImplementation(token => {
        if (token === 'UserHelper') return { updateUserOptions: jest.fn().mockResolvedValue(false) }; // false
        if (token === AuthTokenHelper) return {
          drupalHmacBase64: jest.fn().mockReturnValue('token'),
          getUserInfoForJwt: jest.fn().mockResolvedValue({}),
        };
        if (token === 'lrsHelper') return { sendDataToLrs: jest.fn() };
      });

      configService.get.mockImplementation(key => {
        if (key === 'resetPasswordRequestSave') return 1;
        if (key === 'resetPasswordRequestValidate') return 2;
        if (key === 'drupal_hash_salt') return 'salt';
        if (key === 'sessionCache') return 'sess_';
        return '';
      });

      const dto = { ...mockResetPasswordDto, reqId: '1', gid: 1 }; // ✅ Make sure gid matches
      const result = await service.resetPassword(dto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Some error occurred while resetting your password',
      });
    });

    it('should return BadRequestException if drupal token mismatch', async () => {
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);
      jest.spyOn(Utility, 'isEmpty').mockReturnValue(false);

      const user = {
        uid: 123,
        email: '<EMAIL>',
        status: 1,
        user_groups: ['1'],
        password: 'pwd',
        login: 'login',
      };

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { findOne: jest.fn().mockResolvedValue(user) };
        if (token === CachingService) return { get: jest.fn().mockResolvedValue('token') };
      });

      helperService.getHelper.mockImplementation(token => {
        if (token === 'UserHelper') return { updateUserOptions: jest.fn().mockResolvedValue(true) };
        if (token === AuthTokenHelper) return {
          drupalHmacBase64: jest.fn().mockReturnValue('wrong-token'), // mismatch
          getUserInfoForJwt: jest.fn().mockResolvedValue({})
        };
      });

      configService.get.mockReturnValue('salt');

      const result = await service.resetPassword(mockResetPasswordDto);

      expect(result).toBeInstanceOf(BadRequestException);
      if (result instanceof BadRequestException) {
        expect(result.message).toBe('NotMemberOfGroup');
      }
    });



    it('should return BadRequestException object if user is blocked', async () => {
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);
      jest.spyOn(Utility, 'isEmpty').mockReturnValue(false);

      const user = {
        uid: 123,
        email: '<EMAIL>',
        status: 0, // blocked
        user_groups: ['1'],
        password: 'pwd',
        login: 'login',
      };

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { findOne: jest.fn().mockResolvedValue(user) };
        if (token === CachingService) return { get: jest.fn().mockResolvedValue('token') };
        if (token === 'UserHelper') return { updateUserOptions: jest.fn().mockResolvedValue(true) };
        if (token === AuthTokenHelper) return {
          drupalHmacBase64: jest.fn().mockReturnValue('token'),
          getUserInfoForJwt: jest.fn().mockResolvedValue({})
        };
      });

      configService.get.mockReturnValue('salt');

      const result = await service.resetPassword(mockResetPasswordDto);
      expect(result).toBeInstanceOf(BadRequestException);
      if (result instanceof BadRequestException) {
        expect(result.message).toBe('BlockedUser');
      }
    });
    it('should return error object if request type is invalid', async () => {
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);
      jest.spyOn(Utility, 'isEmpty').mockReturnValue(false);

      const user = {
        uid: 123,
        email: '<EMAIL>',
        status: 1,
        user_groups: [1],
        password: 'pwd',
        login: 'login',
        user_options: 1,
      };

      const mockTokenHelper = {
        drupalHmacBase64: jest.fn().mockReturnValue('token'),
        getUserInfoForJwt: jest.fn().mockResolvedValue({}),
      };

      const mockUserRepo = { findOne: jest.fn().mockResolvedValue(user) };
      const mockCache = { get: jest.fn().mockResolvedValue('token') };
      const mockUserHelper = { updateUserOptions: jest.fn().mockResolvedValue(true) };

      // Fix: get => mock userRepo and cache
      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return mockUserRepo;
        if (token === CachingService) return mockCache;
      });

      // Fix: getHelper => mock tokenHelper and userHelper
      helperService.getHelper.mockImplementation(token => {
        if (token === AuthTokenHelper) return mockTokenHelper;
        if (token === 'UserHelper') return mockUserHelper;
      });

      configService.get.mockImplementation(key => {
        if (key === 'resetPasswordRequestSave') return 1;
        if (key === 'resetPasswordRequestValidate') return 2;
        if (key === 'drupal_hash_salt') return 'salt';
        return 'dummy';
      });

      const dto = { ...mockResetPasswordDto, reqId: '999' }; // invalid request type
      const result = await service.resetPassword(dto);

      expect(result).toEqual({ type: 'error', msg: 'Invalid Request' });
    });
  });

  describe('getUidByName', () => {
    it('should return failed response if username is not provided', async () => {
      const result = await service.getUidByName('');
      expect(result.status).toBe('failed');
    });

    it('should return uid if user is found', async () => {
      const userRepo = { findOne: jest.fn().mockResolvedValue({ uid: 1 }) };
      helperService.get.mockResolvedValue(userRepo);
      const result = await service.getUidByName('testuser');
      expect(result.status).toBe('success');
      expect(result.data.uid).toBe(1);
    });

    it('should return "No data found." if user is not found', async () => {
      const userRepo = { findOne: jest.fn().mockResolvedValue(null) };
      helperService.get.mockResolvedValue(userRepo);

      const result = await service.getUidByName('missinguser');

      expect(result.status).toBe('failed');
      expect(result.msg).toBe('No data found.');
      expect(result.data).toBeNull();
    });
    it('should log error and return undefined if exception occurs', async () => {
      const error = new Error('Database failure');
      helperService.get.mockRejectedValue(error);
      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation();

      const result = await service.getUidByName('errorUser');

      expect(loggerSpy).toHaveBeenCalledWith('getUidByName', expect.objectContaining({
        METHOD: expect.stringContaining('getUidByName'),
        MESSAGE: 'Database failure',
        REQUEST: 'errorUser',
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));

      expect(result).toBeUndefined(); // Because there's no return in `catch`
    });

  });

  describe('setupManagerAccount', () => {
    it('should throw UnauthorizedException if client validation fails', async () => {
      // Arrange
      const dto = {
        reqId: '1',
        client_id: 'invalid_client_id',
        gid: 1,
        userId: 123,
        email: '<EMAIL>',
        requestToken: 'token',
        requestTime: '123456',
        affiliateLmsUrl: 'https://url.com',
        affiliateLogoUrl: 'https://logo.com',
        providerName: 'provider',
        affiliateName: 'affiliate',
        isWhiteLabelingEnabled: true,
        fromEmailAddress: '',
        user_pwd: 'Password@123',
        confirm_pwd: 'Password@123',
      } as SetupManagerAccountDto;

      // Mock config value for clientSecret
      configService.get.mockReturnValue('expected_secret');

      // Simulate validateClientRequest returning false
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(false);

      // Also mock helperService.get to prevent any other failures
      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { findByUID: jest.fn() }; // minimal stub
        return {};
      });

      const result = await service.setupManagerAccount(dto);
      expect(result).toBeInstanceOf(UnauthorizedException);
      if (result instanceof UnauthorizedException) {
        expect(result.message).toBe('Unauthorized access request.');
      }
    });

    it('should throw BadRequestException if user is not in specified group', async () => {
      const dto = {
        reqId: '1',
        client_id: 'invalid_client_id',
        gid: 2,
        userId: 123,
        email: '<EMAIL>',
        requestToken: 'token',
        requestTime: '123456',
        affiliateLmsUrl: 'https://url.com',
        affiliateLogoUrl: 'https://logo.com',
        providerName: 'provider',
        affiliateName: 'affiliate',
        isWhiteLabelingEnabled: true,
        fromEmailAddress: '',
        user_pwd: 'Password@123',
        confirm_pwd: 'Password@123',
      } as SetupManagerAccountDto;
      const validUser = {
        uid: 123,
        email: '<EMAIL>',
        status: 1, // Active user
        name: 'Test User',
        roles: ['authenticated user'], // Can be empty or relevant
        user_groups: [1], // Should match gid in the dto
        user_options: 1, // This is used to decide password setup or token JWT generation
        password: 'Password@123', // required for drupalHmacBase64
      };

      const user = { ...validUser, user_groups: [1] };

      helperService.get.mockResolvedValue({ findByUID: jest.fn().mockResolvedValue(user) });
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);
      configService.get.mockReturnValue('client-secret');

      const result = await service.setupManagerAccount(dto);
      expect(result).toBeInstanceOf(BadRequestException);
      if (result instanceof BadRequestException) {
        expect(result.message).toBe('UserNotInGroup');
      }
    });

    it('should throw BadRequestException if affiliateLmsUrl is empty', async () => {
      const validUser = {
        uid: 123,
        email: '<EMAIL>',
        status: 1,
        name: 'Test User',
        roles: ['authenticated user'],
        user_groups: [1],
        user_options: 1,
        password: 'hashPassword', // Used in drupalHmacBase64
      };

      const dto = { ...validDto, affiliateLmsUrl: '' };

      const user = { ...validUser, user_groups: [dto.gid] };
      helperService.get.mockResolvedValue({ findByUID: jest.fn().mockResolvedValue(user) });
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);
      configService.get.mockReturnValue('client-secret');

      const result = await service.setupManagerAccount(dto);
      expect(result).toBeInstanceOf(BadRequestException);
      if (result instanceof BadRequestException) {
        expect(result.message).toBe('InvalidAffiliateUrl');
      }
    });
    it('should return welcome email info when user_options is not 1', async () => {
      const validUser = {
        uid: 123,
        email: '<EMAIL>',
        status: 1,
        name: 'Test User',
        roles: ['authenticated user'],
        user_groups: [1],
        user_options: 0, // <- not 1
        password: 'hashPassword',
      };

      const welcomeEmailInfo = { data: { email: '<EMAIL>' } };

      helperService.get.mockResolvedValue({
        findByUID: jest.fn().mockResolvedValue(validUser),
      });

      helperService.getHelper.mockImplementation(token => {
        if (token === AuthTokenHelper) return {
          drupalHmacBase64: jest.fn().mockReturnValue('token'),
        };
        if (token === 'UserHelper') return {
          setupWelcomeEmailToManager: jest.fn().mockResolvedValue(welcomeEmailInfo),
        };
      });

      configService.get.mockImplementation(key => {
        if (key === 'setupManagerRequestValidate') return 2;
        if (key === 'clientSecret') return 'client-secret';
        if (key === 'drupal_hash_salt') return 'salt';
        return '';
      });

      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);

      const dto = {
        ...validDto,
        reqId: '2',
        requestToken: 'token', // ✅ Match with mocked drupalHmacBase64
      };

      const result = await service.setupManagerAccount(dto);

      expect(result).toEqual({
        type: 'success',
        msg: 'User information fetched successfully',
        data: {
          userInfo: welcomeEmailInfo.data,
          setPasswordStatus: 0,
        },
      });
    });

    it('should skip and return default error when request type is save', async () => {
      const mockUser = {
        uid: 123,
        status: 1,
        user_groups: [1],
        email: '<EMAIL>',
        name: 'Test User',
        user_options: 1,
        roles: [],
      };

      const dto = {
        reqId: '1',
        client_id: 'client-id',
        userId: 123,
        gid: 1,
        requestTime: '123456',
        requestToken: 'token',
        email: mockUser.email,
        user_pwd: 'Password@123',
        confirm_pwd: 'Password@123',
        affiliateLmsUrl: 'https://url.com',
        affiliateLogoUrl: 'https://logo.com',
        providerName: 'provider',
        affiliateName: 'affiliate',
        isWhiteLabelingEnabled: true,
        fromEmailAddress: '',
      };

      const mockTokenHelper = {
        drupalHmacBase64: jest.fn().mockReturnValue('token'),
        getUserInfoForJwt: jest.fn().mockResolvedValue({ data: {} }),
      };

      const mockCachingService = {
        set: jest.fn(),
      };

      configService.get.mockImplementation(key => {
        switch (key) {
          case 'clientSecret': return 'client-secret';
          case 'setupManagerRequestSave': return 1;
          case 'setupManagerRequestValidate': return 2;
          case 'drupal_hash_salt': return 'salt';
          case 'passwordLength': return 8;
          case 'maxPasswordLength': return 20;
          case 'sessionCache': return 'sess_';
          case 'ssoClientId': return 'sso-client';
          default: return '';
        }
      });

      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);
      jest.spyOn(Utility, 'validatePasswd').mockReturnValue({ type: 'success', msg: 'Valid password' });

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { findByUID: jest.fn().mockResolvedValue(mockUser) };
        if (token === CachingService) return mockCachingService;
      });

      helperService.getHelper.mockImplementation(token => {
        if (token === 'UserHelper') {
          return {
            updateUserOptions: jest.fn().mockResolvedValue(true),
            setupWelcomeEmailToManager: jest.fn().mockResolvedValue(true),
          };
        }
        if (token === AuthTokenHelper) return mockTokenHelper;
        if (token === 'lrsHelper') return { sendDataToLrs: jest.fn() };
      });

      const result = await service.setupManagerAccount(dto);

      expect(mockCachingService.set).toHaveBeenCalledWith('sess_123', null, 0);

      // ✅ Do not assert getUserInfoForJwt here — it's not called during "save"
      expect(result).toEqual({
        type: 'success',
        msg: 'Password has been reset successfully. You will be logged out from all devices.',
        data: null,
      });
    });
    it('should return JWT info when user_options is 1 during validation', async () => {
      const mockUser = {
        uid: 123,
        status: 1,
        user_groups: [1],
        email: '<EMAIL>',
        name: 'Test User',
        user_options: 1,
        roles: [],
        password: 'hashedPass',
      };

      const dto = {
        ...validDto,
        reqId: '2', // Validate request
        userId: 123,
        gid: 1,
        client_id: 'client-id',
        email: mockUser.email,
        requestToken: 'token',
      };

      const mockJwt = { data: { uid: 123 } };

      configService.get.mockImplementation(key => {
        switch (key) {
          case 'clientSecret': return 'client-secret';
          case 'setupManagerRequestValidate': return 2;
          case 'drupal_hash_salt': return 'salt';
          default: return '';
        }
      });

      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) {
          return { findByUID: jest.fn().mockResolvedValue(mockUser) };
        }
      });

      const mockTokenHelper = {
        drupalHmacBase64: jest.fn().mockReturnValue('token'),
        getUserInfoForJwt: jest.fn().mockResolvedValue(mockJwt),
      };

      helperService.getHelper.mockImplementation(token => {
        if (token === AuthTokenHelper) return mockTokenHelper;
        if (token === 'UserHelper') return { setupWelcomeEmailToManager: jest.fn() };
      });

      const result = await service.setupManagerAccount(dto);

      expect(result).toEqual({
        type: 'success',
        msg: 'User information fetched successfully',
        data: {
          userInfo: mockJwt.data,
          setPasswordStatus: 1,
        },
      });

      // ✅ Correct assertion using shared mock reference
      expect(mockTokenHelper.getUserInfoForJwt)
        .toHaveBeenCalledWith(mockUser, dto.client_id);
    });




    it('should return error if password validation fails', async () => {
      const dto = {
        ...validDto,
        reqId: '9', // default path
        userId: 123,
        gid: 1,
        requestTime: '123456',
        requestToken: 'token', // ✅ this must match the mocked token
        user_pwd: 'abc', // simulate short/invalid password
        confirm_pwd: 'abc',
        email: '<EMAIL>',
      };

      configService.get.mockImplementation(key => {
        switch (key) {
          case 'clientSecret': return 'client-secret';
          case 'setupManagerRequestSave': return 1;
          case 'setupManagerRequestValidate': return 2;
          case 'drupal_hash_salt': return 'salt';
          case 'passwordLength': return 6;
          case 'maxPasswordLength': return 12;
          default: return '';
        }
      });

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) {
          return {
            findByUID: jest.fn().mockResolvedValue({
              uid: 123,
              status: 1,
              user_groups: [1],
              email: '<EMAIL>',
              name: 'Test User',
            }),
          };
        }
        if (token === CachingService) {
          return { set: jest.fn() };
        }
      });

      helperService.getHelper.mockImplementation(token => {
        if (token === AuthTokenHelper) {
          return {
            drupalHmacBase64: jest.fn().mockReturnValue('token'), // ✅ matches requestToken
          };
        }
        if (token === 'UserHelper') {
          return { updateUserOptions: jest.fn() };
        }
      });

      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(true);
      jest.spyOn(Utility, 'validatePasswd').mockReturnValue({
        type: 'error',
        msg: 'Password too short',
      });

      const result = await service.setupManagerAccount(dto);

      expect(result).toEqual({
        type: 'error',
        msg: 'Invalid request.',
        data: null
      });
    });

    it('should throw BadRequestException if updateUserOptions returns false', async () => {
      const dto = { ...validDto, reqId: '9' };

      configService.get.mockImplementation(key => {
        if (key === 'clientSecret') return 'client-secret';
        if (key === 'drupal_hash_salt') return 'salt';
        if (key === 'passwordLength') return 6;
        if (key === 'maxPasswordLength') return 16;
        return 2;
      });

      const mockUser = {
        uid: 123,
        status: 1,
        user_groups: [1],
        email: '<EMAIL>',
        name: 'Test User',
      };

      helperService.get.mockImplementation(token => {
        if (token === UserRepository) return { findByUID: jest.fn().mockResolvedValue(mockUser) };
        if (token === CachingService) return { set: jest.fn() };
      });

      helperService.getHelper.mockImplementation(token => {
        if (token === 'UserHelper') return { updateUserOptions: jest.fn().mockResolvedValue(false) };
        if (token === AuthTokenHelper) return {
          drupalHmacBase64: jest.fn().mockReturnValue('token'),
        };
      });

      await expect(service.setupManagerAccount(dto)).resolves.toBeInstanceOf(BadRequestException);
    });
    it('should catch error and log it if unexpected failure occurs', async () => {
      const dto = { ...validDto, reqId: '9' };

      configService.get.mockReturnValue('client-secret');
      helperService.get.mockRejectedValue(new Error('Unexpected failure'));

      const loggerSpy = jest.spyOn(Logger, 'error').mockImplementation();

      const result = await service.setupManagerAccount(dto);
      if (result instanceof Error) {
        expect(result.message).toBe('Unexpected failure');
      }

      expect(loggerSpy).toHaveBeenCalledWith(
        'setupManagerAccount',
        expect.objectContaining({
          METHOD: expect.stringContaining('setupManagerAccount'),
          MESSAGE: 'Unexpected failure',
        }),
      );
    });
    it('should throw BadRequestException for non-active user', async () => {
      const dto = {
        reqId: '1',
        client_id: 'x',
        gid: 1,
        userId: 123,
        email: '<EMAIL>',
        requestToken: 'token',
        requestTime: '123456',
        affiliateLmsUrl: 'https://url.com',
        affiliateLogoUrl: 'https://logo.com',
        providerName: 'provider',
        affiliateName: 'affiliate',
        isWhiteLabelingEnabled: true,
        fromEmailAddress: '',
        user_pwd: 'Password@123',
        confirm_pwd: 'Password@123'
      } as SetupManagerAccountDto;
      configService.get.mockReturnValue('client-secret');
      helperService.get.mockResolvedValue({ findByUID: jest.fn().mockResolvedValue({ status: 0 }) });
      const result = await service.setupManagerAccount(dto);
      if (result instanceof BadRequestException) {
        expect(result).toBeInstanceOf(BadRequestException);
      }
    });
  });
});
