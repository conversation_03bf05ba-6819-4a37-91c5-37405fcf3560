import { BadRequestException, Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import { ForgotPasswordB2B } from '../../common/typeDef/auth.type';
import { IUserRepository, UserRepository } from '../../user/repositories/user/user.repository';
import { HelperService } from '../../helper/helper.service';
import { Logger } from '../../logging/logger';
import { CryptoHelper } from '../../helper/helper.crypto';
import { ConfigService } from '@nestjs/config';
import { CachingService } from '../../caching/caching.service';
import { SetupManagerAccountDto } from '../dto/setup-manager-account-dto';
import { Utility } from '../../common/util/utility';
import { AuthTokenHelper } from '../../auth/helper/auth.tokenhelper';
import { isEmpty } from 'class-validator';
import validator from 'validator';
import { ResetPasswordDto } from '../dto/reset.password.dto';
import { User } from '../../db/mongo/schema/user/user.schema';
@Injectable()
export class EnterpriseLMSService {
  @Inject('CRYPTO_HELPER') private readonly cryptoHelper: CryptoHelper;
  @Inject(ConfigService) private readonly configService: ConfigService;
  @Inject(CachingService) private readonly cachingService: CachingService;
  @Inject(HelperService) private readonly helperService: HelperService;
  #lrsObjectType = 'enterprise-lms-v1';
  async forgotPassword(forgotPasswordDto: ForgotPasswordB2B): Promise<boolean | Error> {
    try {
      const [userRepository, authHelper, emailHelper] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.getHelper('AuthHelper'),
        this.helperService.getHelper('EmailHelper'),
      ]);

      Utility.validateClientRequest(forgotPasswordDto?.client_id, this.configService.get('clientSecret'));
      
      //data validations
      if (isEmpty(forgotPasswordDto?.gid)){
        throw new BadRequestException('InvalidGroupId');
      }

      const user = await userRepository.getUserByEmail(forgotPasswordDto?.email);

      if (!user){
        throw new BadRequestException('UserNotFoundException');
      }

      if (user?.status !== 1) {
        throw new BadRequestException('BlockedUser');
      }

      let user_groups = user.user_groups.map(Number);

      if (!Array.isArray(user_groups) || !user_groups.includes(Number(forgotPasswordDto?.gid))) {
        throw new BadRequestException('NotAGroupMember');
      }

      //Rate limiting
      const attemptResponse = await authHelper.forgotPasswordAttemptLimit(forgotPasswordDto?.email);
      if (attemptResponse?.status === 'limit_exceeded') {
        throw new BadRequestException('AttemptLimitExceed');
      }
      
      forgotPasswordDto = { ...forgotPasswordDto, displayName: user?.display_name };
      const tokens = emailHelper.getMailTokens(forgotPasswordDto);
      forgotPasswordDto = { ...forgotPasswordDto, uid: user?.uid };

      const resetPasswordUrl = await this.getB2bResetPasswordUrl(forgotPasswordDto, user);
      if (resetPasswordUrl.url === '' || resetPasswordUrl.token === '') {
        return false;
      }
      tokens.resetPasswordUrl = resetPasswordUrl?.url;
      //validating the reset password url
      if (validator.isURL(resetPasswordUrl?.url)){
        const tokens = resetPasswordUrl.url.split('/');
        const resetToken = tokens[tokens.length - 3];
        await this.cachingService.set(`${user?.email}_reset_password_token`, resetToken, 60 * 60 * 24); //store the token in memcache for expiry
      }

      const planTypes = this.configService.get('planTypes');
      const mailIdentifier = planTypes[forgotPasswordDto.planType]
        ? planTypes[forgotPasswordDto.planType]
        : 'forgotPasswordB2B';
      const emailResponse = await emailHelper.sendEmail(forgotPasswordDto?.email, mailIdentifier, tokens);
      if (emailResponse) {
        await this.cachingService.set(`${user?.uid}`, resetPasswordUrl?.token, 60 * 60 * 24); //store the token in memcache for expiry
        //LRS event logging
        const userLrsData = {
          id: user?.uid,
          email: user?.email,
          name: user?.display_name || '',
          roles: user?.roles,
        };
  
        const lrsData = {
          verb: 'forgot-password',
          objectType: this.#lrsObjectType,
          objectId: user?.uid,
          dataVals: {
            client_id: this.configService.get('ssoClientId'),
            affiliateUrl: forgotPasswordDto?.affiliateLmsUrl,
            gid: forgotPasswordDto?.gid,
          },
        };
        const lrsInstance = await this.helperService.getHelper('lrsHelper');
        lrsInstance.sendDataToLrs(userLrsData, lrsData);
        // End LRS Data
        return true;
      }
      return false;
    } catch (error: any) {
      Logger.error('forgetPassword', {
        METHOD: this.constructor.name + '@' + this.forgotPassword.name,
        MESSAGE: error.message,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return error;
    }
  }

  async getB2bResetPasswordUrl(forgotPasswordDto: ForgotPasswordB2B, user: Partial<User>): Promise<{ url: string; token: string }> {
    try {
      const UserApiV1Helper = await this.helperService.getHelper('UserApiV1Helper');
      const token = await UserApiV1Helper.userPasswordRehash(forgotPasswordDto, user);
      if (token) {
        return {
          url: `${forgotPasswordDto?.affiliateLmsUrl}/affiliate/user/reset-password-v1/gid/${this.cryptoHelper.encrypt(
          forgotPasswordDto?.gid,
          )}/token/${token}/uid/${this.cryptoHelper.encrypt(String(forgotPasswordDto?.uid))}`,
          token: token,
        };
      }
      return {url: '', token: ''};
    } catch (error: any) {
      Logger.error('getB2bResetPasswordUrl', {
      METHOD: `${this.constructor.name}@getB2bResetPasswordUrl`,
      MESSAGE: error.message,
      REQUEST: { forgotPasswordDto },
      RESPONSE: error.stack,
      TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }
  async resetPassword(resetPwd: ResetPasswordDto): Promise<Object | Error> {
    try {
      const [tokenHelper, userRepository, cacheService, userHelper] = await Promise.all([
        this.helperService.getHelper<AuthTokenHelper>(AuthTokenHelper),
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.get<CachingService>(CachingService),
        this.helperService.getHelper('UserHelper'),
      ]);
      let response = {"type": "error", "msg": "Some error occurred while resetting your password"}; 

      // Validate the client request using the client ID and secret
      Utility.validateClientRequest(resetPwd?.client_id,this.configService.get('clientSecret'),);
      
      const userId = resetPwd?.userId;
     
      if(Utility.isEmpty(resetPwd?.gid)){
        throw new BadRequestException('InvalidGroupId');
      }

      const userObj = await userRepository.findOne({ uid: Number(userId) });

      const storedCache = await cacheService.get(`${userObj?.email}_reset_password_token`);
      if (userObj && userObj?.status !== 1) {
        throw new BadRequestException('BlockedUser');
      }

      if (storedCache !== resetPwd?.requestToken) {
        throw new BadRequestException('InvalidToken');
      }

      if (userObj.user_groups == null || !userObj.user_groups.includes(resetPwd.gid)){
        throw new BadRequestException('NotMemberOfGroup');
      }
      
      const data = `${userObj?.login}${userObj?.uid}`;
      const key = `${this.configService.get('drupal_hash_salt')}${userObj?.password}`;
      const varToken = tokenHelper.drupalHmacBase64(data, key);
      if (varToken !== resetPwd?.requestToken) 
      {
        throw new BadRequestException('InvalidPasswdUrl');
      }

      const resetPasswordSave = Number(this.configService.get('resetPasswordRequestSave'));
      const resetPasswordValidate = Number(this.configService.get('resetPasswordRequestValidate'));


      const reqType : number = Number(resetPwd?.reqId) || resetPasswordValidate;
      let result;
      switch (reqType) {
        case resetPasswordSave:
          break;
        case resetPasswordValidate:
          const jwt = await tokenHelper.getUserInfoForJwt(userObj, resetPwd?.client_id);
          result = {
            "type": "success",
            "userData": jwt?.data || null, 
          };
          return result;
        default:
          response.msg = "Invalid Request";
          return response;
      }

      const updatePasswords = await userHelper.updateUserOptions(userId, resetPwd?.user_pwd, userObj.user_options);
      if(updatePasswords){
        const cacheKey = this.configService.get('sessionCache');
        await cacheService.set(`${cacheKey}${userId}`,null,0);
        const jwt = await tokenHelper.getUserInfoForJwt(userObj, resetPwd?.client_id);
        let passwordUpdated = {
          "type": "success",
          "msg": 'Password has been reset successfully. You will be logged out from all devices.',
          "userData": jwt
        };
         //LRS logging starts
         const userLrsData = {
          id: userObj?.uid,
          email: userObj?.email,
          name: userObj?.display_name || '',
          roles: userObj?.roles,
        };
  
        const lrsData = {
          verb: 'reset-password',
          objectType: this.#lrsObjectType,
          objectId: userObj?.uid,
          dataVals: {
            client_id: this.configService.get('ssoClientId'),
            gid: resetPwd?.gid,
          },
        };
        const lrsInstance = await this.helperService.getHelper('lrsHelper');
        lrsInstance.sendDataToLrs(userLrsData, lrsData);
        // End LRS Data
        return passwordUpdated;
      }

      return response;
    } catch (error: any) {
      Logger.error('resetPassword', {
        METHOD: this.constructor.name + '@' + this.resetPassword.name,
        MESSAGE: error.message,
        REQUEST: resetPwd,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return error;
    }
  }

  async getUidByName(uname: string) {
    let response = {
      status: 'failed',
      msg: 'Invalid request. Please try again.',
      data: null,
    };
    if (!uname) {
      return response;
    }
    try{
    const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
    const user = await userRepository.findOne({ name: uname });
    if (user) {
      response = {
        status: 'success',
        msg: 'Ok',
        data: {
          uid: user.uid,
        },
      };
    }else{
      response.msg =  "No data found."
    }
    return response;
  } catch(error: any){
    Logger.error('getUidByName', {
      METHOD: this.constructor.name + '@getUidByName',
      MESSAGE: error.message,
      REQUEST: uname,
      RESPONSE: error.stack,
      TIMESTAMP: new Date().getTime(),
    });
  }
  }
  async setupManagerAccount(requestBody: SetupManagerAccountDto): Promise<Object | Error> {
    try {
      let result = { 'type': 'error', 'msg': 'Some error occurred while resetting your password', 'data': null };
      const [userRepository, userHelper, tokenHelper] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.getHelper('UserHelper'),
        this.helperService.getHelper<AuthTokenHelper>(AuthTokenHelper),
      ]);
      const cacheService = await this.helperService.get<CachingService>(CachingService);

      const groupInfo = {
        gid: requestBody?.gid,
        affiliateUrl: requestBody?.affiliateLmsUrl,
        affiliateLogoUrl: requestBody?.affiliateLogoUrl,
      };

      // Validating the client request
      const validateClient = Utility.validateClientRequest(
        requestBody?.client_id,
        this.configService.get('clientSecret'),
      );
      if (!validateClient) {
        throw new UnauthorizedException('Unauthorized access request.');
      }

      // Fetching the user by uid
      const user = await userRepository.findByUID(requestBody?.userId);
      if (!user || user.status !== 1) {
        throw new BadRequestException('UserNotFoundException');
      }

      // Verify if the user is part of the specified group
      const userGroups = user?.user_groups || [];
      if (!requestBody?.gid || !userGroups.includes(requestBody.gid)) {
        throw new BadRequestException('UserNotInGroup');
      }

      if (isEmpty(requestBody?.affiliateLmsUrl)) {
        throw new BadRequestException('InvalidAffiliateUrl');
      }

      // Rehashing the token
      const data = `${requestBody?.requestTime}${user?.email}`;
      const key = `${this.configService.get('drupal_hash_salt')}${requestBody?.gid}#${requestBody?.userId}#${user?.email}`;
      const varToken = tokenHelper.drupalHmacBase64(data, key);
      if(!(requestBody?.requestToken === varToken)){
        throw new BadRequestException('InvalidToken');
      }

      // Assign requestType to requestBody?.reqId if it exists; otherwise, use the default value from the configuration.
      let requestType = Number(requestBody?.reqId) || this.configService.get('setupManagerRequestValidate');

      // Handle different request types based on the requestType value
      switch (requestType) {
        // Case for saving setup manager request
        case this.configService.get('setupManagerRequestSave'):
          break;

        // Case for validating setup manager request
        case this.configService.get('setupManagerRequestValidate'):
          const userOptions = user?.user_options;
          const userInfo = userOptions === 1
            ? await tokenHelper.getUserInfoForJwt(user, requestBody?.client_id)
            : await userHelper.setupWelcomeEmailToManager(user, groupInfo);

          result = {
            type: 'success',
            msg: 'User information fetched successfully',
            data: {
              userInfo: userInfo?.data || null,
              setPasswordStatus: userOptions,
            },
          };
          return result;
          break;

        // Default case for invalid request types
        default:
          result.msg = 'Invalid request.';
          return result;
      }

      const passwordResult = Utility.validatePasswd(
        requestBody?.user_pwd,
        this.configService.get('passwordLength'),
        this.configService.get('maxPasswordLength'),
      );
      if (passwordResult.type === 'error') {
        return passwordResult;
      }

      // Updating the user_options in both Mongo and Cloud6
      const userDetails = await userHelper.updateUserOptions(requestBody?.userId, requestBody?.user_pwd);
      if (userDetails !== false) {
        await userHelper.setupWelcomeEmailToManager(user, groupInfo);
       //LRS calling
       const userLrsData = {
        id: requestBody?.userId,
        email: user?.email,
        name: user?.name,
        roles: user?.roles,
       };
        const lrsData = {
        verb: 'reset-password',
        objectType: this.#lrsObjectType,
        objectId: requestBody?.userId,
        dataVals: {
          client_id: this.configService.get('ssoClientId'),
          gid: requestBody?.gid,
        },
      };
      const lrsInstance = await this.helperService.getHelper('lrsHelper');
      lrsInstance.sendDataToLrs(userLrsData, lrsData);
      //LRS ends  
        //Invalidate session cache to invalidate already loggedin user
        const cacheKey = this.configService.get('sessionCache');
        await cacheService.set(`${cacheKey}${user?.uid}`,null,0);
        result = {
          type: 'success',
          msg: 'Password has been reset successfully. You will be logged out from all devices.',
          data: null,
        };
      } else {
        throw new BadRequestException('errorOccuredResetPwd');
      }

      return result;
    } catch (error: any) {
      Logger.error('setupManagerAccount', {
        METHOD: `${this.constructor.name}@${this.setupManagerAccount.name}`,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return error;
    }
  }
}

