import { Test, TestingModule } from '@nestjs/testing';
import { UserDataSyncService } from './user-data-sync.service';
import { HelperService } from '../../helper/helper.service';
import { IUserRepository } from '../../user/repositories/user/user.repository';
import { Logger } from '../../logging/logger';
import * as drupalHash from 'drupal-hash';

describe('UserDataSyncService', () => {
  let service: UserDataSyncService;
  let helperService: HelperService;

  let mockUserRepository: Partial<IUserRepository>;
  let mockUserMgmtHelper: any;
  let mockUserHelper: any;

  beforeEach(async () => {
    mockUserRepository = {
      getUserByEmail: jest.fn(),
      saveUser: jest.fn(),
      deleteOneById: jest.fn(),
      upsert: jest.fn(),
    };

    mockUserMgmtHelper = {
      prepareAssignRoleList: jest.fn(),
      prepareSentinelUserRoleList: jest.fn(),
    };

    mockUserHelper = {
      createCloud6SentinelUser: jest.fn(),
      syncCloud6SentinelUserRole: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserDataSyncService,
        {
          provide: HelperService,
          useValue: {
            getHelper: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserDataSyncService>(UserDataSyncService);
    helperService = module.get<HelperService>(HelperService);

    (helperService.getHelper as jest.Mock).mockImplementation(async (arg) => {
      if (arg === 'UserMgmtUtilityHelper') return mockUserMgmtHelper;
      if (arg === 'UserHelper') return mockUserHelper;
      if (arg.name === 'UserRepository') return mockUserRepository;
    });

    jest.spyOn(drupalHash, 'hashPassword').mockReturnValue('hashedPass');
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should return default error response if email exists and no social data', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue({
      email: '<EMAIL>',
      user_social_data: [], // ✅ prevents .map() crash
    });

    const result = await service.syncUserDataToSentinel({ mail: '<EMAIL>' });

    expect(result).toEqual({
      type: 'error',
      msg: 'Invalid Request',
    });
  });

  it('should handle case when user_social_data is not an array', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: '123' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 101 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 101, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      user_social_data: 'invalid', // 👈 not an array
      roles: ['role1'],
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });

  it('should update existing social data if type already exists', async () => {
    const existingUser = {
      user_social_data: [
        { type: 'linkedin', sub: 'abc', email: '<EMAIL>', source: 'ln', status: 'old', created_on: 'then' },
      ],
    };

    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(existingUser);
    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: 'u128' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 123 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 123, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      roles: ['role1'],
      user_social_data: [
        { type: 'linkedin', sub: 'xyz', email_address: '<EMAIL>', source: 'ln', status: 'updated', created_on: 'now' },
      ],
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });


  it('should handle undefined user_social_data without errors', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: 'id123' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 222 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 222, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      pass: 'password123',
      roles: ['role1'],
      user_social_data: undefined, // ⛳️ target branch
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });

  it('should handle non-array user_social_data in existing user gracefully', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue({
      email: '<EMAIL>',
      user_social_data: [], // ✅ use [] instead of null to avoid .map error
    });

    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: '999' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 333 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 333, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      pass: 'password123',
      roles: ['role1'],
      user_social_data: [
        { type: 'github', sub: 'abc', email_address: '<EMAIL>', source: 'gh', status: 'active', created_on: 'now' },
      ],
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });
  it('should handle non-array user_social_data gracefully', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: 'u124' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 998 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 998, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      roles: ['role1'],
      user_social_data: 'not-an-array', // 👈 malformed
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });

  it('should skip password hashing if pass is undefined', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: 'u123' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 999 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 999, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      uid: 1,
      name: 'Test User',
      mail: '<EMAIL>',
      roles: ['role1'], // No `pass` provided
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });

  it('should handle null user_social_data in existing user', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue({
      user_social_data: [], // ✅ replace `null` with empty array to avoid `.map` crash
    });

    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: 'u125' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 997 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 997, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      pass: 'secure',
      roles: ['role1'],
      user_social_data: [
        { type: 'github', sub: '123', email_address: '<EMAIL>', source: 'gh', status: 'active', created_on: 'now' },
      ],
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });

  it('should return error if email exists and no new social data provided', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue({
      email: '<EMAIL>',
      user_social_data: [],
    });

    const result = await service.syncUserDataToSentinel({ mail: '<EMAIL>' });

    expect(result).toEqual({
      type: 'error',
      msg: 'Invalid Request',
    });
  });

  it('should handle empty roles from prepareAssignRoleList', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
    mockUserMgmtHelper.prepareAssignRoleList.mockResolvedValue([]);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: 'x123' });
    mockUserHelper.createCloud6SentinelUser.mockResolvedValue({ uid: 1001 });
    mockUserMgmtHelper.prepareSentinelUserRoleList.mockResolvedValue([{ uid: 1001, rid: 1 }]);
    mockUserHelper.syncCloud6SentinelUserRole.mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });

  it('should handle input with empty user_social_data array', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
    mockUserMgmtHelper.prepareAssignRoleList.mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: 'x123' });
    mockUserHelper.createCloud6SentinelUser.mockResolvedValue({ uid: 123 });
    mockUserMgmtHelper.prepareSentinelUserRoleList.mockResolvedValue([{ uid: 123, rid: 1 }]);
    mockUserHelper.syncCloud6SentinelUserRole.mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      roles: ['role1'],
      user_social_data: [],
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });


  it('should retain access and login when not "0"', async () => {
    const upsertSpy = jest.fn().mockResolvedValue({ email: '<EMAIL>', _id: 'x123' });
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
    mockUserRepository.upsert = upsertSpy;
    mockUserMgmtHelper.prepareAssignRoleList.mockResolvedValue(['role1']);
    mockUserHelper.createCloud6SentinelUser.mockResolvedValue({ uid: 999 });
    mockUserMgmtHelper.prepareSentinelUserRoleList.mockResolvedValue([{ uid: 999, rid: 1 }]);
    mockUserHelper.syncCloud6SentinelUserRole.mockResolvedValue(true);

    await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      access: '1234',
      login: '5678',
      roles: ['role1'],
    });

    const userData = upsertSpy.mock.calls[0][0];
    expect(userData.access).toBe('1234');
    expect(userData.login).toBe('5678');
  });

  it('should not include access and login if set to "0"', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    const upsertSpy = jest.fn().mockResolvedValue({ email: '<EMAIL>', _id: 'u126' });
    mockUserRepository.upsert = upsertSpy;

    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 996 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 996, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      access: '0',
      login: '0',
      roles: ['role1'],
    });

    const calledWith = upsertSpy.mock.calls[0][0];
    expect(calledWith.access).toBe('');
    expect(calledWith.login).toBe('');
  });

  it('should handle missing uid in Sentinel user creation and delete from Mongo', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: 'u127' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({}); // 👈 no uid
    (mockUserRepository.deleteOneById as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      roles: ['role1'],
    });

    expect(mockUserRepository.deleteOneById).toHaveBeenCalledWith('u127');
    expect(result).toEqual({
      type: 'error',
      msg: 'Failed to sync with Sentinel database',
    });
  });


  it('should handle empty user_groups', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: 'abc123' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 101 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 101, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      pass: 'pwd',
      roles: ['role1'],
      user_groups: [], // ⛳️ include empty array explicitly
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });


  it('should update existing social data if type exists', async () => {
    const existingData = [{ type: 'github', email: '<EMAIL>' }];
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue({
      email: '<EMAIL>',
      user_social_data: [...existingData],
    });

    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: '123' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 111 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 111, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      pass: 'password123',
      roles: ['role1'],
      user_social_data: [
        { type: 'github', sub: 'new', email_address: '<EMAIL>', source: 'gh', status: 'active', created_on: 'today' },
      ],
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });

  it('should handle null user_social_data by returning error', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue({
      email: '<EMAIL>',
      user_social_data: [], // 👈 cause .map crash
    });

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      user_social_data: [
        { type: 'github', sub: 'abc', email_address: '<EMAIL>' }
      ],
    });

    expect(result).toEqual({
      type: 'error',
      msg: 'Invalid Request',
    });
  });
  it('should skip processing if userSocialData is empty', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue({
      email: '<EMAIL>',
      user_social_data: [{ type: 'github', email: '<EMAIL>' }],
    });

    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: 'abc' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 321 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 321, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      roles: ['role1'],
      user_social_data: [], // 👈 nothing to merge/update
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });
  it('should replace empty existing social data with new', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue({
      email: '<EMAIL>',
      user_social_data: [],
    });

    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: 'xyz' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 777 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 777, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      roles: ['role1'],
      user_social_data: [
        { type: 'linkedin', sub: 'xyz', email_address: '<EMAIL>' }
      ],
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });

  it('should set access and login to empty string if value is "0"', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: 'id-000' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 555 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 555, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      uid: 1,
      mail: '<EMAIL>',
      access: '0',
      login: '0',
      roles: ['role1'],
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });

  it('should return default error response if email exists and user_social_data type matches', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue({
      email: '<EMAIL>',
      user_social_data: [{ type: 'github', email: '<EMAIL>' }],
    });

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      user_social_data: [{ type: 'github', email_address: '<EMAIL>' }],
    });

    expect(result).toEqual({
      type: 'error',
      msg: 'Invalid Request',
    });
  });

  it('should return default error if email exists and user_social_data type not matches', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue({
      email: '<EMAIL>',
      user_social_data: [{ type: 'linkedin', email: '<EMAIL>' }],
    });

    const result = await service.syncUserDataToSentinel({
      mail: '<EMAIL>',
      user_social_data: [{ type: 'github', email_address: '<EMAIL>' }],
    });

    expect(result).toEqual({
      type: 'error',
      msg: 'Invalid Request',
    });
  });

  it('should sync user without password field and optional fields', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: '101' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 1001 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 1001, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      uid: 1001,
      mail: '<EMAIL>',
      roles: ['role1'],
      field_display_name: 'Test User',
      user_groups: [],
      access: 0,
      login: 0,
      signature: '',
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });
  it('should create userData with all optional fields set', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['admin']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: 'full101' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue({ uid: 1001 });
    (mockUserMgmtHelper.prepareSentinelUserRoleList as jest.Mock).mockResolvedValue([{ uid: 1001, rid: 1 }]);
    (mockUserHelper.syncCloud6SentinelUserRole as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      uid: 1001,
      name: 'John Full',
      mail: '<EMAIL>',
      pass: 'fullpass',
      field_display_name: 'Johnny',
      field_phone_no: '**********',
      field_country_code: '+91',
      field_city_id: '110',
      field_accept_agreement: 1,
      field_user_options: '2',
      language: 'en',
      status: '1',
      field_account_setup: '1',
      timezone: 'Asia/Kolkata',
      field_password_created: '**********',
      created: '**********',
      signature: 'SIGNED',
      access: '**********',
      login: '**********',
      field_profile_visibility: 'public',
      field_newsletter: '1',
      field_user_category: 'enterprise',
      user_groups: ['group1'],
      roles: ['admin'],
    });

    expect(result).toEqual({
      type: 'success',
      msg: 'User Sync successful with Sentinel',
    });
  });

  it('should delete user and return failure message if Sentinel sync fails', async () => {
    (mockUserRepository.getUserByEmail as jest.Mock).mockResolvedValue(null);
    (mockUserMgmtHelper.prepareAssignRoleList as jest.Mock).mockResolvedValue(['role1']);
    (mockUserRepository.upsert as jest.Mock).mockResolvedValue({ email: '<EMAIL>', _id: 'fail-101' });
    (mockUserHelper.createCloud6SentinelUser as jest.Mock).mockResolvedValue(null);
    (mockUserRepository.deleteOneById as jest.Mock).mockResolvedValue(true);

    const result = await service.syncUserDataToSentinel({
      uid: 1001,
      mail: '<EMAIL>',
      pass: 'pass',
      roles: ['role1'],
    });

    expect(mockUserRepository.deleteOneById).toHaveBeenCalledWith('fail-101');
    expect(result).toEqual({
      type: 'error',
      msg: 'Failed to sync with Sentinel database',
    });
  });

  it('should catch unexpected errors and log them', async () => {
    const error = new Error('Unexpected');
    const loggerSpy = jest.spyOn(Logger, 'error').mockReturnValue({} as any);
    (mockUserRepository.getUserByEmail as jest.Mock).mockRejectedValue(error);

    const input = { mail: '<EMAIL>' };
    const result = await service.syncUserDataToSentinel(input);

    expect(loggerSpy).toHaveBeenCalledWith('syncUserDataToSentinel', expect.objectContaining({
      METHOD: 'UserDataSyncService@syncUserDataToSentinel',
      MESSAGE: 'Unexpected',
      REQUEST: input,
      RESPONSE: expect.any(String),
    }));
    expect(result).toBeInstanceOf(Error);
  });
});
