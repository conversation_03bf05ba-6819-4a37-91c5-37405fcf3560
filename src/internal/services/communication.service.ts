import { Logger } from "../../logging/logger";
import { HelperService } from '../../helper/helper.service';
import { BadRequestException, Inject } from "@nestjs/common";
import { IUserRepository, UserRepository } from '../../user/repositories/user/user.repository';
import { Utility } from "../../common/util/utility";
export class CommunicationService {
  @Inject(HelperService) private readonly helperService: HelperService;
  
  async getDisplayNamesByIds(reqData): Promise<any> {
    try {
        const userRepository = await this.helperService.getHelper<IUserRepository>(UserRepository);

        if (reqData?.ids?.length === 0) {
            throw new BadRequestException('InvalidPayload');
        }
        // Validate and sanitize IDs
        const sanitizedIds = Array.isArray(reqData?.ids)
            ? reqData.ids.filter(id => Number.isInteger(Number(id))).map(Number)
            : [];
        // Check if sanitized IDs are empty when reqData.ids contains invalid values
        if (!sanitizedIds.length) {
            throw new BadRequestException();
        }

        const users = await userRepository.find({ uid: { $in: sanitizedIds } }) || [];
        if (!users.length) {
            throw new BadRequestException();
        }

        return users.map(user => ({
            uid: user.uid,
            displayName: user.display_name,
        }));
    } catch (error: any) {
        Logger.log('getDisplayNamesByIds', {
            METHOD: `${this.constructor.name}@${this.getDisplayNamesByIds.name}`,
            MESSAGE: error.message,
            REQUEST: reqData,
            RESPONSE: error.stack,
            TIMESTAMP: Date.now()
        });
        throw error;
    }
  }
  async getUserIdByEmail(reqData): Promise<{uid: number}> {
    try {
        const userRepository = await this.helperService.getHelper<IUserRepository>(UserRepository);

        if (!Utility.isValidEmail(reqData?.email)) {
            throw new BadRequestException('InvalidEmail');
        }

        const user = await userRepository.findOne({ email: reqData.email });
        if (!user) {
            throw new BadRequestException('UserNotFound');
        }

        return { uid: user.uid };
    } catch (error: any) {
        Logger.log('getUserIdByEmail', {
            METHOD: `${this.constructor.name}@${this.getUserIdByEmail.name}`,
            MESSAGE: error.message,
            REQUEST: reqData,
            RESPONSE: error.stack,
            TIMESTAMP: Date.now()
        });
        throw error;
    }
  }
}