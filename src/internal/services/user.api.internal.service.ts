import { Injectable, Inject, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Logger } from '../../logging/logger';
import { HelperService } from '../../helper/helper.service';
import { IUserRepository, UserRepository } from '../../user/repositories/user/user.repository';
import { IRoleRepository, RoleRepository } from '../../user/repositories/role/role.repository';
import { UpdateUsernameVisibilityDto } from '../dto/update-username-visibility.dto';
import { UpdateUserRoleDto } from '../dto/update-user-role.dto';
import { CryptoHelper } from '../../helper/helper.crypto';
import { AssignUserRoleDto } from '../dto/assign-user-role.dto';
import { UpdateUserProfileDto } from '../dto/update-user-profile.dto';
import { User } from '../../db/mongo/schema/user/user.schema';
import { Utility } from '../../common/util/utility';
import { UpdateSkillUpUserDTO } from '../dto/update-skillip-user.dto';
import { AuthTokenHelper } from '../../auth/helper/auth.tokenhelper';
import { GetTokenByEmailDto } from '../dto/get-token-by-email.dto';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as drupalHash from 'drupal-hash';
import { CachingService } from '../../caching/caching.service';
import { ResetPassDto } from '../dto/reset-password.dto';

import { GdprRequestDto } from '../dto/gdpr-request.dto';
import { SamlService } from '../../saml/services/saml.service';
import { AddMemberToGroupWithRoleDto } from '../dto/add-member-to-group-with-role.dto';
import { SentinelSyncUserUpdateDto } from '../dto/sentinel-synch-user-update.dto';
import { deactivateUserByEmailDto } from '../dto/deactivate-user-byemail.dto';


@Injectable()
export class UserApiInternalService {
  @Inject() configService: ConfigService;
  @Inject(HelperService) private readonly helperService: HelperService;
  @Inject('CRYPTO_HELPER') private readonly cryptoHelper: CryptoHelper;
  @Inject(EventEmitter2) private readonly eventEmitter: EventEmitter2; 

  async assignUserRole(params: AssignUserRoleDto) {
    try {
      const response = { type: 'error', msg: 'Invalid Request.' };
      Utility.validateClientRequest(params?.client_id, this.configService.get('clientSecret'));

      const [userMgmtHelper, userHelper, userRepository, roleRepository] = await Promise.all([
        this.helperService.getHelper('UserMgmtUtilityHelper'),
        this.helperService.getHelper('UserHelper'),
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.get<IRoleRepository>(RoleRepository),
      ]);

      const assignRoles = await userMgmtHelper.prepareAssignRoleList(params?.user_roles, null, true);

      if (assignRoles.length === 0) {
        response.msg = 'Please provide valid user role name.';
        return response;
      }
      //load users by uid
      const user = await userRepository.findByUID(params?.user_id);
      const roles = await roleRepository.findAll({ _id: { $in: assignRoles} });
      
      //Role mapping to update in sentinel_users_roles
      const rIdsMapping = roles.map(role => ({
        uid: user?.uid,
        rid: role.rid
      }));

      //load users
      if (user || user.status == 1) {
        //updating both Mongoo and MySQL
        const [updatedUser, updateSentinelUsers] = await Promise.all([
          userRepository.findOneAndUpdate({ uid: user?.uid }, { roles: assignRoles }),
          userHelper.syncCloud6SentinelUserRole(rIdsMapping)
        ]);

        if (updatedUser && updateSentinelUsers) {
          if (this.configService.get('enableDrupalSync')) {
            //role mapping to update drupal sync
            const roleMapping = await userHelper.prepareRoleListForDrupalSync(roles);
            await userHelper.syncUserDataWithMySQLDrupal({ uid: user?.uid, roles: roleMapping });
          }
          response.type = 'success';
          response.msg = 'Successfully assigned role to user.';
        } else {
          response.msg = 'Some error occurred while assigning role to user.';
        }
      }
      return response;
    } catch (error: any) {
      Logger.error('assignUserRole', {
        METHOD: this.constructor.name + '@' + this.assignUserRole.name,
        MESSAGE: error.message,
        REQUEST: params,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return error;
    }
  }
  async checkAccountSetup(userEmail: string): Promise<object | Error> {
    try {
      let response = { type: 'success', flag: 1, data: {} };
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const userInfo = await userRepository.getUserByEmail(userEmail);
      if (!userInfo || !userInfo.status) {
        throw new BadRequestException('UserNotFoundException');
      }
      const accountSetupStatus = this.configService.get<number>('userOptionsAccountSetupPending');
      if (userInfo?.account_setup === accountSetupStatus) {
        response = {
          type: 'success',
          flag: userInfo?.account_setup,
          data: {
            email: userInfo?.email,
            name: userInfo?.display_name,
            countryCode: userInfo?.country_code,
            phone: userInfo?.phone_no,
          },
        };
       }else {
        response.data = [];
      }
      return response;
    }  catch (error: any) {
      Logger.error('checkAccountSetup', {
        METHOD: this.constructor.name + '@' + this.checkAccountSetup.name,
        MESSAGE: error.message,
        REQUEST: { user_email: userEmail },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return error;
    }
  }
  async getProfileCompletionStats(requestBody: { client_id: string; user_id: number }) {
    const response = { type: 'error', msg: 'Invalid request.' };
    Utility.validateClientRequest(requestBody?.client_id, this.configService.get('clientSecret'));
    if (!requestBody?.user_id) {
      return response;
    }

    const [profileHelper, userRepository] = await Promise.all([
      this.helperService.getHelper('ProfileHelper'),
      this.helperService.get<IUserRepository>(UserRepository)
    ]);

    const userInfo: Partial<User> = await userRepository.findByUID(requestBody?.user_id);
    if (!userInfo || !userInfo?.status) {
      return response;
    }

    const profileCompletion = await profileHelper.getProfileCompletionStats(userInfo, true, 'Yes', false);
    if (profileCompletion && profileCompletion?.overallCompletion) {
      return { 
        type: 'success', 
        profileCompletion: profileCompletion?.overallCompletion || 0,
        unit: '%' 
      };
    }
  }

  async updateUsernameVisibility(reqObj: UpdateUsernameVisibilityDto): Promise<object | Error> {
    try {
      const profileVisibilitySettings = this.configService.get<number>('profileVisibilitySettings');
      let response = { type: 'error', msg: 'Invalid Request.' };

      Utility.validateClientRequest(reqObj?.client_id, this.configService.get('clientSecret'));
      
      if (!reqObj?.username && !profileVisibilitySettings[reqObj.visibility]) {
        response.msg = 'Please provide valid username and visibility.';
        return response;
      }

      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const userHelper = await this.helperService.getHelper('UserHelper');

      const userInfo = await userRepository.findByUID(reqObj.user_id);

      if (userInfo && userInfo.status === 1){
        const usernameExists = await userRepository.findOne({ name: reqObj.username });
        if (usernameExists && usernameExists?.uid != reqObj.user_id) {
          response.msg = 'Username already exists.';
          return response;
        }
        
        const result = await userRepository.findOneAndUpdate(
        { uid: reqObj?.user_id },
        { name: reqObj.username, profile_visibility: profileVisibilitySettings[reqObj.visibility] },
        );
        
        const updateSentinelUsers =  await userHelper.updateCloud6SentinelByUidOrMail(
          {uid: reqObj?.user_id,name: reqObj?.username, profile_visibility: profileVisibilitySettings[reqObj.visibility]}
        );

        if(this.configService.get('enableDrupalSync')) {
         await userHelper.syncUserDataWithMySQLDrupal({uid: userInfo?.uid, profile_visibility: profileVisibilitySettings[reqObj.visibility]}); //update the user drupal data
        }

        if (result && updateSentinelUsers) {         
          response = { type: 'success', msg: 'Successfully updated user profile.' };
        } else {
          response.msg = 'Error occurred while updating profile. Please try again.';
        }

      } else {
        response.msg = 'User account does not exists or is blocked.';
        // // throw new BadRequestException('UserNotFoundException');
         return response;
      }

      return response;
    } catch (error: any) {
      Logger.error('updateUsernameVisibility', {
        METHOD: this.constructor.name + '@' + this.updateUsernameVisibility.name,
        MESSAGE: error.message,
        REQUEST: reqObj,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return error;
    }
  }

  async deactivateUserStatus(requestBody : deactivateUserByEmailDto) {
    try {
      await Utility.validateClientRequest
      ( requestBody?.client_id,
        this.configService.get('clientSecret'),
        requestBody?.redirect_url || ""
      );
      const userHelper = await this.helperService.getHelper('UserHelper');
      return userHelper.deactivateUserStatus(requestBody);
    } catch (error: any) {
      Logger.error('deactivateUserStatus', {
        METHOD: this.constructor.name + '@' + this.deactivateUserStatus.name,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async   updateEngagexRole(requestBody: UpdateUserRoleDto): Promise<object | Error> {
    try {
      Utility.validateClientRequest(requestBody?.client_id, this.configService.get('clientSecret'));
      const userApiV1Helper = await this.helperService.getHelper('UserApiV1Helper');
      const updateRoleResponse = await userApiV1Helper.updateEngagexRole(
        requestBody?.user_id,
        requestBody?.updated_role,
      );
      const authHelper = await this.helperService.getHelper('AuthHelper');
      const token = await authHelper.getTokenByEmail(updateRoleResponse?.email, updateRoleResponse?.client_id);
      const userHelper = await this.helperService.getHelper('UserHelper');
      await userHelper.updateUserLoginTime(updateRoleResponse);
      return token;
    } catch (error: any) {
      Logger.error('updateEngagexRole', {
        METHOD: this.constructor.name + '@' + this.updateEngagexRole.name,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
     return error;
    }
  }

  async encryptDecryptJwtPayload(requestBody) {
    try {
      const payload = {
        id: this.cryptoHelper.encryptDecrypt(requestBody?.type, requestBody?.id),
        uname: this.cryptoHelper.encryptDecrypt(requestBody?.type, requestBody?.uname),
        email: this.cryptoHelper.encryptDecrypt(requestBody?.type, requestBody?.email),
        gid: this.cryptoHelper.encryptDecrypt(requestBody?.type, requestBody?.gid),
      };
      return { status: 200, message: 'Success.', data: payload };
    } catch (error: any) {
      Logger.log('encryptDecryptJwtPayload', {
        METHOD: this.constructor.name + '@' + this.encryptDecryptJwtPayload.name,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
    }
  }

  async getTokenByEmail(reqData: GetTokenByEmailDto) {
    try {
      Utility.validateClientRequest(reqData?.client_id, this.configService.get('clientSecret'), reqData?.redirect_url || "");
      const authHelper = await this.helperService.getHelper('AuthHelper');
      const token = await authHelper.getTokenByEmail(reqData?.user_email, reqData?.client_id , { actor_email: reqData?.actor_email || "" });
      return token;
    } catch (error: any) {
      Logger.log('getTokenByEmail', {
        METHOD: this.constructor.name + '@' + this.getTokenByEmail.name,
        MESSAGE: error.message,
        REQUEST: reqData,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async userProfileData(params: { uid: number }) {
    try {
      const [profileHelper, userRepository] = await Promise.all([
        this.helperService.getHelper('ProfileHelper'),
        this.helperService.get<IUserRepository>(UserRepository)
      ]);
      const user = await userRepository.findByUID(params?.uid);
      if (!user) {
        return {};
      }
      return await profileHelper.fetchProfileData(params?.uid, 1);
    } catch (error: any) {
      Logger.log('userProfileData', {
        METHOD: this.constructor.name + '@' + this.userProfileData.name,
        MESSAGE: error.message,
        REQUEST: params,
        RESPONSE: error?.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async updateUserProfileData(data: Partial<UpdateUserProfileDto>) {
    let response = {type: "error", msg: 'Error occurred while updating profile. Please try again.'};
    try {
      Utility.validateClientRequest(data?.client_id, this.configService.get('clientSecret'));
      const userApiV1Helper = await this.helperService.getHelper('UserApiV1Helper');
      //This is for onboarding step
      if(data?.step){
        response = await userApiV1Helper.updateUserOnboardingData(data);
      } else {
        throw new BadRequestException("Step not provided.")
      }
      return response;
    } catch (error: any) {
      Logger.error('updateUserProfileData', {
        METHOD: this.constructor.name + '@' + this.updateUserProfileData.name,
        MESSAGE: error.message,
        REQUEST: data,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      if (error instanceof UnauthorizedException){
        return {type: 'error', msg: error.message};
      }
      return response;
    }
  }
  /**
   * Service method to add member to group with role
   * @param params
   * @returns
   */
  async addMemberToGroupWithRole(params: AddMemberToGroupWithRoleDto) {
    try {
      // Get required services and repositories through helper service
      const roleRepository = await this.helperService.get<IRoleRepository>(RoleRepository);
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const userHelper = await this.helperService.getHelper('UserHelper');
      const samlService = await this.helperService.get<SamlService>(SamlService);

      // Validate roles exist
      const allRoles = await roleRepository.findAll({ rid: { $in: params.rids } });
      if (!allRoles || allRoles.length !== params.rids.length) {
        return {
          type: 'error',
          msg: 'Please provide valid role IDs.'
        };
      }

      // Find user by email
      const userData = await userRepository.getUserByEmail(params.email);
      
      if (!userData) {
        return {
          type: 'error',
          msg: 'User account does not exist for the specified email-id.'
        };
      }

      // Check if user is active
      if (userData.status !== 1) {
        return {
          type: 'error',
          msg: 'User account does not exist or is blocked.'
        };
      }

      const userGroups = userData?.user_groups || [];
      const newUserGroups = [...new Set([...params.gids, ...userGroups])];
      const newRoles = allRoles.map((role) => role['_id']);

      // Prepare updates
      const userObj: Partial<User> = {
        email: params.email,
        roles: newRoles,
        user_groups: newUserGroups,
      };

      // Update MongoDB and MySQL in parallel
      const [mongoUpdate] = await Promise.all([
        userRepository.upsert(userObj),
        userHelper.updateCloud6SentinelByUidOrMail({
          mail: params.email,
          uid: userData.uid
        }),
        userHelper.syncCloud6SentinelUserRole(
          allRoles.map(role => ({
            uid: userData.uid,
            rid: role.rid
          }))
        )
      ]);

      if (!mongoUpdate) {
        return {
          type: 'error',
          msg: 'Some error occurred while assigning role to user.'
        };
      } else {
        if (this.configService.get('enableDrupalSync')) {
          // Update user data in MySQL Drupal
          const roleMapping = await userHelper.prepareRoleListForDrupalSync(allRoles);
          await userHelper.syncUserDataWithMySQLDrupal({
            uid: userData.uid,
            roles: roleMapping
          });
        }
      }

      // Handle SSO attributes if present
      if (userData.sso_attributes) {
        const ssoAttributes = typeof userData.sso_attributes === 'string'
          ? JSON.parse(userData.sso_attributes)
          : userData.sso_attributes;

        const saveSsoResult = await samlService.saveSsoAttributes(
          params.gids[0],
          params.email,
          ssoAttributes
        );

        if (!saveSsoResult) {
          Logger.log('Failed to save SSO attributes', {
            METHOD: this.constructor.name + '@' + this.addMemberToGroupWithRole.name,
            MESSAGE: 'Failed to save SSO attributes',
            REQUEST: { gid: params.gids[0], email: params.email },
            TIMESTAMP: new Date().getTime(),
          });
        }
      }

      // Create role mapping in exact Cloud6 format
      const roleData = {
        // Add the new roles
        ...Object.fromEntries(allRoles.map(role => [role.rid, role.roleName.toLowerCase()]))
      };

      return {
        type: 'success',
        data: roleData,
        msg: 'Successfully added member to group and assigned user role.'
      };

    } catch (error: any) {
      Logger.log('addMemberToGroupWithRole', {
        METHOD: this.constructor.name + '@' + this.addMemberToGroupWithRole.name,
        MESSAGE: error.message,
        REQUEST: params,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return {
        type: 'error',
        msg: 'Some error occurred while assigning role to user.'
      };
    }
  }

  async updateSkillupRecommendationInProfile(reqData) {
    try {
      const response = { type: 'error', msg: 'Invalid Request.' };
      const [userRepository, profileHelper, userHelper] = await Promise.all([
        this.helperService.getHelper<IUserRepository>(UserRepository),
        this.helperService.getHelper('ProfileHelper'),
        this.helperService.getHelper('UserHelper'),
      ]);
      const user = await userRepository.findByUID(reqData?.user_id);
      if (!user) {
        return response;
      }

      const [userCareer, userObjective] = await Promise.all([
        profileHelper.getOneTaxonomy(reqData?.profession),
        profileHelper.getOneTaxonomy(reqData?.goal),
      ]);

      //Checking if the object ID are related to the fields
      if(userCareer?.category !== 'where_are_you_in_your_career' || userObjective.category !== 'objective_of_taking_course'){
        return response;
      }

      //verifying if the user has already set the values
      let updateObj = { objective_taking_course: '', where_are_you_in_career: '' };
      if (Utility.isEmpty(user.objective_taking_course) || Utility.isEmpty(user.where_are_you_in_career)) {
        if (Utility.isEmpty(user.objective_taking_course)) {
          updateObj.objective_taking_course = userObjective?._id;
        }
        
        if (Utility.isEmpty(user.where_are_you_in_career)) {
          updateObj.where_are_you_in_career = userCareer?._id;
        }
      } else {
        response.type = 'success';
        response.msg = 'Profile data already exists';
        return response;
      }
      
      //Updating the user data in the database in both mongoo and mysql
      const [updatedUser, updateSentinelUsers] = await Promise.all([
        userRepository.findOneAndUpdate({ uid: reqData.user_id }, updateObj),
        userHelper.updateCloud6SentinelByUidOrMail({
          uid: reqData.user_id,
          objective_taking_course: userObjective?.tid,
          where_are_you_in_career: userCareer?.tid,
        }),
      ]);

      if (updatedUser && updateSentinelUsers) {
        response.type = 'success';
        response.msg = 'Profile data updated';
       } else {
        response.msg = 'some error will updating profile data';
      }

      return response;
    } catch (error: any) {
      Logger.log('updateSkillupRecommendationInProfile', {
        METHOD: `${this.constructor.name}@${this.updateSkillupRecommendationInProfile.name}`,
        MESSAGE: error?.message,
        REQUEST: reqData,
        RESPONSE: error?.stack,
        TIMESTAMP: Date.now(),
      });
      // Handle the error appropriately
    }
  }

  async updateSkillupUserProfileData(reqData: UpdateSkillUpUserDTO) {
    try {
      let result = {"type": "error", "msg": "Invalid Request."};

      Utility.validateClientRequest(reqData?.client_id, this.configService.get('clientSecret'));

      const [profileHelper, userHelper, userRepository] = await Promise.all([
        this.helperService.getHelper('ProfileHelper'),
        this.helperService.getHelper('UserHelper'),
        this.helperService.getHelper<IUserRepository>(UserRepository),
      ]);

      const validationRes = Utility.doEmptyCheck(reqData, [
        'user_id',
        'phone',
        'your_career',
        'objective',
        'first_name',
      ]);
      if (!validationRes?.status) {
        return validationRes;
      }
      const user: Partial<User> = await userRepository.findByUID(reqData?.user_id);

      if (user?.status != 0){
        let varUserObj: Partial<User> = {
          first_name: reqData?.first_name,
          last_name: reqData?.last_name,
          phone_no: reqData?.phone,
          gender: reqData?.gender,
          objective_taking_course: reqData?.objective,
          where_are_you_in_career: reqData?.your_career,
          country_code: reqData?.country_code,
        };
        if (reqData.profile_picture && !Utility.isEmpty(reqData?.profile_picture) && reqData?.profile_picture !== null) {
          const uploadedProfilePic = await profileHelper.uploadProfilePic(reqData?.profile_picture, reqData?.user_id);
          if (uploadedProfilePic.status == 'active' ) {
           varUserObj = 
           {...varUserObj,
              profile_pic:uploadedProfilePic.filename
           }
          }
        }

        const [objectiveTakingCourse, whereAreYouInCareer] = await Promise.all([  
          profileHelper.getOneTaxonomy(reqData?.objective),
          profileHelper.getOneTaxonomy(reqData?.your_career),
        ])
        const mysqlVarUserObj =  {...varUserObj};
        mysqlVarUserObj['uid'] = Number(reqData?.user_id);
        mysqlVarUserObj['objective_taking_course'] = String(objectiveTakingCourse?.tid);
        mysqlVarUserObj['where_are_you_in_career'] = String(whereAreYouInCareer?.tid);
        
        //updating the user data into both sentinelUsers and users table
        const [updatedUser, updateSentinelUsers] = await Promise.all([
          userRepository.findOneAndUpdate({ uid: reqData?.user_id }, varUserObj),
          userHelper.updateCloud6SentinelByUidOrMail(mysqlVarUserObj),
        ]);

        if(!updatedUser || !updateSentinelUsers){
          result.msg = 'Error occurred while updating profile. Please try again.';
        } else {
          result.type = 'success';
          result.msg = 'Successfully updated skillup user profile.'
        }
      } else {
        result.msg = 'User account does not exists or is blocked.';
      }
     return result;
    } catch (error: any) {
      Logger.log('updateSkillupUserProfileData', {
        METHOD: `${this.constructor.name}@${this.updateSkillupUserProfileData.name}`,
        MESSAGE: error?.message,
        REQUEST: reqData,
        RESPONSE: error?.stack,
        TIMESTAMP: Date.now(),
      });
      if(error instanceof UnauthorizedException){
        return { type: 'error', msg: error.message };
      }
      return { type: 'error', msg: 'Error occurred while updating user profile.' }; // Handle the error appropriately
    }
  }
  async verifyAuthTokenByEmail(userData: { user_email: string; client_id: string, authToken: string }) {
    const response = { type: 'error', msg: '', data: '' };
    if (
      !userData?.client_id ||
      typeof userData.client_id !== 'string' ||
      !userData?.user_email ||
      !userData?.authToken
    ) {
      return response;
    }
    try {
      const secretKey = this.configService.get('clientSecret');
      Utility.validateClientRequest(userData?.client_id, secretKey);
      const authTokenHelper = await this.helperService.getHelper<AuthTokenHelper>(AuthTokenHelper);
      const tokenPayloadData = {
        email: userData?.user_email,
      };
      const tokenResponse = await authTokenHelper.getSignHash(
        JSON.stringify(tokenPayloadData),
        secretKey[userData.client_id].secret,
      );
      if (tokenResponse && tokenResponse === userData.authToken) {
        response.type = 'success';
        response.msg = 'success';
        response.data = tokenResponse;
      }
      return response;
    } catch (error: any) {
      Logger.log('verifyAuthTokenByEmail', {
        METHOD: `${this.constructor.name}@${this.verifyAuthTokenByEmail.name}`,
        MESSAGE: error?.message,
        REQUEST: userData,
        RESPONSE: error?.stack,
        TIMESTAMP: Date.now(),
      });
      return response; // Handle the error appropriately
    }
  }

  async getDataByEmailOrUid(identifier: { email?: string; uid?: number }) {
    try {
      const response = { type: 'error', msg: 'Invalid Request.', data: null };

      if (!identifier?.email && !identifier?.uid) {
        response.msg = 'Please provide either email or uid.';
        return response;
      }

      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      let userData: Partial<User> | null = null;

      if (identifier?.email) {
        userData = await userRepository.getUserByEmail(identifier.email);
      } else if (identifier?.uid) {
        userData = await userRepository.findByUID(identifier.uid);
      }

      if (userData) {
        response.type = 'success';
        response.msg = 'User data retrieved successfully.';
        response.data = userData;
      } else {
        response.msg = 'User not found.';
      }

      return response;
    } catch (error: any) {
      Logger.error('getDataByEmailOrUid', {
        METHOD: this.constructor.name + '@' + this.getDataByEmailOrUid.name,
        MESSAGE: error.message,
        REQUEST: identifier,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { type: 'error', msg: 'An error occurred while retrieving user data.', data: null };
    }
  }

  async deactivateUserForGdpr(userEmail: string): Promise<any> {
    try {
      // Get User directly from repository
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user = await userRepository.getUserByEmail(userEmail);
      
      if (!user) {
        return { status: 'failed', msg: `User not found with email. ${userEmail}` };
      }

      // Get UserApiV1Helper for deactivation
      const userApiV1Helper = await this.helperService.getHelper('UserApiV1Helper');
      
      // Pass the entire user object instead of just the UID
      const deactivateResponse = await userApiV1Helper.deleteAccountByUidForGDPR(user);
      
      // If user is already deactivated, return that response
      if (deactivateResponse.status === 'failed' && 
          deactivateResponse.msg === 'User already Deactivated') {
        return deactivateResponse;
      }

      // If deactivation was successful, then create GDPR log
      if (deactivateResponse.status === 'success') {
        const gdprRequestHelper = await this.helperService.getHelper('GdprRequestHelper');
        const gdprRequestId = await gdprRequestHelper.addRequest(Number(user.uid), 'delete');
        if (!gdprRequestId) {
          Logger.error('deactivateUserForGdpr', {
            METHOD: 'UserApiService@deactivateUserForGdpr',
            MESSAGE: 'Failed to create GDPR request log',
            REQUEST: { uid: user.uid },
            TIMESTAMP: new Date().getTime(),
          });
          return { status: 'failed', msg: 'Failed to create GDPR request log' };
        }
      }

      return deactivateResponse;
    } catch (error: any) {
      Logger.error('deactivateUserForGdpr', {
        METHOD: 'UserApiService@deactivateUserForGdpr',
        MESSAGE: error.message,
        REQUEST: { userEmail },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { status: 'failed', msg: 'Error deactivating user' };
    }
  }
  
    async registerUserFrs(userParams) { 
      const params = {
        type: "frsImmersive",
        source: 'web',
        first_name: userParams?.firstName,
        last_name: userParams?.lastName,
        email: userParams?.emailAddress,
        phone_no: userParams?.phone_no,
        country_code: userParams?.country_code,
        user_email: userParams?.emailAddress
      };
      try{
      const socialLoginHelper = await this.helperService.getHelper('SocialLoginHelper');
      const response = await socialLoginHelper.createUserAccount(params);
      
      if (response?.type == 'success') {
        const user: Partial<User> = response?.data;
        const refRewardData = { user_id: user?.uid };
        const [paperclipService, authTokenHelper] = await Promise.all([
          this.helperService.get<PaperclipService>(PaperclipService),
          this.helperService.get<AuthTokenHelper>(AuthTokenHelper),
        ]);
        const [rewardData, userTokenDetail] = await Promise.all([
          paperclipService.getSkillupReferralRewardInfo(refRewardData),
          authTokenHelper.generateSessionTokens(user, this.configService.get('clientKey')),
        ]);
        const refCode = rewardData?.respData?.skillUpReferralInfo?.userRefCode || '';
     
        if (userTokenDetail?.idToken) {
          return {
            type: response.type,
            message: response.msg,
            msg: "User is registered",
            status: true,
            refCode: refCode,
            
          };
          
        } 
      } 
      else {
      throw new Error(response.msg)
      }
    
    } catch (error: any){
      Logger.error('registerUserFrs', {
        METHOD: this.constructor.name + '@' + this.registerUserFrs.name,
        MESSAGE: error.message,
        REQUEST: userParams,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return {
        type: 'error',
        message: error?.message,
        msg: 'Something went wrong',
      };
    }
  }

  async frsCompleteSignup(requestBody, cookieBody){
    try{
    const data ={
      email: requestBody.email?.trim(),
      clientId: requestBody.client_id?.trim(),
      authType: 'Signup'
    }
    const recommended_courses = '';
    const frsRedirectUrl = decodeURIComponent(cookieBody['simplilearn_first_page']);
    let eventName = 'Complete Signup';
    eventName = eventName.replace(' ', '_');
    const eventData: any = {
      type: 'frsImmersive',
      sl_utm_src: cookieBody['sl_su_utmz'],
      method: 'one tap',
      source: 'frs',
      utm_source: 'frs',
      branch_utm_source: 'frs',
      recommended_skillup_courses: recommended_courses,
      url: frsRedirectUrl,
    };
    this.eventEmitter.emit('user.complete_signup_frs', {
      email: data?.email.trim(),
      clientId: data?.clientId.trim(),
      gid: 2, 
      payload: eventData,
      eventName: eventName,
    });
    const userHelper = await this.helperService.getHelper('UserHelper');
    await userHelper.sendGA4Events(
     data,
      cookieBody,
      frsRedirectUrl,
      recommended_courses,
      'free' 
    )
   
  } 
  catch(error: any){
    Logger.error('frsCompleteSignup', {
      METHOD: this.constructor.name + '@' + this.frsCompleteSignup.name,
      MESSAGE: error.message,
      REQUEST: requestBody,
      RESPONSE: error.stack,
      TIMESTAMP: new Date().getTime(),
    });
  }
}

  /**
   * Update GDPR Delete Status
   * @param requestBody - Contains email and response data
   * @returns Response indicating success or failure of the update
   */
  async updateGdprDeleteStatus(requestBody: { email: string; response_data: string }): Promise<Object | Error> {
    try {
      let response = { status: 'success', msg: 'Successfully updated result data.' };
      const userRepository = await this.helperService.get<IUserRepository>(UserRepository);
      const user = await userRepository.getUserByEmail(requestBody.email);

      if (!user) {
        throw new BadRequestException('UserNotFoundException');
      }

      // Get UserApiV1Helper to handle the status update
      if (user.status !== 0) {
        throw new BadRequestException('UserAlreadyBlocked');
        }

      const userApiV1Helper = await this.helperService.getHelper('UserApiV1Helper');
      const updateResult = await userApiV1Helper.updateGdprDeleteStatus(user, requestBody.response_data);

        if(updateResult){
          return response;
        } else {
          throw new BadRequestException('ErrorOccured');
        }
    } catch (error: any) {
      Logger.error('updateGdprDeleteStatus', {
        METHOD: `${this.constructor.name}@${this.updateGdprDeleteStatus.name}`,
        MESSAGE: error.message,
        REQUEST: requestBody,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      throw error;
    }
  }

  async gdprAction(requestBody: GdprRequestDto): Promise<object> {
    try {
      const userApiV1Helper = await this.helperService.getHelper('UserApiV1Helper');
      
      // Get the appropriate helper based on action
      const funcName = `gdpr${requestBody.action.charAt(0).toUpperCase() + requestBody.action.slice(1)}`;
      
      if (!(funcName in userApiV1Helper)) {
        throw new Error('MethodNotExists');
      }

      // Call the appropriate GDPR action method
      const actionResult = await userApiV1Helper[funcName](requestBody);
      
      if (!actionResult.status) {
        throw new Error(actionResult.message);
      }

      return actionResult;
    } catch (error: any) {
      throw error;
    }
  }


  /**
   * Synchronize user updates with Sentinel
   * @param userId - User ID to synchronize
   * @param updateData - Data to update in Sentinel
   * @returns Response indicating success or failure of the synchronization
   */
  async sentinelSyncUserUpdate(updateData: SentinelSyncUserUpdateDto): Promise<object> {
    try {
      // Validate input
      
      if (updateData && updateData.user_id) {
         return this.updateSentinelUsers(updateData);
        } else{
          return this.createSentinelUsers(updateData);
        }
    } catch (error: any) {
      Logger.error('sentinelSynchUserUpdate', {
        METHOD: `${this.constructor.name}@${this.sentinelSyncUserUpdate.name}`,
        MESSAGE: error.message,
        REQUEST: {  updateData },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { type: 'error', msg: 'An error occurred during synchronization.' };
    }
  }

  async updateSentinelUsers(updateData: SentinelSyncUserUpdateDto) {
    try {
      // Validate input
      if (!updateData || !updateData.user_id) {
        return { type: 'error', msg: 'Invalid input: user_id is required.' };
      }
  
      // Fetch required services
      const [userRepository, userMgmtHelper, userHelper] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository).catch(() => null),
        this.helperService.getHelper('UserMgmtUtilityHelper').catch(() => null),
        this.helperService.getHelper('UserHelper').catch(() => null),
      ]);
  
      if (!userRepository || !userMgmtHelper || !userHelper) {
        return { type: 'error', msg: 'Failed to fetch required services.' };
      }
  
      // Fetch user data
      const user = await userRepository.findByUID(updateData.user_id.toString());
      if (!user) {
        return { type: 'error', msg: 'User not found.' };
      }
  
      // Prepare update fields
      let assignRoles = [];
      if (updateData?.user_roles) {
        assignRoles = updateData?.user_roles
          ? await userMgmtHelper.prepareAssignRoleList(updateData.user_roles, null, true)
          : [];
      }

      // If display_name is not provided, construct it from first_name and last_name
      let displayName = updateData?.display_name;
      if ((!displayName || displayName == null) && (updateData?.first_name || updateData?.last_name)) {
        displayName = `${updateData?.first_name || ''} ${updateData?.last_name || ''}`;
      }
      const currentTime = new Date();

      const updateFields: Partial<User> = {
        ...(updateData?.user_name && { name: updateData.user_name }),
        ...(updateData?.user_email && { email: updateData.user_email }),
        ...(displayName && { display_name: displayName }),
        ...(updateData?.timezone && { timezone: updateData.timezone }),
        ...(updateData?.first_name && { first_name: updateData.first_name }),
        ...(updateData?.pass && {password: drupalHash.hashPassword(updateData.pass)}),
        ...(updateData?.last_name && { last_name: updateData.last_name }),
        ...(updateData?.country_code && { country_code: updateData.country_code }),
        ...(updateData?.phone_no && { phone_no: updateData.phone_no }),
        ...(updateData?.gid && { user_groups: [...new Set([updateData.gid, ...user.user_groups])] }),
        ...(updateData?.user_roles && { roles: [...new Set([...assignRoles, ...user.roles])] }),
        ...(updateData?.profile_visibility && {profile_visibility: updateData.profile_visibility}),
        ...(updateData?.status && {status: updateData.status}),
        ...(updateData?.linkedin_status && {linkedin_status: updateData.linkedin_status}),
        ...(updateData?.login && { login: currentTime }),
        ...(updateData?.access && { access: currentTime }),
      };

  
      // Update user in MongoDB
      const updatedUser = await userRepository.findOneAndUpdate({ uid: updateData.user_id }, updateFields);
      if (!updatedUser) {
        return { type: 'error', msg: 'Failed to update user data in MongoDB.' };
      }
  
      // Synchronize roles with Sentinel
      if (updateData?.user_roles?.length > 0) {
        const sentinelRoles = await userMgmtHelper.prepareSentinelUserRoleList(updateData.user_roles, updateData.user_id);
        await userHelper.syncCloud6SentinelUserRole(sentinelRoles);
      }
    
      // Synchronize user data with Sentinel
      const syncResult = await userHelper.updateCloud6SentinelByUidOrMail({
        uid: updateData.user_id,
        mail: updateData.user_email,
        ...( displayName && { display_name: displayName }),
        ...(updateData?.timezone && { timezone: updateData.timezone }),
        ...(updateData?.first_name && { first_name: updateData.first_name }),
        ...(updateData?.last_name && { last_name: updateData.last_name }),
        ...(updateData?.country_code && { country_code: updateData.country_code }),
        ...(updateData?.phone_no && { phone_no: updateData.phone_no }),
        ...(updateData?.pass && { pass: drupalHash.hashPassword(updateData.pass)}),
        ...(updateData?.profile_visibility && {profile_visibility: updateData.profile_visibility}),
        ...(updateData?.status && {status: updateData.status}),
        ...(updateData?.linkedin_status && {linkedin_status: updateData.linkedin_status}),
        ...(updateData?.login && { login: currentTime.getDate() }),
        ...(updateData?.access && { access: currentTime.getDate() }),
      });

      if (!syncResult) {
        return { type: 'error', msg: 'Failed to synchronize user data with Sentinel.' };
      }
  
      return { type: 'success', msg: 'User data synchronized successfully.' };
    } catch (error: any) {
      Logger.error('Error in updateSentinelUsers', {
        method: `${this.constructor.name}@updateSentinelUsers`,
        message: error.message,
        timestamp: new Date().toISOString(),
      });
      return { type: 'error', msg: 'An error occurred during synchronization.' };
    }
  }

  async createSentinelUsers(updateData: SentinelSyncUserUpdateDto) {
    try{
      const response = { type: 'error', msg: 'Failed to create user.' };
      console.log(updateData);
      // Validate input
      if (!updateData?.user_email || !updateData?.user_name) {
        response.msg = 'Invalid input data.';
        return response;
      }
     const userHelper = await this.helperService.getHelper('UserHelper');
      // Prepare user parameters
      const userParams = {
        client_id: updateData?.client_id || this.configService.get('clientId'),
        user_email: updateData.user_email,
        user_name: updateData.user_name,
        user_roles: updateData.user_roles,
        user_type: updateData.user_type,
        send_email: updateData.send_email,
        overwrite: updateData.overwrite,
        phone_no: updateData.phone_no,
        country_code: updateData.country_code,
        email_block: updateData.email_block,
        ...(updateData?.gid && { gid: updateData.gid }) 
      };
      // Call the helper method to register the user
     return  userHelper.registerByEmail(userParams);

    }catch(error:any){
      Logger.error('createSentinelUsers', {
        METHOD: `${this.constructor.name}@${this.createSentinelUsers.name}`,
        MESSAGE: error.message,
        REQUEST: {  updateData },
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });
      return { type: 'error', msg: 'An error occurred during synchronization.' };
    }
  }

  /**
 * Resets the password for a user account.
 * Validates client ID, user ID, current password, and new password format.
 * If the password reset is successful, the user will be logged out from all web devices.
 */
  async resetPassword(resetPwd: ResetPassDto): Promise<any> {
    try {
      const [
        userRepository,
        cacheService,
        userHelper
      ] = await Promise.all([
        this.helperService.get<IUserRepository>(UserRepository),
        this.helperService.get<CachingService>(CachingService),
        this.helperService.getHelper('UserHelper'),
      ]);
    
      let response = { type: 'error', msg: 'Some error occurred' };
      // 1. Validate client key
      Utility.validateClientRequest(resetPwd.client_id, this.configService.get('clientSecret'));
      const userId = resetPwd.user_id
  
      // 2. Validate user ID/email
      if (!userId || isNaN(Number(userId))) {
        throw new BadRequestException('InvalidUserParameter');
      }
  
      // 3. Validate new password
      const passwordResult = Utility.validatePasswd(resetPwd.new_password, this.configService.get('passwordLength'),
      this.configService.get('maxPasswordLength'),);
      if (passwordResult?.type?.toLowerCase() === 'error') {
        throw new BadRequestException(passwordResult.msg);
      }
  
      // 4. Fetch user
      const userObj = await userRepository.findOne({ uid: userId });
      if (!userObj) {
        throw new BadRequestException('UserNotFoundException');
      }
  
      // 5. User account status check
      if (userObj.status !== 1) {
        throw new BadRequestException('UserAccountBlocked');
      }
  
     // 6. Check if old password is correct
       if (drupalHash.checkPassword(resetPwd.current_password, userObj.password) === false) {
        throw new BadRequestException('NotAuthorizedException')
       }
  
      // 7. Hash new password
      const hashPassword = drupalHash.hashPassword(resetPwd.new_password);
  
      // 8. Save new password
      const user = await userRepository.findOneAndUpdate({ uid: userId }, { user_options: 1, password: hashPassword });
      const passStatus = await userHelper.updateCloud6SentinelByUidOrMail({uid: userId , pass: hashPassword, user_options: 1});
  
      if (passStatus && user ) {
        // 9. Invalidate session cache
        const cacheKey = this.configService.get('sessionCache') + userObj.uid;
        await cacheService.set(cacheKey, null, 0);
      
        // 10. Return success
        return {
          type: 'success',
          msg: 'Password has been reset successfully. You will be logged out from all web devices. Kindly login again'
        };
      }
  
      return response;
    } catch (error: any) {
      Logger.error('resetPassword', {
        METHOD: this.constructor.name + '@resetPassword',
        MESSAGE: error.message,
        REQUEST: resetPwd,
        RESPONSE: error.stack,
        TIMESTAMP: new Date().getTime(),
      });

      return {
        type: 'error',
        msg: error?.message || 'Some error occurred',
      };
    }
  }
  
 
}