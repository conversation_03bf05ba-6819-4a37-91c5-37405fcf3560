import { CommunicationService } from './communication.service';
import { BadRequestException } from '@nestjs/common';
import { Utility } from '../../common/util/utility';
import { HelperService } from '../../helper/helper.service';
import { IUserRepository } from '../../user/repositories/user/user.repository';
import { UserApiV1Helper } from '../helper/user-api-v1.helper';

jest.mock('../../common/util/utility');

describe('CommunicationService', () => {
  let service: CommunicationService;
  let mockHelperService: Partial<HelperService>;
  let mockUserRepository: Partial<IUserRepository>;
  let mockGroupHelper: Partial<UserApiV1Helper>;

  beforeEach(() => {
    mockUserRepository = {
      find: jest.fn(),
      findOne: jest.fn(),
    };

    mockHelperService = {
      getHelper: jest.fn().mockImplementation(async (type: string) => {
        return type === 'UserApiV1Helper' ? mockGroupHelper : mockUserRepository;
      }),
    };

    service = new CommunicationService();
    // @ts-ignore
    service['helperService'] = mockHelperService as HelperService;
  });

  // getDisplayNamesByIds
  describe('getDisplayNamesByIds', () => {
    it('should return display names for valid IDs', async () => {
      const reqData = { ids: [1001, 1002] };
      const users = [
        { uid: 1001, display_name: 'John Doe' },
        { uid: 1002, display_name: 'Jane Doe' },
      ];

      (mockUserRepository.find as jest.Mock).mockResolvedValue(users);

      const result = await service.getDisplayNamesByIds(reqData);

      expect(result).toEqual([
        { uid: 1001, displayName: 'John Doe' },
        { uid: 1002, displayName: 'Jane Doe' },
      ]);
    });

    it('should throw BadRequestException with "InvalidPayload" for empty ID array', async () => {
      const reqData = { ids: [] };

      await expect(service.getDisplayNamesByIds(reqData)).rejects.toThrow(BadRequestException);
      await expect(service.getDisplayNamesByIds(reqData)).rejects.toThrow('InvalidPayload');
    });

    it('should throw BadRequestException for invalid (non-integer) IDs', async () => {
      const reqData = { ids: ['abc', null] };

      await expect(service.getDisplayNamesByIds(reqData)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if no users found', async () => {
      const reqData = { ids: [123] };
      (mockUserRepository.find as jest.Mock).mockResolvedValue([]);

      await expect(service.getDisplayNamesByIds(reqData)).rejects.toThrow(BadRequestException);
    });

    it('should throw and log error if repository call fails', async () => {
      const reqData = { ids: [9999] };
      const error = new Error('DB error');

      (mockUserRepository.find as jest.Mock).mockRejectedValue(error);

      await expect(service.getDisplayNamesByIds(reqData)).rejects.toThrow('DB error');
    });

    it('should throw BadRequestException if sanitizedIds is empty due to all invalid input', async () => {
      const reqData = { ids: ['not-a-number', 'NaN'] };

      await expect(service.getDisplayNamesByIds(reqData)).rejects.toThrow(BadRequestException);
    });
  });

  // getUserIdByEmail
  describe('getUserIdByEmail', () => {
    it('should return uid if user is found by email', async () => {
      const reqData = { email: '<EMAIL>' };
      const user = { uid: 123, email: '<EMAIL>' };

      (Utility.isValidEmail as jest.Mock).mockReturnValue(true);
      (mockUserRepository.findOne as jest.Mock).mockResolvedValue(user);

      const result = await service.getUserIdByEmail(reqData);
      expect(result).toEqual({ uid: 123 });
    });

    it('should throw BadRequestException for invalid email format', async () => {
      const reqData = { email: 'invalid-email' };

      (Utility.isValidEmail as jest.Mock).mockReturnValue(false);

      await expect(service.getUserIdByEmail(reqData)).rejects.toThrow(BadRequestException);
      await expect(service.getUserIdByEmail(reqData)).rejects.toThrow('InvalidEmail');
    });

    it('should throw BadRequestException if user not found', async () => {
      const reqData = { email: '<EMAIL>' };

      (Utility.isValidEmail as jest.Mock).mockReturnValue(true);
      (mockUserRepository.findOne as jest.Mock).mockResolvedValue(null);

      await expect(service.getUserIdByEmail(reqData)).rejects.toThrow(BadRequestException);
      await expect(service.getUserIdByEmail(reqData)).rejects.toThrow('UserNotFound');
    });

    it('should throw and log error if repository call fails', async () => {
      const reqData = { email: '<EMAIL>' };
      const error = new Error('DB error');

      (Utility.isValidEmail as jest.Mock).mockReturnValue(true);
      (mockUserRepository.findOne as jest.Mock).mockRejectedValue(error);

      await expect(service.getUserIdByEmail(reqData)).rejects.toThrow('DB error');
    });
  });
});
