import { Test, TestingModule } from '@nestjs/testing';
import { UserApiInternalService } from '../services/user.api.internal.service';
import { ConfigService } from '@nestjs/config';
import { HelperService } from '../../helper/helper.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Utility } from '../../common/util/utility';
import { UserRepository } from '../../user/repositories/user/user.repository';
import {SentinelSyncUserUpdateDto} from '../dto/sentinel-synch-user-update.dto';
import {GdprRequestDto} from '../dto/gdpr-request.dto'
import * as drupalHash from 'drupal-hash';
import { CachingService } from '../../caching/caching.service';
import { GetTokenByEmailDto } from '../dto/get-token-by-email.dto';
import { AuthTokenHelper } from '../../auth/helper/auth.tokenhelper';
import { PaperclipService } from '../../common/services/communication/paperclip/paperclip.service';

describe('UserApiInternalService', () => {
  let service: UserApiInternalService;
  let helperService: any;
  let configService: any;

  const mockAssignRoles = ['abc123'];

  const mockUserMgmtHelper = {
    prepareAssignRoleList: jest.fn().mockResolvedValue(mockAssignRoles),
  };

  const mockUserHelper = {
    prepareRoleListForDrupalSync: jest.fn().mockResolvedValue([{ rid: 1 }]),
    syncCloud6SentinelUserRole: jest.fn().mockResolvedValue(true),
    syncUserDataWithMySQLDrupal: jest.fn().mockResolvedValue(true),
    updateCloud6SentinelByUidOrMail: jest.fn().mockResolvedValue(true),
  };

  const mockUserRepository = {
    findByUID: jest.fn().mockResolvedValue({ uid: 1, status: 1 }),
    findOne: jest.fn().mockResolvedValue(null), // Simulate no existing username
    findOneAndUpdate: jest.fn().mockResolvedValue(true),
  };

  const mockRoleRepository = {
    findAll: jest.fn().mockResolvedValue([{ rid: 1, _id: 'abc123' }]),
  };

  const mockEventEmitter = {
    emit: jest.fn(),
  };

  beforeEach(async () => {
    helperService = {
      getHelper: jest.fn(),
      get: jest.fn(),
    };

    configService = {
      get: jest.fn().mockReturnValue(true), // for enableDrupalSync and clientSecret
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserApiInternalService,
        { provide: ConfigService, useValue: configService },
        { provide: HelperService, useValue: helperService },
        { provide: EventEmitter2, useValue: mockEventEmitter },
        { provide: 'CRYPTO_HELPER', useValue: {} }, // mock if needed
      ],
    }).compile();

    service = module.get<UserApiInternalService>(UserApiInternalService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('updateUserProfileData', () => {
    const reqData: any = {
      client_id: 'client123',
      step: 1,
    };

    const mockUserApiV1Helper = {
      updateUserOnboardingData: jest.fn().mockResolvedValue({
        type: 'success',
        msg: 'Updated successfully',
      }),
    };

    beforeEach(() => {
      Utility.validateClientRequest = jest.fn();
      helperService.getHelper = jest.fn();
    });

    it('should return success when onboarding data update succeeds', async () => {
      (helperService.getHelper as jest.Mock).mockResolvedValue(mockUserApiV1Helper);

      const result = await service.updateUserProfileData(reqData);

      expect(helperService.getHelper).toHaveBeenCalledWith('UserApiV1Helper');
      expect(mockUserApiV1Helper.updateUserOnboardingData).toHaveBeenCalledWith(reqData);
      expect(result).toEqual({
        type: 'success',
        msg: 'Updated successfully',
      });
    });

    it('should return error response if step is missing', async () => {
      const reqDataWithoutStep = { client_id: 'client123' };

      const result = await service.updateUserProfileData(reqDataWithoutStep);

      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while updating profile. Please try again.',
      });
    });

    it('should handle UnauthorizedException and return formatted error', async () => {
      const reqDataUnauthorized = { client_id: 'client123', step: 1 };

      Utility.validateClientRequest = jest.fn(); // Should succeed
      const mockHelper = {
        updateUserOnboardingData: jest.fn().mockRejectedValue(
          new UnauthorizedException('Unauthorized access request.'),
        ),
      };
      (helperService.getHelper as jest.Mock).mockResolvedValue(mockHelper);

      const result = await service.updateUserProfileData(reqDataUnauthorized);

      expect(result).toEqual({
        type: 'error',
        msg: 'Unauthorized access request.',
      });
    });

    it('should handle generic error and return default error message', async () => {
      const reqDataGenericError = { client_id: 'client123', step: 1 };

      Utility.validateClientRequest = jest.fn(); // Should succeed
      const mockHelper = {
        updateUserOnboardingData: jest.fn().mockRejectedValue(new Error('something went wrong')),
      };
      (helperService.getHelper as jest.Mock).mockResolvedValue(mockHelper);

      const result = await service.updateUserProfileData(reqDataGenericError);

      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while updating profile. Please try again.',
      });
    });
  });


  describe('assignUserRole', () => {
    const params = { client_id: 'valid', user_roles: ['admin'], user_id: 1 };

    it('should return success on valid role assignment', async () => {
      helperService.getHelper
        .mockResolvedValueOnce(mockUserMgmtHelper) // UserMgmtUtilityHelper
        .mockResolvedValueOnce(mockUserHelper);    // UserHelper

      helperService.get
        .mockResolvedValueOnce(mockUserRepository) // UserRepository
        .mockResolvedValueOnce(mockRoleRepository); // RoleRepository

      const result: any = await service.assignUserRole(params);

      expect(result).toEqual({
        type: 'success',
        msg: 'Successfully assigned role to user.',
      });

      expect(mockUserMgmtHelper.prepareAssignRoleList)
        .toHaveBeenCalledWith(params.user_roles, null, true);
      expect(mockUserRepository.findByUID).toHaveBeenCalledWith(1);
      expect(mockRoleRepository.findAll).toHaveBeenCalledWith({ _id: { $in: mockAssignRoles } });
      expect(mockUserHelper.syncCloud6SentinelUserRole).toHaveBeenCalled();
      expect(mockUserHelper.syncUserDataWithMySQLDrupal).toHaveBeenCalled();
    });

    it('should return error when no valid roles provided', async () => {
      helperService.getHelper.mockResolvedValueOnce({
        prepareAssignRoleList: jest.fn().mockResolvedValue([]),
      });

      const result: any = await service.assignUserRole(params);

      expect(result.type).toBe('error');
      expect(result.msg).toBe('Please provide valid user role name.');
    });

    it('should return error when update fails', async () => {
      helperService.getHelper
        .mockResolvedValueOnce(mockUserMgmtHelper)
        .mockResolvedValueOnce(mockUserHelper);

      helperService.get
        .mockResolvedValueOnce(mockUserRepository)
        .mockResolvedValueOnce(mockRoleRepository);

      mockUserRepository.findOneAndUpdate.mockResolvedValueOnce(false);

      const result: any = await service.assignUserRole(params);

      expect(result.type).toBe('error');
      expect(result.msg).toBe('Some error occurred while assigning role to user.');
    });

    it('should return caught error object if exception occurs', async () => {
      helperService.getHelper.mockRejectedValue(new Error('Mock error'));

      const result = await service.assignUserRole(params);
      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Mock error');
    });
  });

  describe('checkAccountSetup', () => {
    it('should return setup data if account_setup matches', async () => {
      const mockUser = {
        status: 1,
        account_setup: 2,
        email: '<EMAIL>',
        display_name: 'Test User',
        country_code: 'IN',
        phone_no: '**********',
      };

      configService.get = jest.fn().mockReturnValue(2);
      helperService.get = jest.fn().mockResolvedValue({
        getUserByEmail: jest.fn().mockResolvedValue(mockUser),
      });

      const res = await service.checkAccountSetup('<EMAIL>');
      expect((res as any).data.email).toBe('<EMAIL>');
    });

    it('should return BadRequestException for no user', async () => {
      helperService.get.mockResolvedValue({
        getUserByEmail: jest.fn().mockResolvedValue(null),
      });

      const result = await service.checkAccountSetup('x');

      expect(result).toBeInstanceOf(BadRequestException);

      if (result instanceof BadRequestException) {
        expect(result.message).toBe('UserNotFoundException');
      }
    });

    it('should return success with empty data if account_setup does not match', async () => {
      configService.get = jest.fn().mockReturnValue(3);
      helperService.get = jest.fn().mockResolvedValue({
        getUserByEmail: jest.fn().mockResolvedValue({
          status: 1,
          account_setup: 1,
        }),
      });

      const res = await service.checkAccountSetup('x');
      expect((res as any).data).toEqual([]);
    });
  });

  describe('deactivateUserStatus', () => {
    const mockRequestBody = {
      client_id: 'client123',
      redirect_url: 'http://example.com',
      user_email: '<EMAIL>',
    };

    it('should call deactivateUserStatus on UserHelper and return its result', async () => {
      configService.get = jest.fn()
        .mockReturnValueOnce('secret-key'); // for clientSecret
      const mockDeactivateResult = { type: 'success', msg: 'User deactivated' };

      const mockUserHelper = {
        deactivateUserStatus: jest.fn().mockResolvedValue(mockDeactivateResult),
      };

      helperService.getHelper = jest.fn().mockResolvedValue(mockUserHelper);

      const result = await service.deactivateUserStatus(mockRequestBody);

      expect(helperService.getHelper).toHaveBeenCalledWith('UserHelper');
      expect(mockUserHelper.deactivateUserStatus).toHaveBeenCalledWith(mockRequestBody);
      expect(result).toEqual(mockDeactivateResult);
    });

    it('should throw an error if UserHelper fails', async () => {
      configService.get = jest.fn().mockReturnValue('secret-key');

      const mockUserHelper = {
        deactivateUserStatus: jest.fn().mockRejectedValue(new Error('Helper failed')),
      };

      helperService.getHelper = jest.fn().mockResolvedValue(mockUserHelper);

      await expect(service.deactivateUserStatus(mockRequestBody)).rejects.toThrow('Helper failed');
    });

    it('should throw error if client validation fails', async () => {
      // You can mock validateClientRequest to throw error if you've wrapped it.
      const spyValidateClientRequest = jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => {
        throw new BadRequestException('Invalid client');
      });

      await expect(service.deactivateUserStatus(mockRequestBody)).rejects.toThrow(BadRequestException);

      spyValidateClientRequest.mockRestore();
    });
  });

  describe('updateUsernameVisibility', () => {
    const reqObj = {
      client_id: 'valid',
      user_id: 1,
      username: 'newname',
      visibility: 1,
    };

    it('should update username and visibility successfully', async () => {
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(undefined);
      configService.get = jest.fn().mockReturnValue({ 0: 'simplilearn', 1: 'public' });

      // Set up the mocks
      mockUserRepository.findByUID.mockResolvedValue({ uid: 1, status: 1 });
      mockUserRepository.findOne.mockResolvedValue(null); // Username doesn't exist
      mockUserRepository.findOneAndUpdate.mockResolvedValue(true);

      mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);
      mockUserHelper.syncUserDataWithMySQLDrupal.mockResolvedValue(true);

      helperService.get = jest.fn().mockImplementation((target) => {
        if (target === UserRepository) return Promise.resolve(mockUserRepository);
        return Promise.resolve(undefined);
      });
      // Return mocks in correct order as expected by service method
      helperService.getHelper.mockResolvedValueOnce(mockUserHelper);    // UserHelper

      const result = await service.updateUsernameVisibility(reqObj);
      expect(result).toEqual({
        type: 'success',
        msg: 'Successfully updated user profile.',
      });
    });

    it('should return error if username already exists', async () => {
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(undefined);
      configService.get = jest.fn().mockReturnValue({ 0: 'simplilearn', 1: 'public' });

      mockUserRepository.findByUID.mockResolvedValue({ uid: 1, status: 1 });
      mockUserRepository.findOne.mockResolvedValue({ uid: 2 }); // simulate clash

      helperService.get = jest.fn().mockImplementation((target) => {
        if (target === UserRepository) return Promise.resolve(mockUserRepository);
        return Promise.resolve(undefined);
      });
      // Return mocks in correct order as expected by service method
      helperService.getHelper.mockResolvedValueOnce(mockUserHelper);    // UserHelper

      const result = await service.updateUsernameVisibility(reqObj);
      expect(result).toEqual({
        type: 'error',
        msg: 'Username already exists.',
      });
    });

    it('should return error if user is inactive', async () => {
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(undefined);
      configService.get = jest.fn().mockReturnValue({ 0: 'simplilearn', 1: 'public' });

      mockUserRepository.findByUID.mockResolvedValue({ uid: 1, status: 0 });

      helperService.get = jest.fn().mockImplementation((target) => {
        if (target === UserRepository) return Promise.resolve(mockUserRepository);
        return Promise.resolve(undefined);
      });
      // Return mocks in correct order as expected by service method
      helperService.getHelper.mockResolvedValueOnce(mockUserHelper);    // UserHelper

      const result = await service.updateUsernameVisibility(reqObj);
      expect(result).toEqual({
        type: 'error',
        msg: 'User account does not exists or is blocked.',
      });
    });

    it('should return error if invalid username or visibility', async () => {
      jest.spyOn(Utility, 'validateClientRequest').mockReturnValue(undefined);
      configService.get = jest.fn().mockReturnValue({ 0: 'simplilearn', 1: 'public' });

      const invalidReq = { ...reqObj, username: '', visibility: 3 };

      const result = await service.updateUsernameVisibility(invalidReq);
      expect(result).toEqual({
        type: 'error',
        msg: 'Please provide valid username and visibility.',
      });
    });

    it('should return BadRequestException on client validation failure', async () => {
      jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => {
        throw new BadRequestException('bad');
      });

      const result = await service.updateUsernameVisibility(reqObj);
      expect(result).toBeInstanceOf(BadRequestException);
      expect((result as any).message).toBe('bad');
    });
  });

  describe('updateSkillupRecommendationInProfile', () => {
    it('should update profile successfully when user fields are empty', async () => {
      const reqData = { user_id: 1, profession: 'prof1', goal: 'goal1' };

      const mockUser = {
        objective_taking_course: '',
        where_are_you_in_career: '',
        status: 1
      };

      const mockTaxonomyCareer = { _id: 'careerId', tid: '10', category: 'where_are_you_in_your_career' };
      const mockTaxonomyObjective = { _id: 'objectiveId', tid: '20', category: 'objective_of_taking_course' };

      const mockUserRepo = {
        findByUID: jest.fn().mockResolvedValue(mockUser),
        findOneAndUpdate: jest.fn().mockResolvedValue(true),
      };
      const mockProfileHelper = {
        getOneTaxonomy: jest
          .fn()
          .mockResolvedValueOnce(mockTaxonomyCareer)
          .mockResolvedValueOnce(mockTaxonomyObjective),
      };
      const mockUserHelper = {
        updateCloud6SentinelByUidOrMail: jest.fn().mockResolvedValue(true),
      };

      helperService.getHelper = jest.fn()
        .mockResolvedValueOnce(mockUserRepo)
        .mockResolvedValueOnce(mockProfileHelper)
        .mockResolvedValueOnce(mockUserHelper);

      const result = await service.updateSkillupRecommendationInProfile(reqData);
      expect(result).toEqual({ type: 'success', msg: 'Profile data updated' });
    });

    it('should return already exists if user fields are not empty', async () => {
      const reqData = { user_id: 1, profession: 'prof1', goal: 'goal1' };

      const mockUser = {
        objective_taking_course: 'exists',
        where_are_you_in_career: 'exists'
      };

      const mockUserRepo = {
        findByUID: jest.fn().mockResolvedValue(mockUser),
      };
      const mockProfileHelper = {
        getOneTaxonomy: jest.fn(),
      };
      const mockUserHelper = {};

      helperService.getHelper = jest.fn()
        .mockResolvedValueOnce(mockUserRepo)
        .mockResolvedValueOnce(mockProfileHelper)
        .mockResolvedValueOnce(mockUserHelper);

      const result = await service.updateSkillupRecommendationInProfile(reqData);
      expect(result).toEqual({ type: 'error', msg: 'Invalid Request.' });
    });

    it('should return error if user not found', async () => {
      const reqData = { user_id: 1, profession: 'prof1', goal: 'goal1' };

      const mockUserRepo = {
        findByUID: jest.fn().mockResolvedValue(null),
      };

      helperService.getHelper = jest.fn()
        .mockResolvedValueOnce(mockUserRepo);

      const result = await service.updateSkillupRecommendationInProfile(reqData);
      expect(result).toEqual({ type: 'error', msg: 'Invalid Request.' });
    });

    it('should return error if taxonomy categories mismatch', async () => {
      const reqData = { user_id: 1, profession: 'prof1', goal: 'goal1' };

      const mockUser = {
        objective_taking_course: '',
        where_are_you_in_career: ''
      };

      const mockTaxonomyCareer = { _id: 'careerId', tid: '10', category: 'wrong_category' };
      const mockTaxonomyObjective = { _id: 'objectiveId', tid: '20', category: 'wrong_category' };

      const mockUserRepo = {
        findByUID: jest.fn().mockResolvedValue(mockUser),
      };
      const mockProfileHelper = {
        getOneTaxonomy: jest
          .fn()
          .mockResolvedValueOnce(mockTaxonomyCareer)
          .mockResolvedValueOnce(mockTaxonomyObjective),
      };

      helperService.getHelper = jest.fn()
        .mockResolvedValueOnce(mockUserRepo)
        .mockResolvedValueOnce(mockProfileHelper);

      const result = await service.updateSkillupRecommendationInProfile(reqData);
      expect(result).toEqual({ type: 'error', msg: 'Invalid Request.' });
    });

    it('should handle exception gracefully', async () => {
      const reqData = { user_id: 1, profession: 'prof1', goal: 'goal1' };

      helperService.getHelper = jest.fn().mockRejectedValue(new Error('fail'));

      const result = await service.updateSkillupRecommendationInProfile(reqData);
      expect(result).toBeUndefined(); // Since function has no explicit return on error, only logs
    });
  });

  describe('updateSkillupUserProfileData', () => {
    const reqData: any = {
      client_id: 'client123',
      user_id: 1,
      phone: '**********',
      your_career: 'career1',
      objective: 'objective1',
      first_name: 'Test',
      last_name: 'User',
      gender: 'M',
      country_code: 'IN',
      profile_picture: 'pic.png',
    };

    it('should update user profile successfully', async () => {
      Utility.validateClientRequest = jest.fn();
      Utility.doEmptyCheck = jest.fn().mockReturnValue({ status: true });

      const mockUser = { status: 1 };
      const mockUploadedPic = { status: 'active', filename: 'newpic.png' };
      const mockObjective = { tid: '10' };
      const mockCareer = { tid: '20' };

      const mockUserRepo = {
        findByUID: jest.fn().mockResolvedValue(mockUser),
        findOneAndUpdate: jest.fn().mockResolvedValue(true),
      };
      const mockUserHelper = {
        updateCloud6SentinelByUidOrMail: jest.fn().mockResolvedValue(true),
      };
      const mockProfileHelper = {
        uploadProfilePic: jest.fn().mockResolvedValue(mockUploadedPic),
        getOneTaxonomy: jest
          .fn()
          .mockResolvedValueOnce(mockObjective)
          .mockResolvedValueOnce(mockCareer),
      };

      helperService.getHelper = jest.fn()
        .mockResolvedValueOnce(mockProfileHelper)
        .mockResolvedValueOnce(mockUserHelper)
        .mockResolvedValueOnce(mockUserRepo);

      const result = await service.updateSkillupUserProfileData(reqData);
      expect(result).toEqual({
        type: 'success',
        msg: 'Successfully updated skillup user profile.',
      });
    });

    it('should return error if user account does not exist or is blocked', async () => {
      Utility.validateClientRequest = jest.fn();
      Utility.doEmptyCheck = jest.fn().mockReturnValue({ status: true });

      const mockUser = { status: 0 };

      const mockUserRepo = {
        findByUID: jest.fn().mockResolvedValue(mockUser),
      };
      const mockProfileHelper = {};
      const mockUserHelper = {};

      helperService.getHelper = jest.fn()
        .mockResolvedValueOnce(mockProfileHelper)
        .mockResolvedValueOnce(mockUserHelper)
        .mockResolvedValueOnce(mockUserRepo);

      const result = await service.updateSkillupUserProfileData(reqData);
      expect(result).toEqual({
        type: 'error',
        msg: 'User account does not exists or is blocked.',
      });
    });

    it('should return error when empty check fails', async () => {
      Utility.validateClientRequest = jest.fn();
      Utility.doEmptyCheck = jest.fn().mockReturnValue({ status: false, msg: 'Missing fields' });

      const result = await service.updateSkillupUserProfileData(reqData);
      expect(result).toEqual({ status: false, msg: 'Missing fields' });
    });

    it('should return error when DB update fails', async () => {
      Utility.validateClientRequest = jest.fn();
      Utility.doEmptyCheck = jest.fn().mockReturnValue({ status: true });

      const mockUser = { status: 1 };
      const mockUploadedPic = { status: 'active', filename: 'newpic.png' };
      const mockObjective = { tid: '10' };
      const mockCareer = { tid: '20' };

      const mockUserRepo = {
        findByUID: jest.fn().mockResolvedValue(mockUser),
        findOneAndUpdate: jest.fn().mockResolvedValue(null),
      };
      const mockUserHelper = {
        updateCloud6SentinelByUidOrMail: jest.fn().mockResolvedValue(false),
      };
      const mockProfileHelper = {
        uploadProfilePic: jest.fn().mockResolvedValue(mockUploadedPic),
        getOneTaxonomy: jest
          .fn()
          .mockResolvedValueOnce(mockObjective)
          .mockResolvedValueOnce(mockCareer),
      };

      helperService.getHelper = jest.fn()
        .mockResolvedValueOnce(mockProfileHelper)
        .mockResolvedValueOnce(mockUserHelper)
        .mockResolvedValueOnce(mockUserRepo);

      const result = await service.updateSkillupUserProfileData(reqData);
      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while updating profile. Please try again.',
      });
    });

    it('should handle exceptions and return generic error', async () => {
      Utility.validateClientRequest = jest.fn().mockImplementation(() => {
        throw new Error('Validation failed');
      });

      const result = await service.updateSkillupUserProfileData(reqData);
      expect(result).toEqual({
        type: 'error',
        msg: 'Error occurred while updating user profile.',
      });
    });

    it('should return unauthorized error if UnauthorizedException thrown', async () => {
      Utility.validateClientRequest = jest.fn().mockImplementation(() => {
        throw new UnauthorizedException('unauthorized');
      });

      const result = await service.updateSkillupUserProfileData(reqData);
      expect(result).toEqual({
        type: 'error',
        msg: 'unauthorized',
      });
    });
  });

  describe('updateEngagexRole', () => {
    const requestBody = {
      client_id: 'client123',
      user_id: 1,
      updated_role: 'role1',
    };

    it('should return token on successful role update', async () => {
      // Mock client validation
      Utility.validateClientRequest = jest.fn();

      // Mock helpers + their methods
      const mockUpdateRoleResponse = {
        email: '<EMAIL>',
        client_id: 'client123',
      };
      const mockToken = { idToken: 'abc123' };

      const mockUserApiV1Helper = {
        updateEngagexRole: jest.fn().mockResolvedValue(mockUpdateRoleResponse),
      };
      const mockAuthHelper = {
        getTokenByEmail: jest.fn().mockResolvedValue(mockToken),
      };
      const mockUserHelper = {
        updateUserLoginTime: jest.fn().mockResolvedValue(true),
      };

      // Chain helper mocks in the order service calls them
      helperService.getHelper = jest.fn()
        .mockResolvedValueOnce(mockUserApiV1Helper)
        .mockResolvedValueOnce(mockAuthHelper)
        .mockResolvedValueOnce(mockUserHelper);

      const result = await service.updateEngagexRole(requestBody);

      expect(result).toBe(mockToken);
      expect(mockUserApiV1Helper.updateEngagexRole).toHaveBeenCalledWith(1, 'role1');
      expect(mockAuthHelper.getTokenByEmail).toHaveBeenCalledWith('<EMAIL>', 'client123');
      expect(mockUserHelper.updateUserLoginTime).toHaveBeenCalledWith(mockUpdateRoleResponse);
    });

    it('should handle error thrown by validateClientRequest', async () => {
      // Mock validation to throw
      Utility.validateClientRequest = jest.fn(() => {
        throw new Error('Validation failed');
      });

      const result = await service.updateEngagexRole(requestBody);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Validation failed');
    });

    it('should handle error thrown anywhere in the flow', async () => {
      // Mock validation to pass
      Utility.validateClientRequest = jest.fn();

      // Mock UserApiV1Helper to throw error
      const mockUserApiV1Helper = {
        updateEngagexRole: jest.fn().mockImplementation(() => {
          throw new Error('DB error');
        }),
      };

      helperService.getHelper = jest.fn()
        .mockResolvedValueOnce(mockUserApiV1Helper);

      const result = await service.updateEngagexRole(requestBody);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('DB error');
    });
  });

  describe('getProfileCompletionStats', () => {
    const requestBody = {
      client_id: 'client123',
      user_id: 123,
    };
  
    const mockUser = { uid: 123, status: 1 };
  
    beforeEach(() => {
      jest.clearAllMocks();
      Utility.validateClientRequest = jest.fn();
    });
  
    it('should return error response if user_id is missing', async () => {
      const result = await service.getProfileCompletionStats({ client_id: 'client123', user_id: null as any });
      expect(result).toEqual({ type: 'error', msg: 'Invalid request.' });
    });
  
    it('should return error if userInfo is not found or inactive', async () => {
      const mockUserRepository = {
        findByUID: jest.fn().mockResolvedValue(null),
      };
  
      helperService.getHelper = jest.fn().mockResolvedValue({});
      helperService.get = jest.fn().mockResolvedValue(mockUserRepository);
  
      const result = await service.getProfileCompletionStats(requestBody);
      expect(result).toEqual({ type: 'error', msg: 'Invalid request.' });
    });
  
    it('should return error if user status is not 1', async () => {
      const mockUserRepository = {
        findByUID: jest.fn().mockResolvedValue({ uid: 123, status: 0 }),
      };
  
      helperService.getHelper = jest.fn().mockResolvedValue({});
      helperService.get = jest.fn().mockResolvedValue(mockUserRepository);
  
      const result = await service.getProfileCompletionStats(requestBody);
      expect(result).toEqual({ type: 'error', msg: 'Invalid request.' });
    });
  
    it('should return success if profile completion stats found', async () => {
      const mockProfileHelper = {
        getProfileCompletionStats: jest.fn().mockResolvedValue({ overallCompletion: 80 }),
      };
      const mockUserRepository = {
        findByUID: jest.fn().mockResolvedValue(mockUser),
      };
  
      helperService.getHelper = jest.fn().mockResolvedValue(mockProfileHelper);
      helperService.get = jest.fn().mockResolvedValue(mockUserRepository);
  
      const result = await service.getProfileCompletionStats(requestBody);
      expect(result).toEqual({
        type: 'success',
        profileCompletion: 80,
        unit: '%',
      });
    });
  
    it('should return nothing if no completion data found', async () => {
      const mockProfileHelper = {
        getProfileCompletionStats: jest.fn().mockResolvedValue(null),
      };
      const mockUserRepository = {
        findByUID: jest.fn().mockResolvedValue(mockUser),
      };
  
      helperService.getHelper = jest.fn().mockResolvedValue(mockProfileHelper);
      helperService.get = jest.fn().mockResolvedValue(mockUserRepository);
  
      const result = await service.getProfileCompletionStats(requestBody);
      expect(result).toBeUndefined();
    });
  

  });

  describe('userProfileData', () => {
    const uid = 123;
    const params = { uid };
    const mockUser = { uid, name: 'John Doe' };
    const mockProfileData = { uid, profile: 'mocked profile data' };
  
    beforeEach(() => {
      jest.clearAllMocks();
    });
  
    it('should return profile data for a valid user', async () => {
      const mockUserRepo = {
        findByUID: jest.fn().mockResolvedValue(mockUser),
      };
      const mockProfileHelper = {
        fetchProfileData: jest.fn().mockResolvedValue(mockProfileData),
      };
  
      helperService.get = jest.fn().mockResolvedValue(mockUserRepo);
      helperService.getHelper = jest.fn().mockResolvedValue(mockProfileHelper);
  
      const result = await service.userProfileData(params);
  
      expect(mockUserRepo.findByUID).toHaveBeenCalledWith(uid);
      expect(mockProfileHelper.fetchProfileData).toHaveBeenCalledWith(uid, 1);
      expect(result).toEqual(mockProfileData);
    });
  
    it('should return empty object if user not found', async () => {
      const mockUserRepo = {
        findByUID: jest.fn().mockResolvedValue(null),
      };
      const mockProfileHelper = {
        fetchProfileData: jest.fn(),
      };
  
      helperService.get = jest.fn().mockResolvedValue(mockUserRepo);
      helperService.getHelper = jest.fn().mockResolvedValue(mockProfileHelper);
  
      const result = await service.userProfileData(params);
      expect(result).toEqual({});
      expect(mockProfileHelper.fetchProfileData).not.toHaveBeenCalled();
    });
  

  });
  



describe('createSentinelUsers', () => {
  const mockUserParams = {
    user_email: '<EMAIL>',
    user_name: 'Test User',
    user_roles: ['admin'],
    user_type: 'internal',
    send_email: true,
    overwrite: false,
    phone_no: '**********',
    country_code: 'IN',
    email_block: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return error if required fields are missing', async () => {
    const result = await service.createSentinelUsers({
      user_email: '',
      user_name: '',
    } as any);

    expect(result).toEqual({
      type: 'error',
      msg: 'Invalid input data.',
    });
  });

  it('should call registerByEmail with default client_id if not provided', async () => {
    const registerByEmail = jest.fn().mockResolvedValue({ type: 'success' });
    const userHelper = { registerByEmail };

    helperService.getHelper = jest.fn().mockResolvedValue(userHelper);
    configService.get = jest.fn().mockImplementation(key => {
      if (key === 'clientId') return 'defaultClientId';
    });

    const result = await service.createSentinelUsers({
      ...mockUserParams,
    } as any);

    expect(registerByEmail).toHaveBeenCalledWith({
      ...mockUserParams,
      client_id: 'defaultClientId',
    });
    expect(result).toEqual({ type: 'success' });
  });

  it('should call registerByEmail with provided client_id', async () => {
    const registerByEmail = jest.fn().mockResolvedValue({ type: 'success' });
    const userHelper = { registerByEmail };

    helperService.getHelper = jest.fn().mockResolvedValue(userHelper);

    const result = await service.createSentinelUsers({
      ...mockUserParams,
      client_id: 'customClient',
    } as any);

    expect(registerByEmail).toHaveBeenCalledWith({
      ...mockUserParams,
      client_id: 'customClient',
    });
    expect(result).toEqual({ type: 'success' });
  });

});



describe('getDataByEmailOrUid', () => {
  const mockUser = {
    uid: 1,
    email: '<EMAIL>',
    display_name: 'Test User',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return error if neither email nor uid is provided', async () => {
    const result = await service.getDataByEmailOrUid({});
    expect(result).toEqual({
      type: 'error',
      msg: 'Please provide either email or uid.',
      data: null,
    });
  });

  it('should return user data if email is provided and found', async () => {
    const mockUserRepo = {
      getUserByEmail: jest.fn().mockResolvedValue(mockUser),
    };

    helperService.get = jest.fn().mockResolvedValue(mockUserRepo);

    const result = await service.getDataByEmailOrUid({ email: '<EMAIL>' });

    expect(result).toEqual({
      type: 'success',
      msg: 'User data retrieved successfully.',
      data: mockUser,
    });

    expect(mockUserRepo.getUserByEmail).toHaveBeenCalledWith('<EMAIL>');
  });

  it('should return user data if uid is provided and found', async () => {
    const mockUserRepo = {
      findByUID: jest.fn().mockResolvedValue(mockUser),
    };

    helperService.get = jest.fn().mockResolvedValue(mockUserRepo);

    const result = await service.getDataByEmailOrUid({ uid: 1 });

    expect(result).toEqual({
      type: 'success',
      msg: 'User data retrieved successfully.',
      data: mockUser,
    });

    expect(mockUserRepo.findByUID).toHaveBeenCalledWith(1);
  });

  it('should return user not found if data is null', async () => {
    const mockUserRepo = {
      getUserByEmail: jest.fn().mockResolvedValue(null),
    };

    helperService.get = jest.fn().mockResolvedValue(mockUserRepo);

    const result = await service.getDataByEmailOrUid({ email: '<EMAIL>' });

    expect(result).toEqual({
      type: 'error',
      msg: 'User not found.',
      data: null,
    });
  });

 
});


describe('deactivateUserForGdpr', () => {
  const email = '<EMAIL>';
  const mockUser = { uid: 123, email: '<EMAIL>' };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return failure if user not found', async () => {
    const mockUserRepo = {
      getUserByEmail: jest.fn().mockResolvedValue(null),
    };

    helperService.get = jest.fn().mockResolvedValue(mockUserRepo);

    const result = await service.deactivateUserForGdpr(email);

    expect(result).toEqual({
      status: 'failed',
      msg: `User not found with email. ${email}`,
    });
  });

  it('should return response if user is already deactivated', async () => {
    const mockUserRepo = {
      getUserByEmail: jest.fn().mockResolvedValue(mockUser),
    };
    const mockUserApiHelper = {
      deleteAccountByUidForGDPR: jest.fn().mockResolvedValue({
        status: 'failed',
        msg: 'Error deactivating user',
      }),
    };

    helperService.get = jest
      .fn()
      .mockResolvedValueOnce(mockUserRepo)          // for UserRepository
      .mockResolvedValueOnce(mockUserApiHelper);    // for UserApiV1Helper

    const result = await service.deactivateUserForGdpr(email);

    expect(result).toEqual({
      status: 'failed',
      msg: 'Error deactivating user',
    });
  });

});

describe('UserApiInternalService - updateGdprDeleteStatus', () => {
  const email = '<EMAIL>';
  const response_data = 'GDPR delete status updated';
  const requestBody = { email, response_data };

  const mockUser = { uid: 101, email, status: 0 };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should update GDPR delete status successfully', async () => {
    const mockUserRepository = {
      getUserByEmail: jest.fn().mockResolvedValue(mockUser),
    };
    const mockUserApiV1Helper = {
      updateGdprDeleteStatus: jest.fn().mockResolvedValue(true),
    };

    helperService.get = jest.fn().mockResolvedValueOnce(mockUserRepository);
    helperService.getHelper = jest.fn().mockResolvedValueOnce(mockUserApiV1Helper);

    const result = await service.updateGdprDeleteStatus(requestBody);

    expect(result).toEqual({
      status: 'success',
      msg: 'Successfully updated result data.',
    });
  });

  it('should throw BadRequestException if user not found', async () => {
    const mockUserRepository = {
      getUserByEmail: jest.fn().mockResolvedValue(null),
    };

    helperService.get = jest.fn().mockResolvedValueOnce(mockUserRepository);

    await expect(service.updateGdprDeleteStatus(requestBody)).rejects.toThrow('UserNotFoundException');
  });

  it('should throw BadRequestException if user is not blocked', async () => {
    const mockUserRepository = {
      getUserByEmail: jest.fn().mockResolvedValue({ ...mockUser, status: 1 }),
    };

    helperService.get = jest.fn().mockResolvedValueOnce(mockUserRepository);

    await expect(service.updateGdprDeleteStatus(requestBody)).rejects.toThrow('UserAlreadyBlocked');
  });

  it('should throw BadRequestException if update fails', async () => {
    const mockUserRepository = {
      getUserByEmail: jest.fn().mockResolvedValue(mockUser),
    };
    const mockUserApiV1Helper = {
      updateGdprDeleteStatus: jest.fn().mockResolvedValue(false),
    };

    helperService.get = jest.fn().mockResolvedValueOnce(mockUserRepository);
    helperService.getHelper = jest.fn().mockResolvedValueOnce(mockUserApiV1Helper);

    await expect(service.updateGdprDeleteStatus(requestBody)).rejects.toThrow('ErrorOccured');
  });

  it('should catch unexpected errors and throw them', async () => {
    helperService.get = jest.fn().mockRejectedValue(new Error('Unexpected Error'));

    await expect(service.updateGdprDeleteStatus(requestBody)).rejects.toThrow('Unexpected Error');
  });
});

describe('UserApiInternalService - updateSentinelUsers', () => {
  const mockUpdateData: SentinelSyncUserUpdateDto = {
    user_id: '123',
    uid: 123,
    user_email: '<EMAIL>',
    user_name: 'testuser',
    user_roles: ['admin'],
    first_name: 'Test',
    last_name: 'User',
    pass: 'testpass',
    gid: 99,
    timezone: 'Asia/Kolkata',
    country_code: 'IN',
    phone_no: '**********',
    status: 1,
    linkedin_status: 1,
    profile_visibility: 'public',
  };

  const mockUser = {
    uid: 123,
    roles: ['student'],
    user_groups: [10],
  };

  const mockUpdatedUser = { ...mockUser, email: '<EMAIL>' };

  const mockUserRepository = {
    findByUID: jest.fn(),
    findOneAndUpdate: jest.fn(),
  };

  const mockUserMgmtHelper = {
    prepareAssignRoleList: jest.fn(),
    prepareSentinelUserRoleList: jest.fn(),
  };

  const mockUserHelper = {
    syncCloud6SentinelUserRole: jest.fn(),
    updateCloud6SentinelByUidOrMail: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    helperService.get = jest.fn().mockResolvedValue(mockUserRepository);
    helperService.getHelper = jest
      .fn()
      .mockResolvedValueOnce(mockUserMgmtHelper) // UserMgmtUtilityHelper
      .mockResolvedValueOnce(mockUserHelper); // UserHelper
  });

  it('should return error if updateData is missing or user_id is missing', async () => {
    const result = await service.updateSentinelUsers({} as any);
    expect(result).toEqual({
      type: 'error',
      msg: 'Invalid input: user_id is required.',
    });
  });

  it('should return error if any helper fails to load', async () => {
    helperService.get = jest.fn().mockRejectedValueOnce(new Error('fail'));
    const result = await service.updateSentinelUsers(mockUpdateData);
    expect(result).toEqual({
      type: 'error',
      msg: 'Failed to fetch required services.',
    });
  });

  it('should return error if user not found in DB', async () => {
    mockUserRepository.findByUID.mockResolvedValue(null);
    const result = await service.updateSentinelUsers(mockUpdateData);
    expect(result).toEqual({
      type: 'error',
      msg: 'User not found.',
    });
  });

  it('should return error if user update fails', async () => {
    mockUserRepository.findByUID.mockResolvedValue(mockUser);
    mockUserMgmtHelper.prepareAssignRoleList.mockResolvedValue(['admin']);
    mockUserRepository.findOneAndUpdate.mockResolvedValue(null);

    const result = await service.updateSentinelUsers(mockUpdateData);
    expect(result).toEqual({
      type: 'error',
      msg: 'Failed to update user data in MongoDB.',
    });
  });

  it('should return error if Sentinel sync fails', async () => {
    mockUserRepository.findByUID.mockResolvedValue(mockUser);
    mockUserMgmtHelper.prepareAssignRoleList.mockResolvedValue(['admin']);
    mockUserRepository.findOneAndUpdate.mockResolvedValue(mockUpdatedUser);
    mockUserMgmtHelper.prepareSentinelUserRoleList.mockResolvedValue(['admin']);
    mockUserHelper.syncCloud6SentinelUserRole.mockResolvedValue(true);
    mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(null);

    const result = await service.updateSentinelUsers(mockUpdateData);
    expect(result).toEqual({
      type: 'error',
      msg: 'Failed to synchronize user data with Sentinel.',
    });
  });

  it('should return success if all operations pass', async () => {
    mockUserRepository.findByUID.mockResolvedValue(mockUser);
    mockUserMgmtHelper.prepareAssignRoleList.mockResolvedValue(['admin']);
    mockUserRepository.findOneAndUpdate.mockResolvedValue(mockUpdatedUser);
    mockUserMgmtHelper.prepareSentinelUserRoleList.mockResolvedValue(['admin']);
    mockUserHelper.syncCloud6SentinelUserRole.mockResolvedValue(true);
    mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);

    const result = await service.updateSentinelUsers(mockUpdateData);
    expect(result).toEqual({
      type: 'success',
      msg: 'User data synchronized successfully.',
    });
  });

  it('should catch exception and return generic error', async () => {
    mockUserRepository.findByUID.mockRejectedValue(new Error('Something went wrong'));
    const result = await service.updateSentinelUsers(mockUpdateData);
    expect(result).toEqual({
      type: 'error',
      msg: 'An error occurred during synchronization.',
    });
  });
});

describe('UserApiInternalService - sentinelSyncUserUpdate', () => {
  const updateDataWithUid: SentinelSyncUserUpdateDto = {
    user_id: '101',
    uid: 101,
    user_email: '<EMAIL>',
    user_name: 'testuser',
    user_roles: ['admin'],
  };

  const updateDataWithoutUid: SentinelSyncUserUpdateDto = {
    uid: 122,
    user_email: '<EMAIL>',
    user_name: 'newuser',
    user_roles: ['editor'],
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call updateSentinelUsers if user_id is present', async () => {
    const updateSpy = jest
      .spyOn(service, 'updateSentinelUsers')
      .mockResolvedValue({ type: 'success', msg: 'Updated' });

    const result = await service.sentinelSyncUserUpdate(updateDataWithUid);
    expect(updateSpy).toHaveBeenCalledWith(updateDataWithUid);
    expect(result).toEqual({ type: 'success', msg: 'Updated' });
  });

  it('should call createSentinelUsers if user_id is not present', async () => {
    const createSpy = jest
      .spyOn(service, 'createSentinelUsers')
      .mockResolvedValue({ type: 'success', msg: 'Created' });

    const result = await service.sentinelSyncUserUpdate(updateDataWithoutUid as any);
    expect(createSpy).toHaveBeenCalledWith(updateDataWithoutUid);
    expect(result).toEqual({ type: 'success', msg: 'Created' });
  });

  it('should return error if exception is thrown', async () => {
    jest
      .spyOn(service, 'createSentinelUsers')
      .mockImplementation(() => {
        throw new Error('Test error');
      });

    const result = await service.sentinelSyncUserUpdate(updateDataWithoutUid as any);
    expect(result).toEqual({
      type: 'error',
      msg: 'An error occurred during synchronization.',
    });
  });
});

describe('UserApiInternalService - gdprAction', () => {
  const requestBody: GdprRequestDto = {
    action: 'Delete', // Should call `gdprDelete`
    useremail: '<EMAIL>',
    usid: 123,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call the correct GDPR method and return the result', async () => {
    const mockResult = { status: true, message: 'GDPR delete success' };

    const mockUserApiV1Helper = {
      gdprDelete: jest.fn().mockResolvedValue(mockResult),
    };

    helperService.getHelper = jest.fn().mockResolvedValue(mockUserApiV1Helper);

    const result = await service.gdprAction(requestBody);
    expect(helperService.getHelper).toHaveBeenCalledWith('UserApiV1Helper');
    expect(mockUserApiV1Helper.gdprDelete).toHaveBeenCalledWith(requestBody);
    expect(result).toEqual(mockResult);
  });

  it('should throw error if method does not exist in helper', async () => {
    const mockUserApiV1Helper = {}; // gdprDelete missing
    helperService.getHelper = jest.fn().mockResolvedValue(mockUserApiV1Helper);

    await expect(service.gdprAction(requestBody)).rejects.toThrow('MethodNotExists');
  });

  it('should throw error if helper method returns status false', async () => {
    const mockUserApiV1Helper = {
      gdprDelete: jest.fn().mockResolvedValue({
        status: false,
        message: 'GDPR deletion failed',
      }),
    };

    helperService.getHelper = jest.fn().mockResolvedValue(mockUserApiV1Helper);

    await expect(service.gdprAction(requestBody)).rejects.toThrow('GDPR deletion failed');
  });

  it('should catch and throw unexpected errors', async () => {
    helperService.getHelper = jest.fn().mockRejectedValue(new Error('Unexpected'));

    await expect(service.gdprAction(requestBody)).rejects.toThrow('Unexpected');
  });
});

describe('UserApiInternalService - frsCompleteSignup', () => {
  const requestBody = {
    email: ' <EMAIL> ',
    client_id: ' testClientId ',
  };

  const cookieBody = {
    simplilearn_first_page: encodeURIComponent('https://frs.example.com/home'),
    sl_su_utmz: 'utm123',
  };

  const mockUserHelper = {
    sendGA4Events: jest.fn().mockResolvedValue(true),
  };

  const emitSpy = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  
    helperService.getHelper = jest.fn().mockResolvedValue(mockUserHelper);
  
    // Override private readonly eventEmitter using defineProperty
    Object.defineProperty(service, 'eventEmitter', {
      value: { emit: emitSpy },
      writable: true, // necessary to allow Jest to override
    });
  });
  

  it('should emit event and send GA4 events successfully', async () => {
    await service.frsCompleteSignup(requestBody, cookieBody);

    expect(emitSpy).toHaveBeenCalledWith('user.complete_signup_frs', {
      email: '<EMAIL>',
      clientId: 'testClientId',
      gid: 2,
      payload: {
        type: 'frsImmersive',
        sl_utm_src: 'utm123',
        method: 'one tap',
        source: 'frs',
        utm_source: 'frs',
        branch_utm_source: 'frs',
        recommended_skillup_courses: '',
        url: 'https://frs.example.com/home',
      },
      eventName: 'Complete_Signup',
    });

    expect(mockUserHelper.sendGA4Events).toHaveBeenCalledWith(
      {
        email: '<EMAIL>',
        clientId: 'testClientId',
        authType: 'Signup',
      },
      cookieBody,
      'https://frs.example.com/home',
      '',
      'free'
    );
  });

 
});

describe('UserApiInternalService - addMemberToGroupWithRole', () => {
  const params = {
    email: '<EMAIL>',
    gids: [1],
    rids: [100],
  };

  const mockUser = {
    uid: 1,
    email: '<EMAIL>',
    status: 1,
    user_groups: [2],
    sso_attributes: JSON.stringify({ attr1: 'value1' }),
  };

  const mockRoles = [
    { _id: 'mongo-role-id', rid: 100, roleName: 'Admin' }
  ];

  const mockRoleRepo = {
    findAll: jest.fn().mockResolvedValue(mockRoles),
  };

  const mockUserRepo = {
    getUserByEmail: jest.fn().mockResolvedValue(mockUser),
    upsert: jest.fn().mockResolvedValue(true),
  };

  const mockUserHelper = {
    updateCloud6SentinelByUidOrMail: jest.fn().mockResolvedValue(true),
    syncCloud6SentinelUserRole: jest.fn().mockResolvedValue(true),
    prepareRoleListForDrupalSync: jest.fn().mockResolvedValue(['Admin']),
    syncUserDataWithMySQLDrupal: jest.fn().mockResolvedValue(true),
  };

  const mockSamlService = {
    saveSsoAttributes: jest.fn().mockResolvedValue(true),
  };

  beforeEach(() => {
    jest.clearAllMocks();

    helperService.get = jest.fn()
      .mockImplementationOnce(() => mockRoleRepo)
      .mockImplementationOnce(() => mockUserRepo);

    helperService.getHelper = jest.fn()
      .mockImplementationOnce(() => mockUserHelper)
      .mockImplementationOnce(() => mockSamlService);
  });

 

  it('should return error if role IDs are invalid', async () => {
    mockRoleRepo.findAll = jest.fn().mockResolvedValue([]);

    const result = await service.addMemberToGroupWithRole(params);

    expect(result).toEqual({
      type: 'error',
      msg: 'Please provide valid role IDs.',
    });
  });

  it('should return error if user not found', async () => {
    mockRoleRepo.findAll = jest.fn().mockResolvedValue(mockRoles); 
    mockUserRepo.getUserByEmail = jest.fn().mockResolvedValue(null);

    const result = await service.addMemberToGroupWithRole(params);

    expect(result).toEqual({
      type: 'error',
      msg: 'User account does not exist for the specified email-id.',
    });
  });

  it('should return error if user is blocked', async () => {
    mockRoleRepo.findAll = jest.fn().mockResolvedValue(mockRoles); 
    mockUserRepo.getUserByEmail = jest.fn().mockResolvedValue({ ...mockUser, status: 0 });

    const result = await service.addMemberToGroupWithRole(params);

    expect(result).toEqual({
      type: 'error',
      msg: 'User account does not exist or is blocked.',
    });
  });

  it('should return error if MongoDB update fails', async () => {
    mockUserRepo.getUserByEmail = jest.fn().mockResolvedValue({
      ...mockUser,
      status: 1, // critical!
    });
    mockUserRepo.upsert = jest.fn().mockResolvedValue(false);

    const result = await service.addMemberToGroupWithRole(params);

    expect(result).toEqual({
      type: 'error',
      msg: 'Some error occurred while assigning role to user.',
    });
  });

  it('should handle and log unexpected exceptions', async () => {
    mockRoleRepo.findAll = jest.fn().mockImplementation(() => {
      throw new Error('Unexpected failure');
    });

    const result = await service.addMemberToGroupWithRole(params);

    expect(result).toEqual({
      type: 'error',
      msg: 'Some error occurred while assigning role to user.',
    });
  });
});



describe('UserApiInternalService - resetPassword', () => {
  let service: UserApiInternalService;

  const mockUserRepository = {
    findOne: jest.fn(),
    findOneAndUpdate: jest.fn(),
  };

  const mockCacheService = {
    set: jest.fn(),
  };

  const mockUserHelper = {
    updateCloud6SentinelByUidOrMail: jest.fn(),
  };

  const mockHelperService = {
    get: jest.fn(),
    getHelper: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const moduleRef: TestingModule = await Test.createTestingModule({
      providers: [
        UserApiInternalService,
        { provide: HelperService, useValue: mockHelperService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: 'CRYPTO_HELPER', useValue: {} },
        { provide: EventEmitter2, useValue: mockEventEmitter },
      ],
    }).compile();

    service = moduleRef.get<UserApiInternalService>(UserApiInternalService);

    mockHelperService.get.mockImplementation((token) => {
      if (token === UserRepository) return mockUserRepository;
      if (token === CachingService) return mockCacheService;
      return null;
    });

    mockHelperService.getHelper.mockImplementation((token) => {
      if (token === 'UserHelper') return mockUserHelper;
    });

    mockConfigService.get.mockImplementation((key) => {
      if (key === 'clientSecret') return 'dummy-secret';
      if (key === 'passwordLength') return 6;
      if (key === 'maxPasswordLength') return 20;
      if (key === 'sessionCache') return 'sess_';
    });

    jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => true);
  });

  const validDto = {
    client_id: 'client123',
    user_id: 123,
    current_password: 'Old@123',
    new_password: 'New@1234',
  };

  it('should reset password successfully', async () => {
    jest.spyOn(Utility, 'validatePasswd').mockReturnValue({ type: 'success', msg: 'ok' });
    mockUserRepository.findOne.mockResolvedValue({ uid: 123, password: 'oldHashed', status: 1 });
    jest.spyOn(drupalHash, 'checkPassword').mockReturnValue(true);
    jest.spyOn(drupalHash, 'hashPassword').mockReturnValue('newHashed');
    mockUserRepository.findOneAndUpdate.mockResolvedValue({ uid: 123 });
    mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(true);

    const result = await service.resetPassword(validDto);

    expect(result).toEqual({
      type: 'success',
      msg: expect.stringContaining('Password has been reset successfully'),
    });
    expect(mockCacheService.set).toHaveBeenCalledWith('sess_123', null, 0);
  });

  it('should return error for missing user_id', async () => {
    const result = await service.resetPassword({ ...validDto, user_id: null } as any);
    expect(result).toEqual({ type: 'error', msg: 'InvalidUserParameter' });
  });

  it('should return error for weak password', async () => {
    jest.spyOn(Utility, 'validatePasswd').mockReturnValue({ type: 'error', msg: 'Weak password' });
    const result = await service.resetPassword(validDto);
    expect(result).toEqual({ type: 'error', msg: 'Weak password' });
  });

  it('should return error if user not found', async () => {
    jest.spyOn(Utility, 'validatePasswd').mockReturnValue({ type: 'success', msg: 'ok' });
    mockUserRepository.findOne.mockResolvedValue(null);
    const result = await service.resetPassword(validDto);
    expect(result).toEqual({ type: 'error', msg: 'UserNotFoundException' });
  });

  it('should return error if user is blocked', async () => {
    jest.spyOn(Utility, 'validatePasswd').mockReturnValue({ type: 'success', msg: 'ok' });
    mockUserRepository.findOne.mockResolvedValue({ uid: 123, status: 0 });
    const result = await service.resetPassword(validDto);
    expect(result).toEqual({ type: 'error', msg: 'UserAccountBlocked' });
  });

  it('should return error if current password is wrong', async () => {
    jest.spyOn(Utility, 'validatePasswd').mockReturnValue({ type: 'success', msg: 'ok' });
    mockUserRepository.findOne.mockResolvedValue({ uid: 123, password: 'hash', status: 1 });
    jest.spyOn(drupalHash, 'checkPassword').mockReturnValue(false);

    const result = await service.resetPassword(validDto);
    expect(result).toEqual({ type: 'error', msg: 'NotAuthorizedException' });
  });

  it('should return error if update fails', async () => {
    jest.spyOn(Utility, 'validatePasswd').mockReturnValue({ type: 'success', msg: 'ok' });
    mockUserRepository.findOne.mockResolvedValue({ uid: 123, password: 'hash', status: 1 });
    jest.spyOn(drupalHash, 'checkPassword').mockReturnValue(true);
    jest.spyOn(drupalHash, 'hashPassword').mockReturnValue('hashedPass');
    mockUserRepository.findOneAndUpdate.mockResolvedValue(null);
    mockUserHelper.updateCloud6SentinelByUidOrMail.mockResolvedValue(null);

    const result = await service.resetPassword(validDto);
    expect(result).toEqual({ type: 'error', msg: 'Some error occurred' });
  });

  it('should catch and return unexpected errors', async () => {
    mockHelperService.get.mockRejectedValue(new Error('Unexpected failure'));

    const result = await service.resetPassword(validDto);
    expect(result).toEqual({
      type: 'error',
      msg: 'Unexpected failure',
    });
  });
});


describe('UserApiInternalService - getTokenByEmail', () => {
  let service: UserApiInternalService;

  const mockHelperService = {
    getHelper: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockAuthHelper = {
    getTokenByEmail: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const moduleRef = await Test.createTestingModule({
      providers: [
        UserApiInternalService,
        { provide: HelperService, useValue: mockHelperService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: 'CRYPTO_HELPER', useValue: {} },
        { provide: EventEmitter2, useValue: mockEventEmitter },
      ],
    }).compile();

    service = moduleRef.get<UserApiInternalService>(UserApiInternalService);

    mockConfigService.get.mockImplementation((key: string) => {
      if (key === 'clientSecret') return 'dummy-secret';
      return '';
    });

    mockHelperService.getHelper.mockResolvedValue(mockAuthHelper);
    jest.spyOn(Utility, 'validateClientRequest').mockImplementation(() => true);
  });

  const reqData: GetTokenByEmailDto = {
    client_id: 'client123',
    user_email: '<EMAIL>',
    actor_email: '<EMAIL>',
    redirect_url: 'https://test.com',
  };

  it('should return token successfully', async () => {
    const mockToken = { token: 'jwt-token' };
    mockAuthHelper.getTokenByEmail.mockResolvedValue(mockToken);

    const result = await service.getTokenByEmail(reqData);

    expect(Utility.validateClientRequest).toHaveBeenCalledWith(
      reqData.client_id,
      'dummy-secret',
      reqData.redirect_url
    );

    expect(mockHelperService.getHelper).toHaveBeenCalledWith('AuthHelper');
    expect(mockAuthHelper.getTokenByEmail).toHaveBeenCalledWith(
      reqData.user_email,
      reqData.client_id,
      { actor_email: reqData.actor_email }
    );

    expect(result).toEqual(mockToken);
  });

  it('should use empty actor_email if not provided', async () => {
    const noActorData = {
      client_id: 'client123',
      user_email: '<EMAIL>',
    };

    const mockToken = { token: 'jwt-token' };
    mockAuthHelper.getTokenByEmail.mockResolvedValue(mockToken);

    const result = await service.getTokenByEmail(noActorData as GetTokenByEmailDto);

    expect(mockAuthHelper.getTokenByEmail).toHaveBeenCalledWith(
      '<EMAIL>',
      'client123',
      { actor_email: '' }
    );

    expect(result).toEqual(mockToken);
  });


});

describe('UserApiInternalService - registerUserFrs', () => {
  let service: UserApiInternalService;

  const mockHelperService = {
    get: jest.fn(),
    getHelper: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockPaperclipService = {
    getSkillupReferralRewardInfo: jest.fn(),
  };

  const mockAuthTokenHelper = {
    generateSessionTokens: jest.fn(),
  };

  const mockSocialLoginHelper = {
    createUserAccount: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const moduleRef = await Test.createTestingModule({
      providers: [
        UserApiInternalService,
        { provide: HelperService, useValue: mockHelperService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: 'CRYPTO_HELPER', useValue: {} },
        { provide: EventEmitter2, useValue: mockEventEmitter },
      ],
    }).compile();

    service = moduleRef.get<UserApiInternalService>(UserApiInternalService);

    mockHelperService.getHelper.mockImplementation(async (name) => {
      if (name === 'SocialLoginHelper') return mockSocialLoginHelper;
      if (name === 'AuthTokenHelper') return mockAuthTokenHelper;
    });

    mockHelperService.get.mockImplementation(async (token) => {
      if (token === PaperclipService) return mockPaperclipService;
      if (token === AuthTokenHelper) return mockAuthTokenHelper;
    });

    mockConfigService.get.mockReturnValue('client-key');
  });

  const userParams = {
    firstName: 'John',
    lastName: 'Doe',
    emailAddress: '<EMAIL>',
    phone_no: '**********',
    country_code: '+91',
  };

  it('should register user and return success response', async () => {
    const user = { uid: 101 };
    const token = { idToken: 'jwt-token' };
    const rewardData = {
      respData: {
        skillUpReferralInfo: {
          userRefCode: 'ABC123',
        },
      },
    };

    mockSocialLoginHelper.createUserAccount.mockResolvedValue({
      type: 'success',
      msg: 'Account created',
      data: user,
    });

    mockPaperclipService.getSkillupReferralRewardInfo.mockResolvedValue(rewardData);
    mockAuthTokenHelper.generateSessionTokens.mockResolvedValue(token);

    const result = await service.registerUserFrs(userParams);

    expect(mockSocialLoginHelper.createUserAccount).toHaveBeenCalledWith(expect.objectContaining({
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      user_email: '<EMAIL>',
    }));

    expect(result).toEqual({
      type: 'success',
      message: 'Account created',
      msg: 'User is registered',
      status: true,
      refCode: 'ABC123',
    });
  });

  it('should return error if createUserAccount fails', async () => {
    mockSocialLoginHelper.createUserAccount.mockResolvedValue({
      type: 'error',
      msg: 'User already exists',
    });

    const result = await service.registerUserFrs(userParams);

    expect(result).toEqual({
      type: 'error',
      message: 'User already exists',
      msg: 'Something went wrong',
    });
  });

  it('should handle unexpected errors gracefully', async () => {
    mockSocialLoginHelper.createUserAccount.mockRejectedValue(new Error('DB error'));

    const result = await service.registerUserFrs(userParams);

    expect(result).toEqual({
      type: 'error',
      message: 'DB error',
      msg: 'Something went wrong',
    });
  });
});

describe('UserApiInternalService - verifyAuthTokenByEmail', () => {
  let service: UserApiInternalService;

  const mockHelperService = {
    getHelper: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockAuthTokenHelper = {
    getSignHash: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const moduleRef = await Test.createTestingModule({
      providers: [
        UserApiInternalService,
        { provide: HelperService, useValue: mockHelperService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: 'CRYPTO_HELPER', useValue: {} },
        { provide: EventEmitter2, useValue: mockEventEmitter },
      ],
    }).compile();

    service = moduleRef.get<UserApiInternalService>(UserApiInternalService);
  });

  const baseUserData = {
    user_email: '<EMAIL>',
    client_id: 'client-123',
    authToken: 'valid-token',
  };

  it('should return error response if required fields are missing or invalid', async () => {
    const cases = [
      {}, // empty object
      { user_email: '<EMAIL>' }, // missing others
      { user_email: '<EMAIL>', client_id: 123, authToken: 'token' }, // invalid client_id type
    ];

    for (const userData of cases) {
      const result = await service.verifyAuthTokenByEmail(userData as any);
      expect(result).toEqual({ type: 'error', msg: '', data: '' });
    }
  });

  it('should return success if token matches', async () => {
    mockConfigService.get.mockReturnValue({
      'client-123': { secret: 'secret-key' },
    });

    mockHelperService.getHelper.mockResolvedValue(mockAuthTokenHelper);
    mockAuthTokenHelper.getSignHash.mockResolvedValue('valid-token');

    const result = await service.verifyAuthTokenByEmail(baseUserData);

    expect(mockConfigService.get).toHaveBeenCalledWith('clientSecret');
    expect(mockAuthTokenHelper.getSignHash).toHaveBeenCalledWith(
      JSON.stringify({ email: '<EMAIL>' }),
      'secret-key'
    );

    expect(result).toEqual({
      type: 'success',
      msg: 'success',
      data: 'valid-token',
    });
  });

  it('should return error if token does not match', async () => {
    mockConfigService.get.mockReturnValue({
      'client-123': { secret: 'secret-key' },
    });

    mockHelperService.getHelper.mockResolvedValue(mockAuthTokenHelper);
    mockAuthTokenHelper.getSignHash.mockResolvedValue('wrong-token');

    const result = await service.verifyAuthTokenByEmail(baseUserData);

    expect(result).toEqual({
      type: 'error',
      msg: '',
      data: '',
    });
  });

  it('should handle and log exceptions and return default error response', async () => {
    mockConfigService.get.mockImplementation(() => {
      throw new Error('Unexpected config error');
    });

    const result = await service.verifyAuthTokenByEmail(baseUserData);

    expect(result).toEqual({
      type: 'error',
      msg: '',
      data: '',
    });
  });
});

});
