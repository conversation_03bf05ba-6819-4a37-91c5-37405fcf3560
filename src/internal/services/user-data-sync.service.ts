import { Inject } from '@nestjs/common';
import { Logger } from '../../logging/logger';
import { HelperService } from '../../helper/helper.service';
import { IUserRepository, UserRepository } from '../../user/repositories/user/user.repository';
import * as drupalHash from 'drupal-hash';
import { UserSocial } from '../../db/mongo/schema/user/social.schema';
import { InjectRepository } from '@nestjs/typeorm';
import { SentinelUsersRole } from '../../db/mysql/entity/sentinel-users-roles.entity';
import { Repository } from 'typeorm';

export class UserDataSyncService {
    @Inject(HelperService) private readonly helperService: HelperService;
    @InjectRepository(SentinelUsersRole) private cloud6SentinelUserRoleRepository: Repository<SentinelUsersRole>;
    async syncUserDataToSentinel(userParams) {
        try {
            let response = { type: "error", msg: "Invalid Request" };
            let hashPassword;

            const [userRepository, userMgmtHelper, userHelper] = await Promise.all([
                this.helperService.getHelper<IUserRepository>(UserRepository),
                this.helperService.getHelper('UserMgmtUtilityHelper'),
                this.helperService.getHelper('UserHelper'),
            ]);

            //Prepare user social data
            let userSocialData: UserSocial[];
            if (userParams?.user_social_data) {
                let formatedUserData = Array.isArray(userParams?.user_social_data) ? userParams?.user_social_data : [userParams?.user_social_data];
                userSocialData = formatedUserData.map((social: any) => ({
                    type: social.type || '',
                    sub: social.sub || '',
                    email: social.email_address || '',
                    source: social.source || '',
                    status: social.status || '',
                    created_on: social.created_on || '',
                }));
            } else {
                userSocialData = [];
            }
            //Checking if email already exist
            const userInfo = await userRepository.getUserByEmail(userParams?.mail);            

            //Hashing password
            if (userParams.pass !== undefined) {
                hashPassword = drupalHash.hashPassword(userParams.pass);
            }

            //Prepare user roles
            const userRoles = userParams?.roles ? await userMgmtHelper.prepareAssignRoleList(userParams?.roles) : [];

            // get all unique social data types
            if (userInfo) {
                const existingSocialDataType = userInfo?.user_social_data.map(social => social.type) || [];
                if (userInfo?.user_social_data && userSocialData.length > 0) {
                    if (existingSocialDataType.length > 0 && !existingSocialDataType.includes(userSocialData[0]?.type)) {
                        // get the existing user social data
                        const existingUserSocialData = userInfo.user_social_data || [];
                        // if existing social data is empty, keep only new social data; else merge both
                        userSocialData = existingUserSocialData.length === 0
                        ? userSocialData
                        : [...userSocialData, ...existingUserSocialData];
                    } else {
                        //if the type already exists, update the existing social data
                        const indexToUpdate = existingSocialDataType.indexOf(userSocialData[0]?.type);
                        if (indexToUpdate !== -1) {
                            userInfo.user_social_data[indexToUpdate] = {...userSocialData[0] };
                            userSocialData = userInfo.user_social_data;
                        }
                    }
                }
            }
            const currentTime = new Date();

            let userData: any = {};
            userData = {
            ...(userParams.uid && { uid: Number(userParams.uid) }),
            ...(userParams.name && { name: userParams.name }),
            ...(userParams.mail && { email: userParams.mail }),
            ...(userParams.pass && { password: hashPassword }),
            ...(userParams.field_display_name && { display_name: userParams.field_display_name }),
            ...(userParams.field_phone_no && { phone_no: userParams.field_phone_no }),
            ...(userParams.field_country_code && { country_code: userParams.field_country_code }),
            ...(userParams.field_city_id && { location: userParams.field_city_id }),
            ...(userParams.field_accept_agreement && { accept_agreement: Boolean(userParams.field_accept_agreement) }),
            ...(userParams.field_user_options && { user_options: Number(userParams.field_user_options) }),
            ...(userParams.language && { language: userParams.language }),
            ...(userParams.status && { status: Number(userParams.status) }),
            ...(userParams.field_account_setup && { account_setup: Number(userParams.field_account_setup) }),
            ...(userParams.timezone && { timezone: userParams.timezone }),
            ...(userParams.field_password_created && { password_created: Number(userParams.field_password_created) }),
            ...(userParams.created && { created: Number(userParams.created) }),
            ...(userParams.signature !== undefined && { signature: userParams.signature }),
            ...(userParams.access && { access: currentTime }),
            ...(userParams.login && { login: currentTime }),
            ...(userParams.field_profile_visibility && { profile_visibility: userParams.field_profile_visibility }),
            ...(userParams.field_newsletter && { newsletter: Number(userParams.field_newsletter) }),
            ...(userParams.field_user_category && { user_category: userParams.field_user_category }),
            ...(userParams?.roles && { roles: userRoles }),
            ...(userParams.user_groups && { user_groups: userParams.user_groups ? userParams.user_groups : [] }),
            };

            if (userSocialData && userSocialData.length > 0) {
                userData = {
                    ...userData,
                    user_social_data: userSocialData,
                }
            }
            //Save user data to MongoDB
            const createUser = await userRepository.findOneAndUpdate(
              { uid: userData.uid },
              userData,
              { upsert: true, returnDocument: 'after' }
            );
            if (createUser && createUser.uid) {
                // add logger
                Logger.info('User Sync successful with MongoDB', {
                    METHOD: this.constructor.name + '@' + this.syncUserDataToSentinel.name,
                    MESSAGE: 'User Sync successful with MongoDB',
                    REQUEST: userData,
                    RESPONSE: createUser,
                    TIMESTAMP: new Date().getTime(),
                });
                if (userData.password !== undefined ) {
                    userData.pass = userData.password;
                    delete userData.password;
                }
                if(userData.email !== undefined) {
                    userData.mail = userData.email;
                    delete userData.email;
                }
                if(userData.signature !== undefined) {
                    delete userData.signature;
                }
                if(userData.roles) {
                    delete userData.roles;
                }                
                if(userData.user_groups) {
                    delete userData.user_groups;
                }
                if(userData.user_social_data) {
                    delete userData.user_social_data;
                }          
                if(userData.language) {
                    delete userData.language;
                }            
                if(userData.login) {
                    userData.login = currentTime.getTime();
                }
                if(userData.access) {
                    userData.access = currentTime.getTime();
                }
                    

                //Sync user data to Sentinel users table CREATE/UPDATE
                let cUser;
                if(!userInfo) {
                    // If user does not exist, create a new user in Sentinel
                    cUser = await userHelper.createCloud6SentinelUser(userData);
                } else {
                    // If user exists, update the existing user in Sentinel
                    cUser = await userHelper.updateCloud6SentinelByUidOrMail(userData);
                }


                // mysql sync action check
                if (!cUser) {
                    // If user creation or update in Sentinel fails, delete the user from MongoDB
                    Logger.error('Failed to sync with Sentinel database', {
                        METHOD: this.constructor.name + '@' + this.syncUserDataToSentinel.name,
                        MESSAGE: 'Failed to create or update user in Sentinel Mysql',
                        REQUEST: userData,
                        RESPONSE: cUser,
                        TIMESTAMP: new Date().getTime(),
                    });
                    response.msg = "Failed to sync with Sentinel database";
                    return response;
                } else {
                    Logger.info('User Sync successful with Sentinel (Mysql)', {
                        METHOD: this.constructor.name + '@' + this.syncUserDataToSentinel.name,
                        MESSAGE: 'User Sync successful with Sentinel',
                        REQUEST: userData,
                        RESPONSE: cUser,
                        TIMESTAMP: new Date().getTime(),
                    });
                    response.type = "success";
                    response.msg = "User Sync successful with Sentinel";
                }

                // updating user roles
                if (userParams?.roles) {
                    // Sync user roles to sentinel users roles table
                    const createRoleObjArray = await userMgmtHelper.prepareSentinelUserRoleList(userRoles, cUser?.uid);
                    await this.cloud6SentinelUserRoleRepository.upsert(createRoleObjArray, { conflictPaths: ['uid', 'rid'] });
                    Logger.info('User roles Sync successful with Sentinel (Mysql)', {
                        METHOD: this.constructor.name + '@' + this.syncUserDataToSentinel.name,
                        MESSAGE: 'User roles Sync successful with Sentinel',
                        REQUEST: createRoleObjArray,
                        RESPONSE: cUser,
                        TIMESTAMP: new Date().getTime(),
                    });
                }
            }
            return response;
        } catch (error: any) {
            Logger.error('syncUserDataToSentinel', {
                METHOD: this.constructor.name + '@' + this.syncUserDataToSentinel.name,
                MESSAGE: error.message,
                REQUEST: userParams,
                RESPONSE: error.stack,
                TIMESTAMP: new Date().getTime(),
            });
            return error;
        }
    }
}