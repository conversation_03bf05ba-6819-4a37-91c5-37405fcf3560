import { Test, TestingModule } from '@nestjs/testing';
import { UserDataSyncController } from './user-data-sync.controller';
import { UserDataSyncService } from '../services/user-data-sync.service';
import { APILog } from '../../logging/logger'; // Import APILog directly

describe('UserDataSyncController', () => {
  let controller: UserDataSyncController;
  let userDataSyncService: UserDataSyncService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserDataSyncController],
      providers: [
        {
          provide: UserDataSyncService,
          useValue: {
            syncUserDataToSentinel: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UserDataSyncController>(UserDataSyncController);
    userDataSyncService = module.get<UserDataSyncService>(UserDataSyncService);
  });

  describe('syncUserDataToSentinel', () => {
    const mockUserParams = { uid: 1, name: '<PERSON>' };

    it('should call service and return result successfully', async () => {
      const mockResult = { success: true };
      (userDataSyncService.syncUserDataToSentinel as jest.Mock).mockResolvedValue(mockResult);

      const result = await controller.syncUserDataToSentinel(mockUserParams);

      expect(userDataSyncService.syncUserDataToSentinel).toHaveBeenCalledWith(mockUserParams);
      expect(result).toEqual(mockResult);
    });

    it('should return result if service returns an Error instance', async () => {
      const mockError = new Error('Service error');
      (userDataSyncService.syncUserDataToSentinel as jest.Mock).mockResolvedValue(mockError);

      const result = await controller.syncUserDataToSentinel(mockUserParams);

      expect(userDataSyncService.syncUserDataToSentinel).toHaveBeenCalledWith(mockUserParams);
      expect(result).toBe(mockError);
    });

    it('should catch exception thrown by service and log the error', async () => {
      const thrownError = new Error('Unexpected error');
      (userDataSyncService.syncUserDataToSentinel as jest.Mock).mockRejectedValue(thrownError);

      // Correctly mock APILog.error to return a dummy Logger
      const apilogSpy = jest.spyOn(APILog, 'error').mockReturnValue({} as any);

      const result = await controller.syncUserDataToSentinel(mockUserParams);

      expect(apilogSpy).toHaveBeenCalledWith('syncUserDataToSentinel', expect.objectContaining({
        METHOD: expect.stringContaining('UserDataSyncController@syncUserDataToSentinel'),
        MESSAGE: thrownError.message,
        REQUEST: mockUserParams,
        RESPONSE: expect.any(String),
        TIMESTAMP: expect.any(Number),
      }));

      expect(result).toBe(thrownError);
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });
});
