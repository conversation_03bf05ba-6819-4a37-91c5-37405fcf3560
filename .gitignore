# compiled output
/dist
/node_modules
Dockerfile

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode
!.vscode/launch.json

# Ignore the mongocryptd.pid file
mongocryptd.pid
# Ignore the package-lock.json file
package-lock.json
aws.sts.credentials.json
/static/frontend/css/
/ssl
run.sh
