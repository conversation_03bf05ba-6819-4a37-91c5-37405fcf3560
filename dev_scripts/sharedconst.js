/*
 * TODO
 * 
 * Before using this example, 
 * 
 * 1. rename this file to 00_sharedconst.js
 * 2. update the constants below 
 * 
 * REGION : the AWS Region name where you want to use AWS KMS 
 * ROLE_ARN : an AWS IAM Role your created (see IAM directory for a script).
 * KMS_MASTER_KEY_ARN : The ARN of the Customer Master Key you created on AWS KMS
 * This role gives permission to this application to encrypt or decrypt data keys with AWS KMS
 * USER : the MongoDB cluster username
 * PWD  : the MongoDB cluster password 
 * HOST : the MongoDB cluster DNS name
 * 
 */
const fs  = require('fs');
const { join } = require('path');
const { AssumeRoleCommand } = require('@aws-sdk/client-sts');
const { STSClient } = require('@aws-sdk/client-sts');

const ACCOUNT_ID = '************';
const REGION = 'us-east-1';
const ROLE_ARN = `arn:aws:iam::${ACCOUNT_ID}:role/kms-encrypt-decrypt-role`;
const KMS_MASTER_KEY_ARN = `arn:aws:kms:${REGION}:${ACCOUNT_ID}:key/5aecb289-4528-4b7d-8026-d2b4bffc9d26`

const keyVaultDb = "encryption"
const keyVaultCollection = "__keyVault"

const connectionString = `mongodb+srv://udaysinghkushwah:<EMAIL>/${keyVaultDb}/?retryWrites=true&w=majority`;
const keyVaultNamespace = `${keyVaultDb}.${keyVaultCollection}`;

const kmsKeyARn = KMS_MASTER_KEY_ARN
const altKeyName = 'A_DATA_KEY'

async function getAWSCredentials() {
    try {
        const stsClient = new STSClient({ region: REGION });

        // Set the parameters
        const stsParams = {
            RoleArn: ROLE_ARN,
            RoleSessionName: "mongodb-session",
            DurationSeconds: 900
        };

        //Assume Role
        console.log('Assuming role to retrieve AWS credentials');
        const data = await stsClient.send(new AssumeRoleCommand(stsParams));
        console.log('Received STS data : ', data.AssumedRoleUser.AssumedRoleId);
        return {
            accessKeyId: data.Credentials.AccessKeyId,
            secretAccessKey: data.Credentials.SecretAccessKey,
            sessionToken: data.Credentials.SessionToken,
        };
    } catch (err) {
        console.log("Error", err);
    }
}


async function getKMSProviders(useAWSKMS = true) {
    if (useAWSKMS == true) {
        return getAWSKMSProviders();
    } else {
        return getLocalKMSProviders();
    }
}
async function getAWSKMSProviders() {
    // const roleCredentials = await getAWSCredentials() // this only for dev env
    return {
        aws: {
            accessKeyId: process.env.AWS_ACCESS_KEY_ID,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
            sessionToken: process.env.AWS_SESSION_TOKEN
        },
    }
}
async function getLocalKMSProviders() {
    const path = join(process.cwd(), './../master-key.txt');
    this.masterKey = fs.readFileSync(path);
    return {
        local: {
            key: this.masterKey
        },
    }
}
// https://stackoverflow.com/a/60615051/663360 
module.exports = {
    connectionString,
    keyVaultNamespace,
    getKMSProviders,
    kmsKeyARn,
    altKeyName,
    keyVaultDb,
    keyVaultCollection,
    REGION
}