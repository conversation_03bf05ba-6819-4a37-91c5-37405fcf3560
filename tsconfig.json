{"compilerOptions": {"target": "ES2020", "module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "sourceMap": true, "strict": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "noUnusedLocals": true, "noUnusedParameters": true}, "assets": ["**/*.hbs"]}