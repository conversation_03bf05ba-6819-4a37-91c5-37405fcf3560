'use strict'
require('dotenv').config();
const env = process.env.NODE_ENV;

let appName = '';
if (env === 'production') {
    appName = process.env.NEW_RELIC_APP_NAME || 'sentinel-prod-app';
} else if (env === 'testing' || env === 'test' || env === 'qa') {
    appName = process.env.NEW_RELIC_APP_NAME || 'sentinel-qa-app';
} else {
    appName = 'sentinel-dev-app';
}

exports.config = {
    app_name: [appName],
    license_key: process.env.NEW_RELIC_LICENSE_KEY,
    logging: {
        level: 'info', // 'trace' for debugging
    },
    allow_all_headers: true,
    attributes: {
        // capture custom headers
        enabled: true,
    },
};
